-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Ice Crystal duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Ice Crystal duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="Ice Crystals have {0} maximum Life"
			},
			[2]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Ice Crystal has {0} maximum Life"
			}
		},
		stats={
			[1]="frost_wall_maximum_life",
			[2]="frozen_locus_crystal_display_stat"
		}
	},
	[3]={
		[1]={
		},
		stats={
			[1]="skill_ground_effect_duration"
		}
	},
	["base_skill_effect_duration"]=1,
	["frost_wall_maximum_life"]=2,
	["frozen_locus_crystal_display_stat"]=2,
	parent="skill_stat_descriptions",
	["skill_ground_effect_duration"]=3
}