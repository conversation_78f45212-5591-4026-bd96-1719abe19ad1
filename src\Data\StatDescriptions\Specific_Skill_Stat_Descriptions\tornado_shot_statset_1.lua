-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[2]={
		stats={
			[1]="quality_display_base_number_of_projectiles_is_gem"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Tornado spits out {0} copies of Projectiles fired into it"
			}
		},
		stats={
			[1]="base_number_of_projectiles",
			[2]="skill_can_fire_arrows"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Limit {0} Tornado"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Limit {0} Tornadoes"
			}
		},
		stats={
			[1]="base_number_of_tornado_shots_allowed"
		}
	},
	[5]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Maximum Tornado duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Maximum Tornado duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[6]={
		[1]={
		},
		stats={
			[1]="number_of_tornado_shots_allowed"
		}
	},
	[7]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	[8]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Tornado disappears after being Hit once"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Tornado disappears after being Hit {0} times"
			}
		},
		stats={
			[1]="tornado_shot_number_of_hits_allowed"
		}
	},
	[9]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="Copied Projectiles deal {0:+d}% more damage"
			},
			[2]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Copied Projectiles deal {0}% more damage"
			},
			[3]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Copied Projectiles deal {0}% less damage"
			}
		},
		stats={
			[1]="tornado_shot_projectile_damage_+%_final",
			[2]="quality_display_tornado_shot_is_gem"
		}
	},
	[10]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Copied Projectiles have a maximum travel distance of {0} metres"
			}
		},
		stats={
			[1]="tornado_shot_projectile_range"
		}
	},
	[11]={
		[1]={
		},
		stats={
			[1]="total_number_of_projectiles_to_fire"
		}
	},
	["active_skill_base_area_of_effect_radius"]=1,
	["base_number_of_projectiles"]=3,
	["base_number_of_tornado_shots_allowed"]=4,
	["base_skill_effect_duration"]=5,
	["number_of_tornado_shots_allowed"]=6,
	parent="specific_skill_stat_descriptions/tornado_shot_statset_0",
	["quality_display_base_number_of_projectiles_is_gem"]=2,
	["quality_display_tornado_shot_is_gem"]=9,
	["skill_can_fire_arrows"]=3,
	["skill_effect_duration"]=7,
	["tornado_shot_number_of_hits_allowed"]=8,
	["tornado_shot_projectile_damage_+%_final"]=9,
	["tornado_shot_projectile_range"]=10,
	["total_number_of_projectiles_to_fire"]=11
}