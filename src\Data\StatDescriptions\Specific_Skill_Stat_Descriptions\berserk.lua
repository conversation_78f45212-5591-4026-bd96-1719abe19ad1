-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				[1]={
					k="divide_by_one_hundred_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Grants {0}% of Physical Attack Leeched as Life per Rage"
			}
		},
		stats={
			[1]="life_leech_from_physical_attack_damage_permyriad_per_rage"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="per_minute_to_per_second_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Lose {0}% of maximum Life per Rage per second while not losing Rage"
			}
		},
		stats={
			[1]="life_loss_%_per_minute_per_rage_while_not_losing_rage"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Grants {0}% increased Rage effect"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Grants {0}% reduced Rage effect"
			}
		},
		stats={
			[1]="rage_effect_+%"
		}
	},
	["life_leech_from_physical_attack_damage_permyriad_per_rage"]=1,
	["life_loss_%_per_minute_per_rage_while_not_losing_rage"]=2,
	parent="skill_stat_descriptions",
	["rage_effect_+%"]=3
}