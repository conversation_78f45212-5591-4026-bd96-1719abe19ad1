-- This file is automatically generated, do not edit!
--
-- Dexterity support gems
-- Skill data (c) Grinding Gear Games
--
local skills, mod, flag, skill = ...

skills["SupportFasterProjectilesPlayer"] = {
	name = "Acceleration",
	description = "Supports Projectile skills, making those Projectiles travel faster.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Projectile, SkillType.ProjectileSpeed, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.FixedSpeedProjectile, },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Acceleration",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_faster_projectiles_projectile_speed_+%_final"] = {
					mod("ProjectileSpeed", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_faster_projectiles_projectile_speed_+%_final", 40 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportAdherePlayer"] = {
	name = "Adhere",
	description = "Supports Grenade Skills. Grenades from Supported Skills do not bounce, instead halting movement where they intially land, but doing lower damage when they detonate.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Grenade, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Adhere",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_sticky_grenade_damage_+%_final"] = {
					mod("Damage", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_sticky_grenade_damage_+%_final", -20 },
			},
			stats = {
				"grenade_skill_does_not_bounce_off_ground",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportAlignmentPlayer"] = {
	name = "Alignment",
	description = "Supports Bow Attacks. Supported Skills indicate one of four directions, changing indicated direction when an Attack with Supported Skill matches that direction. Attacks from Supported Skills which match the indicated direction deal much more Damage. Cannot Support Channelled Skills.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Bow, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Channel, },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Alignment",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_called_shots_projectile_damage_+%_final_when_matching_direction", 30 },
				{ "called_shot_aiming_delay_ms", 1000 },
				{ "called_shot_angle_allowance_degs", 40 },
			},
			stats = {
				"support_called_shots_enable_directional_buff",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportAmmoConservationPlayer"] = {
	name = "Ammo Conservation",
	description = "Supports Crossbow Ammunition Skills, granting a chance to not consume a Bolt when firing.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.CrossbowAmmoSkill, SkillType.CrossbowSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Ammo Conservation",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["crossbow_attack_%_chance_to_not_consume_ammo"] = {
					mod("ChanceToNotConsumeAmmo", "BASE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "crossbow_attack_%_chance_to_not_consume_ammo", 25 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportBlindPlayer"] = {
	name = "Blind",
	description = "Supports any skill that Hits enemies, causing them to Blind on Hit.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 10, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Blind",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "global_chance_to_blind_on_hit_%", 100 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportBlindsidePlayer"] = {
	name = "Blindside",
	description = "Supports Skills which can cause Damaging Hits. Supported Skills are more likely to Critically Hit Blinded Enemies, and deal more Damage with Critical Hits against Blinded Enemies, but cannot themselves inflict Blind.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowSkill, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.DegenOnlySpellDamage, },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Blindside",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_unseen_critical_damage_multiplier_+%_final_vs_blinded_enemies"] = {
					mod("CritMultiplier", "MORE", nil, 0, 0, { type = "ActorCondition", actor = "enemy", var = "Blinded" } ),
				},
				["support_unseen_critical_strike_chance_+%_final_vs_blinded_enemies"] = {
					mod("CritChance", "MORE", nil, 0, 0, { type = "ActorCondition", actor = "enemy", var = "Blinded" } ),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_unseen_critical_damage_multiplier_+%_final_vs_blinded_enemies", 15 },
				{ "support_unseen_critical_strike_chance_+%_final_vs_blinded_enemies", 15 },
			},
			stats = {
				"cannot_inflict_blind",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportBloodInTheEyesPlayer"] = {
	name = "Blood in the Eyes",
	description = "Supports Skills that Hit Enemies, causing those Hits to Hobble Bleeding Enemies based on a portion of Physical Damage dealt.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Blood in the Eyes",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "physical_damage_%_dealt_as_evasion_break_vs_bleeding", 15 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportDazedBreakPlayer"] = {
	name = "Break Posture",
	description = "Supports Attacks, causing them to Daze Enemies when they fully Break Armour.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Break Posture",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_daze_break_duration_ms", 8000 },
			},
			stats = {
				"support_apply_daze_on_armour_break",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportAdditionalAccuracyPlayer"] = {
	name = "Bullseye",
	description = "Supports Attacks, causing them to gain Accuracy.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 10, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Bullseye",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_additional_accurary_rating_+%_final"] = {
					mod("Accuracy", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_additional_accurary_rating_+%_final", 50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportBurstingPlaguePlayer"] = {
	name = "Bursting Plague",
	description = "Supports skills that can Poison enemies, causing Poisoned enemies to gain Plague over time and explode in a Plague Burst on death. Cannot support the skills of Minions.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	ignoreMinionTypes = true,
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Support",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_bursting_plague_store_%_poison_applied_magnitude_towards_burst", 100 },
				{ "support_bursting_plague_max_value_%_of_max_life", 20 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}skills["PlagueBurstPlayer"] = {
	name = "Plague Burst",
	hidden = true,
	description = "Triggered when an enemy Poisoned by a supported skill dies, dealing Physical damage around them based on the built-up Plague.",
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Triggered] = true, [SkillType.InbuiltTrigger] = true, [SkillType.Damage] = true, [SkillType.SkillGrantedBySupport] = true, [SkillType.Area] = true, [SkillType.Physical] = true, [SkillType.Chaos] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 0.1, },
	},
	statSets = {
		[1] = {
			label = "Plague Burst",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "plague_burst",
			baseFlags = {
			},
			constantStats = {
				{ "active_skill_base_area_of_effect_radius", 18 },
				{ "plague_burst_area_+%_final_maximum", 100 },
				{ "plague_burst_%_stored_value_to_deal_as_physical_damage", 100 },
				{ "additional_base_critical_strike_chance", 500 },
			},
			stats = {
				"is_area_damage",
				"plague_burst_triggered_by_bursting_plague_death",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportCadencePlayer"] = {
	name = "Cadence",
	description = "Supports Attacks you use yourself. Supported Skills gain more Attack speed each time you use them, but will become unusable if used too frequently in a short time frame.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.UsedByTotem, SkillType.SummonsTotem, SkillType.NoAttackOrCastTime, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Cadence",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_measured_speed_attack_speed_+%_final_per_stack", 8 },
				{ "support_measured_speed_maximum_stacks", 6 },
				{ "support_measured_speed_disabled_ms", 10000 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportCaltropsPlayer"] = {
	name = "Caltrops",
	description = "Supports Ranged Spear Attacks. Supported Skills create Caltrops when Projectiles they create reach the end of their flight.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Spear, SkillType.RangedAttack, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.SkillGrantedBySupport, },
	levels = {
		[1] = { attackTime = 1, levelRequirement = 0, manaMultiplier = 20, },
	},
	statSets = {
		[1] = {
			label = "Caltrops",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			stats = {
				"trigger_caltrops_at_end_of_projectile_flight",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["TriggeredCaltropsPlayer"] = {
	name = "Caltrops",
	hidden = true,
	description = "Maims and deals Physical area damage when triggered by an Enemy. Caltrops are destroyed when triggered, and a maximum of 20 Caltrops can be active at once.",
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Triggered] = true, [SkillType.Damage] = true, [SkillType.Area] = true, [SkillType.Physical] = true, [SkillType.Projectile] = true, [SkillType.ProjectileNoCollision] = true, [SkillType.SkillGrantedBySupport] = true, [SkillType.Hazard] = true, [SkillType.Duration] = true, [SkillType.CannotChain] = true, [SkillType.Attack] = true, [SkillType.GroundTargetedProjectile] = true, [SkillType.NoAttackOrCastTime] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { attackTime = 1, critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Caltrops",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "triggered_caltrops",
			baseFlags = {
				projectile = true,
				duration = true,
				area = true,
			},
			constantStats = {
				{ "triggered_by_caltrops_support_%", 100 },
				{ "base_number_of_projectiles", 1 },
				{ "active_skill_base_area_of_effect_radius", 5 },
				{ "active_skill_base_secondary_area_of_effect_radius", 30 },
				{ "base_skill_effect_duration", 8000 },
				{ "main_hand_base_physical_damage_from_%_dex", 60 },
			},
			stats = {
				"global_maim_on_hit",
				"additive_thorns_damage_modifiers_apply_to_attack_damage",
				"replace_main_hand_unarmed_attack_stats_with_nothing_type",
				"base_is_projectile",
				"is_hazard",
				"is_area_damage",
				"projectiles_not_offset",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportChainPlayer"] = {
	name = "Chain",
	description = "Supports Projectile skills and any other skills that Chain, causing them to Chain additional times.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Chains, SkillType.Projectile, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.CannotChain, SkillType.ProjectileNoCollision, },
	levels = {
		[1] = { PvPDamageMultiplier = -30, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Chain",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_chain_hit_damage_+%_final"] = {
					mod("Damage", "MORE", nil, ModFlag.Hit),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "number_of_chains", 1 },
				{ "chains_hit_X_more_times", 1 },
				{ "support_chain_hit_damage_+%_final", -50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportChargedShotsPlayer"] = {
	name = "Charged Shots",
	description = "Supports Bow Attacks. Every third shot with Supported Skills restores a portion of it's Mana cost and Gains Damage as extra Lightning Damage.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Bow, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	ignoreMinionTypes = true,
	levels = {
		[1] = { manaMultiplier = 10, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Charged Shots",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_charged_shots_applies_every_X_shots", 3 },
				{ "support_charged_shots_recover_%_mana_cost_on_use", 100 },
				{ "support_charged_shots_%_damage_to_gain_as_lightning", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportCharmBountyPlayer"] = {
	name = "Charm Bounty",
	description = "Supports any Skill that Hits Enemies, causing Enemies it kills to grant more Charm charges.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowSkill, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Charm Bounty",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_charm_charges_gained_+%_final_from_killing_blow", 100 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportCloseCombatPlayer"] = {
	name = "Close Combat",
	description = "Supports Attacks, causing them to deal more damage to enemies based on how close they are to you.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	ignoreMinionTypes = true,
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Close Combat",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_close_combat_attack_damage_+%_final_from_distance"] = {
					mod("Damage", "MORE", nil, 0, 0, { type = "DistanceRamp", ramp = {{10,1},{35,0}} }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_close_combat_attack_damage_+%_final_from_distance", 25 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportComboFinisherPlayer"] = {
	name = "Combo Finisher",
	description = "Supports Melee Attacks you use yourself. Supported Skills cannot be used until enough Combo has been built up, but deal massively more damage. Cannot support skills which already Combo, or Triggered Skills.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Melee, },
	addSkillTypes = { SkillType.ComboStacking, SkillType.SupportedByComboFinisher, SkillType.HasUsageCondition, },
	excludeSkillTypes = { SkillType.Cooldown, SkillType.Herald, SkillType.Triggered, SkillType.InbuiltTrigger, SkillType.SummonsTotem, SkillType.UsedByTotem, SkillType.Trapped, SkillType.RemoteMined, SkillType.Spell, SkillType.Warcry, SkillType.HasUsageCondition, SkillType.SupportedByComboFinisher, SkillType.NOT, SkillType.AND, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Combo Finisher",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_combo_finisher_required_number_of_combo_stacks", 5 },
				{ "support_combo_finisher_damage_+%_final", 40 },
				{ "base_combo_stacks_decay_delay_ms", 4000 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportCommiseratePlayer"] = {
	name = "Commiserate",
	description = "Supports Skills you use yourself which can cause Damaging Hits. Supported Skills inflict more powerful Ailments if you are afflicted with those Ailments when you inflict them, while also removing from you any Ailment that they inflict.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowSkill, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Minion, SkillType.UsedByTotem, SkillType.SummonsTotem, SkillType.DegenOnlySpellDamage, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Commiserate",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "ailment_effect_+%_while_afflicted_by_relevant_ailment", 30 },
			},
			stats = {
				"remove_ailment_when_applying_relevant_ailment",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportMultiplePoisonPlayer"] = {
	name = "Comorbidity",
	description = "Supports any skill that Hits enemies, allowing it to inflict an extra Poison on enemies but shortening the duration of those Poisons.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Comorbidity",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_multi_poison_poison_duration_+%_final"] = {
					mod("EnemyPoisonDuration", "MORE", nil),
				},
				["number_of_additional_poison_stacks"] = {
					mod("PoisonStacks", "BASE", nil),
					flag("PoisonCanStack"),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "number_of_additional_poison_stacks", 1 },
				{ "support_multi_poison_poison_duration_+%_final", -20 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportChanceToShockPlayer"] = {
	name = "Conduction",
	description = "Supports any skill that Hits enemies, making it more likely to Shock.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Conduction",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_conduction_chance_to_shock_+%_final"] = {
					mod("EnemyShockChance", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_conduction_chance_to_shock_+%_final", 100 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportCorrosionPlayer"] = {
	name = "Corrosion",
	description = "Supports any skill that Hits enemies, causing Poison it applies to also Break enemy Armour. Cannot support skills that Consume Fully Broken Armour.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.ConsumesFullyBrokenArmour, },
	levels = {
		[1] = { manaMultiplier = 10, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Corrosion",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "armour_break_for_%_of_poison_damage_over_poison_duration", 80 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportCrescendoPlayer"] = {
	name = "Crescendo",
	description = "Supports Strikes that perform a Final Strike at the end of a combination of attacks, allowing you to use the Final Strike twice in succession.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.PerformsFinalStrike, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Crescendo",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			stats = {
				"repeat_last_step_of_combo_attack",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportCullingStrikePlayer"] = {
	name = "Culling Strike",
	description = "Supports Attack Skills, causing them to Cull Rare and Unique Enemies on Hit.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Culling Strike",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_culling_strike_vs_rare_or_unique_enemy"] = {
					mod("CullPercent", "MAX", nil, 0, 0, { type = "ActorCondition", actor = "enemy", var = "RareOrUnique" }),
					value = 10
				},
			},
			baseFlags = {
			},
			stats = {
				"support_culling_strike_vs_rare_or_unique_enemy",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportCulminationPlayer"] = {
	name = "Culmination",
	description = "Supports Melee Attack Skills. Supported Skills gain Combo when you successfully Strike Enemies with other Melee Attacks. Supported Skills reset their Combo on use, dealing more Damage the higher Combo you had on use. Cannot support skills which already gain Combo. Cannot Support Skills used by Minions.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Melee, SkillType.AND, },
	addSkillTypes = { SkillType.ComboStacking, SkillType.SupportedByComboMastery, SkillType.HasUsageCondition, },
	excludeSkillTypes = { SkillType.HasUsageCondition, SkillType.SupportedByComboMastery, SkillType.NOT, SkillType.AND, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Culmination",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_damage_+%_final_per_combo_stack", 3 },
				{ "base_combo_stacks_decay_delay_ms", 4000 },
			},
			stats = {
				"skill_uncapped_combo_counter",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportDazzlePlayer"] = {
	name = "Dazzle",
	description = "Supports Attack Skills. Supported Skills always hit Dazed Enemies, but Consume Daze on Hit, and cannot themselves inflict Daze.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.CrossbowSkill, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Dazzle",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			stats = {
				"consume_enemy_daze_to_always_hit",
				"cannot_daze",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportDeadlyHeraldsPlayer"] = {
	name = "Deadly Herald",
	description = "Supports Herald Skills, making their triggered effects deal more damage at the expense of higher Spirit cost.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Herald, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { spiritReservationFlat = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Deadly Herald",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_deadly_heralds_damage_+%_final"] = {
					mod("Damage", "MORE", nil),
				}
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_deadly_heralds_damage_+%_final", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportDeadlyPoisonPlayer"] = {
	name = "Deadly Poison",
	description = "Supports any skill that Hits enemies, causing it to deal less damage but inflict more potent Poison.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Deadly Poison",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_deadly_poison_hit_damage_+%_final"] = {
					mod("Damage", "MORE", nil, ModFlag.Hit),
				},
				["support_deadly_poison_poison_effect_+%_final"] = {
					mod("AilmentMagnitude", "MORE", nil, 0, KeywordFlag.Poison),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_deadly_poison_hit_damage_+%_final", -25 },
				{ "support_deadly_poison_poison_effect_+%_final", 75 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportSlowerProjectilesPlayer"] = {
	name = "Deceleration",
	description = "Supports Projectile skills, making those Projectiles travel more slowly.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Projectile, SkillType.ProjectileSpeed, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.FixedSpeedProjectile, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Deceleration",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_slower_projectiles_projectile_speed_+%_final"] = {
					mod("ProjectileSpeed", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_slower_projectiles_projectile_speed_+%_final", -30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportDelayedGratificationPlayer"] = {
	name = "Delayed Gratification",
	description = "Supports Skills which require some Condition to be met for use. Supported Skills can be used an additional time after their Condition has been met, but meeting that Condition is harder.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.HasUsageCondition, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Delayed Gratification",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "difficulty_of_meeting_conditional_requirement_+%", 150 },
				{ "conditional_skill_additional_usages_after_meeting_requirement", 1 },
				{ "conditional_skill_additional_usages_duration_ms", 10000 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportDelayedReactionPlayer"] = {
	name = "Delayed Reaction",
	description = "Supports Skills which create Hazards, causing those Hazards to only be able to trigger immediately after creation or at the end of their duration. Created Hazards have significantly lowered duration, and deal more damage.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Hazard, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Delayed Reaction",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_sunblast_hazard_hazard_damage_+%_final"] = {
					mod("Damage", "MORE", nil),
				},
				["support_sunblast_hazard_hazard_duration_+%_final"] = {
					mod("Duration", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_sunblast_hazard_hazard_damage_+%_final", 15 },
				{ "support_sunblast_hazard_hazard_duration_+%_final", -70 },
			},
			stats = {
				"hazards_trigger_at_end_of_duration_instead",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportDeliberationPlayer"] = {
	name = "Deliberation",
	description = "Supports skills that can be used while moving, causing you to move slower while using them, but granting more damage in exchange.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.UsableWhileMoving, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Deliberation",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_deliberation_damage_+%_final"] = {
					mod("Damage", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_deliberation_movement_speed_penalty_+%_final_while_performing_action", 30 },
				{ "support_deliberation_damage_+%_final", 20 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportDazingPlayer"] = {
	name = "Discombobulate",
	description = "Supports Attacks, causing them to build up Daze based on a portion of Physical Damage dealt.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Discombobulate",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_physical_damage_%_to_gain_as_daze_build_up", 100 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportElectrocutePlayer"] = {
	name = "Electrocute",
	description = "Supports any Skill which can deal Damage. Lightning Damage from Supported Skills can inflict Electrocute, but Supported Skills cannot inflict Shock.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Electrocute",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			stats = {
				"base_lightning_damage_can_electrocute",
				"never_shock",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportEncumberancePlayer"] = {
	name = "Encumbrance",
	description = "Supports any skill, causing inflicted Slows to be more powerful.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.Spell, SkillType.DegenOnlySpellDamage, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Encumbrance",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "base_debuff_slow_magnitude_+%", 15 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportChanceToPoisonPlayer"] = {
	name = "Envenom",
	description = "Supports any skill that Hits enemies, giving it a chance to Poison enemies.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Envenom",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "base_chance_to_poison_on_hit_%", 50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportExcoriatePlayer"] = {
	name = "Excoriate",
	description = "Supports Melee Attack Skills. Supported Skills deal more Melee Damage for each Elemental Ailment on Enemies they hit, but cannot themselves inflict Elemental Ailments.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Melee, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 15, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Excoriate",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_elemental_assault_melee_damage_+%_final_per_elemental_ailment_on_target"] = {
					mod("Damage", "MORE", nil, ModFlag.Melee, 0, { type = "ActorCondition", actor = "enemy", var = "Ignited" }),
					mod("Damage", "MORE", nil, ModFlag.Melee, 0, { type = "ActorCondition", actor = "enemy", var = "Chilled" }),
					mod("Damage", "MORE", nil, ModFlag.Melee, 0, { type = "ActorCondition", actor = "enemy", var = "Frozen" }),
					mod("Damage", "MORE", nil, ModFlag.Melee, 0, { type = "ActorCondition", actor = "enemy", var = "Shocked" }),
					mod("Damage", "MORE", nil, ModFlag.Melee, 0, { type = "ActorCondition", actor = "enemy", var = "Electrocuted" }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_elemental_assault_melee_damage_+%_final_per_elemental_ailment_on_target", 10 },
			},
			stats = {
				"cannot_inflict_elemental_ailments",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportFerocityPlayer"] = {
	name = "Ferocity",
	description = "Supports Skills that you use yourself. Supported Skills will consume a Frenzy Charge on use if possible, and will gain significant Skill Speed if they do. Supported Skills cannot generate Frenzy Charges.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.CrossbowAmmoSkill, SkillType.Attack, },
	addSkillTypes = { SkillType.SupportedByFerocity, },
	excludeSkillTypes = { SkillType.Minion, SkillType.SummonsTotem, SkillType.SupportedByTumult, SkillType.UsedByTotem, SkillType.Persistent, SkillType.SkillConsumesFrenzyChargesOnUse, SkillType.SupportedByFerocity, SkillType.NOT, SkillType.AND, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Ferocity",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["skill_consume_frenzy_charge_to_gain_skill_speed_+%_final"] = {
					mod("Speed", "MORE", nil, 0, 0, { type = "MultiplierThreshold", var = "RemovableFrenzyCharge", threshold = 1 }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "skill_consume_frenzy_charge_to_gain_skill_speed_+%_final", 40 },
			},
			stats = {
				"skill_cannot_generate_frenzy_charges",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportFlowPlayer"] = {
	name = "Flow",
	description = "Supports Skills which require Combo to use. Supported Skills lose their Combo stacks after a longer delay spent not gaining any Combo.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.ComboStacking, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Flow",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "combo_falloff_speed_+%", -60 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportForkPlayer"] = {
	name = "Fork",
	description = "Supports Projectile skills, making their Projectiles Fork.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Projectile, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.ProjectileNoCollision, },
	levels = {
		[1] = { manaMultiplier = 10, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Fork",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_fork_forked_projectile_damage_+%_final"] = {
					mod("Damage", "MORE", nil, 0, 0, { type = "StatThreshold", stat = "ForkedCount", threshold = 1 }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "terrain_arrow_attachment_chance_reduction_+%", 100 },
				{ "support_fork_forked_projectile_damage_+%_final", -30 },
			},
			stats = {
				"projectiles_fork",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportGambleshotPlayer"] = {
	name = "Gambleshot",
	description = "Supports Skills which fire Projectiles that are not ground targeted. Projectiles from Supported Skills randomly Fork, Chain or Pierce.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Projectile, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.GroundTargetedProjectile, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Gambleshot",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				[ "support_gambleshot_projectile_damage_+%_final"] = {
					mod("Damage", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_gambleshot_projectile_damage_+%_final", -15 },
			},
			stats = {
				"projectile_randomly_fork_chain_or_pierce",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportHitAndRunPlayer"] = {
	name = "Hit and Run",
	description = "Supports Attack Skills you use yourself which do not already have some Condition for use. Supported Skills can only be used after you have moved a certain distance, but have massively increased Skill Speed. Cannot support Skills which have a Cooldown or that are Triggered.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, },
	addSkillTypes = { SkillType.HasUsageCondition, SkillType.SupportedByMobileAssault, },
	excludeSkillTypes = { SkillType.HasUsageCondition, SkillType.SupportedByMobileAssault, SkillType.NOT, SkillType.AND, SkillType.Instant, SkillType.Cooldown, SkillType.Triggered, SkillType.UsedByTotem, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Hit and Run",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_mobile_assault_skill_speed_+%_final", 40 },
				{ "skill_unusable_until_moved_X_distance", 100 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportImpalePlayer"] = {
	name = "Impale",
	description = "Supports Attacks, causing them to Impale on Hit but making them unable to Extract Impale themselves.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.CrossbowSkill, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Impale",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "impale_on_hit_%_chance", 100 },
			},
			stats = {
				"cannot_consume_impale",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportCooldownReductionPlayer"] = {
	name = "Ingenuity",
	description = "Supports skills with Cooldowns, increasing the rate at which those Cooldowns Recover.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Cooldown, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 10, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Ingenuity",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_cooldown_reduction_cooldown_recovery_+%"] = {
					mod("CooldownRecovery", "INC", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_cooldown_reduction_cooldown_recovery_+%", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportInhibitorPlayer"] = {
	name = "Inhibitor",
	description = "Supports any Skill you use yourself or that you Trigger. Supported Skills cannot consume Charges by any means. Cannot Support Skills which require Charges to be used.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowSkill, SkillType.CrossbowAmmoSkill, SkillType.Spell, SkillType.DegenOnlySpellDamage, },
	addSkillTypes = { SkillType.CannotConsumeCharges, },
	excludeSkillTypes = { SkillType.UsedByTotem, SkillType.Trapped, SkillType.RemoteMined, SkillType.RequiresCharges, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Inhibitor",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_inhibitor_damage_+%_final_per_charge_type"] = {
					mod("Damage", "MORE", nil, 0, 0, { type = "MultiplierThreshold", var = "FrenzyCharge", threshold = 1 }),
					mod("Damage", "MORE", nil, 0, 0, { type = "MultiplierThreshold", var = "PowerCharge", threshold = 1 }),
					mod("Damage", "MORE", nil, 0, 0, { type = "MultiplierThreshold", var = "EnduranceCharge", threshold = 1 }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_inhibitor_damage_+%_final_per_charge_type", 4 },
			},
			stats = {
				"cannot_consume_power_frenzy_endurance_charges",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportInnervatePlayer"] = {
	name = "Innervate",
	description = "Supports Attacks you use yourself. Killing a Shocked enemy with supported skills infuses all of your Attacks with Lightning damage for a short time.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { SkillType.Duration, },
	excludeSkillTypes = { SkillType.Trapped, SkillType.RemoteMined, SkillType.SummonsTotem, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Innervate",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_innervate_buff_grant_%_added_lightning_attack_damage"] = {
					mod("DamageGainAsLightning", "BASE", nil, ModFlag.Attack, 0, { type = "Condition", var = "KilledShockedLast3Seconds" }, { type = "GlobalEffect", effectType = "Buff", effectName = "Innervate" }),
				},
				["support_innervate_base_buff_duration"] = {
					mod("Duration", "BASE", nil, 0, 0, { type = "Condition", var = "KilledShockedLast3Seconds" }, { type = "GlobalEffect", effectType = "Buff" }),
					div = 1000,
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_innervate_buff_grant_%_added_lightning_attack_damage", 35 },
				{ "support_innervate_buff_base_duration_ms", 5000 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportLastingShockPlayer"] = {
	name = "Lasting Shock",
	description = "Supports any skill that Hits enemies or inflicts Shock, causing its Shocks to be inflicted less often but last longer.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Lasting Shock",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_lasting_shock_chance_to_shock_+%_final"] = {
					mod("EnemyShockChance", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "shock_duration_+%", 100 },
				{ "support_lasting_shock_chance_to_shock_+%_final", -30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportLeveragePlayer"] = {
	name = "Leverage",
	description = "Supports Skills which Hit Enemies. Supported Skills gain increased chance to Critically Hit against Immobilised Enemies.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.CrossbowAmmoSkill, SkillType.Attack, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Leverage",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "critical_strike_chance_+%_vs_immobilised_enemies", 50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportLifeFlaskPlayer"] = {
	name = "Life Bounty",
	description = "Supports any skill that Hits enemies, causing enemies it kills to grant more Life Flask charges.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Life Bounty",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_life_flask_charges_gained_+%_final_from_killing_blow", 100 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportLifeOnCullPlayer"] = {
	name = "Life Drain",
	description = "Supports any skill that Hits enemies, causing you to recover Life when it Culls an enemy.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Life Drain",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "recover_%_maximum_life_on_cull", 12 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportLightningExposurePlayer"] = {
	name = "Lightning Exposure",
	description = "Supports any skill that Hits enemies, causing it to inflict Lightning Exposure when inflicting Shock.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { SkillType.Duration, },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Lightning Exposure",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "inflict_lightning_exposure_for_x_ms_on_shock", 8000 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportAddedLightningDamagePlayer"] = {
	name = "Lightning Infusion",
	description = "Supports Attacks, causing them to Gain Lightning Damage but deal less Cold and Fire Damage.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Lightning Infusion",
			baseEffectiveness = 0.52710002660751,
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_cold_and_fire_damage_+%_final"] = {
					mod("ColdDamage", "MORE", nil),
					mod("FireDamage", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "non_skill_base_all_damage_%_to_gain_as_lightning_with_attacks", 25 },
				{ "support_cold_and_fire_damage_+%_final", -50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportLightningPenetrationPlayer"] = {
	name = "Lightning Penetration",
	description = "Supports any skill that Hits enemies, making those Hits Penetrate enemy Lightning resistance.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Lightning Penetration",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "base_reduce_enemy_lightning_resistance_%", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportLockdownPlayer"] = {
	name = "Lockdown",
	description = "Supports any skill that Hits enemies, causing any Pin buildup it inflicts to be stronger the closer the enemy is to you.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Lockdown",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_lockdown_distance_based_pin_damage_+%_final", 120 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportFarCombatPlayer"] = {
	name = "Longshot",
	description = "Supports Attacks, causing them to deal more damage from farther away.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	ignoreMinionTypes = true,
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Longshot",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_far_combat_attack_damage_+%_final_from_distance"] = {
					mod("Damage", "MORE", nil, 0, 0, { type = "DistanceRamp", ramp = {{35,0},{70,1}} }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_far_combat_attack_damage_+%_final_from_distance", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportMaimPlayer"] = {
	name = "Maim",
	description = "Supports Attacks, causing them to Maim enemies.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { SkillType.AppliesMaim, },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { levelRequirement = 3, },
		[3] = { levelRequirement = 6, },
	},
	statSets = {
		[1] = {
			label = "Maim",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "maim_on_hit_%", 100 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportMaladyPlayer"] = {
	name = "Malady",
	description = "Supports Skills which can cause Damaging Hits, causing base chance to inflict Bleed with Supported Skills to instead apply to base chance to Poison, and causing base chance to Poison to instead apply to Bleed.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowSkill, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Malady",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			stats = {
				"base_bleed_chance_is_poison_chance_instead",
				"base_poison_chance_is_bleed_chance_instead",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportManaFlaskPlayer"] = {
	name = "Mana Bounty",
	description = "Supports any skill that Hits enemies, causing enemies it kills to grant more Mana Flask charges.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Mana Bounty",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_mana_flask_charges_gained_+%_final_from_killing_blow", 100 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportFasterAttackPlayer"] = {
	name = "Martial Tempo",
	description = "Supports Attacks, causing them to Attack faster.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Herald, SkillType.NoAttackOrCastTime, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Martial Tempo",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_faster_attacks_attack_speed_+%_final"] = {
					mod("Speed", "MORE", nil, ModFlag.Attack),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_faster_attacks_attack_speed_+%_final", 20 },
				{ "support_faster_attacks_damage_+%_final", 0 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportMobilityPlayer"] = {
	name = "Mobility",
	description = "Supports skills that can be used while moving, allowing you to move faster while using them.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.UsableWhileMoving, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Mobility",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_mobility_damage_+%_final", 0 },
				{ "support_mobility_movement_speed_penalty_+%_final_while_performing_action", -25 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportMomentumPlayer"] = {
	name = "Momentum",
	description = "Supports any damaging skill that you use yourself, causing it to deal more damage if you move a sufficient distance while using the skill. Teleportation does not count towards the distance travelled.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Triggered, SkillType.Trapped, SkillType.RemoteMined, SkillType.SummonsTotem, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Momentum",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_momnetum_damage_+%_final_with_momentum"] = {
					mod("Damage", "MORE", nil, 0, 0, { type = "Condition", var = "Moved2m" }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_momentum_distance_travelled_to_gain_momentum", 20 },
				{ "support_momnetum_damage_+%_final_with_momentum", 40 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportEmpoweredCullPlayer"] = {
	name = "Murderous Intent",
	description = "Supports skills that can Empower skills other than themselves, causing skills they Empower to Cull enemies.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.EmpowersOtherSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Murderous Intent",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_empowered_culling_strike"] = {
					mod("ExtraEmpowerMod", "LIST", { mod = mod("CullPercent", "MAX", nil), unscalable = true }),
					value = 10,
				}
			},
			baseFlags = {
			},
			stats = {
				"support_empowered_culling_strike",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportNeuralOverloadPlayer"] = {
	name = "Neural Overload",
	description = "Supports any skill that Hits enemies. Nearby enemies are marked when they are Primed for Electrocution, and hitting a marked enemy with a supported skill will Electrocute them.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Neural Overload",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			stats = {
				"active_skill_show_overload_range",
				"active_skill_can_overload",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportNimbleReloadPlayer"] = {
	name = "Nimble Reload",
	description = "Supports Crossbow Ammunition Skills, causing them to Reload bolts significantly faster.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.CrossbowAmmoSkill, SkillType.CrossbowSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Nimble Reload",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "reload_speed_+%", 60 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportOutmaneuverPlayer"] = {
	name = "Outmaneuver",
	description = "Supports Skills you use yourself which can cause Damaging Hits. Supported Skills Break Armour against Parried Enemies.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowSkill, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.UsedByTotem, SkillType.SummonsTotem, SkillType.Minion, SkillType.Trapped, SkillType.RemoteMined, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { manaMultiplier = 15, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Outmaneuver",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "armour_break_physical_damage_%_dealt_as_armour_break_vs_parried_enemies", 25 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportIncreaseLimitPlayer"] = {
	name = "Overabundance",
	description = "Supports skills which can have a Limited number of effects active at once, increasing that Limit at the cost of their duration. Only applies to restrictions that use the word \"Limit\".",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Limit, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Overabundance",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_limit_skill_effect_duration_+%_final"] = {
					mod("Duration", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "base_limit_+", 1 },
				{ "support_limit_skill_effect_duration_+%_final", -50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportOverchargePlayer"] = {
	name = "Overcharge",
	description = "Supports any skill that Hits enemies, making its Shocks more effective but reflecting them back to you as well.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Overcharge",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "shock_effect_+%", 50 },
			},
			stats = {
				"shocks_reflected_to_self",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportOverextendPlayer"] = {
	name = "Overextend",
	description = "Supports Attack Skills you use yourself. Supported Skills deal more Damage with Critical Hits, but Critically Hitting an Enemy with them will Daze you for a short time.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Minion, SkillType.SummonsTotem, SkillType.UsedByTotem, },
	levels = {
		[1] = { manaMultiplier = 15, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Overextend",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_overextend_critical_strike_multiplier_+%_final"] = {
					mod("CritMultiplier", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_overextend_critical_strike_multiplier_+%_final", 30 },
				{ "daze_self_on_critical_hit_for_X_ms", 5000 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportPayloadPlayer"] = {
	name = "Payload",
	description = "Supports Skills which fire Grenades, giving fired Grenades a chance to activate again, but lengthening their cooldowns.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Grenade, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Payload",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "grenade_skill_%_chance_to_explode_twice", 50 },
				{ "base_cooldown_speed_+%", -70 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportPerfectionPlayer"] = {
	name = "Perfection",
	description = "Supports skills which have a benefit for Perfectly Timing their use. On successfully executing Perfect Timing with Supported Skills, gain Perfection, which is a powerful damage buff. However, failing to execute any Perfect Timing (even with skills not supported by Perfection) will remove all Perfection on you.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.PerfectTiming, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Perfection",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "gain_X_perfection_on_successful_perfect_timing", 1 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportPerpetualChargePlayer"] = {
	name = "Perpetual Charge",
	description = "Supports skills that consume Power, Frenzy or Endurance Charges on use, giving them a chance not to remove each Charge while still gaining the benefits of consuming them.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.ConsumesCharges, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 30, levelRequirement = 0, },
		[2] = { levelRequirement = 0, },
		[3] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Perpetual Charge",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "charge_skip_consume_chance_%", 35 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportPiercePlayer"] = {
	name = "Pierce",
	description = "Supports Projectile skills, making their Projectiles Pierce an enemy but deal less damage after doing so.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Projectile, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.ProjectileNoCollision, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Pierce",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "base_chance_to_pierce_%", 100 },
				{ "support_pierce_projectile_damage_+%_final_if_pierced_enemy", -20 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportPinPlayer"] = {
	name = "Pin",
	description = "Supports any skill that Hits enemies, allowing its Physical damage to Pin enemies but making it unable to Stun.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Pin",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_pin_spell_pinned_art_variation", 1 },
				{ "support_pin_hit_damage_stun_multiplier_+%_final", -100 },
			},
			stats = {
				"support_pin_physical_damage_can_pin",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportPracticedComboPlayer"] = {
	name = "Practiced Combo",
	description = "Supports Strike Skills. Supported Skills have a chance to build additional Combo on Hit. Cannot support Skills used by Minions.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.MeleeSingleTarget, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Minion, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Practiced Combo",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "additional_combo_gain_chance_%", 40 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportPrecisionPlayer"] = {
	name = "Precision",
	description = "Supports Persistent Buff Skills, causing you to gain increased Accuracy while the Skill is active.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Persistent, SkillType.Buff, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { spiritReservationFlat = 10, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Precision",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_precision_accuracy_rating_+%"] = {
					mod("Accuracy", "INC", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Precision" } ),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_precision_accuracy_rating_+%", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportMultipleChargesPlayer"] = {
	name = "Profusion",
	description = "Supports skills that can generate Charges, giving them a chance to generate an additional Charge when they do so.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.GeneratesCharges, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Profusion",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "chance_to_gain_1_more_charge_%", 50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportPunchThroughPlayer"] = {
	name = "Punch Through",
	description = "Supports Melee Attack Skills. Supported Skills apply Easy Target when Knocking Back Enemies, causing the next Projectile Attack Hit they take to deal increased Damage.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Melee, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 15, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Punch Through",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "apply_attack_projectile_weakness_%_per_1m_knockback", 3 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportPursuitPlayer"] = {
	name = "Pursuit",
	description = "Supports Melee Attacks. Supported Skills deal more Melee damage if you have struck with a Projectile Attack Hit in the last eight seconds, but cannot themselves deal Projectile Damage.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Melee, SkillType.Attack, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Pursuit",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_advancing_assault_melee_damage_+%_final_if_projectile_attack_damage_hit_in_past_8_seconds"] = {
					mod("Damage", "MORE", nil, ModFlag.Melee, 0, { type = "Condition", var = "HitProjectileRecently" } ),
				},
				["support_advancing_assault_projectile_damage_+%_final"] = {
					mod("Damage", "MORE", nil, ModFlag.Projectile),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_advancing_assault_melee_damage_+%_final_if_projectile_attack_damage_hit_in_past_8_seconds", 25 },
				{ "support_advancing_assault_projectile_damage_+%_final", -100 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportReachPlayer"] = {
	name = "Reach",
	description = "Supports Attack Skills which deal Damage in an Area. Supported Skills have significantly increased Area of Effect, but are less Accurate against nearby targets.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Area, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Reach",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_reach_accuracy_within_2m_+%_final"] = {
					mod("Accuracy", "MORE", nil, 0, 0, { type = "MultiplierThreshold", var = "enemyDistance", threshold = 20, upper = true } ),
				},
				["support_reach_area_of_effect_+%_final"] = {
					mod("AreaOfEffect", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_reach_area_of_effect_+%_final", 35 },
				{ "support_reach_accuracy_within_2m_+%_final", -80 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportRearmPlayer"] = {
	name = "Rearm",
	description = "Supports Skills which create Hazards. Hazards created by Supported Skills have a chance to be rearmed a short duration after triggering.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Hazard, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 10, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Rearm",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "hazard_rearm_%_chance", 25 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportRetortPlayer"] = {
	name = "Retort",
	description = "Supports Attacks you use yourself, causing them to Consume the Parried Debuff on Hit to grant you a Frenzy Charge. Cannot support Skills which already Consume the Parried Debuff.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Minion, SkillType.UsedByTotem, SkillType.SkillConsumesParried, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Retort",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "consume_parry_debuff_on_hit_to_gain_X_frenzy_charges", 1 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportRetreatPlayer"] = {
	name = "Retreat",
	description = "Supports Skills which create Projectiles. Supported Skills deal more damage with Projectiles if you have struck with a Melee Hit in the last eight seconds, but cannot themselves deal Melee Damage.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Projectile, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Retreat",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_retreating_assault_projectile_damage_+%_final_if_melee_hit_in_past_8_seconds"] = {
					mod("Damage", "MORE", nil, ModFlag.Projectile, 0, { type = "Condition", var = "HitMeleeRecently" } ),
				},
				["support_retreating_assault_melee_damage_+%_final"] = {
					mod("Damage", "MORE", nil, ModFlag.Melee),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_retreating_assault_projectile_damage_+%_final_if_melee_hit_in_past_8_seconds", 25 },
				{ "support_retreating_assault_melee_damage_+%_final", -100 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportRicochetPlayer"] = {
	name = "Ricochet",
	description = "Supports any Skill which creates Projectiles, giving those Projectiles a chance to Chain when impacting terrain.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Projectile, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.CannotChain, SkillType.CannotTerrainChain, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Ricochet",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "projectile_chance_to_chain_1_extra_time_from_terrain_%", 40 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportSalvoPlayer"] = {
	name = "Salvo",
	description = "Supports Attack Skills you use yourself which fire Projectiles. Supported Skills accumulate Seals over time, and consume them when used. For each Seal consumed, the Attack will fire additional Projectiles. Projectiles from Supported Skills are fired in random directions. Cannot Support Skills which require Combo, have a Cooldown or already gain Seals.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Projectile, SkillType.AND, },
	addSkillTypes = { SkillType.HasSeals, SkillType.SupportedBySalvo, },
	excludeSkillTypes = { SkillType.Minion, SkillType.UsedByTotem, SkillType.Vaal, SkillType.Cooldown, SkillType.ComboStacking, SkillType.HasSeals, SkillType.SupportedBySalvo, SkillType.NOT, SkillType.AND, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Salvo",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_salvo_seals_gain_base_interval_ms", 2000 },
				{ "support_salvo_additional_projectiles_fired_per_seal", 2 },
				{ "support_salvo_maximum_seals", 3 },
			},
			stats = {
				"random_projectile_direction",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportMultipleProjectilesPlayer"] = {
	name = "Scattershot",
	description = "Supports Projectile skills, making them fire extra Projectiles. Also lowers Attack and Cast speed of supported skills, as well as lessening their damage.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Projectile, SkillType.ProjectileNumber, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.ProjectilesNumberModifiersNotApplied, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Scattershot",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_multiple_damage_+%_final"] = {
					mod("Damage", "MORE", nil),
				},
				["support_multiple_attack_and_cast_speed_+%_final"] = {
					mod("Speed", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "number_of_additional_projectiles", 2 },
				{ "support_multiple_damage_+%_final", -35 },
				{ "terrain_arrow_attachment_chance_reduction_+%", 200 },
				{ "support_multiple_attack_and_cast_speed_+%_final", -20 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportSecondWindPlayer"] = {
	name = "Second Wind",
	description = "Supports skills with cooldowns, giving them extra uses of that cooldown.\nCannot support instant or triggered skills.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Cooldown, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Triggered, SkillType.Instant, },
	levels = {
		[1] = { manaMultiplier = 50, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Second Wind",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "base_cooldown_speed_+%_final", -50 },
			},
			stats = {
				"support_double_number_of_cooldown_uses",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportShockingLeapPlayer"] = {
	name = "Shocking Leap",
	description = "Supports Skills that leap into the air and deal damage, causing them to create Shocked Ground the first time they Critically Hit an enemy.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Jumping, },
	addSkillTypes = { SkillType.CreatesGroundEffect, SkillType.Area, SkillType.Duration, },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Shocking Leap",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_shocking_leap_shocked_ground_duration_ms", 4000 },
				{ "support_shocking_leap_shocked_ground_radius", 20 },
			},
			stats = {
				"support_shocking_leap_create_shocked_ground_on_leap",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportSingleOutPlayer"] = {
	name = "Single Out",
	description = "Supports Mark Skills. Enemies affected by Supported Marks will have their Armour Broken by Physical Damage they take.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Mark, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Single Out",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "marked_target_%_physical_damage_taken_as_armour_break", 10 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportManaOnCullPlayer"] = {
	name = "Soul Drain",
	description = "Supports any skill that Hits enemies, causing you to recover Mana when it Culls an enemy.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Soul Drain",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "recover_%_maximum_mana_on_cull", 12 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportSpectralVolleyPlayer"] = {
	name = "Spectral Volley",
	description = "Supports Bow or Spear Projectile Attacks you use yourself. Supported Skills deal lower damage initially, but generate Spectral Projectiles, which deal more damage when they fire. Spectral Projectiles linger and will fire a short duration after no new Spectral Projectiles have been created. Cannot support Channelling Skills, Leaping skills, or skills which rain Projectiles from above.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Bow, SkillType.Attack, SkillType.AND, SkillType.Spear, SkillType.Projectile, SkillType.AND, SkillType.Attack, SkillType.AND, SkillType.OR, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Rain, SkillType.Channel, SkillType.Jumping, },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Spectral Volley",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_spectral_arrows_damage_+%_final_with_spectral_projectiles", 20 },
				{ "support_spectral_arrows_damage_+%_final_with_non_spectral_projectiles", -60 },
				{ "support_spectral_arrows_base_duration_ms", 3000 },
				{ "support_spectral_arrows_delay_between_projectiles_ms", 100 },
				{ "support_spectral_arrows_maximum_spectral_arrows", 5 },
			},
			stats = {
				"skill_has_spectral_arrows",
				"triggered_skills_ignore_cost",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportStormchainPlayer"] = {
	name = "Stormchain",
	description = "Supports Projectile Skills and Skills which can Chain. Supported Skills always Chain when initially Hitting a Shocked Enemy, but cannot themselves inflict Shock. Does not support Skills which cannot Chain.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Chains, SkillType.Projectile, },
	addSkillTypes = { SkillType.SupportedByStormchain, },
	excludeSkillTypes = { SkillType.ProjectileNoCollision, SkillType.CannotChain, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Stormchain",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "chance_to_chain_from_shocked_enemy_%", 100 },
			},
			stats = {
				"never_shock",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportSwiftAfflictionPlayer"] = {
	name = "Swift Affliction",
	description = "Supports any skill that deals damage, causing it to deal more damage over time but have a shorter duration.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.DamageOverTime, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Swift Affliction",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_rapid_decay_damage_over_time_+%_final"] = {
					mod("Damage", "MORE", nil, ModFlag.Dot),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_rapid_decay_damage_over_time_+%_final", 30 },
				{ "support_swift_affliction_skill_effect_and_damaging_ailment_duration_+%_final", -20 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportTumultPlayer"] = {
	name = "Tumult",
	description = "Supports Skills you use yourself which can cause Damaging Hits. Supported Skills consume a Frenzy Charge on use to powerfully Break Armour.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowSkill, },
	addSkillTypes = { SkillType.SupportedByTumult, },
	excludeSkillTypes = { SkillType.UsedByTotem, SkillType.SummonsTotem, SkillType.Minion, SkillType.Trapped, SkillType.RemoteMined, SkillType.SkillConsumesFrenzyChargesOnUse, SkillType.SupportedByTumult, SkillType.NOT, SkillType.AND, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Tumult",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "skill_consume_frenzy_charge_to_armour_break_for_%_of_physical_damage", 50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportTwofoldPlayer"] = {
	name = "Twofold",
	description = "Supports any Skill that Consumes Charges, granting a chance for benefits of that Consumption to be doubled.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.ConsumesCharges, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	ignoreMinionTypes = true,
	levels = {
		[1] = { manaMultiplier = 15, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Twofold",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "chance_%_to_double_effect_of_removing_charges", 20 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportUnerringPowerPlayer"] = {
	name = "Unerring Power",
	description = "Supports Attack Skills you use yourself. When Supported Attacks are Empowered, they have significantly lowered cost and raised Accuracy.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.CrossbowSkill, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.UsedByTotem, SkillType.SummonsTotem, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Unerring Power",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_discount_skill_cost_+%_final_if_empowered"] = {
					mod("Cost", "MORE", nil, 0, 0, { type = "Condition", var = "Empowered" } )
				},
				["support_discount_accuracy_rating_+%_final_if_empowered"] = {
					mod("Accuracy", "MORE", nil, 0, 0, { type = "Condition", var = "Empowered" } )
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_discount_skill_cost_+%_final_if_empowered", -30 },
				{ "support_discount_accuracy_rating_+%_final_if_empowered", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportUntouchablePlayer"] = {
	name = "Untouchable",
	description = "Supports Attack Skills you use yourself. While using Supported Skills, you gain increased Evasion Rating, with the increase scaling higher the longer the Attack time of the supported Skill. Cannot Support Channelling Skills.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Attack, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Minion, SkillType.UsedByTotem, SkillType.SummonsTotem, SkillType.Channel, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Untouchable",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "evasion_+%_while_performing_action_per_250_ms_attack_time", 25 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportVoltPlayer"] = {
	name = "Volt",
	description = "Supports Projectile Attack Skills you use yourself. Supported Skills gain Voltaic Charge as you move, up to a maximum. When used, Supported Skills expend that Charge to gain extra Lightning Damage and Chain.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.RangedAttack, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Minion, SkillType.UsedByTotem, },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Volt",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_static_charge_charge_gain_per_metre", 3 },
				{ "support_static_charge_X_chains_per_10_charge", 1 },
				{ "support_static_charge_%_damage_gained_as_lightning_per_10_charge", 5 },
				{ "support_static_charge_maximum_charge", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportWarmbloodedPlayer"] = {
	name = "Warm Blooded",
	description = "Supports Persistent Buff Skills, causing Freeze applied to you to last for a shorter duration while the Supported Skill is active.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.Persistent, SkillType.Buff, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { spiritReservationFlat = 15, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Warm Blooded",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_freeze_protection_spirit_cost_freeze_duration_on_self_+%_final"] = {
					mod("SelfFreezeDuration", "MORE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Warm Blooded" }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_freeze_protection_spirit_cost_freeze_duration_on_self_+%_final", -50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportKnockbackWavePlayer"] = {
	name = "Wind Wave",
	description = "Supports any skill that you can use, triggering a blast of wind that Knocks Back enemies if you are Stunned while using it.",
	color = 2,
	support = true,
	requireSkillTypes = { },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Instant, SkillType.Persistent, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Support",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			stats = {
				"support_knockback_wave_on_stunned",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}skills["KnockbackWavePlayer"] = {
	name = "Wind Wave",
	hidden = true,
	skillTypes = { [SkillType.Area] = true, [SkillType.Triggered] = true, [SkillType.Triggerable] = true, [SkillType.InbuiltTrigger] = true, [SkillType.SkillGrantedBySupport] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Wind Wave",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "knockback_wave",
			baseFlags = {
			},
			constantStats = {
				{ "base_knockback_distance", 1200 },
				{ "generic_knockback_+%_final_at_min_distance", 50 },
				{ "generic_knockback_+%_final_at_max_distance", -90 },
				{ "generic_knockback_distance_limit", 20 },
				{ "active_skill_base_area_of_effect_radius", 20 },
				{ "base_knockback_speed_+%", 300 },
			},
			stats = {
				"trigger_on_stunned_while_performing_supported_skill",
				"base_deal_no_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportWindowOfOpportunityPlayer"] = {
	name = "Window of Opportunity",
	description = "Supports Channelling skills that have special effects if released with Perfect Timing, causing them to deal much more damage if released with Perfect Timing but reducing the Perfect Timing window.",
	color = 2,
	support = true,
	requireSkillTypes = { SkillType.PerfectTiming, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Window of Opportunity",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_window_of_opportunity_perfect_timing_damage_+%_final"] = {
					mod("Damage", "MORE", nil, 0, 0, { type = "Condition", var = "PerfectTiming" }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "perfect_timing_window_ms_+%", -35 },
				{ "support_window_of_opportunity_perfect_timing_damage_+%_final", 35 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}