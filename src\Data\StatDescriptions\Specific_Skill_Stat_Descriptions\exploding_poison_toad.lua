-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% chance to Trigger this Skill on Kill"
			}
		},
		stats={
			[1]="trigger_toad_spawn_chance_%"
		}
	},
	[2]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[3]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Toad Explosion radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Toad Explosion radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} Base Physical Damage"
			}
		},
		stats={
			[1]="attack_minimum_added_physical_damage",
			[2]="attack_maximum_added_physical_damage"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% chance to Poison on Hit"
			}
		},
		stats={
			[1]="base_chance_to_poison_on_hit_%"
		}
	},
	["active_skill_area_of_effect_radius"]=2,
	["active_skill_base_area_of_effect_radius"]=3,
	["attack_maximum_added_physical_damage"]=4,
	["attack_minimum_added_physical_damage"]=4,
	["base_chance_to_poison_on_hit_%"]=5,
	parent="skill_stat_descriptions",
	["trigger_toad_spawn_chance_%"]=1
}