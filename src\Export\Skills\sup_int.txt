-- Path of Building
--
-- Intelligence support gems
-- Skill data (c) Grinding Gear Games
--
local skills, mod, flag, skill = ...

#skill SupportAbidingHexPlayer
#set SupportAbidingHexPlayer
#mods
#skillEnd

#skill SupportAblationPlayer
#set SupportAblationPlayer
statMap = {
	["support_ablation_offering_skill_damage_+%_final"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "SkillType", skillType = SkillType.Offering }),
	},
},
#mods
#skillEnd

#skill SupportAcrimonyPlayer
#set SupportAcrimonyPlayer
#mods
#skillEnd

#skill SupportAmbrosiaPlayer
#set SupportAmbrosiaPlayer
statMap = {
	["consume_%_of_maximum_mana_flask_charges_on_skill_use"] = {
		mod("Multiplier:ManaFlaskMaxChargesPercent", "BASE", nil),
	},
	["gain_%_damage_as_lighting_per_mana_flask_charge_consumed"] = {
		mod("DamageGainAsLightning", "BASE", nil, 0, 0, { type = "Multiplier", var = "ManaFlaskChargeConsumed"}),
	},
},
#baseMod mod("Multiplier:ManaFlaskChargeConsumed", "BASE", 1, 0, 0, { type = "PercentStat", stat = "ManaFlask1MaxCharges", percentVar = "ManaFlaskMaxChargesPercent", floor = true })
#baseMod mod("Multiplier:ManaFlaskChargeConsumed", "BASE", 1, 0, 0, { type = "PercentStat", stat = "ManaFlask2MaxCharges", percentVar = "ManaFlaskMaxChargesPercent", floor = true })
#mods
#skillEnd

#skill SupportAmbushPlayer
#set SupportAmbushPlayer
statMap = {
	["support_ambush_critical_strike_chance_vs_enemies_on_full_life_+%_final"] = {
		mod("CritChance", "MORE", nil, 0, 0, { type = "ActorCondition", actor = "enemy", var = "FullLife" }),
	},
},
#mods
#skillEnd

#skill SupportArcaneSurgePlayer
#set SupportArcaneSurgePlayer
#mods
#skillEnd

#skill SupportFasterCastPlayer
#set SupportFasterCastPlayer
statMap = {
	["support_faster_casting_cast_speed_+%_final"] = {
		mod("Speed", "MORE", nil, ModFlag.Cast),
	},
},
#mods
#skillEnd

#skill SupportAstralProjectionPlayer
#set SupportAstralProjectionPlayer
statMap = {
	["support_astral_projection_aoe_+%_final"] = {
		mod("AreaOfEffect", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportBiddingPlayer
#set SupportBiddingPlayer
#mods
#skillEnd

#skill SupportBitingFrostPlayer
#set SupportBitingFrostPlayer
statMap = {
	["support_active_skill_consume_enemy_freeze_to_gain_damage_+%_final"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "ActorCondition", actor = "enemy", var = "Frozen" }),
	},
},
#mods
#skillEnd

#skill SupportBoneShrapnelPlayer
#set SupportBoneShrapnelPlayer
#mods
#skillEnd

#skill TriggeredBoneShrapnelPlayer
#set TriggeredBoneShrapnelPlayer
#mods
#skillEnd

#skill SupportBurgeonPlayer
#set SupportBurgeonPlayer
statMap = {
	["support_chanelling_damage_+%_final_per_second_channelling"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "Condition", var = "Channelling" }, { type = "Multiplier", var = "ChannellingTime", limitVar = "BurgeonDamageCap", limitTotal = true }),
	},
	["support_channelling_damage_cap"] = {
		mod("Multiplier:BurgeonDamageCap", "BASE"),
	},
},
#mods
#skillEnd

#skill SupportBurningRunesPlayer
#set SupportBurningRunesPlayer
#mods
#skillEnd
#skill TriggeredBurningRunesPlayer
#set TriggeredBurningRunesPlayer
statMap = {
	["support_burning_runes_base_fire_damage_equal_to_%_maximum_mana"] = {
		mod("IgniteFireHitDamage", "OVERRIDE", nil, 0, 0, { type = "PercentStat", stat = "Mana", percent = 1 }),
	},
},
#flags duration
#baseMod mod("EnemyIgniteChance", "BASE", 100)
#baseMod mod("IgniteStacks", "OVERRIDE", 1)
#baseMod flag("NeverCrit")
#mods
#skillEnd

#skill SupportCapacitorPlayer
#set SupportCapacitorPlayer
#mods
#skillEnd

#skill SupportCatharsisPlayer
#set SupportCatharsisPlayer
#mods
#skillEnd

#skill SupportAddedChaosDamagePlayer
#set SupportAddedChaosDamagePlayer
statMap = {
	["support_chaos_support_non_chaos_damage_+%_final"] = {
		mod("ColdDamage", "MORE", nil),
		mod("LightningDamage", "MORE", nil),
		mod("FireDamage", "MORE", nil),
		mod("PhysicalDamage", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportChaosMasteryPlayer
#set SupportChaosMasteryPlayer
#mods
#skillEnd

#skill SupportChaoticFreezePlayer
#set SupportChaoticFreezePlayer
#mods
#skillEnd

#skill SupportClarityPlayer
#set SupportClarityPlayer
statMap = {
	["support_clarity_mana_regeneration_rate_+%"] = {
		mod("ManaRegen", "INC", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Clarity" }),
	},
},
#mods
#skillEnd

#skill SupportColdExposurePlayer
#set SupportColdExposurePlayer
statMap = {
	["inflict_cold_exposure_for_x_ms_on_cold_crit"] = {
		mod("ColdExposureChance", "BASE", nil),
	},
},
#mods
#skillEnd

#skill SupportAddedColdDamagePlayer
#set SupportAddedColdDamagePlayer
statMap = {
	["support_fire_and_lightning_damage_+%_final"] = {
		mod("FireDamage", "MORE", nil),
		mod("LightningDamage", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportColdMasteryPlayer
#set SupportColdMasteryPlayer
#mods
#skillEnd

#skill SupportColdPenetrationPlayer
#set SupportColdPenetrationPlayer
#mods
#skillEnd

#skill SupportCommandment
#set SupportCommandment
statMap = {
	["support_minion_damage_with_non_command_skills_+%_final"] = {
		mod("MinionModifier", "LIST", { mod = mod("Damage", "MORE", nil, 0, 0, {type = "Condition", var = "CommandableSkill", neg = true}) }),
	},
	["minion_command_skill_cooldown_speed_+%"] = {
		mod("MinionModifier", "LIST", { mod = mod("CooldownRecovery", "INC", nil, 0, 0, {type = "Condition", var = "CommandableSkill"}) }),
	},
},
#mods
#skillEnd

#skill SupportConcentratedEffectPlayer
#set SupportConcentratedEffectPlayer
statMap = {
	["support_concentrated_effect_skill_area_of_effect_+%_final"] = {
		mod("AreaOfEffect", "MORE", nil),
	},
	["support_area_concentrate_area_damage_+%_final"] = {
		mod("Damage", "MORE", nil, ModFlag.Area),
	},
},
#mods
#skillEnd

#skill SupportConsideredCastingPlayer
#set SupportConsideredCastingPlayer
statMap = {
	["support_slow_cast_cast_speed_+%_final"] = {
		mod("Speed", "MORE", nil, ModFlag.Cast),
	},
	["support_slow_cast_spell_damage_+%_final"] = {
		mod("Damage", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportControlledDestructionPlayer
#set SupportControlledDestructionPlayer
statMap = {
	["support_controlled_destruction_spell_damage_+%_final"] = {
		mod("Damage", "MORE", nil, ModFlag.Hit),
	},
},
#mods
#skillEnd

#skill SupportCorpseConservationPlayer
#set SupportCorpseConservationPlayer
#mods
#skillEnd

#skill SupportCoursingCurrentPlayer
#set SupportCoursingCurrentPlayer
#mods
#skillEnd

#skill SupportCracklingBarrierPlayer
#set SupportCracklingBarrierPlayer
#mods
#skillEnd

#skill SupportCrazedMinionsPlayer
#set SupportCrazedMinionsPlayer
#mods
#skillEnd

#skill SupportCursedGroundPlayer
#set SupportCursedGroundPlayer
#mods
#skillEnd

#skill SupportDanseMacabrePlayer
#set SupportDanseMacabrePlayer
statMap = {
	["support_danse_macabre_offering_skill_damage_+%_final_if_consumed_additional_skeleton"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "SkillType", skillType = SkillType.Offering }),
	},
	["offering_spells_effect_+%_if_consumed_additional_skeleton"] = {
		mod("BuffEffect", "INC", nil, 0, 0, { type = "SkillType", skillType = SkillType.Offering }),
	},
},
#mods
#skillEnd

#skill SupportDeathmarchPlayer
#set SupportDeathmarchPlayer
#mods
#skillEnd

#skill SupportDecayingHexPlayer
#set SupportDecayingHexPlayer
statMap = {
	["support_decaying_hex_base_chaos_damage_per_minute_as_%_of_intelligence_for_8_seconds"] = {
		skill("decay", nil, { type = "PercentStat", stat = "Int", percent = 1 }),
		div = 60,
	},
},
#mods
#skillEnd

#skill SupportLastingFrostPlayer
#set SupportLastingFrostPlayer
#mods
#skillEnd

#skill SupportDerangePlayer
#set SupportDerangePlayer
statMap = {
	["support_shield_sacrifice_damage_over_time_+%_final_per_100_intelligence"] = {
		mod("Damage", "MORE", nil, ModFlag.Dot, 0, { type = "PerStat", stat = "Int", div = 100 }),
	},
},

#mods
#skillEnd

#skill SupportDissipatePlayer
#set SupportDissipatePlayer
#mods
#skillEnd

#skill SupportDrainedAilmentPlayer
#set SupportDrainedAilmentPlayer
statMap = {
	["support_drained_ailment_damage_over_time_+%_final_if_ailment_consumed"] = {
		mod("Damage", "MORE", nil, ModFlag.Dot, 0, { type = "Condition", var = "AilmentConsumed" }),
	},
},
#mods
#skillEnd

#skill SupportElementalArmyPlayer
#set SupportElementalArmyPlayer
#mods
#skillEnd

#skill SupportElementalDischargePlayer
#set SupportElementalDischargePlayer
#mods
#skillEnd

#skill TriggeredElementalDischargePlayer
#set TriggeredElementalDischargePlayer
#flags spell area
statMap = {
	["spell_minimum_base_fire_damage_as_%_of_intelligence"] = {
		mod("FireMin", "BASE", nil, 0, KeywordFlag.Spell, { type = "PercentStat", stat = "Int", percent = 1 }, { type = "Condition", var = "IgniteConsumed" }),
	},
	["spell_maximum_base_fire_damage_as_%_of_intelligence"] = {
		mod("FireMax", "BASE", nil, 0, KeywordFlag.Spell, { type = "PercentStat", stat = "Int", percent = 1 }, { type = "Condition", var = "IgniteConsumed" }),
	},
	["spell_minimum_base_cold_damage_as_%_of_intelligence"] = {
		mod("ColdMin", "BASE", nil, 0, KeywordFlag.Spell, { type = "PercentStat", stat = "Int", percent = 1 }, { type = "Condition", var = "FreezeConsumed" }),
	},
	["spell_maximum_base_cold_damage_as_%_of_intelligence"] = {
		mod("ColdMax", "BASE", nil, 0, KeywordFlag.Spell, { type = "PercentStat", stat = "Int", percent = 1 }, { type = "Condition", var = "FreezeConsumed" }),
	},
	["spell_minimum_base_lightning_damage_as_%_of_intelligence"] = {
		mod("LightningMin", "BASE", nil, 0, KeywordFlag.Spell, { type = "PercentStat", stat = "Int", percent = 1 }, { type = "Condition", var = "ShockConsumed" }),
	},
	["spell_maximum_base_lightning_damage_as_%_of_intelligence"] = {
		mod("LightningMax", "BASE", nil, 0, KeywordFlag.Spell, { type = "PercentStat", stat = "Int", percent = 1 }, { type = "Condition", var = "ShockConsumed" }),
	},
},
#mods
#skillEnd

#skill SupportElementalFocusPlayer
#set SupportElementalFocusPlayer
statMap = {
	["support_gem_elemental_damage_+%_final"] = {
		mod("ElementalDamage", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportEmbitterPlayer
#set SupportEmbitterPlayer
#mods
#skillEnd

#skill SupportEnergyBarrierPlayer
#set SupportEnergyBarrierPlayer
#mods
#skillEnd

#skill SupportEnergyRetentionPlayer
#set SupportEnergyRetentionPlayer
#mods
#skillEnd

#skill SupportEnormityPlayer
#set SupportEnormityPlayer
statMap = {
	["support_titanblood_minion_damage_+%_final"] = {
		mod("MinionModifier", "LIST", { mod = mod("Damage", "MORE", nil) }),
	},
	["support_titanblood_minion_life_+%_final"] = {
		mod("MinionModifier", "LIST", { mod = mod("Life", "MORE", nil) }),
	},
},
#mods
#skillEnd

#skill SupportEssenceHarvestPlayer
#set SupportEssenceHarvestPlayer
#mods
#skillEnd

#skill SupportExcisePlayer
#set SupportExcisePlayer
statMap = {
	["support_crit_cooldown_crit_chance_+%_final"] = {
		mod("CritChance", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportExecratePlayer
#set SupportExecratePlayer
statMap = {
	["support_ailment_cooldown_ailment_chance_+%_final"] = {
		mod("EnemyIgniteChance", "MORE", nil),
		mod("EnemyShockChance", "MORE", nil),
		mod("BleedChance", "MORE", nil),
		mod("PoisonChance", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportExpansePlayer
#set SupportExpansePlayer
statMap = {
	["support_aoe_cooldown_aoe_+%_final"] = {
		mod("AreaOfEffect", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportExtractionPlayer
#set SupportExtractionPlayer
#mods
#skillEnd

#skill SupportFeedingFrenzyPlayer
#set SupportFeedingFrenzyPlayer
statMap = {
	["feeding_frenzy_minion_damage_+%_final"] = {
	mod("MinionModifier", "LIST", { mod = mod("Damage", "MORE", nil) }),
	},
	["feeding_frenzy_minion_damage_taken_+%_final"] = {
		mod("MinionModifier", "LIST", { mod = mod("DamageTaken", "MORE", nil) }),
	},
},
#mods
#skillEnd

#skill SupportFieryDeathPlayer
#set SupportFieryDeathPlayer
#mods
#skillEnd
#skill TriggeredFieryDeathPlayer
#set TriggeredFieryDeathPlayer
#flags spell area
#baseMod skill("explodeCorpse", true)
#baseMod skill("corpseExplosionDamageType", "Fire")
#mods
#skillEnd

#skill SupportFireMasteryPlayer
#set SupportFireMasteryPlayer
#mods
#skillEnd

#skill SupportFlukePlayer
#set SupportFlukePlayer
#mods
#skillEnd

#skill SupportFocusedCursePlayer
#set SupportFocusedCursePlayer
#mods
#skillEnd

#skill SupportManaFountainPlayer
#set SupportManaFountainPlayer
statMap = {
	["support_mana_fountain_mana_regeneration_rate_+%"] = {
		mod("ManaRegen", "INC", nil, 0, 0, { type = "GlobalEffect", effectType = "Aura" }),
	},
},
#mods
#skillEnd

#skill SupportWallFortressPlayer
#set SupportWallFortressPlayer
statMap = {
	["support_wall_fortress_hit_damage_+%_final"] = {
		mod("Damage", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportFreezeforkPlayer
#set SupportFreezeforkPlayer
#mods
#skillEnd

#skill SupportChillingIcePlayer
#set SupportChillingIcePlayer
#mods
#skillEnd

#skill SupportFrostfirePlayer
#set SupportFrostfirePlayer
statMap = {
	["ignite_effect_+%_final_against_frozen_enemies"] = {
		mod("AilmentMagnitude", "MORE", nil, 0, KeywordFlag.Ignite, { type = "ActorCondition", actor = "enemy", var = "Frozen" }),
	},
},
#mods
#skillEnd

#skill SupportGlaciationPlayer
#set SupportGlaciationPlayer
#mods
#skillEnd

#skill SupportGlacierPlayer
#set SupportGlacierPlayer
statMap = {
	["support_glacier_ice_crystal_maximum_life_+%_final"] = {
		mod("IceCrystalLife", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportCurseEffectPlayer
#set SupportCurseEffectPlayer
#mods
#skillEnd

#skill SupportHexBloomPlayer
#set SupportHexBloomPlayer
#mods
#skillEnd

#skill SupportHinderPlayer
#set SupportHinderPlayer
#mods
#skillEnd

#skill SupportHourglassPlayer
#set SupportHourglassPlayer
statMap = {
	["support_hourglass_damage_+%_final"] = {
		mod("Damage", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportIceBitePlayer
#set SupportIceBitePlayer
statMap = {
	["support_ice_bite_buff_grant_%_added_cold_attack_damage"] = {
		mod("DamageGainAsCold", "BASE", nil, ModFlag.Attack, 0, { type = "Condition", var = "FrozenEnemyRecently" }, { type = "GlobalEffect", effectType = "Buff", effectName = "Ice Bite" }),
	},
	["support_ice_bite_base_buff_duration"] = {
		mod("Duration", "BASE", nil, 0, 0, { type = "Condition", var = "FrozenEnemyRecently" }, { type = "GlobalEffect", effectType = "Buff" }),
		div = 1000,
	},
},
#mods
#skillEnd

#skill SupportIciclePlayer
#set SupportIciclePlayer
statMap = {
	["support_icicle_ice_crystal_maximum_life_+%_final"] = {
		mod("IceCrystalLife", "MORE", nil),
	},
},
#mods
#skillEnd

#skill ViciousHexSupportPlayer
#set ViciousHexSupportPlayer
#mods
#skillEnd
#skill DoomBlastPlayer
#set DoomBlastPlayer
#flags spell area
#baseMod skill("currentManaPercentage", true)
statMap = {
	["impending_doom_base_added_chaos_damage_%_of_current_mana"] = {
		mod("ChaosMin", "BASE", nil, 0, 0, { type = "PercentStat", stat = "Mana", percentVar = "CurrentManaPercentage" }),
		mod("ChaosMax", "BASE", nil, 0, 0, { type = "PercentStat", stat = "Mana", percentVar = "CurrentManaPercentage" }),
		div = 100,
	},
},
#mods
#skillEnd

#skill SupportImpetusPlayer
#set SupportImpetusPlayer
#mods
#skillEnd

#skill SupportInevitableCriticalsPlayer
#set SupportInevitableCriticalsPlayer
statMap = {
	["support_inevitable_criticals_critical_strike_chance_+%_per_second"] = {
		mod("CritChance", "INC", nil, 0, 0, { type = "Multiplier", var = "SecondsSinceInevitableCrit", limitVar = "InevitableCritCap", limitTotal = true }),
	},
	["support_inevitable_criticals_critical_strike_chance_+%_cap"] = {
		mod("Multiplier:InevitableCritCap", "BASE", nil),
	},
},
#mods
#skillEnd

#skill SupportIntenseAgonyPlayer
#set SupportIntenseAgonyPlayer
statMap = {
	["support_chaotic_assassination_damage_over_time_+%_final_against_full_life_enemies"] = {
		mod("Damage", "MORE", nil, ModFlag.Dot, 0, { type = "ActorCondition", actor = "enemy", var = "FullLife" }),
	},
	["support_chaotic_assassination_skill_effect_duration_+%_final"] = {
		mod("Duration", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportLastGaspPlayer
#set SupportLastGaspPlayer
#mods
#skillEnd

#skill SupportLightningMasteryPlayer
#set SupportLightningMasteryPlayer
#mods
#skillEnd

#skill SupportLoyaltyPlayer
#set SupportLoyaltyPlayer
statMap = {
	["support_trusty_companion_minion_life_+%_final"] = {
		mod("MinionModifier", "LIST", { mod = mod("Life", "MORE", nil) }),
	},
},
#mods
#skillEnd

#skill SupportMagnetismPlayer
#set SupportMagnetismPlayer
#mods
#skillEnd

#skill SupportIncreasedAreaOfEffectPlayer
#set SupportIncreasedAreaOfEffectPlayer
#mods
#skillEnd

#skill SupportManaFlarePlayer
#set SupportManaFlarePlayer
#mods
#skillEnd
#skill TriggeredManaFlarePlayer
#set TriggeredManaFlarePlayer
#flags spell area
#baseMod skill("currentManaPercentage", true)
statMap = {
	["support_mana_flare_%_of_current_mana_consumed"] = {
		mod("FireMin", "BASE", nil, 0, 0, { type = "PercentStat", stat = "Mana", percentVar = "CurrentManaPercentage" }),
		mod("FireMax", "BASE", nil, 0, 0, { type = "PercentStat", stat = "Mana", percentVar = "CurrentManaPercentage" }),
		div = 100,
	},
},
#mods
#skillEnd

#skill SupportMinionInstabilityPlayer
#set SupportMinionInstabilityPlayer
statMap = {
	["explode_on_low_life_%_maximum_life_to_deal"] = {
		mod("MinionModifier", "LIST", { mod = mod("Multiplier:MinionInstabilityBaseDamage", "BASE", nil) }),
		mod("ExtraMinionSkill", "LIST", { skillId = "MinionInstability" }),
	},
},
#mods
#skillEnd

#skill SupportMinionMasteryPlayer
#set SupportMinionMasteryPlayer
#mods
#skillEnd

#skill SupportMinionPactPlayer
#set SupportMinionPactPlayer
statMap = {
	["support_minion_pact_damage_+%_final"] = {
		mod("Damage", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportMusterPlayer
#set SupportMusterPlayer
statMap = {
	["support_varied_troops_damage_+%_final_per_different_persistent_ominion"] = {
		mod("MinionModifier", "LIST", { mod = mod("Damage", "MORE", nil, 0, 0, { type = "Multiplier", actor = "parent", var = "PersistentMinionTypes" }) }),
	},
},
#mods
#skillEnd

#skill SupportMysticismPlayer
#set SupportMysticismPlayer
statMap = {
	["support_spell_damage_spirit_cost_spell_damage_+%_on_full_energy_shield"] = {
		mod("Damage", "INC", nil, ModFlag.Spell, 0, { type = "Condition", var = "FullEnergyShield" }, { type = "GlobalEffect", effectType = "Buff", effectName = "Mysticism" }),
	},
},
#mods
#skillEnd

#skill SupportNadirPlayer
#set SupportNadirPlayer
#mods
#skillEnd

#skill SupportPhysicalMasteryPlayer
#set SupportPhysicalMasteryPlayer
#mods
#skillEnd

#skill SupportPinpointCriticalPlayer
#set SupportPinpointCriticalPlayer
statMap = {
	["support_pinpoint_critical_strike_chance_+%_final"] = {
		mod("CritChance", "MORE", nil),
	},
	["support_pinpoint_critical_strike_multiplier_+%_final"] = {
		mod("CritMultiplier", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportPotentialPlayer
#set SupportPotentialPlayer
statMap = {
	["skill_consume_power_charge_to_gain_critical_strike_chance_+%_final"] = {
		mod("CritChance", "MORE", nil, 0, 0, { type = "Multiplier", var = "RemovablePowerCharge", limit = 1 }),
	},
},
#mods
#skillEnd

#skill SupportProfanityPlayer
#set SupportProfanityPlayer
#mods
#skillEnd

#skill SupportRimePlayer
#set SupportRimePlayer
#mods
#skillEnd

#skill SupportTempestuousTempoPlayer
#set SupportTempestuousTempoPlayer
statMap = {
	["support_elemental_damage_+%_final_per_different_elemental_skill_used_recently"] = {
		mod("ElementalDamage", "MORE", nil, 0, 0, { type = "Multiplier", var = "DifferentElementalSkillUsedRecently" }),
	},
},
#mods
#skillEnd

#skill SupportRitualisticCursePlayer
#set SupportRitualisticCursePlayer
#mods
#skillEnd

#skill SupportEnergyShieldOnShockKillPlayer
#set SupportEnergyShieldOnShockKillPlayer
statMap = {
	["support_recover_%_maximum_energy_shield_killing_shocked_enemies"] = {
		mod("EnergyShieldOnKill", "BASE", nil, 0, 0, { type = "PercentStat", stat = "EnergyShield", percent = 1 }, { type = "ActorCondition", actor = "enemy", var = "Shocked" })
	},
},
#mods
#skillEnd

#skill SupportManaLeechPlayer
#set SupportManaLeechPlayer
#mods
#skillEnd

#skill SupportSpellCascadePlayer
#set SupportSpellCascadePlayer
statMap = {
	["support_spell_cascade_area_of_effect_+%_final"] = {
		mod("AreaOfEffect", "MORE", nil),
	},
	["support_spell_cascade_damage_+%_final"] = {
		mod("Damage", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportSpellEchoPlayer
#set SupportSpellEchoPlayer
statMap = {
	["support_multicast_cast_speed_+%_final"] = {
		mod("Speed", "MORE", nil, ModFlag.Cast),
	},
	["support_spell_echo_area_of_effect_+%_final"] = {
		mod("AreaOfEffect", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportStormfirePlayer
#set SupportStormfirePlayer
#mods
#skillEnd

#skill SupportStripAwayPlayer
#set SupportStripAwayPlayer
#mods
#skillEnd

#skill SupportStrongHeartedPlayer
#set SupportStrongHeartedPlayer
statMap = {
	["support_shock_protection_spirit_cost_shock_duration_on_self_+%_final"] = {
		mod("SelfShockDuration", "MORE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Strong Hearted" }),
	},
},
#mods
#skillEnd

#skill SupportSuffusePlayer
#set SupportSuffusePlayer
#mods
#skillEnd

#skill SupportIncreasedCriticalDamagePlayer
#set SupportIncreasedCriticalDamagePlayer
statMap = {
	["support_critical_damage_critical_strike_chance_+%_final"] = {
		mod("CritChance", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportUnbendingPlayer
#set SupportUnbendingPlayer
#mods
#skillEnd

#skill SupportUnleashPlayer
#set SupportUnleashPlayer
statMap = {
	["support_anticipation_rapid_fire_count"] = {
		mod("SealCount", "BASE", nil),
	},
	["unleash_support_seal_gain_frequency_as_%_of_total_cast_time"] = {
		mod("SealGainFrequency", "BASE", nil),
	},
	["support_spell_rapid_fire_repeat_use_damage_+%_final"] = {
		mod("SealRepeatPenalty", "MORE", nil),
	},
},
#baseMod flag("HasSeals")
#mods
#skillEnd

#skill SupportUpwellingPlayer
#set SupportUpwellingPlayer
#mods
#skillEnd

#skill SupportVerglasPlayer
#set SupportVerglasPlayer
#mods
#skillEnd

#skill SupportVolatilePowerPlayer
#set SupportVolatilePowerPlayer
#mods
#skillEnd

#skill SupportVolatilityPlayer
#set SupportVolatilityPlayer
#mods
#skillEnd

#skill SupportWildfirePlayer
#set SupportWildfirePlayer
#mods
#skillEnd

#skill SupportWildshardsPlayer
#set SupportWildshardsPlayer
#mods
#skillEnd

#skill SupportWitheringTouchPlayer
#set SupportWitheringTouchPlayer
statMap = {
	["support_withering_touch_damage_+%_final"] = {
		mod("Damage", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportZenithPlayer
#set SupportZenithPlayer
#mods
#skillEnd