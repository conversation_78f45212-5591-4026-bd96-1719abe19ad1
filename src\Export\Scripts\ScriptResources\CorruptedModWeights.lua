return { 
    blackList = {
        bow = {
            "CorruptionLocalAddedFireDamageTwoHand1",
            "CorruptionLocalAddedColdDamageTwoHand1",
            "CorruptionLocalAddedLightningDamageTwoHand1",
            "CorruptionLocalAddedChaosDamageTwoHand1",
            "CorruptionWeaponElementalDamageTwoHand1",
        }
    },
    whiteList = {
        str_armour = {
            "CorruptionLocalIncreasedPhysicalDamageReductionRatingPercent1",
        },
        dex_armour = {
            "CorruptionLocalIncreasedEvasionRatingPercent1",
        },
        int_armour = {
            "CorruptionLocalIncreasedEnergyShieldPercent1",
        },
        str_dex_armour = {
            "CorruptionLocalIncreasedArmourAndEvasion1",
        },
        str_int_armour = {
            "CorruptionLocalIncreasedArmourAndEnergyShield1",
        },
        dex_int_armour = {
            "CorruptionLocalIncreasedEvasionAndEnergyShield1",
        },
        armour = {
            "CorruptionReducedLocalAttributeRequirements1",
        },
        onehand = {
            "CorruptionReducedLocalAttributeRequirements1",
        },
        twohand = {
            "CorruptionReducedLocalAttributeRequirements1",
        },
        body_armour = {
            "CorruptionAdditionalPhysicalDamageReduction1",
            "CorruptionDamageTakenGainedAsLife1",
            "CorruptionDamageTakenGainedAsMana1",
            "CorruptionMaximumElementalResistance1",
            "CorruptionIncreasedLife1",
            "CorruptionThornsDamageIncrease1",
            "CorruptionChaosResistance1",
        },
        amulet = {
            "CorruptionLifeLeech1",
            "CorruptionManaLeech1",
            "CorruptionMaximumElementalResistance1",
            "CorruptionAllResistances1",
            "CorruptionItemFoundRarityIncrease1",
            "CorruptionGlobalSkillGemLevel1",
            "CorruptionStrength1",
            "CorruptionDexterity1",
            "CorruptionIntelligence1",
            "CorruptionLifeFlaskChargeGeneration1",
            "CorruptionManaFlaskChargeGeneration1",
            "CorruptionCharmChargeGeneration1",
        },
        belt = {
            "CorruptionIncreasedLife1",
            "CorruptionIncreasedPhysicalDamageReductionRatingPercent1",
            "CorruptionIncreasedEvasionRatingPercent1",
            "CorruptionIncreasedEnergyShieldPercent1",
            "CorruptionFireResistance1",
            "CorruptionColdResistance1",
            "CorruptionLightningResistance1",
            "CorruptionMaximumFireResistance1",
            "CorruptionMaximumEnduranceCharges1",
            "CorruptionStrength1",
            "CorruptionDexterity1",
            "CorruptionIntelligence1"
        },
        quiver = {
            "CorruptionIncreasedMana1",
            "CorruptionIncreasedAccuracy1",
            "CorruptionAllDamage1",
            "CorruptionIncreasedSkillSpeed1",
            "CorruptionCriticalStrikeMultiplier1",
            "CorruptionLifeGainedFromEnemyDeath1",
            "CorruptionManaGainedFromEnemyDeath1",
            "CorruptionChanceToPierce1",
            "CorruptionChainFromTerrain1",
        },
        focus = {
            "CorruptionIncreasedMana1",
            "CorruptionSpellDamageOnWeapon1",
            "CorruptionEnergyShieldDelay1",
        },
        ring = {
            "CorruptionIncreasedMana1",
            "CorruptionChaosResistance1",
            "CorruptionLifeRegenerationPercent1",
            "CorruptionManaRegeneration1",
            "CorruptionAllResistances1",
            "CorruptionItemFoundRarityIncrease1",
            "CorruptionAllDamage1",
            "CorruptionIncreasedSkillSpeed1",
            "CorruptionCriticalStrikeMultiplier1",
            "CorruptionStrength1",
            "CorruptionDexterity1",
            "CorruptionIntelligence1"
        },
        shield = {
            "CorruptionThornsDamageIncrease1",
            "CorruptionLocalBlockChance1",
            "CorruptionMaximumBlockChance1",
            "CorruptionGainLifeOnBlock1",
            "CorruptionGainManaOnBlock1",
        },
        boots = {
            "CorruptionFireResistance1",
            "CorruptionColdResistance1",
            "CorruptionLightningResistance1",
            "CorruptionMaximumLightningResistance1",
            "CorruptionMovementVelocity1",
            "CorruptionIncreasedStunThreshold1",
            "CorruptionIncreasedFreezeThreshold1",
            "CorruptionSlowPotency1",

        },
        helmet = {
            "CorruptionMaximumColdResistance1",
            "CorruptionIncreasedSpirit1",
            "CorruptionMaximumPowerCharges1",
            "CorruptionIncreasedAccuracy1",
            "CorruptionLifeRegenerationPercent1",
            "CorruptionManaRegeneration1",
            "CorruptionGlobalMinionSkillGemsLevel1",
            "SpecialCorruptionWarcrySpeed1",
            "SpecialCorruptionCurseEffect1",
            "SpecialCorruptionAreaOfEffect1",
            "SpecialCorruptionPresenceRadius1",
            "SpecialCorruptionCooldownRecovery1",
            "SpecialCorruptionSkillEffectDuration1",
            "SpecialCorruptionEnergyGeneration1",
            "SpecialCorruptionDamageGainedAsChaos1",
        },
        gloves = {
            "CorruptionFirePenetration1",
            "CorruptionColdPenetration1",
            "CorruptionLightningPenetration1",
            "CorruptionArmourBreak1",
            "CorruptionMaximumFrenzyCharges1",
            "CorruptionGlobalMeleeSkillGemsLevel1",
            "CorruptionIncreasedSlowEffect1",
            "CorruptionWeaponSwapSpeed1",
        },
        staff = {
            "CorruptionGlobalFireSpellGemsLevel1",
            "CorruptionGlobalColdSpellGemsLevel1",
            "CorruptionGlobalLightningSpellGemsLevel1",
            "CorruptionGlobalChaosSpellGemsLevel1",
            "CorruptionGlobalPhysicalSpellGemsLevel1",
            "CorruptionSpellDamageOnTwoHandWeapon1",
            "CorruptionIgniteChanceIncrease1",
            "CorruptionFreezeDamageIncrease1",
            "CorruptionShockChanceIncrease1",
            "CorruptionSpellCriticalStrikeChance1",
            "CorruptionLifeGainedFromEnemyDeath1",
            "CorruptionManaGainedFromEnemyDeath1",
            "CorruptionIncreasedCastSpeed1",
        },
        wand = {
            "CorruptionGlobalFireSpellGemsLevel1",
            "CorruptionGlobalColdSpellGemsLevel1",
            "CorruptionGlobalLightningSpellGemsLevel1",
            "CorruptionGlobalChaosSpellGemsLevel1",
            "CorruptionGlobalPhysicalSpellGemsLevel1",
            "CorruptionSpellDamageOnWeapon1",
            "CorruptionIgniteChanceIncrease1",
            "CorruptionFreezeDamageIncrease1",
            "CorruptionShockChanceIncrease1",
            "CorruptionSpellCriticalStrikeChance1",
            "CorruptionLifeGainedFromEnemyDeath1",
            "CorruptionManaGainedFromEnemyDeath1",
            "CorruptionIncreasedCastSpeed1",
        },
        weapon = {
            "CorruptionLocalIncreasedPhysicalDamagePercent1",
            "CorruptionLocalIncreasedAttackSpeed1",
            "CorruptionLocalCriticalStrikeMultiplier1",
        },
        sceptre = {
            "CorruptionLocalIncreasedSpiritPercent1",
            "CorruptionAlliesInPresenceAllDamage1",
            "CorruptionAlliesInPresenceIncreasedAttackSpeed1",
            "CorruptionAlliesInPresenceIncreasedCastSpeed1",
            "CorruptionAlliesInPresenceCriticalStrikeMultiplier1",
        },
        bow = {
            "CorruptionLocalAddedFireDamage1",
            "CorruptionLocalAddedColdDamage1",
            "CorruptionLocalAddedLightningDamage1",
            "CorruptionLocalAddedChaosDamage1",
            "CorruptionWeaponElementalDamage1",
            "CorruptionLocalChanceToMaim1",
            "CorruptionLocalChanceToBlind1",
            "CorruptionAdditionalArrows1",
        },
        one_hand_weapon = {
            "CorruptionLocalAddedFireDamage1",
            "CorruptionLocalAddedColdDamage1",
            "CorruptionLocalAddedLightningDamage1",
            "CorruptionLocalAddedChaosDamage1",
            "CorruptionWeaponElementalDamage1",
        },
        two_hand_weapon = {
            "CorruptionLocalAddedFireDamageTwoHand1",
            "CorruptionLocalAddedColdDamageTwoHand1",
            "CorruptionLocalAddedLightningDamageTwoHand1",
            "CorruptionLocalAddedChaosDamageTwoHand1",
            "CorruptionWeaponElementalDamageTwoHand1",
        },
        mace = {
            "CorruptionLocalStunDamageIncrease1",
            "CorruptionLocalWeaponRangeIncrease1",
            "CorruptionLocalChanceToBleed1",
            "CorruptionLocalRageOnHit1"
        },
        warstaff = {
            "CorruptionLocalStunDamageIncrease1",
            "CorruptionLocalWeaponRangeIncrease1",
            "CorruptionLocalChanceToPoison1",
            "CorruptionLocalRageOnHit1"
        },
        crossbow = {
            "CorruptionLocalChanceToMaim1",
            "CorruptionLocalChanceToBlind1",
            "CorruptionAdditionalAmmo1",
        },
        jewel = {
            "CorruptionJewelStrength1",
            "CorruptionJewelDexterity1",
            "CorruptionJewelIntelligence1",
            "CorruptionJewelFireResist1",
            "CorruptionJewelColdResist1",
            "CorruptionJewelLightningResist1",
            "CorruptionJewelChaosResist1",
            "CorruptionJewelMaimImmunity1",
            "CorruptionJewelHinderImmunity1",
            "CorruptionJewelCorruptedBloodImmunity1",
            "CorruptionJewelBlindImmunity1",
        }
    }
}