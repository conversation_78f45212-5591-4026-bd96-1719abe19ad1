-- This file is automatically generated, do not edit!
-- Path of Building
--
-- Other active skills
-- Skill data (c) Grinding Gear Games
--
local skills, mod, flag, skill = ...

skills["AncestralSpiritsPlayer"] = {
	name = "Ancestral Spirits",
	baseTypeName = "Ancestral Spirits",
	fromTree = true,
	minionList = {
		"AncestralSpiritTurtle",
		"AncestralSpiritHulk",
		"AncestralSpiritCaster",
		"AncestralSpiritWarhorn",
	},
	color = 4,
	description = "Each of your Totems will summon an Ancestral Spirit Minion to fight for you. If the Totem that summoned the Minion dies then the Ancestral Spirit will too.",
	skillTypes = { [SkillType.Minion] = true, [SkillType.Duration] = true, [SkillType.Triggerable] = true, [SkillType.InbuiltTrigger] = true, [SkillType.CreatesMinion] = true, [SkillType.Triggered] = true, },
	minionSkillTypes = { [SkillType.Attack] = true, [SkillType.Melee] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.Area] = true, [SkillType.Projectile] = true, [SkillType.Chains] = true, [SkillType.Duration] = true, [SkillType.DamageOverTime] = true, [SkillType.RangedAttack] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 0,
	qualityStats = {
		{ "active_skill_minion_life_+%_final", 1 },
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { levelRequirement = 3, },
		[3] = { levelRequirement = 6, },
		[4] = { levelRequirement = 10, },
		[5] = { levelRequirement = 14, },
		[6] = { levelRequirement = 18, },
		[7] = { levelRequirement = 22, },
		[8] = { levelRequirement = 26, },
		[9] = { levelRequirement = 31, },
		[10] = { levelRequirement = 36, },
		[11] = { levelRequirement = 41, },
		[12] = { levelRequirement = 46, },
		[13] = { levelRequirement = 52, },
		[14] = { levelRequirement = 58, },
		[15] = { levelRequirement = 64, },
		[16] = { levelRequirement = 66, },
		[17] = { levelRequirement = 72, },
		[18] = { levelRequirement = 78, },
		[19] = { levelRequirement = 84, },
		[20] = { levelRequirement = 90, },
		[21] = { levelRequirement = 90, },
		[22] = { levelRequirement = 90, },
		[23] = { levelRequirement = 90, },
		[24] = { levelRequirement = 90, },
		[25] = { levelRequirement = 90, },
		[26] = { levelRequirement = 90, },
		[27] = { levelRequirement = 90, },
		[28] = { levelRequirement = 90, },
		[29] = { levelRequirement = 90, },
		[30] = { levelRequirement = 90, },
		[31] = { levelRequirement = 90, },
		[32] = { levelRequirement = 90, },
		[33] = { levelRequirement = 90, },
		[34] = { levelRequirement = 90, },
		[35] = { levelRequirement = 90, },
		[36] = { levelRequirement = 90, },
		[37] = { levelRequirement = 90, },
		[38] = { levelRequirement = 90, },
		[39] = { levelRequirement = 90, },
		[40] = { levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Ancestral Spirits",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				minion = true,
			},
			constantStats = {
				{ "minion_1%_damage_+%_per_X_player_strength", 3 },
				{ "minion_1%_accuracy_rating_+%_per_X_player_dexterity", 3 },
			},
			stats = {
				"display_modifiers_to_totem_life_effect_these_minions",
				"triggerable_in_any_set",
				"base_deal_no_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["BleedingConcoctionPlayer"] = {
	name = "Bleeding Concoction",
	baseTypeName = "Bleeding Concoction",
	fromTree = true,
	color = 4,
	description = "Consume charges from your Mana Flask to throw a bottle that explodes, dealing Physical attack damage in an area. Bleeding inflicted by this skill has higher Magnitude.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Area] = true, [SkillType.Projectile] = true, [SkillType.Physical] = true, [SkillType.UsableWhileMoving] = true, [SkillType.ProjectileNoCollision] = true, },
	weaponTypes = {
		["None"] = true,
	},
	castTime = 1,
	qualityStats = {
		{ "faster_bleed_%", 1 },
	},
	levels = {
		[1] = { critChance = 5, baseMultiplier = 0.7, levelRequirement = 0, cost = { Mana = 0, }, },
		[2] = { critChance = 5, baseMultiplier = 0.77, levelRequirement = 3, cost = { Mana = 0, }, },
		[3] = { critChance = 5, baseMultiplier = 0.84, levelRequirement = 6, cost = { Mana = 0, }, },
		[4] = { critChance = 5, baseMultiplier = 0.92, levelRequirement = 10, cost = { Mana = 0, }, },
		[5] = { critChance = 5, baseMultiplier = 0.99, levelRequirement = 14, cost = { Mana = 0, }, },
		[6] = { critChance = 5, baseMultiplier = 1.05, levelRequirement = 18, cost = { Mana = 0, }, },
		[7] = { critChance = 5, baseMultiplier = 1.12, levelRequirement = 22, cost = { Mana = 0, }, },
		[8] = { critChance = 5, baseMultiplier = 1.18, levelRequirement = 26, cost = { Mana = 0, }, },
		[9] = { critChance = 5, baseMultiplier = 1.23, levelRequirement = 31, cost = { Mana = 0, }, },
		[10] = { critChance = 5, baseMultiplier = 1.28, levelRequirement = 36, cost = { Mana = 0, }, },
		[11] = { critChance = 5, baseMultiplier = 1.34, levelRequirement = 41, cost = { Mana = 0, }, },
		[12] = { critChance = 5, baseMultiplier = 1.39, levelRequirement = 46, cost = { Mana = 0, }, },
		[13] = { critChance = 5, baseMultiplier = 1.44, levelRequirement = 52, cost = { Mana = 0, }, },
		[14] = { critChance = 5, baseMultiplier = 1.5, levelRequirement = 58, cost = { Mana = 0, }, },
		[15] = { critChance = 5, baseMultiplier = 1.55, levelRequirement = 64, cost = { Mana = 0, }, },
		[16] = { critChance = 5, baseMultiplier = 1.61, levelRequirement = 66, cost = { Mana = 0, }, },
		[17] = { critChance = 5, baseMultiplier = 1.67, levelRequirement = 72, cost = { Mana = 0, }, },
		[18] = { critChance = 5, baseMultiplier = 1.73, levelRequirement = 78, cost = { Mana = 0, }, },
		[19] = { critChance = 5, baseMultiplier = 1.79, levelRequirement = 84, cost = { Mana = 0, }, },
		[20] = { critChance = 5, baseMultiplier = 1.85, levelRequirement = 90, cost = { Mana = 0, }, },
		[21] = { critChance = 5, baseMultiplier = 1.91, levelRequirement = 90, cost = { Mana = 0, }, },
		[22] = { critChance = 5, baseMultiplier = 1.97, levelRequirement = 90, cost = { Mana = 0, }, },
		[23] = { critChance = 5, baseMultiplier = 2.04, levelRequirement = 90, cost = { Mana = 0, }, },
		[24] = { critChance = 5, baseMultiplier = 2.1, levelRequirement = 90, cost = { Mana = 0, }, },
		[25] = { critChance = 5, baseMultiplier = 2.17, levelRequirement = 90, cost = { Mana = 0, }, },
		[26] = { critChance = 5, baseMultiplier = 2.24, levelRequirement = 90, cost = { Mana = 0, }, },
		[27] = { critChance = 5, baseMultiplier = 2.32, levelRequirement = 90, cost = { Mana = 0, }, },
		[28] = { critChance = 5, baseMultiplier = 2.4, levelRequirement = 90, cost = { Mana = 0, }, },
		[29] = { critChance = 5, baseMultiplier = 2.48, levelRequirement = 90, cost = { Mana = 0, }, },
		[30] = { critChance = 5, baseMultiplier = 2.56, levelRequirement = 90, cost = { Mana = 0, }, },
		[31] = { critChance = 5, baseMultiplier = 2.64, levelRequirement = 90, cost = { Mana = 0, }, },
		[32] = { critChance = 5, baseMultiplier = 2.73, levelRequirement = 90, cost = { Mana = 0, }, },
		[33] = { critChance = 5, baseMultiplier = 2.82, levelRequirement = 90, cost = { Mana = 0, }, },
		[34] = { critChance = 5, baseMultiplier = 2.91, levelRequirement = 90, cost = { Mana = 0, }, },
		[35] = { critChance = 5, baseMultiplier = 3.01, levelRequirement = 90, cost = { Mana = 0, }, },
		[36] = { critChance = 5, baseMultiplier = 3.11, levelRequirement = 90, cost = { Mana = 0, }, },
		[37] = { critChance = 5, baseMultiplier = 3.21, levelRequirement = 90, cost = { Mana = 0, }, },
		[38] = { critChance = 5, baseMultiplier = 3.32, levelRequirement = 90, cost = { Mana = 0, }, },
		[39] = { critChance = 5, baseMultiplier = 3.43, levelRequirement = 90, cost = { Mana = 0, }, },
		[40] = { critChance = 5, baseMultiplier = 3.55, levelRequirement = 90, cost = { Mana = 0, }, },
	},
	statSets = {
		[1] = {
			label = "Bleeding Concoction",
			baseEffectiveness = 4.1999998092651,
			incrementalEffectiveness = 0.27349999547005,
			statDescriptionScope = "throw_flask_bleed",
			statMap = {
				["flask_throw_bleed_effect_+%_final"] = {
					mod("AilmentMagnitude", "MORE", nil, 0, KeywordFlag.Bleed),
				},
			},
			baseFlags = {
				attack = true,
				projectile = true,
				unarmed = true,
				area = true,
			},
			constantStats = {
				{ "flask_throw_base_charges_used", 5 },
				{ "base_number_of_projectiles", 1 },
				{ "active_skill_base_area_of_effect_radius", 15 },
				{ "flask_throw_bleed_effect_+%_final", 200 },
				{ "throw_flask_effects_type", 3 },
				{ "movement_speed_+%_final_while_performing_action", -70 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 60 },
				{ "main_hand_base_weapon_attack_duration_ms", 714 },
				{ "base_chance_to_inflict_bleeding_%", 100 },
			},
			stats = {
				"main_hand_weapon_minimum_physical_damage",
				"main_hand_weapon_maximum_physical_damage",
				"base_is_projectile",
				"projectile_behaviour_only_explode",
				"can_perform_skill_while_moving",
				"replace_main_hand_unarmed_attack_stats_with_nothing_type",
				"is_area_damage",
			},
			levels = {
				[1] = { 11, 21, statInterpolation = { 1, 1, }, actorLevel = 1, },
				[2] = { 19, 35, statInterpolation = { 1, 1, }, actorLevel = 3.4519999027252, },
				[3] = { 29, 55, statInterpolation = { 1, 1, }, actorLevel = 6.7670001983643, },
				[4] = { 41, 75, statInterpolation = { 1, 1, }, actorLevel = 10.307999610901, },
				[5] = { 52, 97, statInterpolation = { 1, 1, }, actorLevel = 14.074999809265, },
				[6] = { 65, 120, statInterpolation = { 1, 1, }, actorLevel = 18.068000793457, },
				[7] = { 78, 145, statInterpolation = { 1, 1, }, actorLevel = 22.287000656128, },
				[8] = { 92, 171, statInterpolation = { 1, 1, }, actorLevel = 26.732000350952, },
				[9] = { 106, 198, statInterpolation = { 1, 1, }, actorLevel = 31.40299987793, },
				[10] = { 122, 226, statInterpolation = { 1, 1, }, actorLevel = 36.299999237061, },
				[11] = { 138, 256, statInterpolation = { 1, 1, }, actorLevel = 41.423000335693, },
				[12] = { 154, 287, statInterpolation = { 1, 1, }, actorLevel = 46.771999359131, },
				[13] = { 172, 319, statInterpolation = { 1, 1, }, actorLevel = 52.34700012207, },
				[14] = { 190, 353, statInterpolation = { 1, 1, }, actorLevel = 58.147998809814, },
				[15] = { 209, 388, statInterpolation = { 1, 1, }, actorLevel = 64.175003051758, },
				[16] = { 228, 424, statInterpolation = { 1, 1, }, actorLevel = 70.428001403809, },
				[17] = { 249, 462, statInterpolation = { 1, 1, }, actorLevel = 76.906997680664, },
				[18] = { 270, 501, statInterpolation = { 1, 1, }, actorLevel = 83.611999511719, },
				[19] = { 291, 541, statInterpolation = { 1, 1, }, actorLevel = 90.542999267578, },
				[20] = { 314, 582, statInterpolation = { 1, 1, }, actorLevel = 97.699996948242, },
				[21] = { 337, 625, statInterpolation = { 1, 1, }, actorLevel = 105.08300018311, },
				[22] = { 360, 669, statInterpolation = { 1, 1, }, actorLevel = 112.69200134277, },
				[23] = { 385, 715, statInterpolation = { 1, 1, }, actorLevel = 120.52700042725, },
				[24] = { 410, 761, statInterpolation = { 1, 1, }, actorLevel = 128.58799743652, },
				[25] = { 436, 810, statInterpolation = { 1, 1, }, actorLevel = 136.875, },
				[26] = { 462, 859, statInterpolation = { 1, 1, }, actorLevel = 145.38800048828, },
				[27] = { 490, 910, statInterpolation = { 1, 1, }, actorLevel = 154.12699890137, },
				[28] = { 518, 962, statInterpolation = { 1, 1, }, actorLevel = 163.09199523926, },
				[29] = { 547, 1015, statInterpolation = { 1, 1, }, actorLevel = 172.28300476074, },
				[30] = { 576, 1070, statInterpolation = { 1, 1, }, actorLevel = 181.69999694824, },
				[31] = { 606, 1126, statInterpolation = { 1, 1, }, actorLevel = 191.34300231934, },
				[32] = { 637, 1183, statInterpolation = { 1, 1, }, actorLevel = 201.21200561523, },
				[33] = { 668, 1241, statInterpolation = { 1, 1, }, actorLevel = 211.30700683594, },
				[34] = { 701, 1301, statInterpolation = { 1, 1, }, actorLevel = 221.62800598145, },
				[35] = { 734, 1362, statInterpolation = { 1, 1, }, actorLevel = 232.17500305176, },
				[36] = { 767, 1425, statInterpolation = { 1, 1, }, actorLevel = 242.94799804688, },
				[37] = { 802, 1489, statInterpolation = { 1, 1, }, actorLevel = 253.94700622559, },
				[38] = { 837, 1554, statInterpolation = { 1, 1, }, actorLevel = 265.17199707031, },
				[39] = { 872, 1620, statInterpolation = { 1, 1, }, actorLevel = 276.62298583984, },
				[40] = { 909, 1688, statInterpolation = { 1, 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["BloodBoilPlayer"] = {
	name = "Blood Boil",
	baseTypeName = "Blood Boil",
	fromTree = true,
	color = 4,
	description = "Enemies in your Presence accumulate blood boils periodically. When they die, the boils pop applying Corrupted Blood to enemies near them.",
	skillTypes = { [SkillType.HasReservation] = true, [SkillType.OngoingSkill] = true, [SkillType.Buff] = true, [SkillType.Persistent] = true, [SkillType.Duration] = true, [SkillType.Physical] = true, [SkillType.DamageOverTime] = true, [SkillType.Area] = true, },
	castTime = 0,
	qualityStats = {
		{ "base_skill_effect_duration", 75 },
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { levelRequirement = 3, },
		[3] = { levelRequirement = 6, },
		[4] = { levelRequirement = 10, },
		[5] = { levelRequirement = 14, },
		[6] = { levelRequirement = 18, },
		[7] = { levelRequirement = 22, },
		[8] = { levelRequirement = 26, },
		[9] = { levelRequirement = 31, },
		[10] = { levelRequirement = 36, },
		[11] = { levelRequirement = 41, },
		[12] = { levelRequirement = 46, },
		[13] = { levelRequirement = 52, },
		[14] = { levelRequirement = 58, },
		[15] = { levelRequirement = 64, },
		[16] = { levelRequirement = 66, },
		[17] = { levelRequirement = 72, },
		[18] = { levelRequirement = 78, },
		[19] = { levelRequirement = 84, },
		[20] = { levelRequirement = 90, },
		[21] = { levelRequirement = 90, },
		[22] = { levelRequirement = 90, },
		[23] = { levelRequirement = 90, },
		[24] = { levelRequirement = 90, },
		[25] = { levelRequirement = 90, },
		[26] = { levelRequirement = 90, },
		[27] = { levelRequirement = 90, },
		[28] = { levelRequirement = 90, },
		[29] = { levelRequirement = 90, },
		[30] = { levelRequirement = 90, },
		[31] = { levelRequirement = 90, },
		[32] = { levelRequirement = 90, },
		[33] = { levelRequirement = 90, },
		[34] = { levelRequirement = 90, },
		[35] = { levelRequirement = 90, },
		[36] = { levelRequirement = 90, },
		[37] = { levelRequirement = 90, },
		[38] = { levelRequirement = 90, },
		[39] = { levelRequirement = 90, },
		[40] = { levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Blood Boil",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "blood_boil",
			baseFlags = {
			},
			constantStats = {
				{ "active_skill_base_area_of_effect_radius", 15 },
				{ "blood_boil_%_health_as_corrupted_blood_damage_per_minute", 120 },
				{ "base_skill_effect_duration", 5000 },
				{ "blood_boil_explosion_area_+%_final_per_boil", 20 },
				{ "blood_boil_explosion_delay_ms", 1000 },
			},
			stats = {
				"blood_boil_application_interval_ms",
				"quality_display_base_skill_effect_duration_is_gem",
			},
			levels = {
				[1] = { 2000, statInterpolation = { 1, }, actorLevel = 1, },
				[2] = { 1975, statInterpolation = { 1, }, actorLevel = 3.4519999027252, },
				[3] = { 1950, statInterpolation = { 1, }, actorLevel = 6.7670001983643, },
				[4] = { 1925, statInterpolation = { 1, }, actorLevel = 10.307999610901, },
				[5] = { 1900, statInterpolation = { 1, }, actorLevel = 14.074999809265, },
				[6] = { 1875, statInterpolation = { 1, }, actorLevel = 18.068000793457, },
				[7] = { 1850, statInterpolation = { 1, }, actorLevel = 22.287000656128, },
				[8] = { 1825, statInterpolation = { 1, }, actorLevel = 26.732000350952, },
				[9] = { 1800, statInterpolation = { 1, }, actorLevel = 31.40299987793, },
				[10] = { 1775, statInterpolation = { 1, }, actorLevel = 36.299999237061, },
				[11] = { 1750, statInterpolation = { 1, }, actorLevel = 41.423000335693, },
				[12] = { 1725, statInterpolation = { 1, }, actorLevel = 46.771999359131, },
				[13] = { 1700, statInterpolation = { 1, }, actorLevel = 52.34700012207, },
				[14] = { 1675, statInterpolation = { 1, }, actorLevel = 58.147998809814, },
				[15] = { 1650, statInterpolation = { 1, }, actorLevel = 64.175003051758, },
				[16] = { 1625, statInterpolation = { 1, }, actorLevel = 70.428001403809, },
				[17] = { 1600, statInterpolation = { 1, }, actorLevel = 76.906997680664, },
				[18] = { 1575, statInterpolation = { 1, }, actorLevel = 83.611999511719, },
				[19] = { 1550, statInterpolation = { 1, }, actorLevel = 90.542999267578, },
				[20] = { 1525, statInterpolation = { 1, }, actorLevel = 97.699996948242, },
				[21] = { 1500, statInterpolation = { 1, }, actorLevel = 105.08300018311, },
				[22] = { 1475, statInterpolation = { 1, }, actorLevel = 112.69200134277, },
				[23] = { 1450, statInterpolation = { 1, }, actorLevel = 120.52700042725, },
				[24] = { 1425, statInterpolation = { 1, }, actorLevel = 128.58799743652, },
				[25] = { 1400, statInterpolation = { 1, }, actorLevel = 136.875, },
				[26] = { 1375, statInterpolation = { 1, }, actorLevel = 145.38800048828, },
				[27] = { 1350, statInterpolation = { 1, }, actorLevel = 154.12699890137, },
				[28] = { 1325, statInterpolation = { 1, }, actorLevel = 163.09199523926, },
				[29] = { 1300, statInterpolation = { 1, }, actorLevel = 172.28300476074, },
				[30] = { 1275, statInterpolation = { 1, }, actorLevel = 181.69999694824, },
				[31] = { 1263, statInterpolation = { 1, }, actorLevel = 191.34300231934, },
				[32] = { 1250, statInterpolation = { 1, }, actorLevel = 201.21200561523, },
				[33] = { 1238, statInterpolation = { 1, }, actorLevel = 211.30700683594, },
				[34] = { 1225, statInterpolation = { 1, }, actorLevel = 221.62800598145, },
				[35] = { 1213, statInterpolation = { 1, }, actorLevel = 232.17500305176, },
				[36] = { 1200, statInterpolation = { 1, }, actorLevel = 242.94799804688, },
				[37] = { 1188, statInterpolation = { 1, }, actorLevel = 253.94700622559, },
				[38] = { 1175, statInterpolation = { 1, }, actorLevel = 265.17199707031, },
				[39] = { 1163, statInterpolation = { 1, }, actorLevel = 276.62298583984, },
				[40] = { 1150, statInterpolation = { 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["MetaCastOnCharmUsePlayer"] = {
	name = "Cast on Charm Use",
	baseTypeName = "Cast on Charm Use",
	fromItem = true,
	color = 4,
	description = "While active, gains Energy when one of your Charms is used and triggers socketed Spells on reaching maximum Energy.",
	skillTypes = { [SkillType.HasReservation] = true, [SkillType.OngoingSkill] = true, [SkillType.Meta] = true, [SkillType.Persistent] = true, [SkillType.Buff] = true, [SkillType.CanHaveMultipleOngoingSkillInstances] = true, [SkillType.GeneratesEnergy] = true, [SkillType.Triggers] = true, },
	castTime = 0,
	qualityStats = {
		{ "energy_generated_+%", 0.75 },
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { levelRequirement = 3, },
		[3] = { levelRequirement = 6, },
		[4] = { levelRequirement = 10, },
		[5] = { levelRequirement = 14, },
		[6] = { levelRequirement = 18, },
		[7] = { levelRequirement = 22, },
		[8] = { levelRequirement = 26, },
		[9] = { levelRequirement = 31, },
		[10] = { levelRequirement = 36, },
		[11] = { levelRequirement = 41, },
		[12] = { levelRequirement = 46, },
		[13] = { levelRequirement = 52, },
		[14] = { levelRequirement = 58, },
		[15] = { levelRequirement = 64, },
		[16] = { levelRequirement = 66, },
		[17] = { levelRequirement = 72, },
		[18] = { levelRequirement = 78, },
		[19] = { levelRequirement = 84, },
		[20] = { levelRequirement = 90, },
		[21] = { levelRequirement = 90, },
		[22] = { levelRequirement = 90, },
		[23] = { levelRequirement = 90, },
		[24] = { levelRequirement = 90, },
		[25] = { levelRequirement = 90, },
		[26] = { levelRequirement = 90, },
		[27] = { levelRequirement = 90, },
		[28] = { levelRequirement = 90, },
		[29] = { levelRequirement = 90, },
		[30] = { levelRequirement = 90, },
		[31] = { levelRequirement = 90, },
		[32] = { levelRequirement = 90, },
		[33] = { levelRequirement = 90, },
		[34] = { levelRequirement = 90, },
		[35] = { levelRequirement = 90, },
		[36] = { levelRequirement = 90, },
		[37] = { levelRequirement = 90, },
		[38] = { levelRequirement = 90, },
		[39] = { levelRequirement = 90, },
		[40] = { levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Cast on Charm Use",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "cast_on_using_charm_gain_X_centienergy_per_charm_charge_used_on_using_charm", 200 },
				{ "skill_desired_amount_override", 1 },
			},
			stats = {
				"energy_generated_+%",
				"generic_ongoing_trigger_triggers_at_maximum_energy",
				"generic_ongoing_trigger_maximum_energy_is_total_of_socketed_skills",
				"base_deal_no_damage",
			},
			levels = {
				[1] = { 0, statInterpolation = { 1, }, actorLevel = 1, },
				[2] = { 3, statInterpolation = { 1, }, actorLevel = 3.4519999027252, },
				[3] = { 6, statInterpolation = { 1, }, actorLevel = 6.7670001983643, },
				[4] = { 9, statInterpolation = { 1, }, actorLevel = 10.307999610901, },
				[5] = { 12, statInterpolation = { 1, }, actorLevel = 14.074999809265, },
				[6] = { 15, statInterpolation = { 1, }, actorLevel = 18.068000793457, },
				[7] = { 18, statInterpolation = { 1, }, actorLevel = 22.287000656128, },
				[8] = { 21, statInterpolation = { 1, }, actorLevel = 26.732000350952, },
				[9] = { 24, statInterpolation = { 1, }, actorLevel = 31.40299987793, },
				[10] = { 27, statInterpolation = { 1, }, actorLevel = 36.299999237061, },
				[11] = { 30, statInterpolation = { 1, }, actorLevel = 41.423000335693, },
				[12] = { 33, statInterpolation = { 1, }, actorLevel = 46.771999359131, },
				[13] = { 36, statInterpolation = { 1, }, actorLevel = 52.34700012207, },
				[14] = { 39, statInterpolation = { 1, }, actorLevel = 58.147998809814, },
				[15] = { 42, statInterpolation = { 1, }, actorLevel = 64.175003051758, },
				[16] = { 45, statInterpolation = { 1, }, actorLevel = 70.428001403809, },
				[17] = { 48, statInterpolation = { 1, }, actorLevel = 76.906997680664, },
				[18] = { 51, statInterpolation = { 1, }, actorLevel = 83.611999511719, },
				[19] = { 54, statInterpolation = { 1, }, actorLevel = 90.542999267578, },
				[20] = { 57, statInterpolation = { 1, }, actorLevel = 97.699996948242, },
				[21] = { 60, statInterpolation = { 1, }, actorLevel = 105.08300018311, },
				[22] = { 63, statInterpolation = { 1, }, actorLevel = 112.69200134277, },
				[23] = { 66, statInterpolation = { 1, }, actorLevel = 120.52700042725, },
				[24] = { 69, statInterpolation = { 1, }, actorLevel = 128.58799743652, },
				[25] = { 72, statInterpolation = { 1, }, actorLevel = 136.875, },
				[26] = { 75, statInterpolation = { 1, }, actorLevel = 145.38800048828, },
				[27] = { 78, statInterpolation = { 1, }, actorLevel = 154.12699890137, },
				[28] = { 81, statInterpolation = { 1, }, actorLevel = 163.09199523926, },
				[29] = { 84, statInterpolation = { 1, }, actorLevel = 172.28300476074, },
				[30] = { 87, statInterpolation = { 1, }, actorLevel = 181.69999694824, },
				[31] = { 90, statInterpolation = { 1, }, actorLevel = 191.34300231934, },
				[32] = { 93, statInterpolation = { 1, }, actorLevel = 201.21200561523, },
				[33] = { 96, statInterpolation = { 1, }, actorLevel = 211.30700683594, },
				[34] = { 99, statInterpolation = { 1, }, actorLevel = 221.62800598145, },
				[35] = { 102, statInterpolation = { 1, }, actorLevel = 232.17500305176, },
				[36] = { 105, statInterpolation = { 1, }, actorLevel = 242.94799804688, },
				[37] = { 108, statInterpolation = { 1, }, actorLevel = 253.94700622559, },
				[38] = { 111, statInterpolation = { 1, }, actorLevel = 265.17199707031, },
				[39] = { 114, statInterpolation = { 1, }, actorLevel = 276.62298583984, },
				[40] = { 117, statInterpolation = { 1, }, actorLevel = 288.29998779297, },
			},
		},
		[2] = {
			label = "Cast on Charm Use",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "cast_on_using_charm_gain_X_centienergy_per_charm_charge_used_on_using_charm", 200 },
				{ "skill_desired_amount_override", 1 },
				{ "generic_ongoing_trigger_1_maximum_energy_per_Xms_total_cast_time", 10 },
			},
			stats = {
				"energy_generated_+%",
				"generic_ongoing_trigger_triggers_at_maximum_energy",
				"generic_ongoing_trigger_maximum_energy_is_total_of_socketed_skills",
				"base_deal_no_damage",
				"triggered_by_generic_ongoing_trigger",
				"skill_cannot_generate_energy",
			},
			levels = {
				[1] = { 0, statInterpolation = { 1, }, actorLevel = 1, },
				[2] = { 3, statInterpolation = { 1, }, actorLevel = 3.4519999027252, },
				[3] = { 6, statInterpolation = { 1, }, actorLevel = 6.7670001983643, },
				[4] = { 9, statInterpolation = { 1, }, actorLevel = 10.307999610901, },
				[5] = { 12, statInterpolation = { 1, }, actorLevel = 14.074999809265, },
				[6] = { 15, statInterpolation = { 1, }, actorLevel = 18.068000793457, },
				[7] = { 18, statInterpolation = { 1, }, actorLevel = 22.287000656128, },
				[8] = { 21, statInterpolation = { 1, }, actorLevel = 26.732000350952, },
				[9] = { 24, statInterpolation = { 1, }, actorLevel = 31.40299987793, },
				[10] = { 27, statInterpolation = { 1, }, actorLevel = 36.299999237061, },
				[11] = { 30, statInterpolation = { 1, }, actorLevel = 41.423000335693, },
				[12] = { 33, statInterpolation = { 1, }, actorLevel = 46.771999359131, },
				[13] = { 36, statInterpolation = { 1, }, actorLevel = 52.34700012207, },
				[14] = { 39, statInterpolation = { 1, }, actorLevel = 58.147998809814, },
				[15] = { 42, statInterpolation = { 1, }, actorLevel = 64.175003051758, },
				[16] = { 45, statInterpolation = { 1, }, actorLevel = 70.428001403809, },
				[17] = { 48, statInterpolation = { 1, }, actorLevel = 76.906997680664, },
				[18] = { 51, statInterpolation = { 1, }, actorLevel = 83.611999511719, },
				[19] = { 54, statInterpolation = { 1, }, actorLevel = 90.542999267578, },
				[20] = { 57, statInterpolation = { 1, }, actorLevel = 97.699996948242, },
				[21] = { 60, statInterpolation = { 1, }, actorLevel = 105.08300018311, },
				[22] = { 63, statInterpolation = { 1, }, actorLevel = 112.69200134277, },
				[23] = { 66, statInterpolation = { 1, }, actorLevel = 120.52700042725, },
				[24] = { 69, statInterpolation = { 1, }, actorLevel = 128.58799743652, },
				[25] = { 72, statInterpolation = { 1, }, actorLevel = 136.875, },
				[26] = { 75, statInterpolation = { 1, }, actorLevel = 145.38800048828, },
				[27] = { 78, statInterpolation = { 1, }, actorLevel = 154.12699890137, },
				[28] = { 81, statInterpolation = { 1, }, actorLevel = 163.09199523926, },
				[29] = { 84, statInterpolation = { 1, }, actorLevel = 172.28300476074, },
				[30] = { 87, statInterpolation = { 1, }, actorLevel = 181.69999694824, },
				[31] = { 90, statInterpolation = { 1, }, actorLevel = 191.34300231934, },
				[32] = { 93, statInterpolation = { 1, }, actorLevel = 201.21200561523, },
				[33] = { 96, statInterpolation = { 1, }, actorLevel = 211.30700683594, },
				[34] = { 99, statInterpolation = { 1, }, actorLevel = 221.62800598145, },
				[35] = { 102, statInterpolation = { 1, }, actorLevel = 232.17500305176, },
				[36] = { 105, statInterpolation = { 1, }, actorLevel = 242.94799804688, },
				[37] = { 108, statInterpolation = { 1, }, actorLevel = 253.94700622559, },
				[38] = { 111, statInterpolation = { 1, }, actorLevel = 265.17199707031, },
				[39] = { 114, statInterpolation = { 1, }, actorLevel = 276.62298583984, },
				[40] = { 117, statInterpolation = { 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["MeleeBowPlayer"] = {
	name = "Bow Shot",
	baseTypeName = "Bow Shot",
	fromItem = true,
	color = 4,
	description = "Fire an arrow with your Bow.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.CanRapidFire] = true, [SkillType.UsableWhileMoving] = true, [SkillType.Bow] = true, },
	weaponTypes = {
		["Bow"] = true,
	},
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { baseMultiplier = 1.1, levelRequirement = 3, },
		[3] = { baseMultiplier = 1.21, levelRequirement = 6, },
		[4] = { baseMultiplier = 1.32, levelRequirement = 10, },
		[5] = { baseMultiplier = 1.42, levelRequirement = 14, },
		[6] = { baseMultiplier = 1.53, levelRequirement = 18, },
		[7] = { baseMultiplier = 1.64, levelRequirement = 22, },
		[8] = { baseMultiplier = 1.74, levelRequirement = 26, },
		[9] = { baseMultiplier = 1.82, levelRequirement = 31, },
		[10] = { baseMultiplier = 1.91, levelRequirement = 36, },
		[11] = { baseMultiplier = 2.01, levelRequirement = 41, },
		[12] = { baseMultiplier = 2.11, levelRequirement = 46, },
		[13] = { baseMultiplier = 2.22, levelRequirement = 52, },
		[14] = { baseMultiplier = 2.33, levelRequirement = 58, },
		[15] = { baseMultiplier = 2.44, levelRequirement = 64, },
		[16] = { baseMultiplier = 2.57, levelRequirement = 66, },
		[17] = { baseMultiplier = 2.69, levelRequirement = 72, },
		[18] = { baseMultiplier = 2.83, levelRequirement = 78, },
		[19] = { baseMultiplier = 2.97, levelRequirement = 84, },
		[20] = { baseMultiplier = 3.12, levelRequirement = 90, },
		[21] = { baseMultiplier = 3.27, levelRequirement = 90, },
		[22] = { baseMultiplier = 3.44, levelRequirement = 90, },
		[23] = { baseMultiplier = 3.61, levelRequirement = 90, },
		[24] = { baseMultiplier = 3.79, levelRequirement = 90, },
		[25] = { baseMultiplier = 3.98, levelRequirement = 90, },
		[26] = { baseMultiplier = 4.18, levelRequirement = 90, },
		[27] = { baseMultiplier = 4.39, levelRequirement = 90, },
		[28] = { baseMultiplier = 4.61, levelRequirement = 90, },
		[29] = { baseMultiplier = 4.84, levelRequirement = 90, },
		[30] = { baseMultiplier = 5.08, levelRequirement = 90, },
		[31] = { baseMultiplier = 5.33, levelRequirement = 90, },
		[32] = { baseMultiplier = 5.6, levelRequirement = 90, },
		[33] = { baseMultiplier = 5.88, levelRequirement = 90, },
		[34] = { baseMultiplier = 6.18, levelRequirement = 90, },
		[35] = { baseMultiplier = 6.48, levelRequirement = 90, },
		[36] = { baseMultiplier = 6.81, levelRequirement = 90, },
		[37] = { baseMultiplier = 7.15, levelRequirement = 90, },
		[38] = { baseMultiplier = 7.51, levelRequirement = 90, },
		[39] = { baseMultiplier = 7.88, levelRequirement = 90, },
		[40] = { baseMultiplier = 8.28, levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Bow Shot",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				projectile = true,
			},
			constantStats = {
				{ "movement_speed_+%_final_while_performing_action", -70 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 60 },
			},
			stats = {
				"base_is_projectile",
				"projectile_uses_contact_position",
				"projectile_uses_contact_direction",
				"check_for_targets_between_initiator_and_projectile_source",
				"skill_can_fire_arrows",
				"can_perform_skill_while_moving",
				"should_use_additive_aiming_animation",
				"has_modular_projectiles_enabled",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["MeleeCrossbowPlayer"] = {
	name = "Crossbow Shot",
	baseTypeName = "Crossbow Shot",
	fromItem = true,
	color = 4,
	description = "Fire a bolt from your crossbow.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Area] = true, [SkillType.CrossbowSkill] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.UsableWhileMoving] = true, },
	weaponTypes = {
		["Crossbow"] = true,
	},
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { baseMultiplier = 1.1, levelRequirement = 3, },
		[3] = { baseMultiplier = 1.21, levelRequirement = 6, },
		[4] = { baseMultiplier = 1.32, levelRequirement = 10, },
		[5] = { baseMultiplier = 1.43, levelRequirement = 14, },
		[6] = { baseMultiplier = 1.54, levelRequirement = 18, },
		[7] = { baseMultiplier = 1.66, levelRequirement = 22, },
		[8] = { baseMultiplier = 1.77, levelRequirement = 26, },
		[9] = { baseMultiplier = 1.87, levelRequirement = 31, },
		[10] = { baseMultiplier = 1.98, levelRequirement = 36, },
		[11] = { baseMultiplier = 2.09, levelRequirement = 41, },
		[12] = { baseMultiplier = 2.22, levelRequirement = 46, },
		[13] = { baseMultiplier = 2.35, levelRequirement = 52, },
		[14] = { baseMultiplier = 2.49, levelRequirement = 58, },
		[15] = { baseMultiplier = 2.65, levelRequirement = 64, },
		[16] = { baseMultiplier = 2.81, levelRequirement = 66, },
		[17] = { baseMultiplier = 2.99, levelRequirement = 72, },
		[18] = { baseMultiplier = 3.19, levelRequirement = 78, },
		[19] = { baseMultiplier = 3.4, levelRequirement = 84, },
		[20] = { baseMultiplier = 3.62, levelRequirement = 90, },
		[21] = { baseMultiplier = 3.86, levelRequirement = 90, },
		[22] = { baseMultiplier = 4.12, levelRequirement = 90, },
		[23] = { baseMultiplier = 4.4, levelRequirement = 90, },
		[24] = { baseMultiplier = 4.69, levelRequirement = 90, },
		[25] = { baseMultiplier = 5, levelRequirement = 90, },
		[26] = { baseMultiplier = 5.33, levelRequirement = 90, },
		[27] = { baseMultiplier = 5.69, levelRequirement = 90, },
		[28] = { baseMultiplier = 6.07, levelRequirement = 90, },
		[29] = { baseMultiplier = 6.47, levelRequirement = 90, },
		[30] = { baseMultiplier = 6.9, levelRequirement = 90, },
		[31] = { baseMultiplier = 7.36, levelRequirement = 90, },
		[32] = { baseMultiplier = 7.85, levelRequirement = 90, },
		[33] = { baseMultiplier = 8.38, levelRequirement = 90, },
		[34] = { baseMultiplier = 8.93, levelRequirement = 90, },
		[35] = { baseMultiplier = 9.53, levelRequirement = 90, },
		[36] = { baseMultiplier = 10.16, levelRequirement = 90, },
		[37] = { baseMultiplier = 10.84, levelRequirement = 90, },
		[38] = { baseMultiplier = 11.56, levelRequirement = 90, },
		[39] = { baseMultiplier = 12.33, levelRequirement = 90, },
		[40] = { baseMultiplier = 13.16, levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Crossbow Shot",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				area = true,
				projectile = true,
			},
			constantStats = {
				{ "action_required_target_facing_angle_tolerance_degrees", 90 },
				{ "crossbow_barrage_attack_time_ratio_%", 10 },
				{ "crossbow_barrage_recoil_per_shot", 2 },
				{ "crossbow_barrage_total_recoil_buff_count", 8 },
				{ "crossbow_barrage_debuff_duration_ms", 300 },
				{ "base_knockback_distance", 0 },
				{ "movement_speed_+%_final_while_performing_action", -70 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 60 },
			},
			stats = {
				"base_is_projectile",
				"has_modular_projectiles_enabled",
				"action_requires_aiming_stance",
				"projectile_uses_contact_position",
				"projectile_uses_contact_direction",
				"check_for_targets_between_initiator_and_projectile_source",
				"projectiles_crossbow_barrage",
				"cannot_cancel_skill_before_contact_point",
				"disable_visual_hit_effect",
				"can_perform_skill_while_moving",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["UnloadAmmoPlayer"] = {
	name = "Basic Bolt",
	hidden = true,
	fromItem = true,
	description = "Clear your active Bolt and reload any ammunition.",
	skillTypes = { [SkillType.CrossbowAmmoSkill] = true, [SkillType.Attack] = true, [SkillType.UsableWhileMoving] = true, },
	weaponTypes = {
		["Crossbow"] = true,
	},
	castTime = 0.5,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { levelRequirement = 0, },
		[3] = { levelRequirement = 0, },
		[4] = { levelRequirement = 0, },
		[5] = { levelRequirement = 0, },
		[6] = { levelRequirement = 0, },
		[7] = { levelRequirement = 0, },
		[8] = { levelRequirement = 0, },
		[9] = { levelRequirement = 0, },
		[10] = { levelRequirement = 0, },
		[11] = { levelRequirement = 0, },
		[12] = { levelRequirement = 0, },
		[13] = { levelRequirement = 0, },
		[14] = { levelRequirement = 0, },
		[15] = { levelRequirement = 0, },
		[16] = { levelRequirement = 0, },
		[17] = { levelRequirement = 0, },
		[18] = { levelRequirement = 0, },
		[19] = { levelRequirement = 0, },
		[20] = { levelRequirement = 0, },
		[21] = { levelRequirement = 0, },
		[22] = { levelRequirement = 0, },
		[23] = { levelRequirement = 0, },
		[24] = { levelRequirement = 0, },
		[25] = { levelRequirement = 0, },
		[26] = { levelRequirement = 0, },
		[27] = { levelRequirement = 0, },
		[28] = { levelRequirement = 0, },
		[29] = { levelRequirement = 0, },
		[30] = { levelRequirement = 0, },
		[31] = { levelRequirement = 0, },
		[32] = { levelRequirement = 0, },
		[33] = { levelRequirement = 0, },
		[34] = { levelRequirement = 0, },
		[35] = { levelRequirement = 0, },
		[36] = { levelRequirement = 0, },
		[37] = { levelRequirement = 0, },
		[38] = { levelRequirement = 0, },
		[39] = { levelRequirement = 0, },
		[40] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Ammunition",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "action_allowed_queue_time_override_ms", 1000 },
				{ "movement_speed_+%_final_while_performing_action", -30 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 60 },
				{ "base_number_of_crossbow_bolts", 7 },
			},
			stats = {
				"crossbow_ammo_skill_binds_default_attack",
				"action_can_be_used_in_aiming_stance",
				"can_perform_skill_while_moving",
				"base_deal_no_damage",
				"crossbow_ammo_skill_does_not_transition",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["DemonFormPlayer"] = {
	name = "Demon Form",
	baseTypeName = "Demon Form",
	fromTree = true,
	color = 4,
	description = "Shapeshift into a demon, vastly boosting the power of your Spells. You gain Demonflame every second you remain in demon form, causing your Life to be lost at an ever-increasing rate. Maximum 10 Demonflame. Revert to human form if you reach 1 Life, use a Skill that isn't a Spell, or reactivate this Skill.",
	skillTypes = { [SkillType.Buff] = true, [SkillType.Persistent] = true, [SkillType.Cooldown] = true, [SkillType.Shapeshift] = true, [SkillType.ManualCooldownConsumption] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 15, cost = { Mana = 7, }, },
		[2] = { storedUses = 1, levelRequirement = 3, cooldown = 15, cost = { Mana = 8, }, },
		[3] = { storedUses = 1, levelRequirement = 6, cooldown = 15, cost = { Mana = 9, }, },
		[4] = { storedUses = 1, levelRequirement = 10, cooldown = 15, cost = { Mana = 10, }, },
		[5] = { storedUses = 1, levelRequirement = 14, cooldown = 15, cost = { Mana = 12, }, },
		[6] = { storedUses = 1, levelRequirement = 18, cooldown = 15, cost = { Mana = 14, }, },
		[7] = { storedUses = 1, levelRequirement = 22, cooldown = 15, cost = { Mana = 16, }, },
		[8] = { storedUses = 1, levelRequirement = 26, cooldown = 15, cost = { Mana = 18, }, },
		[9] = { storedUses = 1, levelRequirement = 31, cooldown = 15, cost = { Mana = 21, }, },
		[10] = { storedUses = 1, levelRequirement = 36, cooldown = 15, cost = { Mana = 24, }, },
		[11] = { storedUses = 1, levelRequirement = 41, cooldown = 15, cost = { Mana = 28, }, },
		[12] = { storedUses = 1, levelRequirement = 46, cooldown = 15, cost = { Mana = 32, }, },
		[13] = { storedUses = 1, levelRequirement = 52, cooldown = 15, cost = { Mana = 37, }, },
		[14] = { storedUses = 1, levelRequirement = 58, cooldown = 15, cost = { Mana = 43, }, },
		[15] = { storedUses = 1, levelRequirement = 64, cooldown = 15, cost = { Mana = 49, }, },
		[16] = { storedUses = 1, levelRequirement = 66, cooldown = 15, cost = { Mana = 56, }, },
		[17] = { storedUses = 1, levelRequirement = 72, cooldown = 15, cost = { Mana = 65, }, },
		[18] = { storedUses = 1, levelRequirement = 78, cooldown = 15, cost = { Mana = 74, }, },
		[19] = { storedUses = 1, levelRequirement = 84, cooldown = 15, cost = { Mana = 85, }, },
		[20] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 98, }, },
		[21] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 113, }, },
		[22] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 129, }, },
		[23] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 148, }, },
		[24] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 170, }, },
		[25] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 196, }, },
		[26] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 225, }, },
		[27] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 258, }, },
		[28] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 296, }, },
		[29] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 340, }, },
		[30] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 391, }, },
		[31] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 449, }, },
		[32] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 515, }, },
		[33] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 592, }, },
		[34] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 679, }, },
		[35] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 780, }, },
		[36] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 895, }, },
		[37] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 1028, }, },
		[38] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 1180, }, },
		[39] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 1355, }, },
		[40] = { storedUses = 1, levelRequirement = 90, cooldown = 15, cost = { Mana = 1556, }, },
	},
	statSets = {
		[1] = {
			label = "Demon Form",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "demon_transformation",
			baseFlags = {
			},
			stats = {
				"demon_form_spell_damage_+%_per_stack",
				"demon_form_grants_cast_speed_+%",
				"demon_form_grants_spell_gem_level_+",
				"demon_form_life_loss_per_minute_per_stack",
				"base_deal_no_damage",
				"demon_transformation_cooldown_does_not_tick",
			},
			levels = {
				[1] = { 7, 12, 3, 48, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 1, },
				[2] = { 8, 13, 3, 64, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 3.4519999027252, },
				[3] = { 8, 13, 3, 72, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 6.7670001983643, },
				[4] = { 9, 14, 3, 100, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 10.307999610901, },
				[5] = { 9, 15, 4, 128, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 14.074999809265, },
				[6] = { 10, 15, 4, 144, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 18.068000793457, },
				[7] = { 10, 16, 4, 160, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 22.287000656128, },
				[8] = { 11, 17, 4, 180, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 26.732000350952, },
				[9] = { 12, 17, 4, 210, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 31.40299987793, },
				[10] = { 12, 18, 4, 244, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 36.299999237061, },
				[11] = { 13, 19, 5, 308, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 41.423000335693, },
				[12] = { 13, 20, 5, 346, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 46.771999359131, },
				[13] = { 14, 20, 5, 394, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 52.34700012207, },
				[14] = { 15, 21, 5, 454, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 58.147998809814, },
				[15] = { 15, 22, 5, 518, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 64.175003051758, },
				[16] = { 16, 22, 5, 585, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 70.428001403809, },
				[17] = { 16, 23, 6, 780, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 76.906997680664, },
				[18] = { 17, 24, 6, 848, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 83.611999511719, },
				[19] = { 18, 24, 6, 980, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 90.542999267578, },
				[20] = { 18, 25, 6, 1112, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 97.699996948242, },
				[21] = { 19, 26, 6, 1202, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 105.08300018311, },
				[22] = { 20, 26, 6, 1315, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 112.69200134277, },
				[23] = { 20, 27, 6, 1428, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 120.52700042725, },
				[24] = { 20, 28, 7, 1541, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 128.58799743652, },
				[25] = { 21, 28, 7, 1654, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 136.875, },
				[26] = { 21, 29, 7, 1767, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 145.38800048828, },
				[27] = { 22, 30, 7, 1880, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 154.12699890137, },
				[28] = { 23, 30, 7, 1992, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 163.09199523926, },
				[29] = { 23, 31, 7, 2105, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 172.28300476074, },
				[30] = { 24, 32, 8, 2218, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 181.69999694824, },
				[31] = { 24, 32, 8, 2331, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 191.34300231934, },
				[32] = { 24, 33, 8, 2444, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 201.21200561523, },
				[33] = { 25, 33, 8, 2557, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 211.30700683594, },
				[34] = { 25, 33, 8, 2670, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 221.62800598145, },
				[35] = { 25, 34, 8, 2783, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 232.17500305176, },
				[36] = { 26, 34, 8, 2896, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 242.94799804688, },
				[37] = { 26, 34, 8, 3009, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 253.94700622559, },
				[38] = { 26, 35, 8, 3122, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 265.17199707031, },
				[39] = { 26, 35, 8, 3235, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 276.62298583984, },
				[40] = { 27, 35, 8, 3347, statInterpolation = { 1, 1, 1, 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["ElementalExpressionTriggeredPlayer"] = {
	name = "Elemental Expression",
	baseTypeName = "Elemental Expression",
	fromTree = true,
	color = 4,
	description = "Create a fiery explosion, an arcing bolt of lightning, or an icy wave of projectiles. The chance for an explosion is proportional to your Strength, for a bolt proportional to your Dexterity, and for a wave proportional to your Intelligence.",
	skillTypes = { [SkillType.Damage] = true, [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Cooldown] = true, [SkillType.Triggered] = true, [SkillType.InbuiltTrigger] = true, [SkillType.Lightning] = true, [SkillType.Cold] = true, [SkillType.Fire] = true, [SkillType.Projectile] = true, [SkillType.Area] = true, [SkillType.Chains] = true, },
	castTime = 0,
	qualityStats = {
		{ "base_cooldown_speed_+%", 1 },
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 0.25, cost = { Mana = 4, }, },
		[2] = { storedUses = 1, levelRequirement = 3, cooldown = 0.25, cost = { Mana = 4, }, },
		[3] = { storedUses = 1, levelRequirement = 6, cooldown = 0.25, cost = { Mana = 5, }, },
		[4] = { storedUses = 1, levelRequirement = 10, cooldown = 0.25, cost = { Mana = 6, }, },
		[5] = { storedUses = 1, levelRequirement = 14, cooldown = 0.25, cost = { Mana = 7, }, },
		[6] = { storedUses = 1, levelRequirement = 18, cooldown = 0.25, cost = { Mana = 8, }, },
		[7] = { storedUses = 1, levelRequirement = 22, cooldown = 0.25, cost = { Mana = 9, }, },
		[8] = { storedUses = 1, levelRequirement = 26, cooldown = 0.25, cost = { Mana = 11, }, },
		[9] = { storedUses = 1, levelRequirement = 31, cooldown = 0.25, cost = { Mana = 13, }, },
		[10] = { storedUses = 1, levelRequirement = 36, cooldown = 0.25, cost = { Mana = 14, }, },
		[11] = { storedUses = 1, levelRequirement = 41, cooldown = 0.25, cost = { Mana = 17, }, },
		[12] = { storedUses = 1, levelRequirement = 46, cooldown = 0.25, cost = { Mana = 19, }, },
		[13] = { storedUses = 1, levelRequirement = 52, cooldown = 0.25, cost = { Mana = 22, }, },
		[14] = { storedUses = 1, levelRequirement = 58, cooldown = 0.25, cost = { Mana = 25, }, },
		[15] = { storedUses = 1, levelRequirement = 64, cooldown = 0.25, cost = { Mana = 29, }, },
		[16] = { storedUses = 1, levelRequirement = 66, cooldown = 0.25, cost = { Mana = 34, }, },
		[17] = { storedUses = 1, levelRequirement = 72, cooldown = 0.25, cost = { Mana = 39, }, },
		[18] = { storedUses = 1, levelRequirement = 78, cooldown = 0.25, cost = { Mana = 45, }, },
		[19] = { storedUses = 1, levelRequirement = 84, cooldown = 0.25, cost = { Mana = 51, }, },
		[20] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 59, }, },
		[21] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 68, }, },
		[22] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 78, }, },
		[23] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 89, }, },
		[24] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 103, }, },
		[25] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 118, }, },
		[26] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 136, }, },
		[27] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 156, }, },
		[28] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 179, }, },
		[29] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 205, }, },
		[30] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 236, }, },
		[31] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 271, }, },
		[32] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 311, }, },
		[33] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 357, }, },
		[34] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 410, }, },
		[35] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 471, }, },
		[36] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 541, }, },
		[37] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 621, }, },
		[38] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 713, }, },
		[39] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 818, }, },
		[40] = { storedUses = 1, levelRequirement = 90, cooldown = 0.25, cost = { Mana = 940, }, },
	},
	statSets = {
		[1] = {
			label = "Elemental Expression",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
			},
			stats = {
				"is_triggerable_strike",
				"triggerable_in_any_set",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
		[2] = {
			label = "Fiery Explosion",
			baseEffectiveness = 1.75,
			incrementalEffectiveness = 0.14000000059605,
			damageIncrementalEffectiveness = 0.006800000090152,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				area = true,
			},
			constantStats = {
				{ "active_skill_base_area_of_effect_radius", 36 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"is_triggerable_strike",
				"triggerable_in_any_set",
				"is_area_damage",
			},
			levels = {
				[1] = { 5, 8, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 1, },
				[2] = { 7, 11, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 3.4519999027252, },
				[3] = { 10, 15, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 6.7670001983643, },
				[4] = { 13, 20, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 10.307999610901, },
				[5] = { 17, 25, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 14.074999809265, },
				[6] = { 21, 31, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 18.068000793457, },
				[7] = { 25, 38, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 22.287000656128, },
				[8] = { 30, 45, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 26.732000350952, },
				[9] = { 35, 53, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 31.40299987793, },
				[10] = { 41, 62, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 36.299999237061, },
				[11] = { 48, 71, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 41.423000335693, },
				[12] = { 55, 82, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 46.771999359131, },
				[13] = { 63, 95, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 52.34700012207, },
				[14] = { 72, 108, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 58.147998809814, },
				[15] = { 82, 123, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 64.175003051758, },
				[16] = { 93, 140, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 70.428001403809, },
				[17] = { 106, 159, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 76.906997680664, },
				[18] = { 120, 179, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 83.611999511719, },
				[19] = { 135, 203, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 90.542999267578, },
				[20] = { 152, 228, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 97.699996948242, },
				[21] = { 171, 257, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 105.08300018311, },
				[22] = { 193, 289, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 112.69200134277, },
				[23] = { 217, 325, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 120.52700042725, },
				[24] = { 244, 365, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 128.58799743652, },
				[25] = { 274, 410, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 136.875, },
				[26] = { 307, 460, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 145.38800048828, },
				[27] = { 345, 517, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 154.12699890137, },
				[28] = { 387, 580, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 163.09199523926, },
				[29] = { 434, 651, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 172.28300476074, },
				[30] = { 487, 730, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 181.69999694824, },
				[31] = { 546, 819, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 191.34300231934, },
				[32] = { 613, 920, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 201.21200561523, },
				[33] = { 689, 1033, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 211.30700683594, },
				[34] = { 774, 1160, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 221.62800598145, },
				[35] = { 869, 1304, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 232.17500305176, },
				[36] = { 978, 1466, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 242.94799804688, },
				[37] = { 1100, 1650, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 253.94700622559, },
				[38] = { 1238, 1857, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 265.17199707031, },
				[39] = { 1394, 2091, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 276.62298583984, },
				[40] = { 1571, 2357, critChance = 7, statInterpolation = { 1, 1, }, actorLevel = 288.29998779297, },
			},
		},
		[3] = {
			label = "Icy Wave",
			baseEffectiveness = 1.4349999427795,
			incrementalEffectiveness = 0.14000000059605,
			damageIncrementalEffectiveness = 0.006800000090152,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
			},
			constantStats = {
				{ "base_number_of_projectiles", 3 },
				{ "fixed_projectile_height", 15 },
			},
			stats = {
				"spell_minimum_base_cold_damage",
				"spell_maximum_base_cold_damage",
				"is_triggerable_strike",
				"triggerable_in_any_set",
				"always_pierce",
				"show_number_of_projectiles",
				"base_is_projectile",
			},
			levels = {
				[1] = { 4, 7, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 1, },
				[2] = { 6, 9, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 3.4519999027252, },
				[3] = { 8, 13, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 6.7670001983643, },
				[4] = { 11, 16, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 10.307999610901, },
				[5] = { 14, 21, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 14.074999809265, },
				[6] = { 17, 25, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 18.068000793457, },
				[7] = { 21, 31, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 22.287000656128, },
				[8] = { 24, 37, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 26.732000350952, },
				[9] = { 29, 43, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 31.40299987793, },
				[10] = { 34, 50, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 36.299999237061, },
				[11] = { 39, 59, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 41.423000335693, },
				[12] = { 45, 68, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 46.771999359131, },
				[13] = { 52, 78, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 52.34700012207, },
				[14] = { 59, 89, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 58.147998809814, },
				[15] = { 67, 101, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 64.175003051758, },
				[16] = { 77, 115, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 70.428001403809, },
				[17] = { 87, 130, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 76.906997680664, },
				[18] = { 98, 147, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 83.611999511719, },
				[19] = { 111, 166, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 90.542999267578, },
				[20] = { 125, 187, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 97.699996948242, },
				[21] = { 141, 211, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 105.08300018311, },
				[22] = { 158, 237, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 112.69200134277, },
				[23] = { 178, 267, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 120.52700042725, },
				[24] = { 200, 300, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 128.58799743652, },
				[25] = { 224, 336, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 136.875, },
				[26] = { 252, 378, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 145.38800048828, },
				[27] = { 283, 424, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 154.12699890137, },
				[28] = { 317, 475, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 163.09199523926, },
				[29] = { 356, 534, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 172.28300476074, },
				[30] = { 399, 599, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 181.69999694824, },
				[31] = { 448, 672, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 191.34300231934, },
				[32] = { 503, 754, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 201.21200561523, },
				[33] = { 565, 847, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 211.30700683594, },
				[34] = { 634, 952, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 221.62800598145, },
				[35] = { 713, 1069, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 232.17500305176, },
				[36] = { 802, 1202, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 242.94799804688, },
				[37] = { 902, 1353, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 253.94700622559, },
				[38] = { 1015, 1523, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 265.17199707031, },
				[39] = { 1143, 1715, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 276.62298583984, },
				[40] = { 1288, 1933, critChance = 12, statInterpolation = { 1, 1, }, actorLevel = 288.29998779297, },
			},
		},
		[4] = {
			label = "Arcing Bolt",
			baseEffectiveness = 1.6100000143051,
			incrementalEffectiveness = 0.14000000059605,
			damageIncrementalEffectiveness = 0.006800000090152,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				chaining = true,
			},
			constantStats = {
				{ "number_of_chains", 7 },
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
				"is_triggerable_strike",
				"triggerable_in_any_set",
			},
			levels = {
				[1] = { 2, 11, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 1, },
				[2] = { 3, 15, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 3.4519999027252, },
				[3] = { 4, 20, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 6.7670001983643, },
				[4] = { 5, 26, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 10.307999610901, },
				[5] = { 6, 33, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 14.074999809265, },
				[6] = { 7, 40, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 18.068000793457, },
				[7] = { 9, 49, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 22.287000656128, },
				[8] = { 10, 58, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 26.732000350952, },
				[9] = { 12, 69, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 31.40299987793, },
				[10] = { 14, 80, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 36.299999237061, },
				[11] = { 16, 93, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 41.423000335693, },
				[12] = { 19, 107, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 46.771999359131, },
				[13] = { 22, 123, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 52.34700012207, },
				[14] = { 25, 141, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 58.147998809814, },
				[15] = { 28, 161, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 64.175003051758, },
				[16] = { 32, 182, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 70.428001403809, },
				[17] = { 36, 207, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 76.906997680664, },
				[18] = { 41, 234, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 83.611999511719, },
				[19] = { 47, 264, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 90.542999267578, },
				[20] = { 53, 298, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 97.699996948242, },
				[21] = { 59, 335, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 105.08300018311, },
				[22] = { 67, 377, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 112.69200134277, },
				[23] = { 75, 424, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 120.52700042725, },
				[24] = { 84, 476, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 128.58799743652, },
				[25] = { 94, 535, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 136.875, },
				[26] = { 106, 600, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 145.38800048828, },
				[27] = { 119, 674, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 154.12699890137, },
				[28] = { 133, 756, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 163.09199523926, },
				[29] = { 150, 848, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 172.28300476074, },
				[30] = { 168, 952, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 181.69999694824, },
				[31] = { 188, 1068, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 191.34300231934, },
				[32] = { 212, 1199, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 201.21200561523, },
				[33] = { 238, 1346, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 211.30700683594, },
				[34] = { 267, 1512, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 221.62800598145, },
				[35] = { 300, 1700, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 232.17500305176, },
				[36] = { 337, 1911, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 242.94799804688, },
				[37] = { 379, 2150, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 253.94700622559, },
				[38] = { 427, 2420, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 265.17199707031, },
				[39] = { 481, 2726, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 276.62298583984, },
				[40] = { 542, 3072, critChance = 9, statInterpolation = { 1, 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["ElementalStormPlayer"] = {
	name = "Elemental Storm",
	baseTypeName = "Elemental Storm",
	fromTree = true,
	color = 4,
	description = "Create a stationary Fire, Cold or Lightning storm at a target location for a duration, based on the highest Elemental Damage type for the Hit that Triggered the storm. Hits which do not deal Elemental Damage will not Trigger the storm.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Duration] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, [SkillType.Area] = true, [SkillType.Orb] = true, [SkillType.AreaSpell] = true, [SkillType.Triggered] = true, [SkillType.InbuiltTrigger] = true, [SkillType.Cooldown] = true, [SkillType.Fire] = true, [SkillType.Cold] = true, [SkillType.Lightning] = true, },
	castTime = 0,
	qualityStats = {
		{ "base_cooldown_speed_+%", 0.5 },
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 5, cost = { Mana = 8, }, },
		[2] = { storedUses = 1, levelRequirement = 3, cooldown = 5, cost = { Mana = 9, }, },
		[3] = { storedUses = 1, levelRequirement = 6, cooldown = 5, cost = { Mana = 10, }, },
		[4] = { storedUses = 1, levelRequirement = 10, cooldown = 5, cost = { Mana = 11, }, },
		[5] = { storedUses = 1, levelRequirement = 14, cooldown = 5, cost = { Mana = 13, }, },
		[6] = { storedUses = 1, levelRequirement = 18, cooldown = 5, cost = { Mana = 14, }, },
		[7] = { storedUses = 1, levelRequirement = 22, cooldown = 5, cost = { Mana = 16, }, },
		[8] = { storedUses = 1, levelRequirement = 26, cooldown = 5, cost = { Mana = 18, }, },
		[9] = { storedUses = 1, levelRequirement = 31, cooldown = 5, cost = { Mana = 20, }, },
		[10] = { storedUses = 1, levelRequirement = 36, cooldown = 5, cost = { Mana = 22, }, },
		[11] = { storedUses = 1, levelRequirement = 41, cooldown = 5, cost = { Mana = 25, }, },
		[12] = { storedUses = 1, levelRequirement = 46, cooldown = 5, cost = { Mana = 28, }, },
		[13] = { storedUses = 1, levelRequirement = 52, cooldown = 5, cost = { Mana = 31, }, },
		[14] = { storedUses = 1, levelRequirement = 58, cooldown = 5, cost = { Mana = 34, }, },
		[15] = { storedUses = 1, levelRequirement = 64, cooldown = 5, cost = { Mana = 38, }, },
		[16] = { storedUses = 1, levelRequirement = 66, cooldown = 5, cost = { Mana = 43, }, },
		[17] = { storedUses = 1, levelRequirement = 72, cooldown = 5, cost = { Mana = 48, }, },
		[18] = { storedUses = 1, levelRequirement = 78, cooldown = 5, cost = { Mana = 54, }, },
		[19] = { storedUses = 1, levelRequirement = 84, cooldown = 5, cost = { Mana = 60, }, },
		[20] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 67, }, },
		[21] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 74, }, },
		[22] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 83, }, },
		[23] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 93, }, },
		[24] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 103, }, },
		[25] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 115, }, },
		[26] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 129, }, },
		[27] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 143, }, },
		[28] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 160, }, },
		[29] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 178, }, },
		[30] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 199, }, },
		[31] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 222, }, },
		[32] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 247, }, },
		[33] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 276, }, },
		[34] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 308, }, },
		[35] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 343, }, },
		[36] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 383, }, },
		[37] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 427, }, },
		[38] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 476, }, },
		[39] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 531, }, },
		[40] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { Mana = 592, }, },
	},
	statSets = {
		[1] = {
			label = "Elemental Storm",
			baseEffectiveness = 0.5625,
			incrementalEffectiveness = 0.14000000059605,
			damageIncrementalEffectiveness = 0.0082000000402331,
			statDescriptionScope = "tornado_triggered",
			statMap = {
				["tornado_base_damage_interval_ms"] = {
					skill("hitTimeOverride", nil),
					div = 1000,
				},
			},
			baseFlags = {
				area = true,
				duration = true,
			},
			constantStats = {
				{ "tornado_base_damage_interval_ms", 250 },
				{ "number_of_tornados_allowed", 50 },
				{ "skill_override_pvp_scaling_time_ms", 1000 },
				{ "base_skill_effect_duration", 5000 },
				{ "active_skill_base_area_of_effect_radius", 18 },
			},
			stats = {
				"skill_can_add_multiple_charges_per_action",
				"damage_cannot_be_reflected_or_leech_if_used_by_other_object",
				"is_area_damage",
				"tornado_hinder",
				"is_triggerable_elemental_storm",
				"global_cannot_crit",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
		[2] = {
			label = "Fire",
			baseEffectiveness = 0.55000001192093,
			incrementalEffectiveness = 0.14000000059605,
			damageIncrementalEffectiveness = 0.0082000000402331,
			statDescriptionScope = "tornado_triggered",
			statMap = {
				["tornado_base_damage_interval_ms"] = {
					skill("hitTimeOverride", nil),
					div = 1000,
				},
			},
			baseFlags = {
				spell = true,
				area = true,
				duration = true,
			},
			constantStats = {
				{ "tornado_base_damage_interval_ms", 250 },
				{ "number_of_tornados_allowed", 50 },
				{ "skill_override_pvp_scaling_time_ms", 1000 },
				{ "base_skill_effect_duration", 5000 },
				{ "active_skill_base_area_of_effect_radius", 18 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"skill_can_add_multiple_charges_per_action",
				"damage_cannot_be_reflected_or_leech_if_used_by_other_object",
				"is_area_damage",
				"tornado_hinder",
				"is_triggerable_elemental_storm",
				"global_cannot_crit",
			},
			levels = {
				[1] = { 2, 3, statInterpolation = { 1, 1, }, actorLevel = 1, },
				[2] = { 2, 4, statInterpolation = { 1, 1, }, actorLevel = 3.4519999027252, },
				[3] = { 3, 5, statInterpolation = { 1, 1, }, actorLevel = 6.7670001983643, },
				[4] = { 4, 6, statInterpolation = { 1, 1, }, actorLevel = 10.307999610901, },
				[5] = { 5, 8, statInterpolation = { 1, 1, }, actorLevel = 14.074999809265, },
				[6] = { 7, 10, statInterpolation = { 1, 1, }, actorLevel = 18.068000793457, },
				[7] = { 8, 12, statInterpolation = { 1, 1, }, actorLevel = 22.287000656128, },
				[8] = { 10, 15, statInterpolation = { 1, 1, }, actorLevel = 26.732000350952, },
				[9] = { 12, 17, statInterpolation = { 1, 1, }, actorLevel = 31.40299987793, },
				[10] = { 14, 20, statInterpolation = { 1, 1, }, actorLevel = 36.299999237061, },
				[11] = { 16, 24, statInterpolation = { 1, 1, }, actorLevel = 41.423000335693, },
				[12] = { 18, 28, statInterpolation = { 1, 1, }, actorLevel = 46.771999359131, },
				[13] = { 21, 32, statInterpolation = { 1, 1, }, actorLevel = 52.34700012207, },
				[14] = { 25, 37, statInterpolation = { 1, 1, }, actorLevel = 58.147998809814, },
				[15] = { 28, 42, statInterpolation = { 1, 1, }, actorLevel = 64.175003051758, },
				[16] = { 32, 48, statInterpolation = { 1, 1, }, actorLevel = 70.428001403809, },
				[17] = { 37, 55, statInterpolation = { 1, 1, }, actorLevel = 76.906997680664, },
				[18] = { 42, 63, statInterpolation = { 1, 1, }, actorLevel = 83.611999511719, },
				[19] = { 48, 72, statInterpolation = { 1, 1, }, actorLevel = 90.542999267578, },
				[20] = { 55, 82, statInterpolation = { 1, 1, }, actorLevel = 97.699996948242, },
				[21] = { 62, 93, statInterpolation = { 1, 1, }, actorLevel = 105.08300018311, },
				[22] = { 71, 106, statInterpolation = { 1, 1, }, actorLevel = 112.69200134277, },
				[23] = { 80, 121, statInterpolation = { 1, 1, }, actorLevel = 120.52700042725, },
				[24] = { 91, 137, statInterpolation = { 1, 1, }, actorLevel = 128.58799743652, },
				[25] = { 104, 156, statInterpolation = { 1, 1, }, actorLevel = 136.875, },
				[26] = { 118, 177, statInterpolation = { 1, 1, }, actorLevel = 145.38800048828, },
				[27] = { 134, 201, statInterpolation = { 1, 1, }, actorLevel = 154.12699890137, },
				[28] = { 152, 228, statInterpolation = { 1, 1, }, actorLevel = 163.09199523926, },
				[29] = { 173, 259, statInterpolation = { 1, 1, }, actorLevel = 172.28300476074, },
				[30] = { 197, 295, statInterpolation = { 1, 1, }, actorLevel = 181.69999694824, },
				[31] = { 224, 336, statInterpolation = { 1, 1, }, actorLevel = 191.34300231934, },
				[32] = { 255, 382, statInterpolation = { 1, 1, }, actorLevel = 201.21200561523, },
				[33] = { 290, 435, statInterpolation = { 1, 1, }, actorLevel = 211.30700683594, },
				[34] = { 330, 496, statInterpolation = { 1, 1, }, actorLevel = 221.62800598145, },
				[35] = { 377, 565, statInterpolation = { 1, 1, }, actorLevel = 232.17500305176, },
				[36] = { 430, 645, statInterpolation = { 1, 1, }, actorLevel = 242.94799804688, },
				[37] = { 491, 737, statInterpolation = { 1, 1, }, actorLevel = 253.94700622559, },
				[38] = { 562, 842, statInterpolation = { 1, 1, }, actorLevel = 265.17199707031, },
				[39] = { 643, 964, statInterpolation = { 1, 1, }, actorLevel = 276.62298583984, },
				[40] = { 736, 1104, statInterpolation = { 1, 1, }, actorLevel = 288.29998779297, },
			},
		},
		[3] = {
			label = "Lightning",
			baseEffectiveness = 0.41999998688698,
			incrementalEffectiveness = 0.14000000059605,
			damageIncrementalEffectiveness = 0.0082000000402331,
			statDescriptionScope = "tornado_triggered",
			statMap = {
				["tornado_base_damage_interval_ms"] = {
					skill("hitTimeOverride", nil),
					div = 1000,
				},
			},
			baseFlags = {
				spell = true,
				area = true,
				duration = true,
			},
			constantStats = {
				{ "tornado_base_damage_interval_ms", 250 },
				{ "number_of_tornados_allowed", 50 },
				{ "skill_override_pvp_scaling_time_ms", 1000 },
				{ "base_skill_effect_duration", 5000 },
				{ "active_skill_base_area_of_effect_radius", 18 },
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
				"skill_can_add_multiple_charges_per_action",
				"damage_cannot_be_reflected_or_leech_if_used_by_other_object",
				"is_area_damage",
				"tornado_hinder",
				"is_triggerable_elemental_storm",
				"global_cannot_crit",
			},
			levels = {
				[1] = { 1, 3, statInterpolation = { 1, 1, }, actorLevel = 1, },
				[2] = { 1, 4, statInterpolation = { 1, 1, }, actorLevel = 3.4519999027252, },
				[3] = { 1, 5, statInterpolation = { 1, 1, }, actorLevel = 6.7670001983643, },
				[4] = { 1, 7, statInterpolation = { 1, 1, }, actorLevel = 10.307999610901, },
				[5] = { 2, 9, statInterpolation = { 1, 1, }, actorLevel = 14.074999809265, },
				[6] = { 2, 11, statInterpolation = { 1, 1, }, actorLevel = 18.068000793457, },
				[7] = { 2, 13, statInterpolation = { 1, 1, }, actorLevel = 22.287000656128, },
				[8] = { 3, 16, statInterpolation = { 1, 1, }, actorLevel = 26.732000350952, },
				[9] = { 3, 19, statInterpolation = { 1, 1, }, actorLevel = 31.40299987793, },
				[10] = { 4, 22, statInterpolation = { 1, 1, }, actorLevel = 36.299999237061, },
				[11] = { 5, 26, statInterpolation = { 1, 1, }, actorLevel = 41.423000335693, },
				[12] = { 5, 30, statInterpolation = { 1, 1, }, actorLevel = 46.771999359131, },
				[13] = { 6, 35, statInterpolation = { 1, 1, }, actorLevel = 52.34700012207, },
				[14] = { 7, 40, statInterpolation = { 1, 1, }, actorLevel = 58.147998809814, },
				[15] = { 8, 46, statInterpolation = { 1, 1, }, actorLevel = 64.175003051758, },
				[16] = { 9, 52, statInterpolation = { 1, 1, }, actorLevel = 70.428001403809, },
				[17] = { 11, 60, statInterpolation = { 1, 1, }, actorLevel = 76.906997680664, },
				[18] = { 12, 68, statInterpolation = { 1, 1, }, actorLevel = 83.611999511719, },
				[19] = { 14, 78, statInterpolation = { 1, 1, }, actorLevel = 90.542999267578, },
				[20] = { 16, 89, statInterpolation = { 1, 1, }, actorLevel = 97.699996948242, },
				[21] = { 18, 101, statInterpolation = { 1, 1, }, actorLevel = 105.08300018311, },
				[22] = { 20, 115, statInterpolation = { 1, 1, }, actorLevel = 112.69200134277, },
				[23] = { 23, 131, statInterpolation = { 1, 1, }, actorLevel = 120.52700042725, },
				[24] = { 26, 148, statInterpolation = { 1, 1, }, actorLevel = 128.58799743652, },
				[25] = { 30, 168, statInterpolation = { 1, 1, }, actorLevel = 136.875, },
				[26] = { 34, 191, statInterpolation = { 1, 1, }, actorLevel = 145.38800048828, },
				[27] = { 38, 217, statInterpolation = { 1, 1, }, actorLevel = 154.12699890137, },
				[28] = { 44, 247, statInterpolation = { 1, 1, }, actorLevel = 163.09199523926, },
				[29] = { 50, 281, statInterpolation = { 1, 1, }, actorLevel = 172.28300476074, },
				[30] = { 56, 319, statInterpolation = { 1, 1, }, actorLevel = 181.69999694824, },
				[31] = { 64, 363, statInterpolation = { 1, 1, }, actorLevel = 191.34300231934, },
				[32] = { 73, 413, statInterpolation = { 1, 1, }, actorLevel = 201.21200561523, },
				[33] = { 83, 470, statInterpolation = { 1, 1, }, actorLevel = 211.30700683594, },
				[34] = { 95, 536, statInterpolation = { 1, 1, }, actorLevel = 221.62800598145, },
				[35] = { 108, 611, statInterpolation = { 1, 1, }, actorLevel = 232.17500305176, },
				[36] = { 123, 698, statInterpolation = { 1, 1, }, actorLevel = 242.94799804688, },
				[37] = { 141, 797, statInterpolation = { 1, 1, }, actorLevel = 253.94700622559, },
				[38] = { 161, 911, statInterpolation = { 1, 1, }, actorLevel = 265.17199707031, },
				[39] = { 184, 1043, statInterpolation = { 1, 1, }, actorLevel = 276.62298583984, },
				[40] = { 211, 1195, statInterpolation = { 1, 1, }, actorLevel = 288.29998779297, },
			},
		},
		[4] = {
			label = "Cold",
			baseEffectiveness = 0.41999998688698,
			incrementalEffectiveness = 0.14000000059605,
			damageIncrementalEffectiveness = 0.0082000000402331,
			statDescriptionScope = "tornado_triggered",
			statMap = {
				["tornado_base_damage_interval_ms"] = {
					skill("hitTimeOverride", nil),
					div = 1000,
				},
			},
			baseFlags = {
				spell = true,
				area = true,
				duration = true,
			},
			constantStats = {
				{ "tornado_base_damage_interval_ms", 250 },
				{ "number_of_tornados_allowed", 50 },
				{ "skill_override_pvp_scaling_time_ms", 1000 },
				{ "base_skill_effect_duration", 5000 },
				{ "active_skill_base_area_of_effect_radius", 18 },
			},
			stats = {
				"spell_minimum_base_cold_damage",
				"spell_maximum_base_cold_damage",
				"skill_can_add_multiple_charges_per_action",
				"damage_cannot_be_reflected_or_leech_if_used_by_other_object",
				"is_area_damage",
				"tornado_hinder",
				"is_triggerable_elemental_storm",
				"global_cannot_crit",
			},
			levels = {
				[1] = { 1, 2, statInterpolation = { 1, 1, }, actorLevel = 1, },
				[2] = { 2, 3, statInterpolation = { 1, 1, }, actorLevel = 3.4519999027252, },
				[3] = { 2, 4, statInterpolation = { 1, 1, }, actorLevel = 6.7670001983643, },
				[4] = { 3, 5, statInterpolation = { 1, 1, }, actorLevel = 10.307999610901, },
				[5] = { 4, 6, statInterpolation = { 1, 1, }, actorLevel = 14.074999809265, },
				[6] = { 5, 8, statInterpolation = { 1, 1, }, actorLevel = 18.068000793457, },
				[7] = { 6, 9, statInterpolation = { 1, 1, }, actorLevel = 22.287000656128, },
				[8] = { 7, 11, statInterpolation = { 1, 1, }, actorLevel = 26.732000350952, },
				[9] = { 9, 13, statInterpolation = { 1, 1, }, actorLevel = 31.40299987793, },
				[10] = { 10, 16, statInterpolation = { 1, 1, }, actorLevel = 36.299999237061, },
				[11] = { 12, 18, statInterpolation = { 1, 1, }, actorLevel = 41.423000335693, },
				[12] = { 14, 21, statInterpolation = { 1, 1, }, actorLevel = 46.771999359131, },
				[13] = { 16, 24, statInterpolation = { 1, 1, }, actorLevel = 52.34700012207, },
				[14] = { 19, 28, statInterpolation = { 1, 1, }, actorLevel = 58.147998809814, },
				[15] = { 22, 32, statInterpolation = { 1, 1, }, actorLevel = 64.175003051758, },
				[16] = { 25, 37, statInterpolation = { 1, 1, }, actorLevel = 70.428001403809, },
				[17] = { 28, 42, statInterpolation = { 1, 1, }, actorLevel = 76.906997680664, },
				[18] = { 32, 48, statInterpolation = { 1, 1, }, actorLevel = 83.611999511719, },
				[19] = { 37, 55, statInterpolation = { 1, 1, }, actorLevel = 90.542999267578, },
				[20] = { 42, 63, statInterpolation = { 1, 1, }, actorLevel = 97.699996948242, },
				[21] = { 48, 71, statInterpolation = { 1, 1, }, actorLevel = 105.08300018311, },
				[22] = { 54, 81, statInterpolation = { 1, 1, }, actorLevel = 112.69200134277, },
				[23] = { 61, 92, statInterpolation = { 1, 1, }, actorLevel = 120.52700042725, },
				[24] = { 70, 105, statInterpolation = { 1, 1, }, actorLevel = 128.58799743652, },
				[25] = { 79, 119, statInterpolation = { 1, 1, }, actorLevel = 136.875, },
				[26] = { 90, 135, statInterpolation = { 1, 1, }, actorLevel = 145.38800048828, },
				[27] = { 102, 153, statInterpolation = { 1, 1, }, actorLevel = 154.12699890137, },
				[28] = { 116, 174, statInterpolation = { 1, 1, }, actorLevel = 163.09199523926, },
				[29] = { 132, 198, statInterpolation = { 1, 1, }, actorLevel = 172.28300476074, },
				[30] = { 150, 225, statInterpolation = { 1, 1, }, actorLevel = 181.69999694824, },
				[31] = { 171, 256, statInterpolation = { 1, 1, }, actorLevel = 191.34300231934, },
				[32] = { 194, 292, statInterpolation = { 1, 1, }, actorLevel = 201.21200561523, },
				[33] = { 221, 332, statInterpolation = { 1, 1, }, actorLevel = 211.30700683594, },
				[34] = { 252, 378, statInterpolation = { 1, 1, }, actorLevel = 221.62800598145, },
				[35] = { 288, 432, statInterpolation = { 1, 1, }, actorLevel = 232.17500305176, },
				[36] = { 328, 493, statInterpolation = { 1, 1, }, actorLevel = 242.94799804688, },
				[37] = { 375, 563, statInterpolation = { 1, 1, }, actorLevel = 253.94700622559, },
				[38] = { 429, 643, statInterpolation = { 1, 1, }, actorLevel = 265.17199707031, },
				[39] = { 491, 736, statInterpolation = { 1, 1, }, actorLevel = 276.62298583984, },
				[40] = { 562, 843, statInterpolation = { 1, 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["EncaseInJadePlayer"] = {
	name = "Encase in Jade",
	baseTypeName = "Encase in Jade",
	fromTree = true,
	color = 4,
	description = "Consume all stacks of Jade to grant Guard based off your maximum Life for each Jade consumed. You cannot gain Jade stacks while you have Guard.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Buff] = true, [SkillType.Guard] = true, [SkillType.Duration] = true, [SkillType.Physical] = true, },
	castTime = 0.3,
	qualityStats = {
		{ "maximum_life_%_damage_absorbed_per_jade_consumed", 0.05 },
	},
	levels = {
		[1] = { levelRequirement = 0, cost = { Mana = 15, }, },
		[2] = { levelRequirement = 3, cost = { Mana = 15, }, },
		[3] = { levelRequirement = 6, cost = { Mana = 15, }, },
		[4] = { levelRequirement = 10, cost = { Mana = 15, }, },
		[5] = { levelRequirement = 14, cost = { Mana = 15, }, },
		[6] = { levelRequirement = 18, cost = { Mana = 15, }, },
		[7] = { levelRequirement = 22, cost = { Mana = 15, }, },
		[8] = { levelRequirement = 26, cost = { Mana = 15, }, },
		[9] = { levelRequirement = 31, cost = { Mana = 15, }, },
		[10] = { levelRequirement = 36, cost = { Mana = 15, }, },
		[11] = { levelRequirement = 41, cost = { Mana = 15, }, },
		[12] = { levelRequirement = 46, cost = { Mana = 15, }, },
		[13] = { levelRequirement = 52, cost = { Mana = 15, }, },
		[14] = { levelRequirement = 58, cost = { Mana = 15, }, },
		[15] = { levelRequirement = 64, cost = { Mana = 15, }, },
		[16] = { levelRequirement = 66, cost = { Mana = 15, }, },
		[17] = { levelRequirement = 72, cost = { Mana = 15, }, },
		[18] = { levelRequirement = 78, cost = { Mana = 15, }, },
		[19] = { levelRequirement = 84, cost = { Mana = 15, }, },
		[20] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[21] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[22] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[23] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[24] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[25] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[26] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[27] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[28] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[29] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[30] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[31] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[32] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[33] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[34] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[35] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[36] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[37] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[38] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[39] = { levelRequirement = 90, cost = { Mana = 15, }, },
		[40] = { levelRequirement = 90, cost = { Mana = 15, }, },
	},
	statSets = {
		[1] = {
			label = "Encase in Jade",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "encase_in_jade",
			baseFlags = {
				duration = true,
			},
			constantStats = {
				{ "maximum_life_%_damage_absorbed_per_jade_consumed", 6 },
			},
			stats = {
				"base_skill_effect_duration",
				"base_deal_no_damage",
			},
			levels = {
				[1] = { 4000, statInterpolation = { 1, }, actorLevel = 1, },
				[2] = { 4050, statInterpolation = { 1, }, actorLevel = 3.4519999027252, },
				[3] = { 4100, statInterpolation = { 1, }, actorLevel = 6.7670001983643, },
				[4] = { 4150, statInterpolation = { 1, }, actorLevel = 10.307999610901, },
				[5] = { 4200, statInterpolation = { 1, }, actorLevel = 14.074999809265, },
				[6] = { 4250, statInterpolation = { 1, }, actorLevel = 18.068000793457, },
				[7] = { 4300, statInterpolation = { 1, }, actorLevel = 22.287000656128, },
				[8] = { 4350, statInterpolation = { 1, }, actorLevel = 26.732000350952, },
				[9] = { 4400, statInterpolation = { 1, }, actorLevel = 31.40299987793, },
				[10] = { 4450, statInterpolation = { 1, }, actorLevel = 36.299999237061, },
				[11] = { 4500, statInterpolation = { 1, }, actorLevel = 41.423000335693, },
				[12] = { 4550, statInterpolation = { 1, }, actorLevel = 46.771999359131, },
				[13] = { 4600, statInterpolation = { 1, }, actorLevel = 52.34700012207, },
				[14] = { 4650, statInterpolation = { 1, }, actorLevel = 58.147998809814, },
				[15] = { 4700, statInterpolation = { 1, }, actorLevel = 64.175003051758, },
				[16] = { 4750, statInterpolation = { 1, }, actorLevel = 70.428001403809, },
				[17] = { 4800, statInterpolation = { 1, }, actorLevel = 76.906997680664, },
				[18] = { 4850, statInterpolation = { 1, }, actorLevel = 83.611999511719, },
				[19] = { 4900, statInterpolation = { 1, }, actorLevel = 90.542999267578, },
				[20] = { 4950, statInterpolation = { 1, }, actorLevel = 97.699996948242, },
				[21] = { 5000, statInterpolation = { 1, }, actorLevel = 105.08300018311, },
				[22] = { 5050, statInterpolation = { 1, }, actorLevel = 112.69200134277, },
				[23] = { 5100, statInterpolation = { 1, }, actorLevel = 120.52700042725, },
				[24] = { 5150, statInterpolation = { 1, }, actorLevel = 128.58799743652, },
				[25] = { 5200, statInterpolation = { 1, }, actorLevel = 136.875, },
				[26] = { 5250, statInterpolation = { 1, }, actorLevel = 145.38800048828, },
				[27] = { 5300, statInterpolation = { 1, }, actorLevel = 154.12699890137, },
				[28] = { 5350, statInterpolation = { 1, }, actorLevel = 163.09199523926, },
				[29] = { 5400, statInterpolation = { 1, }, actorLevel = 172.28300476074, },
				[30] = { 5450, statInterpolation = { 1, }, actorLevel = 181.69999694824, },
				[31] = { 5475, statInterpolation = { 1, }, actorLevel = 191.34300231934, },
				[32] = { 5500, statInterpolation = { 1, }, actorLevel = 201.21200561523, },
				[33] = { 5525, statInterpolation = { 1, }, actorLevel = 211.30700683594, },
				[34] = { 5550, statInterpolation = { 1, }, actorLevel = 221.62800598145, },
				[35] = { 5575, statInterpolation = { 1, }, actorLevel = 232.17500305176, },
				[36] = { 5600, statInterpolation = { 1, }, actorLevel = 242.94799804688, },
				[37] = { 5625, statInterpolation = { 1, }, actorLevel = 253.94700622559, },
				[38] = { 5650, statInterpolation = { 1, }, actorLevel = 265.17199707031, },
				[39] = { 5675, statInterpolation = { 1, }, actorLevel = 276.62298583984, },
				[40] = { 5700, statInterpolation = { 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["MetaCastFireSpellOnHitPlayer"] = {
	name = "Fire Spell on Melee Hit",
	baseTypeName = "Fire Spell on Melee Hit",
	fromTree = true,
	color = 4,
	description = "While active, gains Energy when you Hit enemies with Melee Attacks and triggers socketed Fire spells on reaching maximum Energy.",
	skillTypes = { [SkillType.HasReservation] = true, [SkillType.OngoingSkill] = true, [SkillType.Meta] = true, [SkillType.Persistent] = true, [SkillType.Buff] = true, [SkillType.Fire] = true, [SkillType.CanHaveMultipleOngoingSkillInstances] = true, [SkillType.GeneratesEnergy] = true, [SkillType.Triggers] = true, },
	castTime = 0,
	qualityStats = {
		{ "energy_generated_+%", 0.75 },
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { levelRequirement = 3, },
		[3] = { levelRequirement = 6, },
		[4] = { levelRequirement = 10, },
		[5] = { levelRequirement = 14, },
		[6] = { levelRequirement = 18, },
		[7] = { levelRequirement = 22, },
		[8] = { levelRequirement = 26, },
		[9] = { levelRequirement = 31, },
		[10] = { levelRequirement = 36, },
		[11] = { levelRequirement = 41, },
		[12] = { levelRequirement = 46, },
		[13] = { levelRequirement = 52, },
		[14] = { levelRequirement = 58, },
		[15] = { levelRequirement = 64, },
		[16] = { levelRequirement = 66, },
		[17] = { levelRequirement = 72, },
		[18] = { levelRequirement = 78, },
		[19] = { levelRequirement = 84, },
		[20] = { levelRequirement = 90, },
		[21] = { levelRequirement = 90, },
		[22] = { levelRequirement = 90, },
		[23] = { levelRequirement = 90, },
		[24] = { levelRequirement = 90, },
		[25] = { levelRequirement = 90, },
		[26] = { levelRequirement = 90, },
		[27] = { levelRequirement = 90, },
		[28] = { levelRequirement = 90, },
		[29] = { levelRequirement = 90, },
		[30] = { levelRequirement = 90, },
		[31] = { levelRequirement = 90, },
		[32] = { levelRequirement = 90, },
		[33] = { levelRequirement = 90, },
		[34] = { levelRequirement = 90, },
		[35] = { levelRequirement = 90, },
		[36] = { levelRequirement = 90, },
		[37] = { levelRequirement = 90, },
		[38] = { levelRequirement = 90, },
		[39] = { levelRequirement = 90, },
		[40] = { levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Fire Spell on Melee Hit",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "cast_fire_spell_on_hit",
			baseFlags = {
			},
			constantStats = {
				{ "cast_fire_spell_on_hit_gain_X_centienergy_per_monster_power_on_hit", 300 },
				{ "skill_desired_amount_override", 1 },
			},
			stats = {
				"energy_generated_+%",
				"generic_ongoing_trigger_triggers_at_maximum_energy",
				"generic_ongoing_trigger_maximum_energy_is_total_of_socketed_skills",
				"base_deal_no_damage",
			},
			levels = {
				[1] = { 0, statInterpolation = { 1, }, actorLevel = 1, },
				[2] = { 3, statInterpolation = { 1, }, actorLevel = 3.4519999027252, },
				[3] = { 6, statInterpolation = { 1, }, actorLevel = 6.7670001983643, },
				[4] = { 9, statInterpolation = { 1, }, actorLevel = 10.307999610901, },
				[5] = { 12, statInterpolation = { 1, }, actorLevel = 14.074999809265, },
				[6] = { 15, statInterpolation = { 1, }, actorLevel = 18.068000793457, },
				[7] = { 18, statInterpolation = { 1, }, actorLevel = 22.287000656128, },
				[8] = { 21, statInterpolation = { 1, }, actorLevel = 26.732000350952, },
				[9] = { 24, statInterpolation = { 1, }, actorLevel = 31.40299987793, },
				[10] = { 27, statInterpolation = { 1, }, actorLevel = 36.299999237061, },
				[11] = { 30, statInterpolation = { 1, }, actorLevel = 41.423000335693, },
				[12] = { 33, statInterpolation = { 1, }, actorLevel = 46.771999359131, },
				[13] = { 36, statInterpolation = { 1, }, actorLevel = 52.34700012207, },
				[14] = { 39, statInterpolation = { 1, }, actorLevel = 58.147998809814, },
				[15] = { 42, statInterpolation = { 1, }, actorLevel = 64.175003051758, },
				[16] = { 45, statInterpolation = { 1, }, actorLevel = 70.428001403809, },
				[17] = { 48, statInterpolation = { 1, }, actorLevel = 76.906997680664, },
				[18] = { 51, statInterpolation = { 1, }, actorLevel = 83.611999511719, },
				[19] = { 54, statInterpolation = { 1, }, actorLevel = 90.542999267578, },
				[20] = { 57, statInterpolation = { 1, }, actorLevel = 97.699996948242, },
				[21] = { 60, statInterpolation = { 1, }, actorLevel = 105.08300018311, },
				[22] = { 63, statInterpolation = { 1, }, actorLevel = 112.69200134277, },
				[23] = { 66, statInterpolation = { 1, }, actorLevel = 120.52700042725, },
				[24] = { 69, statInterpolation = { 1, }, actorLevel = 128.58799743652, },
				[25] = { 72, statInterpolation = { 1, }, actorLevel = 136.875, },
				[26] = { 75, statInterpolation = { 1, }, actorLevel = 145.38800048828, },
				[27] = { 78, statInterpolation = { 1, }, actorLevel = 154.12699890137, },
				[28] = { 81, statInterpolation = { 1, }, actorLevel = 163.09199523926, },
				[29] = { 84, statInterpolation = { 1, }, actorLevel = 172.28300476074, },
				[30] = { 87, statInterpolation = { 1, }, actorLevel = 181.69999694824, },
				[31] = { 90, statInterpolation = { 1, }, actorLevel = 191.34300231934, },
				[32] = { 93, statInterpolation = { 1, }, actorLevel = 201.21200561523, },
				[33] = { 96, statInterpolation = { 1, }, actorLevel = 211.30700683594, },
				[34] = { 99, statInterpolation = { 1, }, actorLevel = 221.62800598145, },
				[35] = { 102, statInterpolation = { 1, }, actorLevel = 232.17500305176, },
				[36] = { 105, statInterpolation = { 1, }, actorLevel = 242.94799804688, },
				[37] = { 108, statInterpolation = { 1, }, actorLevel = 253.94700622559, },
				[38] = { 111, statInterpolation = { 1, }, actorLevel = 265.17199707031, },
				[39] = { 114, statInterpolation = { 1, }, actorLevel = 276.62298583984, },
				[40] = { 117, statInterpolation = { 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["SupportMetaCastFireSpellOnHitPlayer"] = {
	name = "SupportMetaCastFireSpellOnHitPlayer",
	hidden = true,
	fromTree = true,
	support = true,
	requireSkillTypes = { SkillType.Spell, SkillType.Triggerable, SkillType.Fire, SkillType.AND, SkillType.AND, },
	addSkillTypes = { SkillType.Triggered, SkillType.Cooldown, },
	excludeSkillTypes = { SkillType.SupportedByHourglass, },
	isTrigger = true,
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { levelRequirement = 0, },
		[3] = { levelRequirement = 0, },
		[4] = { levelRequirement = 0, },
		[5] = { levelRequirement = 0, },
		[6] = { levelRequirement = 0, },
		[7] = { levelRequirement = 0, },
		[8] = { levelRequirement = 0, },
		[9] = { levelRequirement = 0, },
		[10] = { levelRequirement = 0, },
		[11] = { levelRequirement = 0, },
		[12] = { levelRequirement = 0, },
		[13] = { levelRequirement = 0, },
		[14] = { levelRequirement = 0, },
		[15] = { levelRequirement = 0, },
		[16] = { levelRequirement = 0, },
		[17] = { levelRequirement = 0, },
		[18] = { levelRequirement = 0, },
		[19] = { levelRequirement = 0, },
		[20] = { levelRequirement = 0, },
		[21] = { levelRequirement = 0, },
		[22] = { levelRequirement = 0, },
		[23] = { levelRequirement = 0, },
		[24] = { levelRequirement = 0, },
		[25] = { levelRequirement = 0, },
		[26] = { levelRequirement = 0, },
		[27] = { levelRequirement = 0, },
		[28] = { levelRequirement = 0, },
		[29] = { levelRequirement = 0, },
		[30] = { levelRequirement = 0, },
		[31] = { levelRequirement = 0, },
		[32] = { levelRequirement = 0, },
		[33] = { levelRequirement = 0, },
		[34] = { levelRequirement = 0, },
		[35] = { levelRequirement = 0, },
		[36] = { levelRequirement = 0, },
		[37] = { levelRequirement = 0, },
		[38] = { levelRequirement = 0, },
		[39] = { levelRequirement = 0, },
		[40] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "SupportMetaCastFireSpellOnHitPlayer",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "generic_ongoing_trigger_1_maximum_energy_per_Xms_total_cast_time", 10 },
			},
			stats = {
				"triggered_by_generic_ongoing_trigger",
				"generic_ongoing_trigger_triggers_at_maximum_energy",
				"skill_cannot_generate_energy",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["ExplosiveConcoctionPlayer"] = {
	name = "Explosive Concoction",
	baseTypeName = "Explosive Concoction",
	fromTree = true,
	color = 4,
	description = "Consume charges from your Mana Flask to throw a bottle that explodes, dealing Fire attack damage in an area and inflicting Fire Exposure.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Area] = true, [SkillType.Projectile] = true, [SkillType.Duration] = true, [SkillType.Fire] = true, [SkillType.UsableWhileMoving] = true, [SkillType.ProjectileNoCollision] = true, },
	weaponTypes = {
		["None"] = true,
	},
	castTime = 1,
	qualityStats = {
		{ "fire_exposure_effect_+%", 1 },
	},
	levels = {
		[1] = { critChance = 5, baseMultiplier = 0.7, levelRequirement = 0, cost = { Mana = 0, }, },
		[2] = { critChance = 5, baseMultiplier = 0.77, levelRequirement = 3, cost = { Mana = 0, }, },
		[3] = { critChance = 5, baseMultiplier = 0.84, levelRequirement = 6, cost = { Mana = 0, }, },
		[4] = { critChance = 5, baseMultiplier = 0.92, levelRequirement = 10, cost = { Mana = 0, }, },
		[5] = { critChance = 5, baseMultiplier = 0.99, levelRequirement = 14, cost = { Mana = 0, }, },
		[6] = { critChance = 5, baseMultiplier = 1.05, levelRequirement = 18, cost = { Mana = 0, }, },
		[7] = { critChance = 5, baseMultiplier = 1.12, levelRequirement = 22, cost = { Mana = 0, }, },
		[8] = { critChance = 5, baseMultiplier = 1.18, levelRequirement = 26, cost = { Mana = 0, }, },
		[9] = { critChance = 5, baseMultiplier = 1.23, levelRequirement = 31, cost = { Mana = 0, }, },
		[10] = { critChance = 5, baseMultiplier = 1.28, levelRequirement = 36, cost = { Mana = 0, }, },
		[11] = { critChance = 5, baseMultiplier = 1.34, levelRequirement = 41, cost = { Mana = 0, }, },
		[12] = { critChance = 5, baseMultiplier = 1.39, levelRequirement = 46, cost = { Mana = 0, }, },
		[13] = { critChance = 5, baseMultiplier = 1.44, levelRequirement = 52, cost = { Mana = 0, }, },
		[14] = { critChance = 5, baseMultiplier = 1.5, levelRequirement = 58, cost = { Mana = 0, }, },
		[15] = { critChance = 5, baseMultiplier = 1.55, levelRequirement = 64, cost = { Mana = 0, }, },
		[16] = { critChance = 5, baseMultiplier = 1.61, levelRequirement = 66, cost = { Mana = 0, }, },
		[17] = { critChance = 5, baseMultiplier = 1.67, levelRequirement = 72, cost = { Mana = 0, }, },
		[18] = { critChance = 5, baseMultiplier = 1.73, levelRequirement = 78, cost = { Mana = 0, }, },
		[19] = { critChance = 5, baseMultiplier = 1.79, levelRequirement = 84, cost = { Mana = 0, }, },
		[20] = { critChance = 5, baseMultiplier = 1.85, levelRequirement = 90, cost = { Mana = 0, }, },
		[21] = { critChance = 5, baseMultiplier = 1.91, levelRequirement = 90, cost = { Mana = 0, }, },
		[22] = { critChance = 5, baseMultiplier = 1.97, levelRequirement = 90, cost = { Mana = 0, }, },
		[23] = { critChance = 5, baseMultiplier = 2.04, levelRequirement = 90, cost = { Mana = 0, }, },
		[24] = { critChance = 5, baseMultiplier = 2.1, levelRequirement = 90, cost = { Mana = 0, }, },
		[25] = { critChance = 5, baseMultiplier = 2.17, levelRequirement = 90, cost = { Mana = 0, }, },
		[26] = { critChance = 5, baseMultiplier = 2.24, levelRequirement = 90, cost = { Mana = 0, }, },
		[27] = { critChance = 5, baseMultiplier = 2.32, levelRequirement = 90, cost = { Mana = 0, }, },
		[28] = { critChance = 5, baseMultiplier = 2.4, levelRequirement = 90, cost = { Mana = 0, }, },
		[29] = { critChance = 5, baseMultiplier = 2.48, levelRequirement = 90, cost = { Mana = 0, }, },
		[30] = { critChance = 5, baseMultiplier = 2.56, levelRequirement = 90, cost = { Mana = 0, }, },
		[31] = { critChance = 5, baseMultiplier = 2.64, levelRequirement = 90, cost = { Mana = 0, }, },
		[32] = { critChance = 5, baseMultiplier = 2.73, levelRequirement = 90, cost = { Mana = 0, }, },
		[33] = { critChance = 5, baseMultiplier = 2.82, levelRequirement = 90, cost = { Mana = 0, }, },
		[34] = { critChance = 5, baseMultiplier = 2.91, levelRequirement = 90, cost = { Mana = 0, }, },
		[35] = { critChance = 5, baseMultiplier = 3.01, levelRequirement = 90, cost = { Mana = 0, }, },
		[36] = { critChance = 5, baseMultiplier = 3.11, levelRequirement = 90, cost = { Mana = 0, }, },
		[37] = { critChance = 5, baseMultiplier = 3.21, levelRequirement = 90, cost = { Mana = 0, }, },
		[38] = { critChance = 5, baseMultiplier = 3.32, levelRequirement = 90, cost = { Mana = 0, }, },
		[39] = { critChance = 5, baseMultiplier = 3.43, levelRequirement = 90, cost = { Mana = 0, }, },
		[40] = { critChance = 5, baseMultiplier = 3.55, levelRequirement = 90, cost = { Mana = 0, }, },
	},
	statSets = {
		[1] = {
			label = "Explosive Concoction",
			baseEffectiveness = 4.5,
			incrementalEffectiveness = 0.27349999547005,
			statDescriptionScope = "throw_flask_fire",
			statMap = {
				["flask_throw_fire_exposure_ms"] = {
					mod("FireExposureChance", "BASE", nil),
					value = 100,
				},
			},
			baseFlags = {
				attack = true,
				projectile = true,
				duration = true,
				unarmed = true,
				area = true,
			},
			constantStats = {
				{ "flask_throw_base_charges_used", 5 },
				{ "base_number_of_projectiles", 1 },
				{ "active_skill_base_area_of_effect_radius", 15 },
				{ "flask_throw_fire_exposure_ms", 4000 },
				{ "movement_speed_+%_final_while_performing_action", -70 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 60 },
				{ "main_hand_base_weapon_attack_duration_ms", 714 },
				{ "active_skill_ignite_chance_+%_final", 100 },
				{ "active_skill_ignite_effect_+%_final", 200 },
			},
			stats = {
				"main_hand_weapon_minimum_fire_damage",
				"main_hand_weapon_maximum_fire_damage",
				"base_is_projectile",
				"projectile_behaviour_only_explode",
				"can_perform_skill_while_moving",
				"replace_main_hand_unarmed_attack_stats_with_nothing_type",
				"is_area_damage",
			},
			levels = {
				[1] = { 14, 21, statInterpolation = { 1, 1, }, actorLevel = 1, },
				[2] = { 23, 35, statInterpolation = { 1, 1, }, actorLevel = 3.4519999027252, },
				[3] = { 36, 54, statInterpolation = { 1, 1, }, actorLevel = 6.7670001983643, },
				[4] = { 50, 74, statInterpolation = { 1, 1, }, actorLevel = 10.307999610901, },
				[5] = { 64, 96, statInterpolation = { 1, 1, }, actorLevel = 14.074999809265, },
				[6] = { 79, 119, statInterpolation = { 1, 1, }, actorLevel = 18.068000793457, },
				[7] = { 95, 143, statInterpolation = { 1, 1, }, actorLevel = 22.287000656128, },
				[8] = { 112, 169, statInterpolation = { 1, 1, }, actorLevel = 26.732000350952, },
				[9] = { 130, 195, statInterpolation = { 1, 1, }, actorLevel = 31.40299987793, },
				[10] = { 149, 224, statInterpolation = { 1, 1, }, actorLevel = 36.299999237061, },
				[11] = { 169, 253, statInterpolation = { 1, 1, }, actorLevel = 41.423000335693, },
				[12] = { 189, 284, statInterpolation = { 1, 1, }, actorLevel = 46.771999359131, },
				[13] = { 210, 316, statInterpolation = { 1, 1, }, actorLevel = 52.34700012207, },
				[14] = { 233, 349, statInterpolation = { 1, 1, }, actorLevel = 58.147998809814, },
				[15] = { 256, 383, statInterpolation = { 1, 1, }, actorLevel = 64.175003051758, },
				[16] = { 280, 419, statInterpolation = { 1, 1, }, actorLevel = 70.428001403809, },
				[17] = { 304, 457, statInterpolation = { 1, 1, }, actorLevel = 76.906997680664, },
				[18] = { 330, 495, statInterpolation = { 1, 1, }, actorLevel = 83.611999511719, },
				[19] = { 357, 535, statInterpolation = { 1, 1, }, actorLevel = 90.542999267578, },
				[20] = { 384, 576, statInterpolation = { 1, 1, }, actorLevel = 97.699996948242, },
				[21] = { 412, 618, statInterpolation = { 1, 1, }, actorLevel = 105.08300018311, },
				[22] = { 441, 662, statInterpolation = { 1, 1, }, actorLevel = 112.69200134277, },
				[23] = { 471, 707, statInterpolation = { 1, 1, }, actorLevel = 120.52700042725, },
				[24] = { 502, 753, statInterpolation = { 1, 1, }, actorLevel = 128.58799743652, },
				[25] = { 534, 801, statInterpolation = { 1, 1, }, actorLevel = 136.875, },
				[26] = { 566, 849, statInterpolation = { 1, 1, }, actorLevel = 145.38800048828, },
				[27] = { 600, 900, statInterpolation = { 1, 1, }, actorLevel = 154.12699890137, },
				[28] = { 634, 951, statInterpolation = { 1, 1, }, actorLevel = 163.09199523926, },
				[29] = { 669, 1004, statInterpolation = { 1, 1, }, actorLevel = 172.28300476074, },
				[30] = { 705, 1058, statInterpolation = { 1, 1, }, actorLevel = 181.69999694824, },
				[31] = { 742, 1113, statInterpolation = { 1, 1, }, actorLevel = 191.34300231934, },
				[32] = { 780, 1170, statInterpolation = { 1, 1, }, actorLevel = 201.21200561523, },
				[33] = { 818, 1228, statInterpolation = { 1, 1, }, actorLevel = 211.30700683594, },
				[34] = { 858, 1287, statInterpolation = { 1, 1, }, actorLevel = 221.62800598145, },
				[35] = { 898, 1347, statInterpolation = { 1, 1, }, actorLevel = 232.17500305176, },
				[36] = { 940, 1409, statInterpolation = { 1, 1, }, actorLevel = 242.94799804688, },
				[37] = { 982, 1472, statInterpolation = { 1, 1, }, actorLevel = 253.94700622559, },
				[38] = { 1025, 1537, statInterpolation = { 1, 1, }, actorLevel = 265.17199707031, },
				[39] = { 1068, 1603, statInterpolation = { 1, 1, }, actorLevel = 276.62298583984, },
				[40] = { 1113, 1670, statInterpolation = { 1, 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["FulminatingConcoctionPlayer"] = {
	name = "Fulminating Concoction",
	baseTypeName = "Fulminating Concoction",
	fromTree = true,
	color = 4,
	description = "Consume charges from your Mana Flask to throw a bottle that explodes, dealing Lightning attack damage in an area and inflicting Lightning Exposure.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Area] = true, [SkillType.Projectile] = true, [SkillType.Duration] = true, [SkillType.Lightning] = true, [SkillType.UsableWhileMoving] = true, [SkillType.ProjectileNoCollision] = true, },
	weaponTypes = {
		["None"] = true,
	},
	castTime = 1,
	qualityStats = {
		{ "lightning_exposure_effect_+%", 1 },
	},
	levels = {
		[1] = { critChance = 8, baseMultiplier = 0.7, levelRequirement = 0, cost = { Mana = 0, }, },
		[2] = { critChance = 8, baseMultiplier = 0.77, levelRequirement = 3, cost = { Mana = 0, }, },
		[3] = { critChance = 8, baseMultiplier = 0.84, levelRequirement = 6, cost = { Mana = 0, }, },
		[4] = { critChance = 8, baseMultiplier = 0.92, levelRequirement = 10, cost = { Mana = 0, }, },
		[5] = { critChance = 8, baseMultiplier = 0.99, levelRequirement = 14, cost = { Mana = 0, }, },
		[6] = { critChance = 8, baseMultiplier = 1.05, levelRequirement = 18, cost = { Mana = 0, }, },
		[7] = { critChance = 8, baseMultiplier = 1.12, levelRequirement = 22, cost = { Mana = 0, }, },
		[8] = { critChance = 8, baseMultiplier = 1.18, levelRequirement = 26, cost = { Mana = 0, }, },
		[9] = { critChance = 8, baseMultiplier = 1.23, levelRequirement = 31, cost = { Mana = 0, }, },
		[10] = { critChance = 8, baseMultiplier = 1.28, levelRequirement = 36, cost = { Mana = 0, }, },
		[11] = { critChance = 8, baseMultiplier = 1.34, levelRequirement = 41, cost = { Mana = 0, }, },
		[12] = { critChance = 8, baseMultiplier = 1.39, levelRequirement = 46, cost = { Mana = 0, }, },
		[13] = { critChance = 8, baseMultiplier = 1.44, levelRequirement = 52, cost = { Mana = 0, }, },
		[14] = { critChance = 8, baseMultiplier = 1.5, levelRequirement = 58, cost = { Mana = 0, }, },
		[15] = { critChance = 8, baseMultiplier = 1.55, levelRequirement = 64, cost = { Mana = 0, }, },
		[16] = { critChance = 8, baseMultiplier = 1.61, levelRequirement = 66, cost = { Mana = 0, }, },
		[17] = { critChance = 8, baseMultiplier = 1.67, levelRequirement = 72, cost = { Mana = 0, }, },
		[18] = { critChance = 8, baseMultiplier = 1.73, levelRequirement = 78, cost = { Mana = 0, }, },
		[19] = { critChance = 8, baseMultiplier = 1.79, levelRequirement = 84, cost = { Mana = 0, }, },
		[20] = { critChance = 8, baseMultiplier = 1.85, levelRequirement = 90, cost = { Mana = 0, }, },
		[21] = { critChance = 8, baseMultiplier = 1.91, levelRequirement = 90, cost = { Mana = 0, }, },
		[22] = { critChance = 8, baseMultiplier = 1.97, levelRequirement = 90, cost = { Mana = 0, }, },
		[23] = { critChance = 8, baseMultiplier = 2.04, levelRequirement = 90, cost = { Mana = 0, }, },
		[24] = { critChance = 8, baseMultiplier = 2.1, levelRequirement = 90, cost = { Mana = 0, }, },
		[25] = { critChance = 8, baseMultiplier = 2.17, levelRequirement = 90, cost = { Mana = 0, }, },
		[26] = { critChance = 8, baseMultiplier = 2.24, levelRequirement = 90, cost = { Mana = 0, }, },
		[27] = { critChance = 8, baseMultiplier = 2.32, levelRequirement = 90, cost = { Mana = 0, }, },
		[28] = { critChance = 8, baseMultiplier = 2.4, levelRequirement = 90, cost = { Mana = 0, }, },
		[29] = { critChance = 8, baseMultiplier = 2.48, levelRequirement = 90, cost = { Mana = 0, }, },
		[30] = { critChance = 8, baseMultiplier = 2.56, levelRequirement = 90, cost = { Mana = 0, }, },
		[31] = { critChance = 8, baseMultiplier = 2.64, levelRequirement = 90, cost = { Mana = 0, }, },
		[32] = { critChance = 8, baseMultiplier = 2.73, levelRequirement = 90, cost = { Mana = 0, }, },
		[33] = { critChance = 8, baseMultiplier = 2.82, levelRequirement = 90, cost = { Mana = 0, }, },
		[34] = { critChance = 8, baseMultiplier = 2.91, levelRequirement = 90, cost = { Mana = 0, }, },
		[35] = { critChance = 8, baseMultiplier = 3.01, levelRequirement = 90, cost = { Mana = 0, }, },
		[36] = { critChance = 8, baseMultiplier = 3.11, levelRequirement = 90, cost = { Mana = 0, }, },
		[37] = { critChance = 8, baseMultiplier = 3.21, levelRequirement = 90, cost = { Mana = 0, }, },
		[38] = { critChance = 8, baseMultiplier = 3.32, levelRequirement = 90, cost = { Mana = 0, }, },
		[39] = { critChance = 8, baseMultiplier = 3.43, levelRequirement = 90, cost = { Mana = 0, }, },
		[40] = { critChance = 8, baseMultiplier = 3.55, levelRequirement = 90, cost = { Mana = 0, }, },
	},
	statSets = {
		[1] = {
			label = "Fulminating Concoction",
			baseEffectiveness = 4.1999998092651,
			incrementalEffectiveness = 0.27349999547005,
			statDescriptionScope = "throw_flask_lightning",
			statMap = {
				["flask_throw_lightning_exposure_ms"] = {
					mod("LightningExposureChance", "BASE", nil),
					value = 100,
				},
			},
			baseFlags = {
				attack = true,
				projectile = true,
				duration = true,
				unarmed = true,
				area = true,
			},
			constantStats = {
				{ "flask_throw_base_charges_used", 5 },
				{ "base_number_of_projectiles", 1 },
				{ "active_skill_base_area_of_effect_radius", 15 },
				{ "flask_throw_lightning_exposure_ms", 4000 },
				{ "throw_flask_effects_type", 2 },
				{ "movement_speed_+%_final_while_performing_action", -70 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 60 },
				{ "main_hand_base_weapon_attack_duration_ms", 714 },
				{ "active_skill_shock_chance_+%_final", 200 },
			},
			stats = {
				"main_hand_weapon_minimum_lightning_damage",
				"main_hand_weapon_maximum_lightning_damage",
				"base_is_projectile",
				"projectile_behaviour_only_explode",
				"can_perform_skill_while_moving",
				"replace_main_hand_unarmed_attack_stats_with_nothing_type",
				"is_area_damage",
			},
			levels = {
				[1] = { 8, 24, statInterpolation = { 1, 1, }, actorLevel = 1, },
				[2] = { 14, 41, statInterpolation = { 1, 1, }, actorLevel = 3.4519999027252, },
				[3] = { 21, 63, statInterpolation = { 1, 1, }, actorLevel = 6.7670001983643, },
				[4] = { 29, 87, statInterpolation = { 1, 1, }, actorLevel = 10.307999610901, },
				[5] = { 37, 112, statInterpolation = { 1, 1, }, actorLevel = 14.074999809265, },
				[6] = { 46, 139, statInterpolation = { 1, 1, }, actorLevel = 18.068000793457, },
				[7] = { 56, 167, statInterpolation = { 1, 1, }, actorLevel = 22.287000656128, },
				[8] = { 66, 197, statInterpolation = { 1, 1, }, actorLevel = 26.732000350952, },
				[9] = { 76, 228, statInterpolation = { 1, 1, }, actorLevel = 31.40299987793, },
				[10] = { 87, 261, statInterpolation = { 1, 1, }, actorLevel = 36.299999237061, },
				[11] = { 98, 295, statInterpolation = { 1, 1, }, actorLevel = 41.423000335693, },
				[12] = { 110, 331, statInterpolation = { 1, 1, }, actorLevel = 46.771999359131, },
				[13] = { 123, 368, statInterpolation = { 1, 1, }, actorLevel = 52.34700012207, },
				[14] = { 136, 407, statInterpolation = { 1, 1, }, actorLevel = 58.147998809814, },
				[15] = { 149, 447, statInterpolation = { 1, 1, }, actorLevel = 64.175003051758, },
				[16] = { 163, 489, statInterpolation = { 1, 1, }, actorLevel = 70.428001403809, },
				[17] = { 178, 533, statInterpolation = { 1, 1, }, actorLevel = 76.906997680664, },
				[18] = { 193, 578, statInterpolation = { 1, 1, }, actorLevel = 83.611999511719, },
				[19] = { 208, 624, statInterpolation = { 1, 1, }, actorLevel = 90.542999267578, },
				[20] = { 224, 672, statInterpolation = { 1, 1, }, actorLevel = 97.699996948242, },
				[21] = { 240, 721, statInterpolation = { 1, 1, }, actorLevel = 105.08300018311, },
				[22] = { 257, 772, statInterpolation = { 1, 1, }, actorLevel = 112.69200134277, },
				[23] = { 275, 825, statInterpolation = { 1, 1, }, actorLevel = 120.52700042725, },
				[24] = { 293, 879, statInterpolation = { 1, 1, }, actorLevel = 128.58799743652, },
				[25] = { 311, 934, statInterpolation = { 1, 1, }, actorLevel = 136.875, },
				[26] = { 330, 991, statInterpolation = { 1, 1, }, actorLevel = 145.38800048828, },
				[27] = { 350, 1050, statInterpolation = { 1, 1, }, actorLevel = 154.12699890137, },
				[28] = { 370, 1110, statInterpolation = { 1, 1, }, actorLevel = 163.09199523926, },
				[29] = { 390, 1171, statInterpolation = { 1, 1, }, actorLevel = 172.28300476074, },
				[30] = { 411, 1234, statInterpolation = { 1, 1, }, actorLevel = 181.69999694824, },
				[31] = { 433, 1299, statInterpolation = { 1, 1, }, actorLevel = 191.34300231934, },
				[32] = { 455, 1365, statInterpolation = { 1, 1, }, actorLevel = 201.21200561523, },
				[33] = { 477, 1432, statInterpolation = { 1, 1, }, actorLevel = 211.30700683594, },
				[34] = { 500, 1501, statInterpolation = { 1, 1, }, actorLevel = 221.62800598145, },
				[35] = { 524, 1572, statInterpolation = { 1, 1, }, actorLevel = 232.17500305176, },
				[36] = { 548, 1644, statInterpolation = { 1, 1, }, actorLevel = 242.94799804688, },
				[37] = { 573, 1718, statInterpolation = { 1, 1, }, actorLevel = 253.94700622559, },
				[38] = { 598, 1793, statInterpolation = { 1, 1, }, actorLevel = 265.17199707031, },
				[39] = { 623, 1870, statInterpolation = { 1, 1, }, actorLevel = 276.62298583984, },
				[40] = { 649, 1948, statInterpolation = { 1, 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["AmazonTriggerElementalInfusionPlayer"] = {
	name = "Infuse Weapon",
	baseTypeName = "Infuse Weapon",
	fromTree = true,
	color = 4,
	description = "Grants your weapon Infusion. Non-Melee Projectile Attacks with that weapon Consume Infusions to Infuse the Projectiles fired, causing them to explode at the end of their flight.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.Area] = true, [SkillType.Fire] = true, [SkillType.Cold] = true, [SkillType.Lightning] = true, [SkillType.Triggered] = true, [SkillType.Triggerable] = true, [SkillType.GeneratesInfusion] = true, [SkillType.NoAttackOrCastTime] = true, [SkillType.CannotConsumeCharges] = true, },
	castTime = 1,
	qualityStats = {
		{ "active_skill_base_area_of_effect_radius", 0.15 },
	},
	levels = {
		[1] = { baseMultiplier = 0.5, levelRequirement = 0, },
		[2] = { baseMultiplier = 0.55, levelRequirement = 3, },
		[3] = { baseMultiplier = 0.61, levelRequirement = 6, },
		[4] = { baseMultiplier = 0.66, levelRequirement = 10, },
		[5] = { baseMultiplier = 0.72, levelRequirement = 14, },
		[6] = { baseMultiplier = 0.77, levelRequirement = 18, },
		[7] = { baseMultiplier = 0.83, levelRequirement = 22, },
		[8] = { baseMultiplier = 0.88, levelRequirement = 26, },
		[9] = { baseMultiplier = 0.93, levelRequirement = 31, },
		[10] = { baseMultiplier = 0.99, levelRequirement = 36, },
		[11] = { baseMultiplier = 1.05, levelRequirement = 41, },
		[12] = { baseMultiplier = 1.11, levelRequirement = 46, },
		[13] = { baseMultiplier = 1.17, levelRequirement = 52, },
		[14] = { baseMultiplier = 1.25, levelRequirement = 58, },
		[15] = { baseMultiplier = 1.32, levelRequirement = 64, },
		[16] = { baseMultiplier = 1.41, levelRequirement = 66, },
		[17] = { baseMultiplier = 1.5, levelRequirement = 72, },
		[18] = { baseMultiplier = 1.59, levelRequirement = 78, },
		[19] = { baseMultiplier = 1.7, levelRequirement = 84, },
		[20] = { baseMultiplier = 1.81, levelRequirement = 90, },
		[21] = { baseMultiplier = 1.93, levelRequirement = 90, },
		[22] = { baseMultiplier = 2.06, levelRequirement = 90, },
		[23] = { baseMultiplier = 2.2, levelRequirement = 90, },
		[24] = { baseMultiplier = 2.34, levelRequirement = 90, },
		[25] = { baseMultiplier = 2.5, levelRequirement = 90, },
		[26] = { baseMultiplier = 2.67, levelRequirement = 90, },
		[27] = { baseMultiplier = 2.84, levelRequirement = 90, },
		[28] = { baseMultiplier = 3.03, levelRequirement = 90, },
		[29] = { baseMultiplier = 3.24, levelRequirement = 90, },
		[30] = { baseMultiplier = 3.45, levelRequirement = 90, },
		[31] = { baseMultiplier = 3.68, levelRequirement = 90, },
		[32] = { baseMultiplier = 3.93, levelRequirement = 90, },
		[33] = { baseMultiplier = 4.19, levelRequirement = 90, },
		[34] = { baseMultiplier = 4.47, levelRequirement = 90, },
		[35] = { baseMultiplier = 4.76, levelRequirement = 90, },
		[36] = { baseMultiplier = 5.08, levelRequirement = 90, },
		[37] = { baseMultiplier = 5.42, levelRequirement = 90, },
		[38] = { baseMultiplier = 5.78, levelRequirement = 90, },
		[39] = { baseMultiplier = 6.17, levelRequirement = 90, },
		[40] = { baseMultiplier = 6.58, levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Cold-Infused",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "trigger_elemental_infusion",
			baseFlags = {
				attack = true,
				area = true,
			},
			constantStats = {
				{ "chance_to_be_triggered_by_amazon_infusion_%", 100 },
				{ "active_skill_base_area_of_effect_radius", 18 },
				{ "active_skill_base_physical_damage_%_to_convert_to_cold", 100 },
			},
			stats = {
				"is_area_damage",
				"skill_is_infusion_skill",
				"cannot_consume_power_frenzy_endurance_charges",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
		[2] = {
			label = "Fire-Infused",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "trigger_elemental_infusion",
			baseFlags = {
				attack = true,
				area = true,
			},
			constantStats = {
				{ "chance_to_be_triggered_by_amazon_infusion_%", 100 },
				{ "active_skill_base_area_of_effect_radius", 18 },
				{ "active_skill_base_physical_damage_%_to_convert_to_fire", 100 },
			},
			stats = {
				"is_area_damage",
				"skill_is_infusion_skill",
				"cannot_consume_power_frenzy_endurance_charges",
			},
			levels = {
				[1] = { baseMultiplier = 0.5, actorLevel = 1, },
				[2] = { baseMultiplier = 0.55, actorLevel = 3.4519999027252, },
				[3] = { baseMultiplier = 0.61, actorLevel = 6.7670001983643, },
				[4] = { baseMultiplier = 0.66, actorLevel = 10.307999610901, },
				[5] = { baseMultiplier = 0.72, actorLevel = 14.074999809265, },
				[6] = { baseMultiplier = 0.77, actorLevel = 18.068000793457, },
				[7] = { baseMultiplier = 0.83, actorLevel = 22.287000656128, },
				[8] = { baseMultiplier = 0.88, actorLevel = 26.732000350952, },
				[9] = { baseMultiplier = 0.93, actorLevel = 31.40299987793, },
				[10] = { baseMultiplier = 0.99, actorLevel = 36.299999237061, },
				[11] = { baseMultiplier = 1.05, actorLevel = 41.423000335693, },
				[12] = { baseMultiplier = 1.11, actorLevel = 46.771999359131, },
				[13] = { baseMultiplier = 1.17, actorLevel = 52.34700012207, },
				[14] = { baseMultiplier = 1.25, actorLevel = 58.147998809814, },
				[15] = { baseMultiplier = 1.32, actorLevel = 64.175003051758, },
				[16] = { baseMultiplier = 1.41, actorLevel = 70.428001403809, },
				[17] = { baseMultiplier = 1.5, actorLevel = 76.906997680664, },
				[18] = { baseMultiplier = 1.59, actorLevel = 83.611999511719, },
				[19] = { baseMultiplier = 1.7, actorLevel = 90.542999267578, },
				[20] = { baseMultiplier = 1.81, actorLevel = 97.699996948242, },
				[21] = { baseMultiplier = 1.93, actorLevel = 105.08300018311, },
				[22] = { baseMultiplier = 2.06, actorLevel = 112.69200134277, },
				[23] = { baseMultiplier = 2.2, actorLevel = 120.52700042725, },
				[24] = { baseMultiplier = 2.34, actorLevel = 128.58799743652, },
				[25] = { baseMultiplier = 2.5, actorLevel = 136.875, },
				[26] = { baseMultiplier = 2.67, actorLevel = 145.38800048828, },
				[27] = { baseMultiplier = 2.84, actorLevel = 154.12699890137, },
				[28] = { baseMultiplier = 3.03, actorLevel = 163.09199523926, },
				[29] = { baseMultiplier = 3.24, actorLevel = 172.28300476074, },
				[30] = { baseMultiplier = 3.45, actorLevel = 181.69999694824, },
				[31] = { baseMultiplier = 3.68, actorLevel = 191.34300231934, },
				[32] = { baseMultiplier = 3.93, actorLevel = 201.21200561523, },
				[33] = { baseMultiplier = 4.19, actorLevel = 211.30700683594, },
				[34] = { baseMultiplier = 4.47, actorLevel = 221.62800598145, },
				[35] = { baseMultiplier = 4.76, actorLevel = 232.17500305176, },
				[36] = { baseMultiplier = 5.08, actorLevel = 242.94799804688, },
				[37] = { baseMultiplier = 5.42, actorLevel = 253.94700622559, },
				[38] = { baseMultiplier = 5.78, actorLevel = 265.17199707031, },
				[39] = { baseMultiplier = 6.17, actorLevel = 276.62298583984, },
				[40] = { baseMultiplier = 6.58, actorLevel = 288.29998779297, },
			},
		},
		[3] = {
			label = "Lightning-Infused",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "trigger_elemental_infusion",
			baseFlags = {
				attack = true,
				area = true,
			},
			constantStats = {
				{ "chance_to_be_triggered_by_amazon_infusion_%", 100 },
				{ "active_skill_base_area_of_effect_radius", 18 },
				{ "active_skill_base_physical_damage_%_to_convert_to_lightning", 100 },
			},
			stats = {
				"is_area_damage",
				"skill_is_infusion_skill",
				"cannot_consume_power_frenzy_endurance_charges",
			},
			levels = {
				[1] = { baseMultiplier = 0.5, actorLevel = 1, },
				[2] = { baseMultiplier = 0.55, actorLevel = 3.4519999027252, },
				[3] = { baseMultiplier = 0.61, actorLevel = 6.7670001983643, },
				[4] = { baseMultiplier = 0.66, actorLevel = 10.307999610901, },
				[5] = { baseMultiplier = 0.72, actorLevel = 14.074999809265, },
				[6] = { baseMultiplier = 0.77, actorLevel = 18.068000793457, },
				[7] = { baseMultiplier = 0.83, actorLevel = 22.287000656128, },
				[8] = { baseMultiplier = 0.88, actorLevel = 26.732000350952, },
				[9] = { baseMultiplier = 0.93, actorLevel = 31.40299987793, },
				[10] = { baseMultiplier = 0.99, actorLevel = 36.299999237061, },
				[11] = { baseMultiplier = 1.05, actorLevel = 41.423000335693, },
				[12] = { baseMultiplier = 1.11, actorLevel = 46.771999359131, },
				[13] = { baseMultiplier = 1.17, actorLevel = 52.34700012207, },
				[14] = { baseMultiplier = 1.25, actorLevel = 58.147998809814, },
				[15] = { baseMultiplier = 1.32, actorLevel = 64.175003051758, },
				[16] = { baseMultiplier = 1.41, actorLevel = 70.428001403809, },
				[17] = { baseMultiplier = 1.5, actorLevel = 76.906997680664, },
				[18] = { baseMultiplier = 1.59, actorLevel = 83.611999511719, },
				[19] = { baseMultiplier = 1.7, actorLevel = 90.542999267578, },
				[20] = { baseMultiplier = 1.81, actorLevel = 97.699996948242, },
				[21] = { baseMultiplier = 1.93, actorLevel = 105.08300018311, },
				[22] = { baseMultiplier = 2.06, actorLevel = 112.69200134277, },
				[23] = { baseMultiplier = 2.2, actorLevel = 120.52700042725, },
				[24] = { baseMultiplier = 2.34, actorLevel = 128.58799743652, },
				[25] = { baseMultiplier = 2.5, actorLevel = 136.875, },
				[26] = { baseMultiplier = 2.67, actorLevel = 145.38800048828, },
				[27] = { baseMultiplier = 2.84, actorLevel = 154.12699890137, },
				[28] = { baseMultiplier = 3.03, actorLevel = 163.09199523926, },
				[29] = { baseMultiplier = 3.24, actorLevel = 172.28300476074, },
				[30] = { baseMultiplier = 3.45, actorLevel = 181.69999694824, },
				[31] = { baseMultiplier = 3.68, actorLevel = 191.34300231934, },
				[32] = { baseMultiplier = 3.93, actorLevel = 201.21200561523, },
				[33] = { baseMultiplier = 4.19, actorLevel = 211.30700683594, },
				[34] = { baseMultiplier = 4.47, actorLevel = 221.62800598145, },
				[35] = { baseMultiplier = 4.76, actorLevel = 232.17500305176, },
				[36] = { baseMultiplier = 5.08, actorLevel = 242.94799804688, },
				[37] = { baseMultiplier = 5.42, actorLevel = 253.94700622559, },
				[38] = { baseMultiplier = 5.78, actorLevel = 265.17199707031, },
				[39] = { baseMultiplier = 6.17, actorLevel = 276.62298583984, },
				[40] = { baseMultiplier = 6.58, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["IntoTheBreachPlayer"] = {
	name = "Into the Breach",
	baseTypeName = "Into the Breach",
	fromTree = true,
	color = 4,
	description = "Create a Breach around you, allowing you to see nearby Flames of Chayula. You are considered to be in a Breach while this skill is active.",
	skillTypes = { [SkillType.Buff] = true, [SkillType.Persistent] = true, [SkillType.OngoingSkill] = true, [SkillType.HasReservation] = true, [SkillType.GeneratesRemnants] = true, },
	castTime = 0,
	qualityStats = {
		{ "breach_flame_spawn_rate_ms", -15 },
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { levelRequirement = 3, },
		[3] = { levelRequirement = 6, },
		[4] = { levelRequirement = 10, },
		[5] = { levelRequirement = 14, },
		[6] = { levelRequirement = 18, },
		[7] = { levelRequirement = 22, },
		[8] = { levelRequirement = 26, },
		[9] = { levelRequirement = 31, },
		[10] = { levelRequirement = 36, },
		[11] = { levelRequirement = 41, },
		[12] = { levelRequirement = 46, },
		[13] = { levelRequirement = 52, },
		[14] = { levelRequirement = 58, },
		[15] = { levelRequirement = 64, },
		[16] = { levelRequirement = 66, },
		[17] = { levelRequirement = 72, },
		[18] = { levelRequirement = 78, },
		[19] = { levelRequirement = 84, },
		[20] = { levelRequirement = 90, },
		[21] = { levelRequirement = 90, },
		[22] = { levelRequirement = 90, },
		[23] = { levelRequirement = 90, },
		[24] = { levelRequirement = 90, },
		[25] = { levelRequirement = 90, },
		[26] = { levelRequirement = 90, },
		[27] = { levelRequirement = 90, },
		[28] = { levelRequirement = 90, },
		[29] = { levelRequirement = 90, },
		[30] = { levelRequirement = 90, },
		[31] = { levelRequirement = 90, },
		[32] = { levelRequirement = 90, },
		[33] = { levelRequirement = 90, },
		[34] = { levelRequirement = 90, },
		[35] = { levelRequirement = 90, },
		[36] = { levelRequirement = 90, },
		[37] = { levelRequirement = 90, },
		[38] = { levelRequirement = 90, },
		[39] = { levelRequirement = 90, },
		[40] = { levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Into the Breach",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "breach_walk",
			baseFlags = {
			},
			constantStats = {
				{ "skill_desired_amount_override", 1 },
				{ "breach_flame_mana_leech_%", 7 },
				{ "breach_flame_life_leech_%", 7 },
				{ "breach_flame_chaos_addition_%", 7 },
				{ "breach_flame_spawn_radius", 50 },
				{ "base_remnant_duration_ms", 25000 },
			},
			stats = {
				"breach_flame_spawn_rate_ms",
			},
			levels = {
				[1] = { 1400, statInterpolation = { 1, }, actorLevel = 1, },
				[2] = { 1390, statInterpolation = { 1, }, actorLevel = 3.4519999027252, },
				[3] = { 1380, statInterpolation = { 1, }, actorLevel = 6.7670001983643, },
				[4] = { 1370, statInterpolation = { 1, }, actorLevel = 10.307999610901, },
				[5] = { 1360, statInterpolation = { 1, }, actorLevel = 14.074999809265, },
				[6] = { 1350, statInterpolation = { 1, }, actorLevel = 18.068000793457, },
				[7] = { 1340, statInterpolation = { 1, }, actorLevel = 22.287000656128, },
				[8] = { 1330, statInterpolation = { 1, }, actorLevel = 26.732000350952, },
				[9] = { 1320, statInterpolation = { 1, }, actorLevel = 31.40299987793, },
				[10] = { 1310, statInterpolation = { 1, }, actorLevel = 36.299999237061, },
				[11] = { 1300, statInterpolation = { 1, }, actorLevel = 41.423000335693, },
				[12] = { 1290, statInterpolation = { 1, }, actorLevel = 46.771999359131, },
				[13] = { 1280, statInterpolation = { 1, }, actorLevel = 52.34700012207, },
				[14] = { 1270, statInterpolation = { 1, }, actorLevel = 58.147998809814, },
				[15] = { 1260, statInterpolation = { 1, }, actorLevel = 64.175003051758, },
				[16] = { 1250, statInterpolation = { 1, }, actorLevel = 70.428001403809, },
				[17] = { 1240, statInterpolation = { 1, }, actorLevel = 76.906997680664, },
				[18] = { 1230, statInterpolation = { 1, }, actorLevel = 83.611999511719, },
				[19] = { 1220, statInterpolation = { 1, }, actorLevel = 90.542999267578, },
				[20] = { 1210, statInterpolation = { 1, }, actorLevel = 97.699996948242, },
				[21] = { 1200, statInterpolation = { 1, }, actorLevel = 105.08300018311, },
				[22] = { 1190, statInterpolation = { 1, }, actorLevel = 112.69200134277, },
				[23] = { 1180, statInterpolation = { 1, }, actorLevel = 120.52700042725, },
				[24] = { 1170, statInterpolation = { 1, }, actorLevel = 128.58799743652, },
				[25] = { 1160, statInterpolation = { 1, }, actorLevel = 136.875, },
				[26] = { 1150, statInterpolation = { 1, }, actorLevel = 145.38800048828, },
				[27] = { 1140, statInterpolation = { 1, }, actorLevel = 154.12699890137, },
				[28] = { 1130, statInterpolation = { 1, }, actorLevel = 163.09199523926, },
				[29] = { 1120, statInterpolation = { 1, }, actorLevel = 172.28300476074, },
				[30] = { 1110, statInterpolation = { 1, }, actorLevel = 181.69999694824, },
				[31] = { 1105, statInterpolation = { 1, }, actorLevel = 191.34300231934, },
				[32] = { 1100, statInterpolation = { 1, }, actorLevel = 201.21200561523, },
				[33] = { 1095, statInterpolation = { 1, }, actorLevel = 211.30700683594, },
				[34] = { 1090, statInterpolation = { 1, }, actorLevel = 221.62800598145, },
				[35] = { 1085, statInterpolation = { 1, }, actorLevel = 232.17500305176, },
				[36] = { 1080, statInterpolation = { 1, }, actorLevel = 242.94799804688, },
				[37] = { 1075, statInterpolation = { 1, }, actorLevel = 253.94700622559, },
				[38] = { 1070, statInterpolation = { 1, }, actorLevel = 265.17199707031, },
				[39] = { 1065, statInterpolation = { 1, }, actorLevel = 276.62298583984, },
				[40] = { 1060, statInterpolation = { 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["LifeRemnantsPlayer"] = {
	name = "Life Remnants",
	baseTypeName = "Life Remnants",
	fromTree = true,
	color = 4,
	description = "Drink the blood of your enemies to restore your Life. While active, enemies you kill have a chance to spawn a Life Remnant, and Critically Hitting a target spawns a Life Remnant every few seconds. Picking up a Life Remnant grants you Life which can Overflow maximum Life.",
	skillTypes = { [SkillType.Buff] = true, [SkillType.HasReservation] = true, [SkillType.OngoingSkill] = true, [SkillType.Persistent] = true, [SkillType.GeneratesRemnants] = true, },
	castTime = 0,
	qualityStats = {
		{ "life_remnants_chance_to_spawn_orb_on_killing_enemy_%", 0.5 },
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { levelRequirement = 3, },
		[3] = { levelRequirement = 6, },
		[4] = { levelRequirement = 10, },
		[5] = { levelRequirement = 14, },
		[6] = { levelRequirement = 18, },
		[7] = { levelRequirement = 22, },
		[8] = { levelRequirement = 26, },
		[9] = { levelRequirement = 31, },
		[10] = { levelRequirement = 36, },
		[11] = { levelRequirement = 41, },
		[12] = { levelRequirement = 46, },
		[13] = { levelRequirement = 52, },
		[14] = { levelRequirement = 58, },
		[15] = { levelRequirement = 64, },
		[16] = { levelRequirement = 66, },
		[17] = { levelRequirement = 72, },
		[18] = { levelRequirement = 78, },
		[19] = { levelRequirement = 84, },
		[20] = { levelRequirement = 90, },
		[21] = { levelRequirement = 90, },
		[22] = { levelRequirement = 90, },
		[23] = { levelRequirement = 90, },
		[24] = { levelRequirement = 90, },
		[25] = { levelRequirement = 90, },
		[26] = { levelRequirement = 90, },
		[27] = { levelRequirement = 90, },
		[28] = { levelRequirement = 90, },
		[29] = { levelRequirement = 90, },
		[30] = { levelRequirement = 90, },
		[31] = { levelRequirement = 90, },
		[32] = { levelRequirement = 90, },
		[33] = { levelRequirement = 90, },
		[34] = { levelRequirement = 90, },
		[35] = { levelRequirement = 90, },
		[36] = { levelRequirement = 90, },
		[37] = { levelRequirement = 90, },
		[38] = { levelRequirement = 90, },
		[39] = { levelRequirement = 90, },
		[40] = { levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Life Remnants",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "life_remnants",
			baseFlags = {
			},
			constantStats = {
				{ "life_remnants_chance_to_spawn_orb_on_killing_enemy_%", 25 },
				{ "life_remnants_spawn_remnant_on_crit_vs_enemy_every_X_ms", 2000 },
				{ "skill_desired_amount_override", 1 },
				{ "base_remnant_duration_ms", 8000 },
			},
			stats = {
				"life_remnants_gain_per_globe",
				"base_deal_no_damage_over_time",
			},
			levels = {
				[1] = { 11, statInterpolation = { 1, }, actorLevel = 1, },
				[2] = { 14, statInterpolation = { 1, }, actorLevel = 3.4519999027252, },
				[3] = { 23, statInterpolation = { 1, }, actorLevel = 6.7670001983643, },
				[4] = { 31, statInterpolation = { 1, }, actorLevel = 10.307999610901, },
				[5] = { 41, statInterpolation = { 1, }, actorLevel = 14.074999809265, },
				[6] = { 52, statInterpolation = { 1, }, actorLevel = 18.068000793457, },
				[7] = { 65, statInterpolation = { 1, }, actorLevel = 22.287000656128, },
				[8] = { 81, statInterpolation = { 1, }, actorLevel = 26.732000350952, },
				[9] = { 95, statInterpolation = { 1, }, actorLevel = 31.40299987793, },
				[10] = { 112, statInterpolation = { 1, }, actorLevel = 36.299999237061, },
				[11] = { 131, statInterpolation = { 1, }, actorLevel = 41.423000335693, },
				[12] = { 157, statInterpolation = { 1, }, actorLevel = 46.771999359131, },
				[13] = { 176, statInterpolation = { 1, }, actorLevel = 52.34700012207, },
				[14] = { 205, statInterpolation = { 1, }, actorLevel = 58.147998809814, },
				[15] = { 232, statInterpolation = { 1, }, actorLevel = 64.175003051758, },
				[16] = { 263, statInterpolation = { 1, }, actorLevel = 70.428001403809, },
				[17] = { 301, statInterpolation = { 1, }, actorLevel = 76.906997680664, },
				[18] = { 337, statInterpolation = { 1, }, actorLevel = 83.611999511719, },
				[19] = { 374, statInterpolation = { 1, }, actorLevel = 90.542999267578, },
				[20] = { 410, statInterpolation = { 1, }, actorLevel = 97.699996948242, },
				[21] = { 448, statInterpolation = { 1, }, actorLevel = 105.08300018311, },
				[22] = { 490, statInterpolation = { 1, }, actorLevel = 112.69200134277, },
				[23] = { 531, statInterpolation = { 1, }, actorLevel = 120.52700042725, },
				[24] = { 574, statInterpolation = { 1, }, actorLevel = 128.58799743652, },
				[25] = { 616, statInterpolation = { 1, }, actorLevel = 136.875, },
				[26] = { 657, statInterpolation = { 1, }, actorLevel = 145.38800048828, },
				[27] = { 706, statInterpolation = { 1, }, actorLevel = 154.12699890137, },
				[28] = { 752, statInterpolation = { 1, }, actorLevel = 163.09199523926, },
				[29] = { 799, statInterpolation = { 1, }, actorLevel = 172.28300476074, },
				[30] = { 851, statInterpolation = { 1, }, actorLevel = 181.69999694824, },
				[31] = { 900, statInterpolation = { 1, }, actorLevel = 191.34300231934, },
				[32] = { 952, statInterpolation = { 1, }, actorLevel = 201.21200561523, },
				[33] = { 1004, statInterpolation = { 1, }, actorLevel = 211.30700683594, },
				[34] = { 1062, statInterpolation = { 1, }, actorLevel = 221.62800598145, },
				[35] = { 1114, statInterpolation = { 1, }, actorLevel = 232.17500305176, },
				[36] = { 1172, statInterpolation = { 1, }, actorLevel = 242.94799804688, },
				[37] = { 1229, statInterpolation = { 1, }, actorLevel = 253.94700622559, },
				[38] = { 1287, statInterpolation = { 1, }, actorLevel = 265.17199707031, },
				[39] = { 1350, statInterpolation = { 1, }, actorLevel = 276.62298583984, },
				[40] = { 1408, statInterpolation = { 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["Melee1HMacePlayer"] = {
	name = "Mace Strike",
	baseTypeName = "Mace Strike",
	fromItem = true,
	color = 4,
	description = "Strike with your Mace.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.Area] = true, },
	weaponTypes = {
		["One Handed Mace"] = true,
	},
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { baseMultiplier = 1.1, levelRequirement = 3, },
		[3] = { baseMultiplier = 1.21, levelRequirement = 6, },
		[4] = { baseMultiplier = 1.32, levelRequirement = 10, },
		[5] = { baseMultiplier = 1.42, levelRequirement = 14, },
		[6] = { baseMultiplier = 1.53, levelRequirement = 18, },
		[7] = { baseMultiplier = 1.64, levelRequirement = 22, },
		[8] = { baseMultiplier = 1.74, levelRequirement = 26, },
		[9] = { baseMultiplier = 1.82, levelRequirement = 31, },
		[10] = { baseMultiplier = 1.91, levelRequirement = 36, },
		[11] = { baseMultiplier = 2.01, levelRequirement = 41, },
		[12] = { baseMultiplier = 2.11, levelRequirement = 46, },
		[13] = { baseMultiplier = 2.22, levelRequirement = 52, },
		[14] = { baseMultiplier = 2.33, levelRequirement = 58, },
		[15] = { baseMultiplier = 2.44, levelRequirement = 64, },
		[16] = { baseMultiplier = 2.57, levelRequirement = 66, },
		[17] = { baseMultiplier = 2.69, levelRequirement = 72, },
		[18] = { baseMultiplier = 2.83, levelRequirement = 78, },
		[19] = { baseMultiplier = 2.97, levelRequirement = 84, },
		[20] = { baseMultiplier = 3.12, levelRequirement = 90, },
		[21] = { baseMultiplier = 3.27, levelRequirement = 90, },
		[22] = { baseMultiplier = 3.44, levelRequirement = 90, },
		[23] = { baseMultiplier = 3.61, levelRequirement = 90, },
		[24] = { baseMultiplier = 3.79, levelRequirement = 90, },
		[25] = { baseMultiplier = 3.98, levelRequirement = 90, },
		[26] = { baseMultiplier = 4.18, levelRequirement = 90, },
		[27] = { baseMultiplier = 4.39, levelRequirement = 90, },
		[28] = { baseMultiplier = 4.61, levelRequirement = 90, },
		[29] = { baseMultiplier = 4.84, levelRequirement = 90, },
		[30] = { baseMultiplier = 5.08, levelRequirement = 90, },
		[31] = { baseMultiplier = 5.33, levelRequirement = 90, },
		[32] = { baseMultiplier = 5.6, levelRequirement = 90, },
		[33] = { baseMultiplier = 5.88, levelRequirement = 90, },
		[34] = { baseMultiplier = 6.18, levelRequirement = 90, },
		[35] = { baseMultiplier = 6.48, levelRequirement = 90, },
		[36] = { baseMultiplier = 6.81, levelRequirement = 90, },
		[37] = { baseMultiplier = 7.15, levelRequirement = 90, },
		[38] = { baseMultiplier = 7.51, levelRequirement = 90, },
		[39] = { baseMultiplier = 7.88, levelRequirement = 90, },
		[40] = { baseMultiplier = 8.28, levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Mace Strike",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				area = true,
				melee = true,
			},
			constantStats = {
				{ "melee_conditional_step_distance", 10 },
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["Melee2HMacePlayer"] = {
	name = "Mace Strike",
	baseTypeName = "Mace Strike",
	fromItem = true,
	color = 4,
	description = "Strike with your Mace.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.Area] = true, },
	weaponTypes = {
		["Two Handed Mace"] = true,
	},
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { baseMultiplier = 1.1, levelRequirement = 3, },
		[3] = { baseMultiplier = 1.21, levelRequirement = 6, },
		[4] = { baseMultiplier = 1.32, levelRequirement = 10, },
		[5] = { baseMultiplier = 1.42, levelRequirement = 14, },
		[6] = { baseMultiplier = 1.53, levelRequirement = 18, },
		[7] = { baseMultiplier = 1.64, levelRequirement = 22, },
		[8] = { baseMultiplier = 1.74, levelRequirement = 26, },
		[9] = { baseMultiplier = 1.82, levelRequirement = 31, },
		[10] = { baseMultiplier = 1.91, levelRequirement = 36, },
		[11] = { baseMultiplier = 2.01, levelRequirement = 41, },
		[12] = { baseMultiplier = 2.11, levelRequirement = 46, },
		[13] = { baseMultiplier = 2.22, levelRequirement = 52, },
		[14] = { baseMultiplier = 2.33, levelRequirement = 58, },
		[15] = { baseMultiplier = 2.44, levelRequirement = 64, },
		[16] = { baseMultiplier = 2.57, levelRequirement = 66, },
		[17] = { baseMultiplier = 2.69, levelRequirement = 72, },
		[18] = { baseMultiplier = 2.83, levelRequirement = 78, },
		[19] = { baseMultiplier = 2.97, levelRequirement = 84, },
		[20] = { baseMultiplier = 3.12, levelRequirement = 90, },
		[21] = { baseMultiplier = 3.27, levelRequirement = 90, },
		[22] = { baseMultiplier = 3.44, levelRequirement = 90, },
		[23] = { baseMultiplier = 3.61, levelRequirement = 90, },
		[24] = { baseMultiplier = 3.79, levelRequirement = 90, },
		[25] = { baseMultiplier = 3.98, levelRequirement = 90, },
		[26] = { baseMultiplier = 4.18, levelRequirement = 90, },
		[27] = { baseMultiplier = 4.39, levelRequirement = 90, },
		[28] = { baseMultiplier = 4.61, levelRequirement = 90, },
		[29] = { baseMultiplier = 4.84, levelRequirement = 90, },
		[30] = { baseMultiplier = 5.08, levelRequirement = 90, },
		[31] = { baseMultiplier = 5.33, levelRequirement = 90, },
		[32] = { baseMultiplier = 5.6, levelRequirement = 90, },
		[33] = { baseMultiplier = 5.88, levelRequirement = 90, },
		[34] = { baseMultiplier = 6.18, levelRequirement = 90, },
		[35] = { baseMultiplier = 6.48, levelRequirement = 90, },
		[36] = { baseMultiplier = 6.81, levelRequirement = 90, },
		[37] = { baseMultiplier = 7.15, levelRequirement = 90, },
		[38] = { baseMultiplier = 7.51, levelRequirement = 90, },
		[39] = { baseMultiplier = 7.88, levelRequirement = 90, },
		[40] = { baseMultiplier = 8.28, levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Mace Strike",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				area = true,
				melee = true,
			},
			constantStats = {
				{ "melee_conditional_step_distance", 10 },
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["MeleeMaceMacePlayer"] = {
	name = "Mace Strike",
	baseTypeName = "Mace Strike",
	fromItem = true,
	color = 4,
	description = "Strike with your Maces.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.Area] = true, [SkillType.DualWieldOnly] = true, },
	weaponTypes = {
		["One Handed Mace"] = true,
		["Two Handed Mace"] = true,
	},
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { baseMultiplier = 1.1, levelRequirement = 3, },
		[3] = { baseMultiplier = 1.21, levelRequirement = 6, },
		[4] = { baseMultiplier = 1.32, levelRequirement = 10, },
		[5] = { baseMultiplier = 1.42, levelRequirement = 14, },
		[6] = { baseMultiplier = 1.53, levelRequirement = 18, },
		[7] = { baseMultiplier = 1.64, levelRequirement = 22, },
		[8] = { baseMultiplier = 1.74, levelRequirement = 26, },
		[9] = { baseMultiplier = 1.82, levelRequirement = 31, },
		[10] = { baseMultiplier = 1.91, levelRequirement = 36, },
		[11] = { baseMultiplier = 2.01, levelRequirement = 41, },
		[12] = { baseMultiplier = 2.11, levelRequirement = 46, },
		[13] = { baseMultiplier = 2.22, levelRequirement = 52, },
		[14] = { baseMultiplier = 2.33, levelRequirement = 58, },
		[15] = { baseMultiplier = 2.44, levelRequirement = 64, },
		[16] = { baseMultiplier = 2.57, levelRequirement = 66, },
		[17] = { baseMultiplier = 2.69, levelRequirement = 72, },
		[18] = { baseMultiplier = 2.83, levelRequirement = 78, },
		[19] = { baseMultiplier = 2.97, levelRequirement = 84, },
		[20] = { baseMultiplier = 3.12, levelRequirement = 90, },
		[21] = { baseMultiplier = 3.27, levelRequirement = 90, },
		[22] = { baseMultiplier = 3.44, levelRequirement = 90, },
		[23] = { baseMultiplier = 3.61, levelRequirement = 90, },
		[24] = { baseMultiplier = 3.79, levelRequirement = 90, },
		[25] = { baseMultiplier = 3.98, levelRequirement = 90, },
		[26] = { baseMultiplier = 4.18, levelRequirement = 90, },
		[27] = { baseMultiplier = 4.39, levelRequirement = 90, },
		[28] = { baseMultiplier = 4.61, levelRequirement = 90, },
		[29] = { baseMultiplier = 4.84, levelRequirement = 90, },
		[30] = { baseMultiplier = 5.08, levelRequirement = 90, },
		[31] = { baseMultiplier = 5.33, levelRequirement = 90, },
		[32] = { baseMultiplier = 5.6, levelRequirement = 90, },
		[33] = { baseMultiplier = 5.88, levelRequirement = 90, },
		[34] = { baseMultiplier = 6.18, levelRequirement = 90, },
		[35] = { baseMultiplier = 6.48, levelRequirement = 90, },
		[36] = { baseMultiplier = 6.81, levelRequirement = 90, },
		[37] = { baseMultiplier = 7.15, levelRequirement = 90, },
		[38] = { baseMultiplier = 7.51, levelRequirement = 90, },
		[39] = { baseMultiplier = 7.88, levelRequirement = 90, },
		[40] = { baseMultiplier = 8.28, levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Mace Strike",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				area = true,
				melee = true,
			},
			constantStats = {
				{ "melee_conditional_step_distance", 10 },
				{ "active_skill_attack_speed_+%_final_while_dual_wielding", -30 },
			},
			stats = {
				"is_area_damage",
				"skill_double_hits_when_dual_wielding",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["ChaosSpearTriggerChaosInfusionPlayer"] = {
	name = "Chaotic Infusion",
	baseTypeName = "Chaotic Infusion",
	fromItem = true,
	color = 4,
	description = "Grants your weapon Infusion. Non-Melee Projectile Attacks with that weapon Consume Infusions to Infuse the first Projectile fired, causing it to explode at the end of its flight.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.Area] = true, [SkillType.Chaos] = true, [SkillType.Triggered] = true, [SkillType.Triggerable] = true, [SkillType.GeneratesInfusion] = true, [SkillType.NoAttackOrCastTime] = true, [SkillType.CannotConsumeCharges] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 0.5, levelRequirement = 0, },
		[2] = { baseMultiplier = 0.55, levelRequirement = 3, },
		[3] = { baseMultiplier = 0.61, levelRequirement = 6, },
		[4] = { baseMultiplier = 0.66, levelRequirement = 10, },
		[5] = { baseMultiplier = 0.72, levelRequirement = 14, },
		[6] = { baseMultiplier = 0.77, levelRequirement = 18, },
		[7] = { baseMultiplier = 0.83, levelRequirement = 22, },
		[8] = { baseMultiplier = 0.88, levelRequirement = 26, },
		[9] = { baseMultiplier = 0.93, levelRequirement = 31, },
		[10] = { baseMultiplier = 0.99, levelRequirement = 36, },
		[11] = { baseMultiplier = 1.05, levelRequirement = 41, },
		[12] = { baseMultiplier = 1.11, levelRequirement = 46, },
		[13] = { baseMultiplier = 1.17, levelRequirement = 52, },
		[14] = { baseMultiplier = 1.25, levelRequirement = 58, },
		[15] = { baseMultiplier = 1.32, levelRequirement = 64, },
		[16] = { baseMultiplier = 1.41, levelRequirement = 66, },
		[17] = { baseMultiplier = 1.5, levelRequirement = 72, },
		[18] = { baseMultiplier = 1.59, levelRequirement = 78, },
		[19] = { baseMultiplier = 1.7, levelRequirement = 84, },
		[20] = { baseMultiplier = 1.81, levelRequirement = 90, },
		[21] = { baseMultiplier = 1.93, levelRequirement = 90, },
		[22] = { baseMultiplier = 2.06, levelRequirement = 90, },
		[23] = { baseMultiplier = 2.2, levelRequirement = 90, },
		[24] = { baseMultiplier = 2.34, levelRequirement = 90, },
		[25] = { baseMultiplier = 2.5, levelRequirement = 90, },
		[26] = { baseMultiplier = 2.67, levelRequirement = 90, },
		[27] = { baseMultiplier = 2.84, levelRequirement = 90, },
		[28] = { baseMultiplier = 3.03, levelRequirement = 90, },
		[29] = { baseMultiplier = 3.24, levelRequirement = 90, },
		[30] = { baseMultiplier = 3.45, levelRequirement = 90, },
		[31] = { baseMultiplier = 3.68, levelRequirement = 90, },
		[32] = { baseMultiplier = 3.93, levelRequirement = 90, },
		[33] = { baseMultiplier = 4.19, levelRequirement = 90, },
		[34] = { baseMultiplier = 4.47, levelRequirement = 90, },
		[35] = { baseMultiplier = 4.76, levelRequirement = 90, },
		[36] = { baseMultiplier = 5.08, levelRequirement = 90, },
		[37] = { baseMultiplier = 5.42, levelRequirement = 90, },
		[38] = { baseMultiplier = 5.78, levelRequirement = 90, },
		[39] = { baseMultiplier = 6.17, levelRequirement = 90, },
		[40] = { baseMultiplier = 6.58, levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Chaotic Infusion",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "trigger_chaos_infusion",
			baseFlags = {
				attack = true,
				area = true,
			},
			constantStats = {
				{ "chance_to_be_triggered_by_chaos_spear_%", 100 },
				{ "active_skill_base_area_of_effect_radius", 18 },
				{ "active_skill_base_physical_damage_%_to_convert_to_chaos", 100 },
			},
			stats = {
				"is_area_damage",
				"skill_is_infusion_skill",
				"cannot_consume_power_frenzy_endurance_charges",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["ManifestWeaponPlayer"] = {
	name = "Manifest Weapon",
	baseTypeName = "Manifest Weapon",
	fromTree = true,
	minionList = {
		"ManifestWeapon",
	},
	color = 4,
	description = "Manifest a copy of your main hand Melee Martial Weapon as an immortal Companion to fight by your side. In addition to its standard Strikes, the Manifested Weapon gains an additional Attack depending on its weapon type.",
	skillTypes = { [SkillType.Minion] = true, [SkillType.CreatesMinion] = true, [SkillType.Persistent] = true, [SkillType.HasReservation] = true, [SkillType.Companion] = true, [SkillType.MinionsAreUndamagable] = true, [SkillType.CreatesCompanion] = true, },
	minionSkillTypes = { [SkillType.Attack] = true, [SkillType.Melee] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Area] = true, },
	weaponTypes = {
		["One Handed Mace"] = true,
		["Flail"] = true,
		["Two Handed Sword"] = true,
		["Dagger"] = true,
		["Claw"] = true,
		["Spear"] = true,
		["Two Handed Axe"] = true,
		["Two Handed Mace"] = true,
		["One Handed Axe"] = true,
		["Staff"] = true,
		["One Handed Sword"] = true,
	},
	castTime = 0,
	qualityStats = {
		{ "minion_attack_speed_+%", 1 },
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { levelRequirement = 3, },
		[3] = { levelRequirement = 6, },
		[4] = { levelRequirement = 10, },
		[5] = { levelRequirement = 14, },
		[6] = { levelRequirement = 18, },
		[7] = { levelRequirement = 22, },
		[8] = { levelRequirement = 26, },
		[9] = { levelRequirement = 31, },
		[10] = { levelRequirement = 36, },
		[11] = { levelRequirement = 41, },
		[12] = { levelRequirement = 46, },
		[13] = { levelRequirement = 52, },
		[14] = { levelRequirement = 58, },
		[15] = { levelRequirement = 64, },
		[16] = { levelRequirement = 66, },
		[17] = { levelRequirement = 72, },
		[18] = { levelRequirement = 78, },
		[19] = { levelRequirement = 84, },
		[20] = { levelRequirement = 90, },
		[21] = { levelRequirement = 90, },
		[22] = { levelRequirement = 90, },
		[23] = { levelRequirement = 90, },
		[24] = { levelRequirement = 90, },
		[25] = { levelRequirement = 90, },
		[26] = { levelRequirement = 90, },
		[27] = { levelRequirement = 90, },
		[28] = { levelRequirement = 90, },
		[29] = { levelRequirement = 90, },
		[30] = { levelRequirement = 90, },
		[31] = { levelRequirement = 90, },
		[32] = { levelRequirement = 90, },
		[33] = { levelRequirement = 90, },
		[34] = { levelRequirement = 90, },
		[35] = { levelRequirement = 90, },
		[36] = { levelRequirement = 90, },
		[37] = { levelRequirement = 90, },
		[38] = { levelRequirement = 90, },
		[39] = { levelRequirement = 90, },
		[40] = { levelRequirement = 90, },
	},
			minionHasItemSet = true,
			minionUses = {
				["Weapon 1"] = true,
			},
	statSets = {
		[1] = {
			label = "Manifest Weapon",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				minion = true,
				duration = true,
			},
			constantStats = {
				{ "skill_desired_initial_amount", 1 },
				{ "minion_1%_damage_+%_per_X_player_strength", 3 },
				{ "minion_1%_accuracy_rating_+%_per_X_player_dexterity", 3 },
			},
			stats = {
				"is_resummoning_minion",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["MeditatePlayer"] = {
	name = "Meditate",
	baseTypeName = "Meditate",
	fromTree = true,
	color = 4,
	description = "Channel to Recharge Energy Shield and allow that Recharge to Overflow. Channelling ends when you take damage or your Energy Shield is double its normal maximum. This skill cannot be used if your Energy Shield is already double its normal maximum or you have no maximum Energy Shield.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Channel] = true, [SkillType.Cooldown] = true, },
	castTime = 1,
	qualityStats = {
		{ "meditate_energy_shield_recharge_rate_+%_final", 0.5 },
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 10, },
		[2] = { storedUses = 1, levelRequirement = 3, cooldown = 9.9, },
		[3] = { storedUses = 1, levelRequirement = 6, cooldown = 9.8, },
		[4] = { storedUses = 1, levelRequirement = 10, cooldown = 9.7, },
		[5] = { storedUses = 1, levelRequirement = 14, cooldown = 9.6, },
		[6] = { storedUses = 1, levelRequirement = 18, cooldown = 9.5, },
		[7] = { storedUses = 1, levelRequirement = 22, cooldown = 9.4, },
		[8] = { storedUses = 1, levelRequirement = 26, cooldown = 9.3, },
		[9] = { storedUses = 1, levelRequirement = 31, cooldown = 9.2, },
		[10] = { storedUses = 1, levelRequirement = 36, cooldown = 9.1, },
		[11] = { storedUses = 1, levelRequirement = 41, cooldown = 9, },
		[12] = { storedUses = 1, levelRequirement = 46, cooldown = 8.9, },
		[13] = { storedUses = 1, levelRequirement = 52, cooldown = 8.8, },
		[14] = { storedUses = 1, levelRequirement = 58, cooldown = 8.7, },
		[15] = { storedUses = 1, levelRequirement = 64, cooldown = 8.6, },
		[16] = { storedUses = 1, levelRequirement = 66, cooldown = 8.5, },
		[17] = { storedUses = 1, levelRequirement = 72, cooldown = 8.4, },
		[18] = { storedUses = 1, levelRequirement = 78, cooldown = 8.3, },
		[19] = { storedUses = 1, levelRequirement = 84, cooldown = 8.2, },
		[20] = { storedUses = 1, levelRequirement = 90, cooldown = 8.1, },
		[21] = { storedUses = 1, levelRequirement = 90, cooldown = 8, },
		[22] = { storedUses = 1, levelRequirement = 90, cooldown = 7.9, },
		[23] = { storedUses = 1, levelRequirement = 90, cooldown = 7.8, },
		[24] = { storedUses = 1, levelRequirement = 90, cooldown = 7.7, },
		[25] = { storedUses = 1, levelRequirement = 90, cooldown = 7.6, },
		[26] = { storedUses = 1, levelRequirement = 90, cooldown = 7.5, },
		[27] = { storedUses = 1, levelRequirement = 90, cooldown = 7.4, },
		[28] = { storedUses = 1, levelRequirement = 90, cooldown = 7.3, },
		[29] = { storedUses = 1, levelRequirement = 90, cooldown = 7.2, },
		[30] = { storedUses = 1, levelRequirement = 90, cooldown = 7.1, },
		[31] = { storedUses = 1, levelRequirement = 90, cooldown = 7.05, },
		[32] = { storedUses = 1, levelRequirement = 90, cooldown = 7, },
		[33] = { storedUses = 1, levelRequirement = 90, cooldown = 6.95, },
		[34] = { storedUses = 1, levelRequirement = 90, cooldown = 6.9, },
		[35] = { storedUses = 1, levelRequirement = 90, cooldown = 6.85, },
		[36] = { storedUses = 1, levelRequirement = 90, cooldown = 6.8, },
		[37] = { storedUses = 1, levelRequirement = 90, cooldown = 6.75, },
		[38] = { storedUses = 1, levelRequirement = 90, cooldown = 6.7, },
		[39] = { storedUses = 1, levelRequirement = 90, cooldown = 6.65, },
		[40] = { storedUses = 1, levelRequirement = 90, cooldown = 6.6, },
	},
	statSets = {
		[1] = {
			label = "Meditate",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "movement_speed_+%_final_while_performing_action", -70 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 60 },
			},
			stats = {
				"meditate_energy_shield_recharge_rate_+%_final",
				"can_perform_skill_while_moving",
				"base_deal_no_damage",
			},
			levels = {
				[1] = { 0, statInterpolation = { 1, }, actorLevel = 1, },
				[2] = { 1, statInterpolation = { 1, }, actorLevel = 3.4519999027252, },
				[3] = { 3, statInterpolation = { 1, }, actorLevel = 6.7670001983643, },
				[4] = { 4, statInterpolation = { 1, }, actorLevel = 10.307999610901, },
				[5] = { 6, statInterpolation = { 1, }, actorLevel = 14.074999809265, },
				[6] = { 7, statInterpolation = { 1, }, actorLevel = 18.068000793457, },
				[7] = { 9, statInterpolation = { 1, }, actorLevel = 22.287000656128, },
				[8] = { 10, statInterpolation = { 1, }, actorLevel = 26.732000350952, },
				[9] = { 12, statInterpolation = { 1, }, actorLevel = 31.40299987793, },
				[10] = { 13, statInterpolation = { 1, }, actorLevel = 36.299999237061, },
				[11] = { 15, statInterpolation = { 1, }, actorLevel = 41.423000335693, },
				[12] = { 16, statInterpolation = { 1, }, actorLevel = 46.771999359131, },
				[13] = { 18, statInterpolation = { 1, }, actorLevel = 52.34700012207, },
				[14] = { 19, statInterpolation = { 1, }, actorLevel = 58.147998809814, },
				[15] = { 21, statInterpolation = { 1, }, actorLevel = 64.175003051758, },
				[16] = { 22, statInterpolation = { 1, }, actorLevel = 70.428001403809, },
				[17] = { 24, statInterpolation = { 1, }, actorLevel = 76.906997680664, },
				[18] = { 25, statInterpolation = { 1, }, actorLevel = 83.611999511719, },
				[19] = { 27, statInterpolation = { 1, }, actorLevel = 90.542999267578, },
				[20] = { 28, statInterpolation = { 1, }, actorLevel = 97.699996948242, },
				[21] = { 30, statInterpolation = { 1, }, actorLevel = 105.08300018311, },
				[22] = { 31, statInterpolation = { 1, }, actorLevel = 112.69200134277, },
				[23] = { 33, statInterpolation = { 1, }, actorLevel = 120.52700042725, },
				[24] = { 34, statInterpolation = { 1, }, actorLevel = 128.58799743652, },
				[25] = { 36, statInterpolation = { 1, }, actorLevel = 136.875, },
				[26] = { 37, statInterpolation = { 1, }, actorLevel = 145.38800048828, },
				[27] = { 39, statInterpolation = { 1, }, actorLevel = 154.12699890137, },
				[28] = { 40, statInterpolation = { 1, }, actorLevel = 163.09199523926, },
				[29] = { 42, statInterpolation = { 1, }, actorLevel = 172.28300476074, },
				[30] = { 43, statInterpolation = { 1, }, actorLevel = 181.69999694824, },
				[31] = { 44, statInterpolation = { 1, }, actorLevel = 191.34300231934, },
				[32] = { 45, statInterpolation = { 1, }, actorLevel = 201.21200561523, },
				[33] = { 45, statInterpolation = { 1, }, actorLevel = 211.30700683594, },
				[34] = { 46, statInterpolation = { 1, }, actorLevel = 221.62800598145, },
				[35] = { 47, statInterpolation = { 1, }, actorLevel = 232.17500305176, },
				[36] = { 48, statInterpolation = { 1, }, actorLevel = 242.94799804688, },
				[37] = { 48, statInterpolation = { 1, }, actorLevel = 253.94700622559, },
				[38] = { 49, statInterpolation = { 1, }, actorLevel = 265.17199707031, },
				[39] = { 50, statInterpolation = { 1, }, actorLevel = 276.62298583984, },
				[40] = { 51, statInterpolation = { 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["ParryPlayer"] = {
	name = "Parry",
	baseTypeName = "Parry",
	fromTree = true,
	color = 4,
	description = "Ready your Buckler to parry the next Strike or Projectile that would Hit you, Blocking the Hit and retaliating with a quick sweep that leaves enemies off balance, causing them to take massively increased Attack damage for a short duration. Parrying causes you to accumulate Heavy Stun buildup. You cannot Evade a Hit you could parry, but Evasion instead grants an equal chance to avoid this Heavy Stun buildup.",
	skillTypes = { [SkillType.RequiresBuckler] = true, [SkillType.UsableWhileMoving] = true, [SkillType.Attack] = true, [SkillType.Melee] = true, [SkillType.Physical] = true, [SkillType.Channel] = true, [SkillType.Duration] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { attackTime = 750, baseMultiplier = 0.65, critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Parry",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "parry",
			statMap = {
				["base_parry_buff_damage_taken_+%_final_to_apply"] = {
					mod("DamageTaken", "MORE", nil, ModFlag.Attack, 0, { type = "GlobalEffect", effectType = "Debuff", effectName = "Parry" }, { type = "Condition", var = "ParryActive" }),
				},
			},
			baseFlags = {
				attack = true,
				melee = true,
				duration = true,
				shieldAttack = true,
			},
			constantStats = {
				{ "movement_speed_+%_final_while_performing_action", -70 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 60 },
				{ "parry_blocked_projectile_distance", 10 },
				{ "base_parry_buff_damage_taken_+%_final_to_apply", 50 },
				{ "base_skill_effect_duration", 2000 },
				{ "active_skill_hit_damage_stun_multiplier_+%_final", 400 },
				{ "active_skill_heavy_stun_decay_after_action_delay_ms", 2000 },
				{ "stun_threshold_+%_final_while_performing_action", -65 },
				{ "active_skill_override_turn_duration_ms", 100 },
			},
			stats = {
				"off_hand_minimum_added_physical_damage_per_5_shield_evasion_rating",
				"off_hand_maximum_added_physical_damage_per_5_shield_evasion_rating",
				"can_perform_skill_while_moving",
				"skill_is_not_considered_a_skill",
				"global_always_hit",
				"active_skill_does_not_decay_heavy_stun_during_action",
				"always_light_stun",
				"quality_display_base_skill_effect_duration_is_gem",
				"base_skill_show_average_damage_instead_of_dps",
				"replace_off_hand_unarmed_attack_stats_with_shield_type",
			},
			levels = {
				[1] = { 0.80000001192093, 1, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["PoisonousConcoctionPlayer"] = {
	name = "Poisonous Concoction",
	baseTypeName = "Poisonous Concoction",
	fromTree = true,
	color = 4,
	description = "Consume charges from your Mana Flask to throw a bottle that explodes, dealing Chaos attack damage in an area. Poison inflicted by this skill has more effect.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Area] = true, [SkillType.Projectile] = true, [SkillType.Chaos] = true, [SkillType.UsableWhileMoving] = true, [SkillType.ProjectileNoCollision] = true, [SkillType.Physical] = true, },
	weaponTypes = {
		["None"] = true,
	},
	castTime = 1,
	qualityStats = {
		{ "faster_poison_%", 1 },
	},
	levels = {
		[1] = { critChance = 5, baseMultiplier = 0.7, levelRequirement = 0, cost = { Mana = 0, }, },
		[2] = { critChance = 5, baseMultiplier = 0.77, levelRequirement = 3, cost = { Mana = 0, }, },
		[3] = { critChance = 5, baseMultiplier = 0.84, levelRequirement = 6, cost = { Mana = 0, }, },
		[4] = { critChance = 5, baseMultiplier = 0.92, levelRequirement = 10, cost = { Mana = 0, }, },
		[5] = { critChance = 5, baseMultiplier = 0.99, levelRequirement = 14, cost = { Mana = 0, }, },
		[6] = { critChance = 5, baseMultiplier = 1.05, levelRequirement = 18, cost = { Mana = 0, }, },
		[7] = { critChance = 5, baseMultiplier = 1.12, levelRequirement = 22, cost = { Mana = 0, }, },
		[8] = { critChance = 5, baseMultiplier = 1.18, levelRequirement = 26, cost = { Mana = 0, }, },
		[9] = { critChance = 5, baseMultiplier = 1.23, levelRequirement = 31, cost = { Mana = 0, }, },
		[10] = { critChance = 5, baseMultiplier = 1.28, levelRequirement = 36, cost = { Mana = 0, }, },
		[11] = { critChance = 5, baseMultiplier = 1.34, levelRequirement = 41, cost = { Mana = 0, }, },
		[12] = { critChance = 5, baseMultiplier = 1.39, levelRequirement = 46, cost = { Mana = 0, }, },
		[13] = { critChance = 5, baseMultiplier = 1.44, levelRequirement = 52, cost = { Mana = 0, }, },
		[14] = { critChance = 5, baseMultiplier = 1.5, levelRequirement = 58, cost = { Mana = 0, }, },
		[15] = { critChance = 5, baseMultiplier = 1.55, levelRequirement = 64, cost = { Mana = 0, }, },
		[16] = { critChance = 5, baseMultiplier = 1.61, levelRequirement = 66, cost = { Mana = 0, }, },
		[17] = { critChance = 5, baseMultiplier = 1.67, levelRequirement = 72, cost = { Mana = 0, }, },
		[18] = { critChance = 5, baseMultiplier = 1.73, levelRequirement = 78, cost = { Mana = 0, }, },
		[19] = { critChance = 5, baseMultiplier = 1.79, levelRequirement = 84, cost = { Mana = 0, }, },
		[20] = { critChance = 5, baseMultiplier = 1.85, levelRequirement = 90, cost = { Mana = 0, }, },
		[21] = { critChance = 5, baseMultiplier = 1.91, levelRequirement = 90, cost = { Mana = 0, }, },
		[22] = { critChance = 5, baseMultiplier = 1.97, levelRequirement = 90, cost = { Mana = 0, }, },
		[23] = { critChance = 5, baseMultiplier = 2.04, levelRequirement = 90, cost = { Mana = 0, }, },
		[24] = { critChance = 5, baseMultiplier = 2.1, levelRequirement = 90, cost = { Mana = 0, }, },
		[25] = { critChance = 5, baseMultiplier = 2.17, levelRequirement = 90, cost = { Mana = 0, }, },
		[26] = { critChance = 5, baseMultiplier = 2.24, levelRequirement = 90, cost = { Mana = 0, }, },
		[27] = { critChance = 5, baseMultiplier = 2.32, levelRequirement = 90, cost = { Mana = 0, }, },
		[28] = { critChance = 5, baseMultiplier = 2.4, levelRequirement = 90, cost = { Mana = 0, }, },
		[29] = { critChance = 5, baseMultiplier = 2.48, levelRequirement = 90, cost = { Mana = 0, }, },
		[30] = { critChance = 5, baseMultiplier = 2.56, levelRequirement = 90, cost = { Mana = 0, }, },
		[31] = { critChance = 5, baseMultiplier = 2.64, levelRequirement = 90, cost = { Mana = 0, }, },
		[32] = { critChance = 5, baseMultiplier = 2.73, levelRequirement = 90, cost = { Mana = 0, }, },
		[33] = { critChance = 5, baseMultiplier = 2.82, levelRequirement = 90, cost = { Mana = 0, }, },
		[34] = { critChance = 5, baseMultiplier = 2.91, levelRequirement = 90, cost = { Mana = 0, }, },
		[35] = { critChance = 5, baseMultiplier = 3.01, levelRequirement = 90, cost = { Mana = 0, }, },
		[36] = { critChance = 5, baseMultiplier = 3.11, levelRequirement = 90, cost = { Mana = 0, }, },
		[37] = { critChance = 5, baseMultiplier = 3.21, levelRequirement = 90, cost = { Mana = 0, }, },
		[38] = { critChance = 5, baseMultiplier = 3.32, levelRequirement = 90, cost = { Mana = 0, }, },
		[39] = { critChance = 5, baseMultiplier = 3.43, levelRequirement = 90, cost = { Mana = 0, }, },
		[40] = { critChance = 5, baseMultiplier = 3.55, levelRequirement = 90, cost = { Mana = 0, }, },
	},
	statSets = {
		[1] = {
			label = "Poisonous Concoction",
			baseEffectiveness = 3.9000000953674,
			incrementalEffectiveness = 0.27349999547005,
			statDescriptionScope = "throw_flask_poison",
			statMap = {
				["flask_throw_poison_effect_+%_final"] = {
					mod("AilmentMagnitude", "MORE", nil, 0, KeywordFlag.Poison),
				},
			},
			baseFlags = {
				attack = true,
				projectile = true,
				unarmed = true,
				area = true,
			},
			constantStats = {
				{ "flask_throw_base_charges_used", 5 },
				{ "base_number_of_projectiles", 1 },
				{ "active_skill_base_area_of_effect_radius", 15 },
				{ "flask_throw_poison_effect_+%_final", 200 },
				{ "throw_flask_effects_type", 4 },
				{ "movement_speed_+%_final_while_performing_action", -70 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 60 },
				{ "main_hand_base_weapon_attack_duration_ms", 714 },
				{ "base_chance_to_poison_on_hit_%", 100 },
			},
			stats = {
				"main_hand_weapon_minimum_physical_damage",
				"main_hand_weapon_maximum_physical_damage",
				"base_is_projectile",
				"projectile_behaviour_only_explode",
				"can_perform_skill_while_moving",
				"replace_main_hand_unarmed_attack_stats_with_nothing_type",
				"is_area_damage",
			},
			levels = {
				[1] = { 12, 18, statInterpolation = { 1, 1, }, actorLevel = 1, },
				[2] = { 20, 30, statInterpolation = { 1, 1, }, actorLevel = 3.4519999027252, },
				[3] = { 31, 47, statInterpolation = { 1, 1, }, actorLevel = 6.7670001983643, },
				[4] = { 43, 64, statInterpolation = { 1, 1, }, actorLevel = 10.307999610901, },
				[5] = { 55, 83, statInterpolation = { 1, 1, }, actorLevel = 14.074999809265, },
				[6] = { 69, 103, statInterpolation = { 1, 1, }, actorLevel = 18.068000793457, },
				[7] = { 83, 124, statInterpolation = { 1, 1, }, actorLevel = 22.287000656128, },
				[8] = { 97, 146, statInterpolation = { 1, 1, }, actorLevel = 26.732000350952, },
				[9] = { 113, 169, statInterpolation = { 1, 1, }, actorLevel = 31.40299987793, },
				[10] = { 129, 194, statInterpolation = { 1, 1, }, actorLevel = 36.299999237061, },
				[11] = { 146, 219, statInterpolation = { 1, 1, }, actorLevel = 41.423000335693, },
				[12] = { 164, 246, statInterpolation = { 1, 1, }, actorLevel = 46.771999359131, },
				[13] = { 182, 274, statInterpolation = { 1, 1, }, actorLevel = 52.34700012207, },
				[14] = { 202, 302, statInterpolation = { 1, 1, }, actorLevel = 58.147998809814, },
				[15] = { 222, 332, statInterpolation = { 1, 1, }, actorLevel = 64.175003051758, },
				[16] = { 242, 363, statInterpolation = { 1, 1, }, actorLevel = 70.428001403809, },
				[17] = { 264, 396, statInterpolation = { 1, 1, }, actorLevel = 76.906997680664, },
				[18] = { 286, 429, statInterpolation = { 1, 1, }, actorLevel = 83.611999511719, },
				[19] = { 309, 463, statInterpolation = { 1, 1, }, actorLevel = 90.542999267578, },
				[20] = { 333, 499, statInterpolation = { 1, 1, }, actorLevel = 97.699996948242, },
				[21] = { 357, 536, statInterpolation = { 1, 1, }, actorLevel = 105.08300018311, },
				[22] = { 382, 574, statInterpolation = { 1, 1, }, actorLevel = 112.69200134277, },
				[23] = { 408, 613, statInterpolation = { 1, 1, }, actorLevel = 120.52700042725, },
				[24] = { 435, 653, statInterpolation = { 1, 1, }, actorLevel = 128.58799743652, },
				[25] = { 463, 694, statInterpolation = { 1, 1, }, actorLevel = 136.875, },
				[26] = { 491, 736, statInterpolation = { 1, 1, }, actorLevel = 145.38800048828, },
				[27] = { 520, 780, statInterpolation = { 1, 1, }, actorLevel = 154.12699890137, },
				[28] = { 550, 824, statInterpolation = { 1, 1, }, actorLevel = 163.09199523926, },
				[29] = { 580, 870, statInterpolation = { 1, 1, }, actorLevel = 172.28300476074, },
				[30] = { 611, 917, statInterpolation = { 1, 1, }, actorLevel = 181.69999694824, },
				[31] = { 643, 965, statInterpolation = { 1, 1, }, actorLevel = 191.34300231934, },
				[32] = { 676, 1014, statInterpolation = { 1, 1, }, actorLevel = 201.21200561523, },
				[33] = { 709, 1064, statInterpolation = { 1, 1, }, actorLevel = 211.30700683594, },
				[34] = { 744, 1115, statInterpolation = { 1, 1, }, actorLevel = 221.62800598145, },
				[35] = { 779, 1168, statInterpolation = { 1, 1, }, actorLevel = 232.17500305176, },
				[36] = { 814, 1221, statInterpolation = { 1, 1, }, actorLevel = 242.94799804688, },
				[37] = { 851, 1276, statInterpolation = { 1, 1, }, actorLevel = 253.94700622559, },
				[38] = { 888, 1332, statInterpolation = { 1, 1, }, actorLevel = 265.17199707031, },
				[39] = { 926, 1389, statInterpolation = { 1, 1, }, actorLevel = 276.62298583984, },
				[40] = { 965, 1447, statInterpolation = { 1, 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["MeleeUnarmedPlayer"] = {
	name = "Punch",
	hidden = true,
	color = 4,
	description = "Perform an Unarmed Strike.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.Area] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { baseMultiplier = 1.1, levelRequirement = 3, },
		[3] = { baseMultiplier = 1.21, levelRequirement = 6, },
		[4] = { baseMultiplier = 1.32, levelRequirement = 10, },
		[5] = { baseMultiplier = 1.42, levelRequirement = 14, },
		[6] = { baseMultiplier = 1.53, levelRequirement = 18, },
		[7] = { baseMultiplier = 1.64, levelRequirement = 22, },
		[8] = { baseMultiplier = 1.74, levelRequirement = 26, },
		[9] = { baseMultiplier = 1.82, levelRequirement = 31, },
		[10] = { baseMultiplier = 1.91, levelRequirement = 36, },
		[11] = { baseMultiplier = 2.01, levelRequirement = 41, },
		[12] = { baseMultiplier = 2.11, levelRequirement = 46, },
		[13] = { baseMultiplier = 2.22, levelRequirement = 52, },
		[14] = { baseMultiplier = 2.33, levelRequirement = 58, },
		[15] = { baseMultiplier = 2.44, levelRequirement = 64, },
		[16] = { baseMultiplier = 2.57, levelRequirement = 66, },
		[17] = { baseMultiplier = 2.69, levelRequirement = 72, },
		[18] = { baseMultiplier = 2.83, levelRequirement = 78, },
		[19] = { baseMultiplier = 2.97, levelRequirement = 84, },
		[20] = { baseMultiplier = 3.12, levelRequirement = 90, },
		[21] = { baseMultiplier = 3.27, levelRequirement = 90, },
		[22] = { baseMultiplier = 3.44, levelRequirement = 90, },
		[23] = { baseMultiplier = 3.61, levelRequirement = 90, },
		[24] = { baseMultiplier = 3.79, levelRequirement = 90, },
		[25] = { baseMultiplier = 3.98, levelRequirement = 90, },
		[26] = { baseMultiplier = 4.18, levelRequirement = 90, },
		[27] = { baseMultiplier = 4.39, levelRequirement = 90, },
		[28] = { baseMultiplier = 4.61, levelRequirement = 90, },
		[29] = { baseMultiplier = 4.84, levelRequirement = 90, },
		[30] = { baseMultiplier = 5.08, levelRequirement = 90, },
		[31] = { baseMultiplier = 5.33, levelRequirement = 90, },
		[32] = { baseMultiplier = 5.6, levelRequirement = 90, },
		[33] = { baseMultiplier = 5.88, levelRequirement = 90, },
		[34] = { baseMultiplier = 6.18, levelRequirement = 90, },
		[35] = { baseMultiplier = 6.48, levelRequirement = 90, },
		[36] = { baseMultiplier = 6.81, levelRequirement = 90, },
		[37] = { baseMultiplier = 7.15, levelRequirement = 90, },
		[38] = { baseMultiplier = 7.51, levelRequirement = 90, },
		[39] = { baseMultiplier = 7.88, levelRequirement = 90, },
		[40] = { baseMultiplier = 8.28, levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Punch",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				area = true,
				melee = true,
			},
			constantStats = {
				{ "melee_conditional_step_distance", 10 },
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["MeleeQuarterstaffPlayer"] = {
	name = "Quarterstaff Strike",
	baseTypeName = "Quarterstaff Strike",
	fromItem = true,
	color = 4,
	description = "Strike with your Quarterstaff.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.Area] = true, [SkillType.QuarterstaffSkill] = true, },
	weaponTypes = {
		["Staff"] = true,
	},
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { baseMultiplier = 1.1, levelRequirement = 3, },
		[3] = { baseMultiplier = 1.21, levelRequirement = 6, },
		[4] = { baseMultiplier = 1.32, levelRequirement = 10, },
		[5] = { baseMultiplier = 1.42, levelRequirement = 14, },
		[6] = { baseMultiplier = 1.53, levelRequirement = 18, },
		[7] = { baseMultiplier = 1.64, levelRequirement = 22, },
		[8] = { baseMultiplier = 1.74, levelRequirement = 26, },
		[9] = { baseMultiplier = 1.82, levelRequirement = 31, },
		[10] = { baseMultiplier = 1.91, levelRequirement = 36, },
		[11] = { baseMultiplier = 2.01, levelRequirement = 41, },
		[12] = { baseMultiplier = 2.11, levelRequirement = 46, },
		[13] = { baseMultiplier = 2.22, levelRequirement = 52, },
		[14] = { baseMultiplier = 2.33, levelRequirement = 58, },
		[15] = { baseMultiplier = 2.44, levelRequirement = 64, },
		[16] = { baseMultiplier = 2.57, levelRequirement = 66, },
		[17] = { baseMultiplier = 2.69, levelRequirement = 72, },
		[18] = { baseMultiplier = 2.83, levelRequirement = 78, },
		[19] = { baseMultiplier = 2.97, levelRequirement = 84, },
		[20] = { baseMultiplier = 3.12, levelRequirement = 90, },
		[21] = { baseMultiplier = 3.27, levelRequirement = 90, },
		[22] = { baseMultiplier = 3.44, levelRequirement = 90, },
		[23] = { baseMultiplier = 3.61, levelRequirement = 90, },
		[24] = { baseMultiplier = 3.79, levelRequirement = 90, },
		[25] = { baseMultiplier = 3.98, levelRequirement = 90, },
		[26] = { baseMultiplier = 4.18, levelRequirement = 90, },
		[27] = { baseMultiplier = 4.39, levelRequirement = 90, },
		[28] = { baseMultiplier = 4.61, levelRequirement = 90, },
		[29] = { baseMultiplier = 4.84, levelRequirement = 90, },
		[30] = { baseMultiplier = 5.08, levelRequirement = 90, },
		[31] = { baseMultiplier = 5.33, levelRequirement = 90, },
		[32] = { baseMultiplier = 5.6, levelRequirement = 90, },
		[33] = { baseMultiplier = 5.88, levelRequirement = 90, },
		[34] = { baseMultiplier = 6.18, levelRequirement = 90, },
		[35] = { baseMultiplier = 6.48, levelRequirement = 90, },
		[36] = { baseMultiplier = 6.81, levelRequirement = 90, },
		[37] = { baseMultiplier = 7.15, levelRequirement = 90, },
		[38] = { baseMultiplier = 7.51, levelRequirement = 90, },
		[39] = { baseMultiplier = 7.88, levelRequirement = 90, },
		[40] = { baseMultiplier = 8.28, levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Quarterstaff Strike",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
				area = true,
			},
			constantStats = {
				{ "melee_conditional_step_distance", 10 },
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["ShieldBlockPlayer"] = {
	name = "Raise Shield",
	baseTypeName = "Raise Shield",
	fromItem = true,
	color = 4,
	description = "Raise your Shield to Block all incoming Blockable hits. Release immediately after Blocking to perform a Shield Bash that damages and Stuns enemies. While your Shield is raised you cannot be Light Stunned by hits you Block, but Blocking too much damage may Heavy Stun you. You cannot Evade while your Shield is raised, but Evasion instead grants an equal chance to avoid this Heavy Stun buildup.",
	skillTypes = { [SkillType.Channel] = true, [SkillType.RequiresShield] = true, [SkillType.UsableWhileMoving] = true, [SkillType.Attack] = true, [SkillType.Area] = true, [SkillType.Physical] = true, [SkillType.Melee] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { attackTime = 550, baseMultiplier = 0.8, critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Raise Shield",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "shield_block",
			baseFlags = {
				attack = true,
				area = true,
				shieldAttack = true,
				melee = true,
			},
			constantStats = {
				{ "movement_speed_+%_final_while_performing_action", -70 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 60 },
				{ "channel_skill_end_animation_duration_override_ms", 35 },
				{ "active_block_light_stun_threshold_+%_final", 50 },
				{ "active_skill_heavy_stun_decay_after_action_delay_ms", 2000 },
				{ "skill_animation_duration_multiplier_override", 4 },
				{ "base_minimum_channel_time_ms", 250 },
				{ "raise_shield_base_retaliation_duration_ms", 1000 },
				{ "raise_shield_blocked_projectile_distance", 10 },
				{ "active_skill_hit_damage_stun_multiplier_+%_final", 600 },
				{ "melee_strike_bonus_attack_distance", 14 },
				{ "off_hand_minimum_added_physical_damage_per_5_shield_armour", 3 },
				{ "off_hand_maximum_added_physical_damage_per_5_shield_armour", 4 },
				{ "active_skill_override_turn_duration_ms", 100 },
			},
			stats = {
				"can_perform_skill_while_moving",
				"global_always_hit",
				"active_skill_does_not_decay_heavy_stun_during_action",
				"skill_is_not_considered_a_skill",
				"always_light_stun",
				"base_skill_show_average_damage_instead_of_dps",
				"replace_off_hand_unarmed_attack_stats_with_shield_type",
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["RitualSacrificePlayer"] = {
	name = "Ritual Sacrifice",
	baseTypeName = "Ritual Sacrifice",
	fromTree = true,
	color = 4,
	description = "Sacrifice the Corpse of a Rare Monster to grant you its Modifiers for a duration.",
	skillTypes = { [SkillType.Buff] = true, [SkillType.Duration] = true, [SkillType.Cooldown] = true, [SkillType.TargetsDestructibleRareCorpses] = true, },
	castTime = 5,
	qualityStats = {
		{ "base_skill_effect_duration", 100 },
	},
	levels = {
		[1] = { levelRequirement = 0, cost = { Mana = 20, }, },
		[2] = { levelRequirement = 3, cost = { Mana = 20, }, },
		[3] = { levelRequirement = 6, cost = { Mana = 20, }, },
		[4] = { levelRequirement = 10, cost = { Mana = 20, }, },
		[5] = { levelRequirement = 14, cost = { Mana = 20, }, },
		[6] = { levelRequirement = 18, cost = { Mana = 20, }, },
		[7] = { levelRequirement = 22, cost = { Mana = 20, }, },
		[8] = { levelRequirement = 26, cost = { Mana = 20, }, },
		[9] = { levelRequirement = 31, cost = { Mana = 20, }, },
		[10] = { levelRequirement = 36, cost = { Mana = 20, }, },
		[11] = { levelRequirement = 41, cost = { Mana = 20, }, },
		[12] = { levelRequirement = 46, cost = { Mana = 20, }, },
		[13] = { levelRequirement = 52, cost = { Mana = 20, }, },
		[14] = { levelRequirement = 58, cost = { Mana = 20, }, },
		[15] = { levelRequirement = 64, cost = { Mana = 20, }, },
		[16] = { levelRequirement = 66, cost = { Mana = 20, }, },
		[17] = { levelRequirement = 72, cost = { Mana = 20, }, },
		[18] = { levelRequirement = 78, cost = { Mana = 20, }, },
		[19] = { levelRequirement = 84, cost = { Mana = 20, }, },
		[20] = { storedUses = 1, levelRequirement = 90, cooldown = 8, cost = { Mana = 20, }, },
		[21] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[22] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[23] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[24] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[25] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[26] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[27] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[28] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[29] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[30] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[31] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[32] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[33] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[34] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[35] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[36] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[37] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[38] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[39] = { levelRequirement = 90, cost = { Mana = 20, }, },
		[40] = { levelRequirement = 90, cost = { Mana = 20, }, },
	},
	statSets = {
		[1] = {
			label = "Ritual Sacrifice",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "ritual_sacrifice",
			baseFlags = {
				attack = true,
				area = true,
			},
			constantStats = {
				{ "head_hunt_%_life_to_remove_on_self_stab", 20 },
				{ "movement_speed_+%_final_while_performing_action", -70 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 60 },
			},
			stats = {
				"base_skill_effect_duration",
				"can_perform_skill_while_moving",
				"quality_display_base_skill_effect_duration_is_gem",
			},
			levels = {
				[1] = { 15000, statInterpolation = { 1, }, actorLevel = 1, },
				[2] = { 15250, statInterpolation = { 1, }, actorLevel = 3.4519999027252, },
				[3] = { 15500, statInterpolation = { 1, }, actorLevel = 6.7670001983643, },
				[4] = { 15750, statInterpolation = { 1, }, actorLevel = 10.307999610901, },
				[5] = { 16000, statInterpolation = { 1, }, actorLevel = 14.074999809265, },
				[6] = { 16250, statInterpolation = { 1, }, actorLevel = 18.068000793457, },
				[7] = { 16500, statInterpolation = { 1, }, actorLevel = 22.287000656128, },
				[8] = { 16750, statInterpolation = { 1, }, actorLevel = 26.732000350952, },
				[9] = { 17000, statInterpolation = { 1, }, actorLevel = 31.40299987793, },
				[10] = { 17250, statInterpolation = { 1, }, actorLevel = 36.299999237061, },
				[11] = { 17500, statInterpolation = { 1, }, actorLevel = 41.423000335693, },
				[12] = { 17750, statInterpolation = { 1, }, actorLevel = 46.771999359131, },
				[13] = { 18000, statInterpolation = { 1, }, actorLevel = 52.34700012207, },
				[14] = { 18250, statInterpolation = { 1, }, actorLevel = 58.147998809814, },
				[15] = { 18500, statInterpolation = { 1, }, actorLevel = 64.175003051758, },
				[16] = { 18750, statInterpolation = { 1, }, actorLevel = 70.428001403809, },
				[17] = { 19000, statInterpolation = { 1, }, actorLevel = 76.906997680664, },
				[18] = { 19250, statInterpolation = { 1, }, actorLevel = 83.611999511719, },
				[19] = { 19500, statInterpolation = { 1, }, actorLevel = 90.542999267578, },
				[20] = { 19750, statInterpolation = { 1, }, actorLevel = 97.699996948242, },
				[21] = { 20000, statInterpolation = { 1, }, actorLevel = 105.08300018311, },
				[22] = { 20250, statInterpolation = { 1, }, actorLevel = 112.69200134277, },
				[23] = { 20500, statInterpolation = { 1, }, actorLevel = 120.52700042725, },
				[24] = { 20750, statInterpolation = { 1, }, actorLevel = 128.58799743652, },
				[25] = { 21000, statInterpolation = { 1, }, actorLevel = 136.875, },
				[26] = { 21250, statInterpolation = { 1, }, actorLevel = 145.38800048828, },
				[27] = { 21500, statInterpolation = { 1, }, actorLevel = 154.12699890137, },
				[28] = { 21750, statInterpolation = { 1, }, actorLevel = 163.09199523926, },
				[29] = { 22000, statInterpolation = { 1, }, actorLevel = 172.28300476074, },
				[30] = { 22250, statInterpolation = { 1, }, actorLevel = 181.69999694824, },
				[31] = { 22375, statInterpolation = { 1, }, actorLevel = 191.34300231934, },
				[32] = { 22500, statInterpolation = { 1, }, actorLevel = 201.21200561523, },
				[33] = { 22625, statInterpolation = { 1, }, actorLevel = 211.30700683594, },
				[34] = { 22750, statInterpolation = { 1, }, actorLevel = 221.62800598145, },
				[35] = { 22875, statInterpolation = { 1, }, actorLevel = 232.17500305176, },
				[36] = { 23000, statInterpolation = { 1, }, actorLevel = 242.94799804688, },
				[37] = { 23125, statInterpolation = { 1, }, actorLevel = 253.94700622559, },
				[38] = { 23250, statInterpolation = { 1, }, actorLevel = 265.17199707031, },
				[39] = { 23375, statInterpolation = { 1, }, actorLevel = 276.62298583984, },
				[40] = { 23500, statInterpolation = { 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["ShatteringConcoctionPlayer"] = {
	name = "Shattering Concoction",
	baseTypeName = "Shattering Concoction",
	fromTree = true,
	color = 4,
	description = "Consume charges from your Mana Flask to throw a bottle that explodes, dealing Cold attack damage in an area and inflicting Cold Exposure.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Area] = true, [SkillType.Projectile] = true, [SkillType.Duration] = true, [SkillType.Cold] = true, [SkillType.UsableWhileMoving] = true, [SkillType.ProjectileNoCollision] = true, },
	weaponTypes = {
		["None"] = true,
	},
	castTime = 1,
	qualityStats = {
		{ "cold_exposure_effect_+%", 1 },
	},
	levels = {
		[1] = { critChance = 11, baseMultiplier = 0.7, levelRequirement = 0, cost = { Mana = 0, }, },
		[2] = { critChance = 11, baseMultiplier = 0.77, levelRequirement = 3, cost = { Mana = 0, }, },
		[3] = { critChance = 11, baseMultiplier = 0.84, levelRequirement = 6, cost = { Mana = 0, }, },
		[4] = { critChance = 11, baseMultiplier = 0.92, levelRequirement = 10, cost = { Mana = 0, }, },
		[5] = { critChance = 11, baseMultiplier = 0.99, levelRequirement = 14, cost = { Mana = 0, }, },
		[6] = { critChance = 11, baseMultiplier = 1.05, levelRequirement = 18, cost = { Mana = 0, }, },
		[7] = { critChance = 11, baseMultiplier = 1.12, levelRequirement = 22, cost = { Mana = 0, }, },
		[8] = { critChance = 11, baseMultiplier = 1.18, levelRequirement = 26, cost = { Mana = 0, }, },
		[9] = { critChance = 11, baseMultiplier = 1.23, levelRequirement = 31, cost = { Mana = 0, }, },
		[10] = { critChance = 11, baseMultiplier = 1.28, levelRequirement = 36, cost = { Mana = 0, }, },
		[11] = { critChance = 11, baseMultiplier = 1.34, levelRequirement = 41, cost = { Mana = 0, }, },
		[12] = { critChance = 11, baseMultiplier = 1.39, levelRequirement = 46, cost = { Mana = 0, }, },
		[13] = { critChance = 11, baseMultiplier = 1.44, levelRequirement = 52, cost = { Mana = 0, }, },
		[14] = { critChance = 11, baseMultiplier = 1.5, levelRequirement = 58, cost = { Mana = 0, }, },
		[15] = { critChance = 11, baseMultiplier = 1.55, levelRequirement = 64, cost = { Mana = 0, }, },
		[16] = { critChance = 11, baseMultiplier = 1.61, levelRequirement = 66, cost = { Mana = 0, }, },
		[17] = { critChance = 11, baseMultiplier = 1.67, levelRequirement = 72, cost = { Mana = 0, }, },
		[18] = { critChance = 11, baseMultiplier = 1.73, levelRequirement = 78, cost = { Mana = 0, }, },
		[19] = { critChance = 11, baseMultiplier = 1.79, levelRequirement = 84, cost = { Mana = 0, }, },
		[20] = { critChance = 11, baseMultiplier = 1.85, levelRequirement = 90, cost = { Mana = 0, }, },
		[21] = { critChance = 11, baseMultiplier = 1.91, levelRequirement = 90, cost = { Mana = 0, }, },
		[22] = { critChance = 11, baseMultiplier = 1.97, levelRequirement = 90, cost = { Mana = 0, }, },
		[23] = { critChance = 11, baseMultiplier = 2.04, levelRequirement = 90, cost = { Mana = 0, }, },
		[24] = { critChance = 11, baseMultiplier = 2.1, levelRequirement = 90, cost = { Mana = 0, }, },
		[25] = { critChance = 11, baseMultiplier = 2.17, levelRequirement = 90, cost = { Mana = 0, }, },
		[26] = { critChance = 11, baseMultiplier = 2.24, levelRequirement = 90, cost = { Mana = 0, }, },
		[27] = { critChance = 11, baseMultiplier = 2.32, levelRequirement = 90, cost = { Mana = 0, }, },
		[28] = { critChance = 11, baseMultiplier = 2.4, levelRequirement = 90, cost = { Mana = 0, }, },
		[29] = { critChance = 11, baseMultiplier = 2.48, levelRequirement = 90, cost = { Mana = 0, }, },
		[30] = { critChance = 11, baseMultiplier = 2.56, levelRequirement = 90, cost = { Mana = 0, }, },
		[31] = { critChance = 11, baseMultiplier = 2.64, levelRequirement = 90, cost = { Mana = 0, }, },
		[32] = { critChance = 11, baseMultiplier = 2.73, levelRequirement = 90, cost = { Mana = 0, }, },
		[33] = { critChance = 11, baseMultiplier = 2.82, levelRequirement = 90, cost = { Mana = 0, }, },
		[34] = { critChance = 11, baseMultiplier = 2.91, levelRequirement = 90, cost = { Mana = 0, }, },
		[35] = { critChance = 11, baseMultiplier = 3.01, levelRequirement = 90, cost = { Mana = 0, }, },
		[36] = { critChance = 11, baseMultiplier = 3.11, levelRequirement = 90, cost = { Mana = 0, }, },
		[37] = { critChance = 11, baseMultiplier = 3.21, levelRequirement = 90, cost = { Mana = 0, }, },
		[38] = { critChance = 11, baseMultiplier = 3.32, levelRequirement = 90, cost = { Mana = 0, }, },
		[39] = { critChance = 11, baseMultiplier = 3.43, levelRequirement = 90, cost = { Mana = 0, }, },
		[40] = { critChance = 11, baseMultiplier = 3.55, levelRequirement = 90, cost = { Mana = 0, }, },
	},
	statSets = {
		[1] = {
			label = "Shattering Concoction",
			baseEffectiveness = 3.9000000953674,
			incrementalEffectiveness = 0.27349999547005,
			statDescriptionScope = "throw_flask_cold",
			statMap = {
				["flask_throw_cold_exposure_ms"] = {
					mod("ColdExposureChance", "BASE", nil),
					value = 100,
				},
			},
			baseFlags = {
				attack = true,
				projectile = true,
				duration = true,
				unarmed = true,
				area = true,
			},
			constantStats = {
				{ "flask_throw_base_charges_used", 5 },
				{ "base_number_of_projectiles", 1 },
				{ "active_skill_base_area_of_effect_radius", 15 },
				{ "flask_throw_cold_exposure_ms", 4000 },
				{ "throw_flask_effects_type", 1 },
				{ "movement_speed_+%_final_while_performing_action", -70 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 60 },
				{ "main_hand_base_weapon_attack_duration_ms", 714 },
				{ "active_skill_hit_damage_freeze_multiplier_+%_final", 200 },
			},
			stats = {
				"main_hand_weapon_minimum_cold_damage",
				"main_hand_weapon_maximum_cold_damage",
				"base_is_projectile",
				"projectile_behaviour_only_explode",
				"can_perform_skill_while_moving",
				"replace_main_hand_unarmed_attack_stats_with_nothing_type",
				"is_area_damage",
			},
			levels = {
				[1] = { 12, 18, statInterpolation = { 1, 1, }, actorLevel = 1, },
				[2] = { 20, 30, statInterpolation = { 1, 1, }, actorLevel = 3.4519999027252, },
				[3] = { 31, 47, statInterpolation = { 1, 1, }, actorLevel = 6.7670001983643, },
				[4] = { 43, 64, statInterpolation = { 1, 1, }, actorLevel = 10.307999610901, },
				[5] = { 55, 83, statInterpolation = { 1, 1, }, actorLevel = 14.074999809265, },
				[6] = { 69, 103, statInterpolation = { 1, 1, }, actorLevel = 18.068000793457, },
				[7] = { 83, 124, statInterpolation = { 1, 1, }, actorLevel = 22.287000656128, },
				[8] = { 97, 146, statInterpolation = { 1, 1, }, actorLevel = 26.732000350952, },
				[9] = { 113, 169, statInterpolation = { 1, 1, }, actorLevel = 31.40299987793, },
				[10] = { 129, 194, statInterpolation = { 1, 1, }, actorLevel = 36.299999237061, },
				[11] = { 146, 219, statInterpolation = { 1, 1, }, actorLevel = 41.423000335693, },
				[12] = { 164, 246, statInterpolation = { 1, 1, }, actorLevel = 46.771999359131, },
				[13] = { 182, 274, statInterpolation = { 1, 1, }, actorLevel = 52.34700012207, },
				[14] = { 202, 302, statInterpolation = { 1, 1, }, actorLevel = 58.147998809814, },
				[15] = { 222, 332, statInterpolation = { 1, 1, }, actorLevel = 64.175003051758, },
				[16] = { 242, 363, statInterpolation = { 1, 1, }, actorLevel = 70.428001403809, },
				[17] = { 264, 396, statInterpolation = { 1, 1, }, actorLevel = 76.906997680664, },
				[18] = { 286, 429, statInterpolation = { 1, 1, }, actorLevel = 83.611999511719, },
				[19] = { 309, 463, statInterpolation = { 1, 1, }, actorLevel = 90.542999267578, },
				[20] = { 333, 499, statInterpolation = { 1, 1, }, actorLevel = 97.699996948242, },
				[21] = { 357, 536, statInterpolation = { 1, 1, }, actorLevel = 105.08300018311, },
				[22] = { 382, 574, statInterpolation = { 1, 1, }, actorLevel = 112.69200134277, },
				[23] = { 408, 613, statInterpolation = { 1, 1, }, actorLevel = 120.52700042725, },
				[24] = { 435, 653, statInterpolation = { 1, 1, }, actorLevel = 128.58799743652, },
				[25] = { 463, 694, statInterpolation = { 1, 1, }, actorLevel = 136.875, },
				[26] = { 491, 736, statInterpolation = { 1, 1, }, actorLevel = 145.38800048828, },
				[27] = { 520, 780, statInterpolation = { 1, 1, }, actorLevel = 154.12699890137, },
				[28] = { 550, 824, statInterpolation = { 1, 1, }, actorLevel = 163.09199523926, },
				[29] = { 580, 870, statInterpolation = { 1, 1, }, actorLevel = 172.28300476074, },
				[30] = { 611, 917, statInterpolation = { 1, 1, }, actorLevel = 181.69999694824, },
				[31] = { 643, 965, statInterpolation = { 1, 1, }, actorLevel = 191.34300231934, },
				[32] = { 676, 1014, statInterpolation = { 1, 1, }, actorLevel = 201.21200561523, },
				[33] = { 709, 1064, statInterpolation = { 1, 1, }, actorLevel = 211.30700683594, },
				[34] = { 744, 1115, statInterpolation = { 1, 1, }, actorLevel = 221.62800598145, },
				[35] = { 779, 1168, statInterpolation = { 1, 1, }, actorLevel = 232.17500305176, },
				[36] = { 814, 1221, statInterpolation = { 1, 1, }, actorLevel = 242.94799804688, },
				[37] = { 851, 1276, statInterpolation = { 1, 1, }, actorLevel = 253.94700622559, },
				[38] = { 888, 1332, statInterpolation = { 1, 1, }, actorLevel = 265.17199707031, },
				[39] = { 926, 1389, statInterpolation = { 1, 1, }, actorLevel = 276.62298583984, },
				[40] = { 965, 1447, statInterpolation = { 1, 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["SorceryWardPlayer"] = {
	name = "Sorcery Ward",
	baseTypeName = "Sorcery Ward",
	fromTree = true,
	color = 4,
	description = "Passively manifests a protective barrier which takes Elemental Damage from Hits for you until depleted. The barrier instantly recharges to its full value a short time after it stops taking damage or is fully depleted.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Aegis] = true, [SkillType.Fire] = true, [SkillType.Cold] = true, [SkillType.Lightning] = true, [SkillType.Buff] = true, [SkillType.OngoingSkill] = true, [SkillType.Persistent] = true, [SkillType.HasReservation] = true, },
	castTime = 1,
	qualityStats = {
		{ "aegis_unique_shield_max_value_from_%_armour_evasion", 0.5 },
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { levelRequirement = 3, },
		[3] = { levelRequirement = 6, },
		[4] = { levelRequirement = 10, },
		[5] = { levelRequirement = 14, },
		[6] = { levelRequirement = 18, },
		[7] = { levelRequirement = 22, },
		[8] = { levelRequirement = 26, },
		[9] = { levelRequirement = 31, },
		[10] = { levelRequirement = 36, },
		[11] = { levelRequirement = 41, },
		[12] = { levelRequirement = 46, },
		[13] = { levelRequirement = 52, },
		[14] = { levelRequirement = 58, },
		[15] = { levelRequirement = 64, },
		[16] = { levelRequirement = 66, },
		[17] = { levelRequirement = 72, },
		[18] = { levelRequirement = 78, },
		[19] = { levelRequirement = 84, },
		[20] = { levelRequirement = 90, },
		[21] = { levelRequirement = 90, },
		[22] = { levelRequirement = 90, },
		[23] = { levelRequirement = 90, },
		[24] = { levelRequirement = 90, },
		[25] = { levelRequirement = 90, },
		[26] = { levelRequirement = 90, },
		[27] = { levelRequirement = 90, },
		[28] = { levelRequirement = 90, },
		[29] = { levelRequirement = 90, },
		[30] = { levelRequirement = 90, },
		[31] = { levelRequirement = 90, },
		[32] = { levelRequirement = 90, },
		[33] = { levelRequirement = 90, },
		[34] = { levelRequirement = 90, },
		[35] = { levelRequirement = 90, },
		[36] = { levelRequirement = 90, },
		[37] = { levelRequirement = 90, },
		[38] = { levelRequirement = 90, },
		[39] = { levelRequirement = 90, },
		[40] = { levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Sorcery Ward",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "sorcery_ward",
			baseFlags = {
			},
			constantStats = {
				{ "skill_desired_amount_override", 1 },
				{ "active_skill_display_aegis_variation", 4 },
				{ "aegis_unique_shield_max_value_from_%_armour_evasion", 30 },
			},
			stats = {
				"aegis_recharge_delay_ms",
				"base_deal_no_damage",
				"display_statset_hide_usage_stats",
			},
			levels = {
				[1] = { 8000, statInterpolation = { 1, }, actorLevel = 1, },
				[2] = { 7900, statInterpolation = { 1, }, actorLevel = 3.4519999027252, },
				[3] = { 7800, statInterpolation = { 1, }, actorLevel = 6.7670001983643, },
				[4] = { 7700, statInterpolation = { 1, }, actorLevel = 10.307999610901, },
				[5] = { 7600, statInterpolation = { 1, }, actorLevel = 14.074999809265, },
				[6] = { 7500, statInterpolation = { 1, }, actorLevel = 18.068000793457, },
				[7] = { 7400, statInterpolation = { 1, }, actorLevel = 22.287000656128, },
				[8] = { 7300, statInterpolation = { 1, }, actorLevel = 26.732000350952, },
				[9] = { 7200, statInterpolation = { 1, }, actorLevel = 31.40299987793, },
				[10] = { 7100, statInterpolation = { 1, }, actorLevel = 36.299999237061, },
				[11] = { 7000, statInterpolation = { 1, }, actorLevel = 41.423000335693, },
				[12] = { 6900, statInterpolation = { 1, }, actorLevel = 46.771999359131, },
				[13] = { 6800, statInterpolation = { 1, }, actorLevel = 52.34700012207, },
				[14] = { 6700, statInterpolation = { 1, }, actorLevel = 58.147998809814, },
				[15] = { 6600, statInterpolation = { 1, }, actorLevel = 64.175003051758, },
				[16] = { 6500, statInterpolation = { 1, }, actorLevel = 70.428001403809, },
				[17] = { 6400, statInterpolation = { 1, }, actorLevel = 76.906997680664, },
				[18] = { 6300, statInterpolation = { 1, }, actorLevel = 83.611999511719, },
				[19] = { 6200, statInterpolation = { 1, }, actorLevel = 90.542999267578, },
				[20] = { 6100, statInterpolation = { 1, }, actorLevel = 97.699996948242, },
				[21] = { 6000, statInterpolation = { 1, }, actorLevel = 105.08300018311, },
				[22] = { 5900, statInterpolation = { 1, }, actorLevel = 112.69200134277, },
				[23] = { 5800, statInterpolation = { 1, }, actorLevel = 120.52700042725, },
				[24] = { 5700, statInterpolation = { 1, }, actorLevel = 128.58799743652, },
				[25] = { 5600, statInterpolation = { 1, }, actorLevel = 136.875, },
				[26] = { 5500, statInterpolation = { 1, }, actorLevel = 145.38800048828, },
				[27] = { 5400, statInterpolation = { 1, }, actorLevel = 154.12699890137, },
				[28] = { 5300, statInterpolation = { 1, }, actorLevel = 163.09199523926, },
				[29] = { 5200, statInterpolation = { 1, }, actorLevel = 172.28300476074, },
				[30] = { 5100, statInterpolation = { 1, }, actorLevel = 181.69999694824, },
				[31] = { 5050, statInterpolation = { 1, }, actorLevel = 191.34300231934, },
				[32] = { 5000, statInterpolation = { 1, }, actorLevel = 201.21200561523, },
				[33] = { 4950, statInterpolation = { 1, }, actorLevel = 211.30700683594, },
				[34] = { 4900, statInterpolation = { 1, }, actorLevel = 221.62800598145, },
				[35] = { 4850, statInterpolation = { 1, }, actorLevel = 232.17500305176, },
				[36] = { 4800, statInterpolation = { 1, }, actorLevel = 242.94799804688, },
				[37] = { 4750, statInterpolation = { 1, }, actorLevel = 253.94700622559, },
				[38] = { 4700, statInterpolation = { 1, }, actorLevel = 265.17199707031, },
				[39] = { 4650, statInterpolation = { 1, }, actorLevel = 276.62298583984, },
				[40] = { 4600, statInterpolation = { 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["MeleeSpearOffHandPlayer"] = {
	name = "Spear Stab",
	baseTypeName = "Spear Stab",
	fromItem = true,
	color = 4,
	description = "Strike with your Spear.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.Area] = true, [SkillType.Spear] = true, },
	weaponTypes = {
		["Spear"] = true,
	},
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { baseMultiplier = 1.1, levelRequirement = 3, },
		[3] = { baseMultiplier = 1.21, levelRequirement = 6, },
		[4] = { baseMultiplier = 1.32, levelRequirement = 10, },
		[5] = { baseMultiplier = 1.42, levelRequirement = 14, },
		[6] = { baseMultiplier = 1.53, levelRequirement = 18, },
		[7] = { baseMultiplier = 1.64, levelRequirement = 22, },
		[8] = { baseMultiplier = 1.74, levelRequirement = 26, },
		[9] = { baseMultiplier = 1.82, levelRequirement = 31, },
		[10] = { baseMultiplier = 1.91, levelRequirement = 36, },
		[11] = { baseMultiplier = 2.01, levelRequirement = 41, },
		[12] = { baseMultiplier = 2.11, levelRequirement = 46, },
		[13] = { baseMultiplier = 2.22, levelRequirement = 52, },
		[14] = { baseMultiplier = 2.33, levelRequirement = 58, },
		[15] = { baseMultiplier = 2.44, levelRequirement = 64, },
		[16] = { baseMultiplier = 2.57, levelRequirement = 66, },
		[17] = { baseMultiplier = 2.69, levelRequirement = 72, },
		[18] = { baseMultiplier = 2.83, levelRequirement = 78, },
		[19] = { baseMultiplier = 2.97, levelRequirement = 84, },
		[20] = { baseMultiplier = 3.12, levelRequirement = 90, },
		[21] = { baseMultiplier = 3.27, levelRequirement = 90, },
		[22] = { baseMultiplier = 3.44, levelRequirement = 90, },
		[23] = { baseMultiplier = 3.61, levelRequirement = 90, },
		[24] = { baseMultiplier = 3.79, levelRequirement = 90, },
		[25] = { baseMultiplier = 3.98, levelRequirement = 90, },
		[26] = { baseMultiplier = 4.18, levelRequirement = 90, },
		[27] = { baseMultiplier = 4.39, levelRequirement = 90, },
		[28] = { baseMultiplier = 4.61, levelRequirement = 90, },
		[29] = { baseMultiplier = 4.84, levelRequirement = 90, },
		[30] = { baseMultiplier = 5.08, levelRequirement = 90, },
		[31] = { baseMultiplier = 5.33, levelRequirement = 90, },
		[32] = { baseMultiplier = 5.6, levelRequirement = 90, },
		[33] = { baseMultiplier = 5.88, levelRequirement = 90, },
		[34] = { baseMultiplier = 6.18, levelRequirement = 90, },
		[35] = { baseMultiplier = 6.48, levelRequirement = 90, },
		[36] = { baseMultiplier = 6.81, levelRequirement = 90, },
		[37] = { baseMultiplier = 7.15, levelRequirement = 90, },
		[38] = { baseMultiplier = 7.51, levelRequirement = 90, },
		[39] = { baseMultiplier = 7.88, levelRequirement = 90, },
		[40] = { baseMultiplier = 8.28, levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Spear Stab",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
				area = true,
			},
			constantStats = {
				{ "melee_conditional_step_distance", 15 },
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["MeleeSpearPlayer"] = {
	name = "Spear Stab",
	baseTypeName = "Spear Stab",
	fromItem = true,
	color = 4,
	description = "Strike with your Spear.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.Area] = true, [SkillType.Spear] = true, },
	weaponTypes = {
		["Spear"] = true,
	},
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { baseMultiplier = 1.1, levelRequirement = 3, },
		[3] = { baseMultiplier = 1.21, levelRequirement = 6, },
		[4] = { baseMultiplier = 1.32, levelRequirement = 10, },
		[5] = { baseMultiplier = 1.42, levelRequirement = 14, },
		[6] = { baseMultiplier = 1.53, levelRequirement = 18, },
		[7] = { baseMultiplier = 1.64, levelRequirement = 22, },
		[8] = { baseMultiplier = 1.74, levelRequirement = 26, },
		[9] = { baseMultiplier = 1.82, levelRequirement = 31, },
		[10] = { baseMultiplier = 1.91, levelRequirement = 36, },
		[11] = { baseMultiplier = 2.01, levelRequirement = 41, },
		[12] = { baseMultiplier = 2.11, levelRequirement = 46, },
		[13] = { baseMultiplier = 2.22, levelRequirement = 52, },
		[14] = { baseMultiplier = 2.33, levelRequirement = 58, },
		[15] = { baseMultiplier = 2.44, levelRequirement = 64, },
		[16] = { baseMultiplier = 2.57, levelRequirement = 66, },
		[17] = { baseMultiplier = 2.69, levelRequirement = 72, },
		[18] = { baseMultiplier = 2.83, levelRequirement = 78, },
		[19] = { baseMultiplier = 2.97, levelRequirement = 84, },
		[20] = { baseMultiplier = 3.12, levelRequirement = 90, },
		[21] = { baseMultiplier = 3.27, levelRequirement = 90, },
		[22] = { baseMultiplier = 3.44, levelRequirement = 90, },
		[23] = { baseMultiplier = 3.61, levelRequirement = 90, },
		[24] = { baseMultiplier = 3.79, levelRequirement = 90, },
		[25] = { baseMultiplier = 3.98, levelRequirement = 90, },
		[26] = { baseMultiplier = 4.18, levelRequirement = 90, },
		[27] = { baseMultiplier = 4.39, levelRequirement = 90, },
		[28] = { baseMultiplier = 4.61, levelRequirement = 90, },
		[29] = { baseMultiplier = 4.84, levelRequirement = 90, },
		[30] = { baseMultiplier = 5.08, levelRequirement = 90, },
		[31] = { baseMultiplier = 5.33, levelRequirement = 90, },
		[32] = { baseMultiplier = 5.6, levelRequirement = 90, },
		[33] = { baseMultiplier = 5.88, levelRequirement = 90, },
		[34] = { baseMultiplier = 6.18, levelRequirement = 90, },
		[35] = { baseMultiplier = 6.48, levelRequirement = 90, },
		[36] = { baseMultiplier = 6.81, levelRequirement = 90, },
		[37] = { baseMultiplier = 7.15, levelRequirement = 90, },
		[38] = { baseMultiplier = 7.51, levelRequirement = 90, },
		[39] = { baseMultiplier = 7.88, levelRequirement = 90, },
		[40] = { baseMultiplier = 8.28, levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Spear Stab",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
				area = true,
			},
			constantStats = {
				{ "melee_conditional_step_distance", 15 },
				{ "melee_range_+", 4 },
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["SpearThrowPlayer"] = {
	name = "Spear Throw",
	baseTypeName = "Spear Throw",
	fromItem = true,
	color = 4,
	description = "Hurl your Spear with force. Consumes a Frenzy Charge if you have one to cause the Spear to explode at the end of its flight.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.ProjectileSpeed] = true, [SkillType.RangedAttack] = true, [SkillType.UsableWhileMoving] = true, [SkillType.CanRapidFire] = true, [SkillType.Spear] = true, [SkillType.UsableWhileMounted] = true, [SkillType.ConsumesCharges] = true, [SkillType.SkillConsumesFrenzyChargesOnUse] = true, [SkillType.Area] = true, },
	weaponTypes = {
		["Spear"] = true,
	},
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { attackSpeedMultiplier = -30, baseMultiplier = 1.25, levelRequirement = 0, },
		[2] = { attackSpeedMultiplier = -30, baseMultiplier = 1.38, levelRequirement = 3, },
		[3] = { attackSpeedMultiplier = -30, baseMultiplier = 1.51, levelRequirement = 6, },
		[4] = { attackSpeedMultiplier = -30, baseMultiplier = 1.65, levelRequirement = 10, },
		[5] = { attackSpeedMultiplier = -30, baseMultiplier = 1.78, levelRequirement = 14, },
		[6] = { attackSpeedMultiplier = -30, baseMultiplier = 1.91, levelRequirement = 18, },
		[7] = { attackSpeedMultiplier = -30, baseMultiplier = 2.05, levelRequirement = 22, },
		[8] = { attackSpeedMultiplier = -30, baseMultiplier = 2.17, levelRequirement = 26, },
		[9] = { attackSpeedMultiplier = -30, baseMultiplier = 2.28, levelRequirement = 31, },
		[10] = { attackSpeedMultiplier = -30, baseMultiplier = 2.39, levelRequirement = 36, },
		[11] = { attackSpeedMultiplier = -30, baseMultiplier = 2.51, levelRequirement = 41, },
		[12] = { attackSpeedMultiplier = -30, baseMultiplier = 2.64, levelRequirement = 46, },
		[13] = { attackSpeedMultiplier = -30, baseMultiplier = 2.77, levelRequirement = 52, },
		[14] = { attackSpeedMultiplier = -30, baseMultiplier = 2.91, levelRequirement = 58, },
		[15] = { attackSpeedMultiplier = -30, baseMultiplier = 3.05, levelRequirement = 64, },
		[16] = { attackSpeedMultiplier = -30, baseMultiplier = 3.21, levelRequirement = 66, },
		[17] = { attackSpeedMultiplier = -30, baseMultiplier = 3.37, levelRequirement = 72, },
		[18] = { attackSpeedMultiplier = -30, baseMultiplier = 3.54, levelRequirement = 78, },
		[19] = { attackSpeedMultiplier = -30, baseMultiplier = 3.71, levelRequirement = 84, },
		[20] = { attackSpeedMultiplier = -30, baseMultiplier = 3.9, levelRequirement = 90, },
		[21] = { attackSpeedMultiplier = -30, baseMultiplier = 4.09, levelRequirement = 90, },
		[22] = { attackSpeedMultiplier = -30, baseMultiplier = 4.3, levelRequirement = 90, },
		[23] = { attackSpeedMultiplier = -30, baseMultiplier = 4.51, levelRequirement = 90, },
		[24] = { attackSpeedMultiplier = -30, baseMultiplier = 4.74, levelRequirement = 90, },
		[25] = { attackSpeedMultiplier = -30, baseMultiplier = 4.98, levelRequirement = 90, },
		[26] = { attackSpeedMultiplier = -30, baseMultiplier = 5.22, levelRequirement = 90, },
		[27] = { attackSpeedMultiplier = -30, baseMultiplier = 5.49, levelRequirement = 90, },
		[28] = { attackSpeedMultiplier = -30, baseMultiplier = 5.76, levelRequirement = 90, },
		[29] = { attackSpeedMultiplier = -30, baseMultiplier = 6.05, levelRequirement = 90, },
		[30] = { attackSpeedMultiplier = -30, baseMultiplier = 6.35, levelRequirement = 90, },
		[31] = { attackSpeedMultiplier = -30, baseMultiplier = 6.67, levelRequirement = 90, },
		[32] = { attackSpeedMultiplier = -30, baseMultiplier = 7, levelRequirement = 90, },
		[33] = { attackSpeedMultiplier = -30, baseMultiplier = 7.35, levelRequirement = 90, },
		[34] = { attackSpeedMultiplier = -30, baseMultiplier = 7.72, levelRequirement = 90, },
		[35] = { attackSpeedMultiplier = -30, baseMultiplier = 8.11, levelRequirement = 90, },
		[36] = { attackSpeedMultiplier = -30, baseMultiplier = 8.51, levelRequirement = 90, },
		[37] = { attackSpeedMultiplier = -30, baseMultiplier = 8.94, levelRequirement = 90, },
		[38] = { attackSpeedMultiplier = -30, baseMultiplier = 9.38, levelRequirement = 90, },
		[39] = { attackSpeedMultiplier = -30, baseMultiplier = 9.85, levelRequirement = 90, },
		[40] = { attackSpeedMultiplier = -30, baseMultiplier = 10.34, levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Spear Throw",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "player_ranged_spear",
			baseFlags = {
				attack = true,
				projectile = true,
			},
			constantStats = {
				{ "movement_speed_+%_final_while_performing_action", -70 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 60 },
				{ "main_hand_weapon_range_+", 105 },
			},
			stats = {
				"base_is_projectile",
				"projectile_uses_contact_position",
				"projectile_uses_contact_direction",
				"check_for_targets_between_initiator_and_projectile_source",
				"can_perform_skill_while_moving",
				"has_modular_projectiles_enabled",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
		[2] = {
			label = "Explosion",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "player_ranged_spear",
			baseFlags = {
				attack = true,
				projectile = true,
				area = true,
			},
			constantStats = {
				{ "movement_speed_+%_final_while_performing_action", -70 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 60 },
				{ "main_hand_weapon_range_+", 105 },
				{ "active_skill_base_area_of_effect_radius", 20 },
			},
			stats = {
				"base_is_projectile",
				"projectile_uses_contact_position",
				"projectile_uses_contact_direction",
				"check_for_targets_between_initiator_and_projectile_source",
				"can_perform_skill_while_moving",
				"has_modular_projectiles_enabled",
				"is_area_damage",
				"display_statset_hide_usage_stats",
			},
			levels = {
				[1] = { baseMultiplier = 2.32, actorLevel = 1, },
				[2] = { baseMultiplier = 2.55, actorLevel = 3.4519999027252, },
				[3] = { baseMultiplier = 2.81, actorLevel = 6.7670001983643, },
				[4] = { baseMultiplier = 3.06, actorLevel = 10.307999610901, },
				[5] = { baseMultiplier = 3.3, actorLevel = 14.074999809265, },
				[6] = { baseMultiplier = 3.55, actorLevel = 18.068000793457, },
				[7] = { baseMultiplier = 3.8, actorLevel = 22.287000656128, },
				[8] = { baseMultiplier = 4.03, actorLevel = 26.732000350952, },
				[9] = { baseMultiplier = 4.23, actorLevel = 31.40299987793, },
				[10] = { baseMultiplier = 4.44, actorLevel = 36.299999237061, },
				[11] = { baseMultiplier = 4.66, actorLevel = 41.423000335693, },
				[12] = { baseMultiplier = 4.9, actorLevel = 46.771999359131, },
				[13] = { baseMultiplier = 5.14, actorLevel = 52.34700012207, },
				[14] = { baseMultiplier = 5.4, actorLevel = 58.147998809814, },
				[15] = { baseMultiplier = 5.67, actorLevel = 64.175003051758, },
				[16] = { baseMultiplier = 5.95, actorLevel = 70.428001403809, },
				[17] = { baseMultiplier = 6.25, actorLevel = 76.906997680664, },
				[18] = { baseMultiplier = 6.56, actorLevel = 83.611999511719, },
				[19] = { baseMultiplier = 6.89, actorLevel = 90.542999267578, },
				[20] = { baseMultiplier = 7.24, actorLevel = 97.699996948242, },
				[21] = { baseMultiplier = 7.6, actorLevel = 105.08300018311, },
				[22] = { baseMultiplier = 7.98, actorLevel = 112.69200134277, },
				[23] = { baseMultiplier = 8.38, actorLevel = 120.52700042725, },
				[24] = { baseMultiplier = 8.8, actorLevel = 128.58799743652, },
				[25] = { baseMultiplier = 9.23, actorLevel = 136.875, },
				[26] = { baseMultiplier = 9.7, actorLevel = 145.38800048828, },
				[27] = { baseMultiplier = 10.18, actorLevel = 154.12699890137, },
				[28] = { baseMultiplier = 10.69, actorLevel = 163.09199523926, },
				[29] = { baseMultiplier = 11.23, actorLevel = 172.28300476074, },
				[30] = { baseMultiplier = 11.79, actorLevel = 181.69999694824, },
				[31] = { baseMultiplier = 12.38, actorLevel = 191.34300231934, },
				[32] = { baseMultiplier = 12.99, actorLevel = 201.21200561523, },
				[33] = { baseMultiplier = 13.64, actorLevel = 211.30700683594, },
				[34] = { baseMultiplier = 14.33, actorLevel = 221.62800598145, },
				[35] = { baseMultiplier = 15.04, actorLevel = 232.17500305176, },
				[36] = { baseMultiplier = 15.8, actorLevel = 242.94799804688, },
				[37] = { baseMultiplier = 16.58, actorLevel = 253.94700622559, },
				[38] = { baseMultiplier = 17.41, actorLevel = 265.17199707031, },
				[39] = { baseMultiplier = 18.28, actorLevel = 276.62298583984, },
				[40] = { baseMultiplier = 19.2, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["SummonInfernalHoundPlayer"] = {
	name = "Summon Infernal Hound",
	baseTypeName = "Summon Infernal Hound",
	fromTree = true,
	minionList = {
		"SummonedHellhound",
	},
	color = 4,
	description = "Activate to summon a Reviving Infernal Hound which Ignites enemies near it.",
	skillTypes = { [SkillType.Minion] = true, [SkillType.CreatesMinion] = true, [SkillType.Persistent] = true, [SkillType.Fire] = true, [SkillType.CreatesDemonMinion] = true, [SkillType.MinionsCanExplode] = true, [SkillType.HasReservation] = true, [SkillType.Companion] = true, [SkillType.CreatesCompanion] = true, },
	minionSkillTypes = { [SkillType.Attack] = true, [SkillType.Melee] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Area] = true, },
	castTime = 0,
	qualityStats = {
		{ "active_skill_minion_life_+%_final", 1 },
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { levelRequirement = 3, },
		[3] = { levelRequirement = 6, },
		[4] = { levelRequirement = 10, },
		[5] = { levelRequirement = 14, },
		[6] = { levelRequirement = 18, },
		[7] = { levelRequirement = 22, },
		[8] = { levelRequirement = 26, },
		[9] = { levelRequirement = 31, },
		[10] = { levelRequirement = 36, },
		[11] = { levelRequirement = 41, },
		[12] = { levelRequirement = 46, },
		[13] = { levelRequirement = 52, },
		[14] = { levelRequirement = 58, },
		[15] = { levelRequirement = 64, },
		[16] = { levelRequirement = 66, },
		[17] = { levelRequirement = 72, },
		[18] = { levelRequirement = 78, },
		[19] = { levelRequirement = 84, },
		[20] = { levelRequirement = 90, },
		[21] = { levelRequirement = 90, },
		[22] = { levelRequirement = 90, },
		[23] = { levelRequirement = 90, },
		[24] = { levelRequirement = 90, },
		[25] = { levelRequirement = 90, },
		[26] = { levelRequirement = 90, },
		[27] = { levelRequirement = 90, },
		[28] = { levelRequirement = 90, },
		[29] = { levelRequirement = 90, },
		[30] = { levelRequirement = 90, },
		[31] = { levelRequirement = 90, },
		[32] = { levelRequirement = 90, },
		[33] = { levelRequirement = 90, },
		[34] = { levelRequirement = 90, },
		[35] = { levelRequirement = 90, },
		[36] = { levelRequirement = 90, },
		[37] = { levelRequirement = 90, },
		[38] = { levelRequirement = 90, },
		[39] = { levelRequirement = 90, },
		[40] = { levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Summon Infernal Hound",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "summon_infernal_familiar",
			baseFlags = {
				spell = true,
				minion = true,
				permanentMinion = true,
			},
			constantStats = {
				{ "display_minion_monster_type", 8 },
				{ "infernal_familiar_minion_burns_for_%_max_life", 20 },
				{ "infernal_familiar_minion_burn_radius", 15 },
				{ "skill_desired_initial_amount", 1 },
			},
			stats = {
				"is_resummoning_minion",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["SupportingFirePlayer"] = {
	name = "Supporting Fire",
	baseTypeName = "Supporting Fire",
	fromTree = true,
	minionList = {
	},
	color = 4,
	description = "Recruit artillery Minions that takes up positions behind you. They will lay in wait for your Command then fire volleys of arrows at the target location.",
	skillTypes = { [SkillType.Minion] = true, [SkillType.CreatesMinion] = true, [SkillType.HasReservation] = true, [SkillType.Persistent] = true, [SkillType.CommandableMinion] = true, [SkillType.MinionsAreUndamagable] = true, },
	minionSkillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.Rain] = true, [SkillType.Area] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesNumberModifiersNotApplied] = true, [SkillType.Cooldown] = true, [SkillType.CannotChain] = true, [SkillType.GroundTargetedProjectile] = true, [SkillType.ProjectileNoCollision] = true, },
	castTime = 0,
	qualityStats = {
		{ "active_skill_minion_damage_+%_final", 1 },
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { levelRequirement = 3, },
		[3] = { levelRequirement = 6, },
		[4] = { levelRequirement = 10, },
		[5] = { levelRequirement = 14, },
		[6] = { levelRequirement = 18, },
		[7] = { levelRequirement = 22, },
		[8] = { levelRequirement = 26, },
		[9] = { levelRequirement = 31, },
		[10] = { levelRequirement = 36, },
		[11] = { levelRequirement = 41, },
		[12] = { levelRequirement = 46, },
		[13] = { levelRequirement = 52, },
		[14] = { levelRequirement = 58, },
		[15] = { levelRequirement = 64, },
		[16] = { levelRequirement = 66, },
		[17] = { levelRequirement = 72, },
		[18] = { levelRequirement = 78, },
		[19] = { levelRequirement = 84, },
		[20] = { levelRequirement = 90, },
		[21] = { levelRequirement = 90, },
		[22] = { levelRequirement = 90, },
		[23] = { levelRequirement = 90, },
		[24] = { levelRequirement = 90, },
		[25] = { levelRequirement = 90, },
		[26] = { levelRequirement = 90, },
		[27] = { levelRequirement = 90, },
		[28] = { levelRequirement = 90, },
		[29] = { levelRequirement = 90, },
		[30] = { levelRequirement = 90, },
		[31] = { levelRequirement = 90, },
		[32] = { levelRequirement = 90, },
		[33] = { levelRequirement = 90, },
		[34] = { levelRequirement = 90, },
		[35] = { levelRequirement = 90, },
		[36] = { levelRequirement = 90, },
		[37] = { levelRequirement = 90, },
		[38] = { levelRequirement = 90, },
		[39] = { levelRequirement = 90, },
		[40] = { levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Minion Info",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				minion = true,
				permanentMinion = true,
			},
			constantStats = {
				{ "minion_1%_damage_+%_per_X_player_strength", 3 },
				{ "minion_1%_accuracy_rating_+%_per_X_player_dexterity", 3 },
				{ "skill_desired_amount_override", 1 },
			},
			stats = {
				"display_statset_no_hit_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
		[2] = {
			label = "Command",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				minion = true,
				permanentMinion = true,
			},
			constantStats = {
				{ "minion_1%_damage_+%_per_X_player_strength", 3 },
				{ "minion_1%_accuracy_rating_+%_per_X_player_dexterity", 3 },
				{ "skill_desired_amount_override", 1 },
				{ "movement_speed_+%_final_while_performing_action", -70 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 60 },
			},
			stats = {
				"display_statset_no_hit_damage",
				"minions_do_not_move_to_skill_target",
				"can_perform_skill_while_moving",
				"skill_is_command",
				"base_deal_no_damage",
				"use_supporting_fire_settings",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["TemperWeaponPlayer"] = {
	name = "Temper Weapon",
	baseTypeName = "Temper Weapon",
	fromTree = true,
	color = 4,
	description = "Channel to temper your main hand Melee Martial Weapon. Each hit of the hammer against the anvil Empowers subsequent Attacks to Combust when Hitting enemies.",
	skillTypes = { [SkillType.Buff] = true, [SkillType.EmpowersOtherSkill] = true, [SkillType.Channel] = true, [SkillType.Cooldown] = true, },
	weaponTypes = {
		["One Handed Mace"] = true,
		["Flail"] = true,
		["Two Handed Sword"] = true,
		["Dagger"] = true,
		["Claw"] = true,
		["Spear"] = true,
		["Two Handed Axe"] = true,
		["Two Handed Mace"] = true,
		["One Handed Axe"] = true,
		["Staff"] = true,
		["One Handed Sword"] = true,
	},
	castTime = 1.125,
	qualityStats = {
		{ "skill_speed_+%", 1 },
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 5, cost = { ManaPerMinute = 180, }, },
		[2] = { storedUses = 1, levelRequirement = 3, cooldown = 5, cost = { ManaPerMinute = 199, }, },
		[3] = { storedUses = 1, levelRequirement = 6, cooldown = 5, cost = { ManaPerMinute = 221, }, },
		[4] = { storedUses = 1, levelRequirement = 10, cooldown = 5, cost = { ManaPerMinute = 244, }, },
		[5] = { storedUses = 1, levelRequirement = 14, cooldown = 5, cost = { ManaPerMinute = 271, }, },
		[6] = { storedUses = 1, levelRequirement = 18, cooldown = 5, cost = { ManaPerMinute = 300, }, },
		[7] = { storedUses = 1, levelRequirement = 22, cooldown = 5, cost = { ManaPerMinute = 333, }, },
		[8] = { storedUses = 1, levelRequirement = 26, cooldown = 5, cost = { ManaPerMinute = 369, }, },
		[9] = { storedUses = 1, levelRequirement = 31, cooldown = 5, cost = { ManaPerMinute = 409, }, },
		[10] = { storedUses = 1, levelRequirement = 36, cooldown = 5, cost = { ManaPerMinute = 453, }, },
		[11] = { storedUses = 1, levelRequirement = 41, cooldown = 5, cost = { ManaPerMinute = 502, }, },
		[12] = { storedUses = 1, levelRequirement = 46, cooldown = 5, cost = { ManaPerMinute = 556, }, },
		[13] = { storedUses = 1, levelRequirement = 52, cooldown = 5, cost = { ManaPerMinute = 616, }, },
		[14] = { storedUses = 1, levelRequirement = 58, cooldown = 5, cost = { ManaPerMinute = 683, }, },
		[15] = { storedUses = 1, levelRequirement = 64, cooldown = 5, cost = { ManaPerMinute = 757, }, },
		[16] = { storedUses = 1, levelRequirement = 66, cooldown = 5, cost = { ManaPerMinute = 839, }, },
		[17] = { storedUses = 1, levelRequirement = 72, cooldown = 5, cost = { ManaPerMinute = 930, }, },
		[18] = { storedUses = 1, levelRequirement = 78, cooldown = 5, cost = { ManaPerMinute = 1030, }, },
		[19] = { storedUses = 1, levelRequirement = 84, cooldown = 5, cost = { ManaPerMinute = 1142, }, },
		[20] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 1265, }, },
		[21] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 1402, }, },
		[22] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 1553, }, },
		[23] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 1721, }, },
		[24] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 1908, }, },
		[25] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 2114, }, },
		[26] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 2342, }, },
		[27] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 2596, }, },
		[28] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 2876, }, },
		[29] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 3187, }, },
		[30] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 3532, }, },
		[31] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 3914, }, },
		[32] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 4337, }, },
		[33] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 4806, }, },
		[34] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 5325, }, },
		[35] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 5901, }, },
		[36] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 6539, }, },
		[37] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 7246, }, },
		[38] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 8029, }, },
		[39] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 8897, }, },
		[40] = { storedUses = 1, levelRequirement = 90, cooldown = 5, cost = { ManaPerMinute = 9859, }, },
	},
	statSets = {
		[1] = {
			label = "Temper Weapon",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "imbue_weapon_max_exerts", 12 },
				{ "channel_skill_end_animation_duration_override_ms", 83 },
				{ "temper_weapon_empowers_per_strike", 3 },
			},
			stats = {
				"base_deal_no_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["TemperWeaponCombustionPlayer"] = {
	name = "Combust",
	hidden = true,
	fromTree = true,
	description = "Explode, dealing Fire damage in an area.",
	skillTypes = { [SkillType.Fire] = true, [SkillType.Triggered] = true, [SkillType.Area] = true, [SkillType.Triggerable] = true, [SkillType.InbuiltTrigger] = true, [SkillType.Attack] = true, [SkillType.Melee] = true, [SkillType.NoAttackOrCastTime] = true, },
	weaponTypes = {
		["One Handed Mace"] = true,
		["Flail"] = true,
		["Two Handed Sword"] = true,
		["Dagger"] = true,
		["Claw"] = true,
		["Spear"] = true,
		["Two Handed Axe"] = true,
		["Two Handed Mace"] = true,
		["One Handed Axe"] = true,
		["Staff"] = true,
		["One Handed Sword"] = true,
	},
	castTime = 0,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 0.8, levelRequirement = 0, },
		[2] = { baseMultiplier = 0.88, levelRequirement = 0, },
		[3] = { baseMultiplier = 0.97, levelRequirement = 0, },
		[4] = { baseMultiplier = 1.06, levelRequirement = 0, },
		[5] = { baseMultiplier = 1.15, levelRequirement = 0, },
		[6] = { baseMultiplier = 1.24, levelRequirement = 0, },
		[7] = { baseMultiplier = 1.33, levelRequirement = 0, },
		[8] = { baseMultiplier = 1.42, levelRequirement = 0, },
		[9] = { baseMultiplier = 1.5, levelRequirement = 0, },
		[10] = { baseMultiplier = 1.58, levelRequirement = 0, },
		[11] = { baseMultiplier = 1.67, levelRequirement = 0, },
		[12] = { baseMultiplier = 1.77, levelRequirement = 0, },
		[13] = { baseMultiplier = 1.88, levelRequirement = 0, },
		[14] = { baseMultiplier = 1.99, levelRequirement = 0, },
		[15] = { baseMultiplier = 2.12, levelRequirement = 0, },
		[16] = { baseMultiplier = 2.25, levelRequirement = 0, },
		[17] = { baseMultiplier = 2.39, levelRequirement = 0, },
		[18] = { baseMultiplier = 2.55, levelRequirement = 0, },
		[19] = { baseMultiplier = 2.72, levelRequirement = 0, },
		[20] = { baseMultiplier = 2.9, levelRequirement = 0, },
		[21] = { baseMultiplier = 3.09, levelRequirement = 0, },
		[22] = { baseMultiplier = 3.3, levelRequirement = 0, },
		[23] = { baseMultiplier = 3.52, levelRequirement = 0, },
		[24] = { baseMultiplier = 3.75, levelRequirement = 0, },
		[25] = { baseMultiplier = 4, levelRequirement = 0, },
		[26] = { baseMultiplier = 4.27, levelRequirement = 0, },
		[27] = { baseMultiplier = 4.55, levelRequirement = 0, },
		[28] = { baseMultiplier = 4.85, levelRequirement = 0, },
		[29] = { baseMultiplier = 5.18, levelRequirement = 0, },
		[30] = { baseMultiplier = 5.52, levelRequirement = 0, },
		[31] = { baseMultiplier = 5.89, levelRequirement = 0, },
		[32] = { baseMultiplier = 6.28, levelRequirement = 0, },
		[33] = { baseMultiplier = 6.7, levelRequirement = 0, },
		[34] = { baseMultiplier = 7.15, levelRequirement = 0, },
		[35] = { baseMultiplier = 7.62, levelRequirement = 0, },
		[36] = { baseMultiplier = 8.13, levelRequirement = 0, },
		[37] = { baseMultiplier = 8.67, levelRequirement = 0, },
		[38] = { baseMultiplier = 9.25, levelRequirement = 0, },
		[39] = { baseMultiplier = 9.87, levelRequirement = 0, },
		[40] = { baseMultiplier = 10.52, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Combust",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "temper_weapon_combustion",
			baseFlags = {
				attack = true,
				area = true,
			},
			constantStats = {
				{ "imbue_weapon_combust_trigger_chance_%", 100 },
				{ "active_skill_base_area_of_effect_radius", 25 },
				{ "active_skill_base_physical_damage_%_to_convert_to_fire", 100 },
			},
			stats = {
				"is_area_damage",
				"base_skill_show_average_damage_instead_of_dps",
				"global_always_hit",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["TemporalRiftPlayer"] = {
	name = "Temporal Rift",
	baseTypeName = "Temporal Rift",
	fromTree = true,
	color = 4,
	description = "While active, passively leaves afterimages of your recent past. Cast the Spell to return to the oldest afterimage, teleporting to that location and resetting your Life, Mana and Energy Shield to the values they had at the time.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Cooldown] = true, [SkillType.HasReservation] = true, [SkillType.Buff] = true, [SkillType.Persistent] = true, [SkillType.OngoingSkill] = true, [SkillType.PersistentShowsCastTime] = true, [SkillType.FixedCastTime] = true, },
	castTime = 0.5,
	qualityStats = {
		{ "base_cooldown_speed_+%", 0.5 },
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 7, },
		[2] = { storedUses = 1, levelRequirement = 3, cooldown = 6.9, },
		[3] = { storedUses = 1, levelRequirement = 6, cooldown = 6.8, },
		[4] = { storedUses = 1, levelRequirement = 10, cooldown = 6.7, },
		[5] = { storedUses = 1, levelRequirement = 14, cooldown = 6.6, },
		[6] = { storedUses = 1, levelRequirement = 18, cooldown = 6.5, },
		[7] = { storedUses = 1, levelRequirement = 22, cooldown = 6.4, },
		[8] = { storedUses = 1, levelRequirement = 26, cooldown = 6.3, },
		[9] = { storedUses = 1, levelRequirement = 31, cooldown = 6.2, },
		[10] = { storedUses = 1, levelRequirement = 36, cooldown = 6.1, },
		[11] = { storedUses = 1, levelRequirement = 41, cooldown = 6, },
		[12] = { storedUses = 1, levelRequirement = 46, cooldown = 5.9, },
		[13] = { storedUses = 1, levelRequirement = 52, cooldown = 5.8, },
		[14] = { storedUses = 1, levelRequirement = 58, cooldown = 5.7, },
		[15] = { storedUses = 1, levelRequirement = 64, cooldown = 5.6, },
		[16] = { storedUses = 1, levelRequirement = 66, cooldown = 5.5, },
		[17] = { storedUses = 1, levelRequirement = 72, cooldown = 5.4, },
		[18] = { storedUses = 1, levelRequirement = 78, cooldown = 5.3, },
		[19] = { storedUses = 1, levelRequirement = 84, cooldown = 5.2, },
		[20] = { storedUses = 1, levelRequirement = 90, cooldown = 5.1, },
		[21] = { storedUses = 1, levelRequirement = 90, cooldown = 5, },
		[22] = { storedUses = 1, levelRequirement = 90, cooldown = 4.9, },
		[23] = { storedUses = 1, levelRequirement = 90, cooldown = 4.8, },
		[24] = { storedUses = 1, levelRequirement = 90, cooldown = 4.7, },
		[25] = { storedUses = 1, levelRequirement = 90, cooldown = 4.6, },
		[26] = { storedUses = 1, levelRequirement = 90, cooldown = 4.5, },
		[27] = { storedUses = 1, levelRequirement = 90, cooldown = 4.4, },
		[28] = { storedUses = 1, levelRequirement = 90, cooldown = 4.3, },
		[29] = { storedUses = 1, levelRequirement = 90, cooldown = 4.2, },
		[30] = { storedUses = 1, levelRequirement = 90, cooldown = 4.1, },
		[31] = { storedUses = 1, levelRequirement = 90, cooldown = 4.05, },
		[32] = { storedUses = 1, levelRequirement = 90, cooldown = 4, },
		[33] = { storedUses = 1, levelRequirement = 90, cooldown = 3.95, },
		[34] = { storedUses = 1, levelRequirement = 90, cooldown = 3.9, },
		[35] = { storedUses = 1, levelRequirement = 90, cooldown = 3.85, },
		[36] = { storedUses = 1, levelRequirement = 90, cooldown = 3.8, },
		[37] = { storedUses = 1, levelRequirement = 90, cooldown = 3.75, },
		[38] = { storedUses = 1, levelRequirement = 90, cooldown = 3.7, },
		[39] = { storedUses = 1, levelRequirement = 90, cooldown = 3.65, },
		[40] = { storedUses = 1, levelRequirement = 90, cooldown = 3.6, },
	},
	statSets = {
		[1] = {
			label = "Temporal Rift",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "temporal_rift",
			baseFlags = {
			},
			constantStats = {
				{ "skill_desired_amount_override", 1 },
				{ "temporal_rift_snapshot_interval", 250 },
				{ "temporal_rift_maximum_snapshots", 16 },
			},
			stats = {
				"spell_cast_time_cannot_be_modified",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["TimeFreezePlayer"] = {
	name = "Time Freeze",
	baseTypeName = "Time Freeze",
	fromTree = true,
	color = 4,
	description = "Release a large wave that stops time for all affected enemies for a duration. Duration is lower the more times the enemy has had time stopped for them.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Cooldown] = true, [SkillType.Area] = true, [SkillType.Duration] = true, },
	castTime = 0.7,
	qualityStats = {
		{ "base_cooldown_speed_+%", 0.5 },
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 65, cost = { Mana = 20, }, },
		[2] = { storedUses = 1, levelRequirement = 3, cooldown = 64.5, cost = { Mana = 20, }, },
		[3] = { storedUses = 1, levelRequirement = 6, cooldown = 64, cost = { Mana = 20, }, },
		[4] = { storedUses = 1, levelRequirement = 10, cooldown = 63.5, cost = { Mana = 20, }, },
		[5] = { storedUses = 1, levelRequirement = 14, cooldown = 63, cost = { Mana = 20, }, },
		[6] = { storedUses = 1, levelRequirement = 18, cooldown = 62.5, cost = { Mana = 20, }, },
		[7] = { storedUses = 1, levelRequirement = 22, cooldown = 62, cost = { Mana = 20, }, },
		[8] = { storedUses = 1, levelRequirement = 26, cooldown = 61.5, cost = { Mana = 20, }, },
		[9] = { storedUses = 1, levelRequirement = 31, cooldown = 61, cost = { Mana = 20, }, },
		[10] = { storedUses = 1, levelRequirement = 36, cooldown = 60.5, cost = { Mana = 20, }, },
		[11] = { storedUses = 1, levelRequirement = 41, cooldown = 60, cost = { Mana = 20, }, },
		[12] = { storedUses = 1, levelRequirement = 46, cooldown = 59.5, cost = { Mana = 20, }, },
		[13] = { storedUses = 1, levelRequirement = 52, cooldown = 59, cost = { Mana = 20, }, },
		[14] = { storedUses = 1, levelRequirement = 58, cooldown = 58.5, cost = { Mana = 20, }, },
		[15] = { storedUses = 1, levelRequirement = 64, cooldown = 58, cost = { Mana = 20, }, },
		[16] = { storedUses = 1, levelRequirement = 66, cooldown = 57.5, cost = { Mana = 20, }, },
		[17] = { storedUses = 1, levelRequirement = 72, cooldown = 57, cost = { Mana = 20, }, },
		[18] = { storedUses = 1, levelRequirement = 78, cooldown = 56.5, cost = { Mana = 20, }, },
		[19] = { storedUses = 1, levelRequirement = 84, cooldown = 56, cost = { Mana = 20, }, },
		[20] = { storedUses = 1, levelRequirement = 90, cooldown = 55.5, cost = { Mana = 20, }, },
		[21] = { storedUses = 1, levelRequirement = 90, cooldown = 55, cost = { Mana = 20, }, },
		[22] = { storedUses = 1, levelRequirement = 90, cooldown = 54.5, cost = { Mana = 20, }, },
		[23] = { storedUses = 1, levelRequirement = 90, cooldown = 54, cost = { Mana = 20, }, },
		[24] = { storedUses = 1, levelRequirement = 90, cooldown = 53.5, cost = { Mana = 20, }, },
		[25] = { storedUses = 1, levelRequirement = 90, cooldown = 53, cost = { Mana = 20, }, },
		[26] = { storedUses = 1, levelRequirement = 90, cooldown = 52.5, cost = { Mana = 20, }, },
		[27] = { storedUses = 1, levelRequirement = 90, cooldown = 52, cost = { Mana = 20, }, },
		[28] = { storedUses = 1, levelRequirement = 90, cooldown = 51.5, cost = { Mana = 20, }, },
		[29] = { storedUses = 1, levelRequirement = 90, cooldown = 51, cost = { Mana = 20, }, },
		[30] = { storedUses = 1, levelRequirement = 90, cooldown = 50.5, cost = { Mana = 20, }, },
		[31] = { storedUses = 1, levelRequirement = 90, cooldown = 50.25, cost = { Mana = 20, }, },
		[32] = { storedUses = 1, levelRequirement = 90, cooldown = 50, cost = { Mana = 20, }, },
		[33] = { storedUses = 1, levelRequirement = 90, cooldown = 49.75, cost = { Mana = 20, }, },
		[34] = { storedUses = 1, levelRequirement = 90, cooldown = 49.5, cost = { Mana = 20, }, },
		[35] = { storedUses = 1, levelRequirement = 90, cooldown = 49.25, cost = { Mana = 20, }, },
		[36] = { storedUses = 1, levelRequirement = 90, cooldown = 49, cost = { Mana = 20, }, },
		[37] = { storedUses = 1, levelRequirement = 90, cooldown = 48.75, cost = { Mana = 20, }, },
		[38] = { storedUses = 1, levelRequirement = 90, cooldown = 48.5, cost = { Mana = 20, }, },
		[39] = { storedUses = 1, levelRequirement = 90, cooldown = 48.25, cost = { Mana = 20, }, },
		[40] = { storedUses = 1, levelRequirement = 90, cooldown = 48, cost = { Mana = 20, }, },
	},
	statSets = {
		[1] = {
			label = "Time Freeze",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "time_stop",
			baseFlags = {
				area = true,
				duration = true,
			},
			constantStats = {
				{ "base_skill_effect_duration", 3000 },
				{ "active_skill_base_area_of_effect_radius", 100 },
				{ "time_freeze_resistance_duration_ms", 30000 },
			},
			stats = {
				"cannot_cancel_skill_before_contact_point",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["TimeSnapPlayer"] = {
	name = "Time Snap",
	baseTypeName = "Time Snap",
	fromTree = true,
	color = 4,
	description = "Manipulate time, resetting the cooldowns of your other Skills.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Cooldown] = true, },
	castTime = 0.6,
	qualityStats = {
		{ "base_cooldown_speed_+%", 0.5 },
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 40, cost = { Mana = 20, }, },
		[2] = { storedUses = 1, levelRequirement = 3, cooldown = 39.6, cost = { Mana = 20, }, },
		[3] = { storedUses = 1, levelRequirement = 6, cooldown = 39.2, cost = { Mana = 20, }, },
		[4] = { storedUses = 1, levelRequirement = 10, cooldown = 38.8, cost = { Mana = 20, }, },
		[5] = { storedUses = 1, levelRequirement = 14, cooldown = 38.4, cost = { Mana = 20, }, },
		[6] = { storedUses = 1, levelRequirement = 18, cooldown = 38, cost = { Mana = 20, }, },
		[7] = { storedUses = 1, levelRequirement = 22, cooldown = 37.6, cost = { Mana = 20, }, },
		[8] = { storedUses = 1, levelRequirement = 26, cooldown = 37.2, cost = { Mana = 20, }, },
		[9] = { storedUses = 1, levelRequirement = 31, cooldown = 36.8, cost = { Mana = 20, }, },
		[10] = { storedUses = 1, levelRequirement = 36, cooldown = 36.4, cost = { Mana = 20, }, },
		[11] = { storedUses = 1, levelRequirement = 41, cooldown = 36, cost = { Mana = 20, }, },
		[12] = { storedUses = 1, levelRequirement = 46, cooldown = 35.6, cost = { Mana = 20, }, },
		[13] = { storedUses = 1, levelRequirement = 52, cooldown = 35.2, cost = { Mana = 20, }, },
		[14] = { storedUses = 1, levelRequirement = 58, cooldown = 34.8, cost = { Mana = 20, }, },
		[15] = { storedUses = 1, levelRequirement = 64, cooldown = 34.4, cost = { Mana = 20, }, },
		[16] = { storedUses = 1, levelRequirement = 66, cooldown = 34, cost = { Mana = 20, }, },
		[17] = { storedUses = 1, levelRequirement = 72, cooldown = 33.6, cost = { Mana = 20, }, },
		[18] = { storedUses = 1, levelRequirement = 78, cooldown = 33.2, cost = { Mana = 20, }, },
		[19] = { storedUses = 1, levelRequirement = 84, cooldown = 32.8, cost = { Mana = 20, }, },
		[20] = { storedUses = 1, levelRequirement = 90, cooldown = 32.4, cost = { Mana = 20, }, },
		[21] = { storedUses = 1, levelRequirement = 90, cooldown = 32, cost = { Mana = 20, }, },
		[22] = { storedUses = 1, levelRequirement = 90, cooldown = 31.6, cost = { Mana = 20, }, },
		[23] = { storedUses = 1, levelRequirement = 90, cooldown = 31.2, cost = { Mana = 20, }, },
		[24] = { storedUses = 1, levelRequirement = 90, cooldown = 30.8, cost = { Mana = 20, }, },
		[25] = { storedUses = 1, levelRequirement = 90, cooldown = 30.4, cost = { Mana = 20, }, },
		[26] = { storedUses = 1, levelRequirement = 90, cooldown = 30, cost = { Mana = 20, }, },
		[27] = { storedUses = 1, levelRequirement = 90, cooldown = 29.6, cost = { Mana = 20, }, },
		[28] = { storedUses = 1, levelRequirement = 90, cooldown = 29.2, cost = { Mana = 20, }, },
		[29] = { storedUses = 1, levelRequirement = 90, cooldown = 28.8, cost = { Mana = 20, }, },
		[30] = { storedUses = 1, levelRequirement = 90, cooldown = 28.4, cost = { Mana = 20, }, },
		[31] = { storedUses = 1, levelRequirement = 90, cooldown = 28.2, cost = { Mana = 20, }, },
		[32] = { storedUses = 1, levelRequirement = 90, cooldown = 28, cost = { Mana = 20, }, },
		[33] = { storedUses = 1, levelRequirement = 90, cooldown = 27.8, cost = { Mana = 20, }, },
		[34] = { storedUses = 1, levelRequirement = 90, cooldown = 27.6, cost = { Mana = 20, }, },
		[35] = { storedUses = 1, levelRequirement = 90, cooldown = 27.4, cost = { Mana = 20, }, },
		[36] = { storedUses = 1, levelRequirement = 90, cooldown = 27.2, cost = { Mana = 20, }, },
		[37] = { storedUses = 1, levelRequirement = 90, cooldown = 27, cost = { Mana = 20, }, },
		[38] = { storedUses = 1, levelRequirement = 90, cooldown = 26.8, cost = { Mana = 20, }, },
		[39] = { storedUses = 1, levelRequirement = 90, cooldown = 26.6, cost = { Mana = 20, }, },
		[40] = { storedUses = 1, levelRequirement = 90, cooldown = 26.4, cost = { Mana = 20, }, },
	},
	statSets = {
		[1] = {
			label = "Time Snap",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["UnboundAvatarPlayer"] = {
	name = "Unbound Avatar",
	baseTypeName = "Unbound Avatar",
	fromTree = true,
	color = 4,
	description = "Gain Unbound Fury by inflicting Elemental Ailments with Hits on enemies. Once you reach maximum Unbound Fury, consume it to become Unbound for a duration, vastly augmenting your Elemental prowess.",
	skillTypes = { [SkillType.Buff] = true, [SkillType.Cooldown] = true, [SkillType.Instant] = true, [SkillType.Fire] = true, [SkillType.Cold] = true, [SkillType.Lightning] = true, [SkillType.InstantNoRepeatWhenHeld] = true, [SkillType.InstantShiftAttackForLeftMouse] = true, [SkillType.Duration] = true, [SkillType.HasUsageCondition] = true, },
	castTime = 0,
	qualityStats = {
		{ "base_skill_effect_duration", 50 },
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 0.5, },
		[2] = { storedUses = 1, levelRequirement = 3, cooldown = 0.5, },
		[3] = { storedUses = 1, levelRequirement = 6, cooldown = 0.5, },
		[4] = { storedUses = 1, levelRequirement = 10, cooldown = 0.5, },
		[5] = { storedUses = 1, levelRequirement = 14, cooldown = 0.5, },
		[6] = { storedUses = 1, levelRequirement = 18, cooldown = 0.5, },
		[7] = { storedUses = 1, levelRequirement = 22, cooldown = 0.5, },
		[8] = { storedUses = 1, levelRequirement = 26, cooldown = 0.5, },
		[9] = { storedUses = 1, levelRequirement = 31, cooldown = 0.5, },
		[10] = { storedUses = 1, levelRequirement = 36, cooldown = 0.5, },
		[11] = { storedUses = 1, levelRequirement = 41, cooldown = 0.5, },
		[12] = { storedUses = 1, levelRequirement = 46, cooldown = 0.5, },
		[13] = { storedUses = 1, levelRequirement = 52, cooldown = 0.5, },
		[14] = { storedUses = 1, levelRequirement = 58, cooldown = 0.5, },
		[15] = { storedUses = 1, levelRequirement = 64, cooldown = 0.5, },
		[16] = { storedUses = 1, levelRequirement = 66, cooldown = 0.5, },
		[17] = { storedUses = 1, levelRequirement = 72, cooldown = 0.5, },
		[18] = { storedUses = 1, levelRequirement = 78, cooldown = 0.5, },
		[19] = { storedUses = 1, levelRequirement = 84, cooldown = 0.5, },
		[20] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[21] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[22] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[23] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[24] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[25] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[26] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[27] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[28] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[29] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[30] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[31] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[32] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[33] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[34] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[35] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[36] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[37] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[38] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[39] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
		[40] = { storedUses = 1, levelRequirement = 90, cooldown = 0.5, },
	},
	statSets = {
		[1] = {
			label = "Unbound Avatar",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "ailment_bearer",
			baseFlags = {
			},
			constantStats = {
				{ "display_max_ailment_bearer_charges", 100 },
				{ "display_ailment_bearer_charge_interval", 1 },
				{ "ailment_bearer_elemental_damage_+%_final", 40 },
				{ "unbound_ailment_elemental_ailment_chance_+%_final", 80 },
				{ "unbound_ailment_elemental_ailment_buildup_+%_final", 40 },
				{ "ailment_bearer_required_stacks_to_use", 100 },
			},
			stats = {
				"base_skill_effect_duration",
				"base_deal_no_damage",
			},
			levels = {
				[1] = { 8000, statInterpolation = { 1, }, actorLevel = 1, },
				[2] = { 8100, statInterpolation = { 1, }, actorLevel = 3.4519999027252, },
				[3] = { 8200, statInterpolation = { 1, }, actorLevel = 6.7670001983643, },
				[4] = { 8300, statInterpolation = { 1, }, actorLevel = 10.307999610901, },
				[5] = { 8400, statInterpolation = { 1, }, actorLevel = 14.074999809265, },
				[6] = { 8500, statInterpolation = { 1, }, actorLevel = 18.068000793457, },
				[7] = { 8600, statInterpolation = { 1, }, actorLevel = 22.287000656128, },
				[8] = { 8700, statInterpolation = { 1, }, actorLevel = 26.732000350952, },
				[9] = { 8800, statInterpolation = { 1, }, actorLevel = 31.40299987793, },
				[10] = { 8900, statInterpolation = { 1, }, actorLevel = 36.299999237061, },
				[11] = { 9000, statInterpolation = { 1, }, actorLevel = 41.423000335693, },
				[12] = { 9100, statInterpolation = { 1, }, actorLevel = 46.771999359131, },
				[13] = { 9200, statInterpolation = { 1, }, actorLevel = 52.34700012207, },
				[14] = { 9300, statInterpolation = { 1, }, actorLevel = 58.147998809814, },
				[15] = { 9400, statInterpolation = { 1, }, actorLevel = 64.175003051758, },
				[16] = { 9500, statInterpolation = { 1, }, actorLevel = 70.428001403809, },
				[17] = { 9600, statInterpolation = { 1, }, actorLevel = 76.906997680664, },
				[18] = { 9700, statInterpolation = { 1, }, actorLevel = 83.611999511719, },
				[19] = { 9800, statInterpolation = { 1, }, actorLevel = 90.542999267578, },
				[20] = { 9900, statInterpolation = { 1, }, actorLevel = 97.699996948242, },
				[21] = { 10000, statInterpolation = { 1, }, actorLevel = 105.08300018311, },
				[22] = { 10100, statInterpolation = { 1, }, actorLevel = 112.69200134277, },
				[23] = { 10200, statInterpolation = { 1, }, actorLevel = 120.52700042725, },
				[24] = { 10300, statInterpolation = { 1, }, actorLevel = 128.58799743652, },
				[25] = { 10400, statInterpolation = { 1, }, actorLevel = 136.875, },
				[26] = { 10500, statInterpolation = { 1, }, actorLevel = 145.38800048828, },
				[27] = { 10600, statInterpolation = { 1, }, actorLevel = 154.12699890137, },
				[28] = { 10700, statInterpolation = { 1, }, actorLevel = 163.09199523926, },
				[29] = { 10800, statInterpolation = { 1, }, actorLevel = 172.28300476074, },
				[30] = { 10900, statInterpolation = { 1, }, actorLevel = 181.69999694824, },
				[31] = { 10950, statInterpolation = { 1, }, actorLevel = 191.34300231934, },
				[32] = { 11000, statInterpolation = { 1, }, actorLevel = 201.21200561523, },
				[33] = { 11050, statInterpolation = { 1, }, actorLevel = 211.30700683594, },
				[34] = { 11100, statInterpolation = { 1, }, actorLevel = 221.62800598145, },
				[35] = { 11150, statInterpolation = { 1, }, actorLevel = 232.17500305176, },
				[36] = { 11200, statInterpolation = { 1, }, actorLevel = 242.94799804688, },
				[37] = { 11250, statInterpolation = { 1, }, actorLevel = 253.94700622559, },
				[38] = { 11300, statInterpolation = { 1, }, actorLevel = 265.17199707031, },
				[39] = { 11350, statInterpolation = { 1, }, actorLevel = 276.62298583984, },
				[40] = { 11400, statInterpolation = { 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["UniqueBreachLightningBoltPlayer"] = {
	name = "Lightning Bolt",
	baseTypeName = "Lightning Bolt",
	fromItem = true,
	color = 3,
	description = "Call down a Shocking bolt of Lightning to strike enemies in a small area.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.Lightning] = true, [SkillType.CanRapidFire] = true, [SkillType.Area] = true, [SkillType.AreaSpell] = true, [SkillType.Cascadable] = true, [SkillType.UsableWhileMoving] = true, [SkillType.Cooldown] = true, [SkillType.Triggerable] = true, [SkillType.Triggered] = true, },
	castTime = 0.85,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 10, storedUses = 1, levelRequirement = 0, cooldown = 0.5, cost = { Mana = 0, }, },
		[2] = { critChance = 10, storedUses = 1, levelRequirement = 3, cooldown = 0.5, cost = { Mana = 0, }, },
		[3] = { critChance = 10, storedUses = 1, levelRequirement = 6, cooldown = 0.5, cost = { Mana = 0, }, },
		[4] = { critChance = 10, storedUses = 1, levelRequirement = 10, cooldown = 0.5, cost = { Mana = 0, }, },
		[5] = { critChance = 10, storedUses = 1, levelRequirement = 14, cooldown = 0.5, cost = { Mana = 0, }, },
		[6] = { critChance = 10, storedUses = 1, levelRequirement = 18, cooldown = 0.5, cost = { Mana = 0, }, },
		[7] = { critChance = 10, storedUses = 1, levelRequirement = 22, cooldown = 0.5, cost = { Mana = 0, }, },
		[8] = { critChance = 10, storedUses = 1, levelRequirement = 26, cooldown = 0.5, cost = { Mana = 0, }, },
		[9] = { critChance = 10, storedUses = 1, levelRequirement = 31, cooldown = 0.5, cost = { Mana = 0, }, },
		[10] = { critChance = 10, storedUses = 1, levelRequirement = 36, cooldown = 0.5, cost = { Mana = 0, }, },
		[11] = { critChance = 10, storedUses = 1, levelRequirement = 41, cooldown = 0.5, cost = { Mana = 0, }, },
		[12] = { critChance = 10, storedUses = 1, levelRequirement = 46, cooldown = 0.5, cost = { Mana = 0, }, },
		[13] = { critChance = 10, storedUses = 1, levelRequirement = 52, cooldown = 0.5, cost = { Mana = 0, }, },
		[14] = { critChance = 10, storedUses = 1, levelRequirement = 58, cooldown = 0.5, cost = { Mana = 0, }, },
		[15] = { critChance = 10, storedUses = 1, levelRequirement = 64, cooldown = 0.5, cost = { Mana = 0, }, },
		[16] = { critChance = 10, storedUses = 1, levelRequirement = 66, cooldown = 0.5, cost = { Mana = 0, }, },
		[17] = { critChance = 10, storedUses = 1, levelRequirement = 72, cooldown = 0.5, cost = { Mana = 0, }, },
		[18] = { critChance = 10, storedUses = 1, levelRequirement = 78, cooldown = 0.5, cost = { Mana = 0, }, },
		[19] = { critChance = 10, storedUses = 1, levelRequirement = 84, cooldown = 0.5, cost = { Mana = 0, }, },
		[20] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[21] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[22] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[23] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[24] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[25] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[26] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[27] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[28] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[29] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[30] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[31] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[32] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[33] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[34] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[35] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[36] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[37] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[38] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[39] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
		[40] = { critChance = 10, storedUses = 1, levelRequirement = 90, cooldown = 0.5, cost = { Mana = 0, }, },
	},
	statSets = {
		[1] = {
			label = "Lightning Bolt",
			baseEffectiveness = 2.2999999523163,
			incrementalEffectiveness = 0.14000000059605,
			damageIncrementalEffectiveness = 0.006800000090152,
			statDescriptionScope = "lightning_bolt",
			baseFlags = {
				spell = true,
				area = true,
			},
			constantStats = {
				{ "active_skill_shock_chance_+%_final", 200 },
				{ "active_skill_base_area_of_effect_radius", 8 },
				{ "movement_speed_+%_final_while_performing_action", -70 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 60 },
				{ "triggered_on_critical_strike_%", 100 },
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
				"is_area_damage",
				"can_perform_skill_while_moving",
			},
			levels = {
				[1] = { 1, 17, statInterpolation = { 1, 1, }, actorLevel = 1, },
				[2] = { 1, 23, statInterpolation = { 1, 1, }, actorLevel = 3.4519999027252, },
				[3] = { 2, 32, statInterpolation = { 1, 1, }, actorLevel = 6.7670001983643, },
				[4] = { 2, 42, statInterpolation = { 1, 1, }, actorLevel = 10.307999610901, },
				[5] = { 3, 53, statInterpolation = { 1, 1, }, actorLevel = 14.074999809265, },
				[6] = { 3, 65, statInterpolation = { 1, 1, }, actorLevel = 18.068000793457, },
				[7] = { 4, 78, statInterpolation = { 1, 1, }, actorLevel = 22.287000656128, },
				[8] = { 5, 93, statInterpolation = { 1, 1, }, actorLevel = 26.732000350952, },
				[9] = { 6, 110, statInterpolation = { 1, 1, }, actorLevel = 31.40299987793, },
				[10] = { 7, 128, statInterpolation = { 1, 1, }, actorLevel = 36.299999237061, },
				[11] = { 8, 149, statInterpolation = { 1, 1, }, actorLevel = 41.423000335693, },
				[12] = { 9, 172, statInterpolation = { 1, 1, }, actorLevel = 46.771999359131, },
				[13] = { 10, 197, statInterpolation = { 1, 1, }, actorLevel = 52.34700012207, },
				[14] = { 12, 225, statInterpolation = { 1, 1, }, actorLevel = 58.147998809814, },
				[15] = { 13, 256, statInterpolation = { 1, 1, }, actorLevel = 64.175003051758, },
				[16] = { 15, 291, statInterpolation = { 1, 1, }, actorLevel = 70.428001403809, },
				[17] = { 17, 330, statInterpolation = { 1, 1, }, actorLevel = 76.906997680664, },
				[18] = { 20, 373, statInterpolation = { 1, 1, }, actorLevel = 83.611999511719, },
				[19] = { 22, 422, statInterpolation = { 1, 1, }, actorLevel = 90.542999267578, },
				[20] = { 25, 475, statInterpolation = { 1, 1, }, actorLevel = 97.699996948242, },
				[21] = { 28, 535, statInterpolation = { 1, 1, }, actorLevel = 105.08300018311, },
				[22] = { 32, 602, statInterpolation = { 1, 1, }, actorLevel = 112.69200134277, },
				[23] = { 36, 677, statInterpolation = { 1, 1, }, actorLevel = 120.52700042725, },
				[24] = { 40, 760, statInterpolation = { 1, 1, }, actorLevel = 128.58799743652, },
				[25] = { 45, 854, statInterpolation = { 1, 1, }, actorLevel = 136.875, },
				[26] = { 50, 958, statInterpolation = { 1, 1, }, actorLevel = 145.38800048828, },
				[27] = { 57, 1075, statInterpolation = { 1, 1, }, actorLevel = 154.12699890137, },
				[28] = { 64, 1207, statInterpolation = { 1, 1, }, actorLevel = 163.09199523926, },
				[29] = { 71, 1354, statInterpolation = { 1, 1, }, actorLevel = 172.28300476074, },
				[30] = { 80, 1519, statInterpolation = { 1, 1, }, actorLevel = 181.69999694824, },
				[31] = { 90, 1705, statInterpolation = { 1, 1, }, actorLevel = 191.34300231934, },
				[32] = { 101, 1914, statInterpolation = { 1, 1, }, actorLevel = 201.21200561523, },
				[33] = { 113, 2150, statInterpolation = { 1, 1, }, actorLevel = 211.30700683594, },
				[34] = { 127, 2415, statInterpolation = { 1, 1, }, actorLevel = 221.62800598145, },
				[35] = { 143, 2714, statInterpolation = { 1, 1, }, actorLevel = 232.17500305176, },
				[36] = { 161, 3051, statInterpolation = { 1, 1, }, actorLevel = 242.94799804688, },
				[37] = { 181, 3433, statInterpolation = { 1, 1, }, actorLevel = 253.94700622559, },
				[38] = { 203, 3864, statInterpolation = { 1, 1, }, actorLevel = 265.17199707031, },
				[39] = { 229, 4352, statInterpolation = { 1, 1, }, actorLevel = 276.62298583984, },
				[40] = { 258, 4905, statInterpolation = { 1, 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["CrossbowRequiemAmmoPlayer"] = {
	name = "Compose Requiem",
	baseTypeName = "Compose Requiem",
	fromItem = true,
	color = 1,
	description = "Your weapon passively accumulates anguish from the tormented souls within. When fully charged, use this skill to compose the Requiem, transforming your Crossbow shot into a torrent of anguish for a short, fixed amount of time. Does not use Ammunition.",
	skillTypes = { [SkillType.CrossbowAmmoSkill] = true, [SkillType.Attack] = true, [SkillType.UsableWhileMoving] = true, [SkillType.HasUsageCondition] = true, },
	weaponTypes = {
		["Crossbow"] = true,
	},
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, cost = { Mana = 23, }, },
		[2] = { levelRequirement = 3, cost = { Mana = 25, }, },
		[3] = { levelRequirement = 6, cost = { Mana = 28, }, },
		[4] = { levelRequirement = 10, cost = { Mana = 31, }, },
		[5] = { levelRequirement = 14, cost = { Mana = 34, }, },
		[6] = { levelRequirement = 18, cost = { Mana = 38, }, },
		[7] = { levelRequirement = 22, cost = { Mana = 42, }, },
		[8] = { levelRequirement = 26, cost = { Mana = 47, }, },
		[9] = { levelRequirement = 31, cost = { Mana = 52, }, },
		[10] = { levelRequirement = 36, cost = { Mana = 57, }, },
		[11] = { levelRequirement = 41, cost = { Mana = 64, }, },
		[12] = { levelRequirement = 46, cost = { Mana = 71, }, },
		[13] = { levelRequirement = 52, cost = { Mana = 78, }, },
		[14] = { levelRequirement = 58, cost = { Mana = 87, }, },
		[15] = { levelRequirement = 64, cost = { Mana = 96, }, },
		[16] = { levelRequirement = 66, cost = { Mana = 107, }, },
		[17] = { levelRequirement = 72, cost = { Mana = 118, }, },
		[18] = { levelRequirement = 78, cost = { Mana = 131, }, },
		[19] = { levelRequirement = 84, cost = { Mana = 145, }, },
		[20] = { levelRequirement = 90, cost = { Mana = 161, }, },
		[21] = { levelRequirement = 90, cost = { Mana = 179, }, },
		[22] = { levelRequirement = 90, cost = { Mana = 198, }, },
		[23] = { levelRequirement = 90, cost = { Mana = 220, }, },
		[24] = { levelRequirement = 90, cost = { Mana = 243, }, },
		[25] = { levelRequirement = 90, cost = { Mana = 270, }, },
		[26] = { levelRequirement = 90, cost = { Mana = 299, }, },
		[27] = { levelRequirement = 90, cost = { Mana = 331, }, },
		[28] = { levelRequirement = 90, cost = { Mana = 367, }, },
		[29] = { levelRequirement = 90, cost = { Mana = 407, }, },
		[30] = { levelRequirement = 90, cost = { Mana = 451, }, },
		[31] = { levelRequirement = 90, cost = { Mana = 500, }, },
		[32] = { levelRequirement = 90, cost = { Mana = 554, }, },
		[33] = { levelRequirement = 90, cost = { Mana = 614, }, },
		[34] = { levelRequirement = 90, cost = { Mana = 680, }, },
		[35] = { levelRequirement = 90, cost = { Mana = 754, }, },
		[36] = { levelRequirement = 90, cost = { Mana = 835, }, },
		[37] = { levelRequirement = 90, cost = { Mana = 925, }, },
		[38] = { levelRequirement = 90, cost = { Mana = 1026, }, },
		[39] = { levelRequirement = 90, cost = { Mana = 1136, }, },
		[40] = { levelRequirement = 90, cost = { Mana = 1259, }, },
	},
	statSets = {
		[1] = {
			label = "Compose Requiem",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "rapid_shot_requiem_ammo",
			baseFlags = {
			},
			constantStats = {
				{ "action_allowed_queue_time_override_ms", 1000 },
				{ "movement_speed_+%_final_while_performing_action", -30 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 60 },
				{ "rapidshot_requiem_number_of_stacks_per_minute", 200 },
				{ "rapidshot_requiem_base_buff_count_requirement", 100 },
				{ "rapidshot_requiem_active_duration_ms", 12000 },
			},
			stats = {
				"action_can_be_used_in_aiming_stance",
				"can_perform_skill_while_moving",
				"base_deal_no_damage",
				"display_statset_hide_usage_stats",
				"crossbow_ammo_skill_does_not_transition",
				"crossbow_ammo_has_no_ammo",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["CrossbowRequiemPlayer"] = {
	name = "Requiem",
	hidden = true,
	fromItem = true,
	description = "Unleashes a torrent of anguish that bombard the target area, dealing damage in an area on impact. Does not use Ammunition and can be fired freely until the Requiem ends.",
	skillTypes = { [SkillType.Damage] = true, [SkillType.Area] = true, [SkillType.Projectile] = true, [SkillType.Cold] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.CannotChain] = true, [SkillType.ProjectileNoCollision] = true, [SkillType.GroundTargetedProjectile] = true, [SkillType.CrossbowSkill] = true, },
	weaponTypes = {
		["Crossbow"] = true,
	},
	castTime = 1,
	qualityStats = {
		{ "active_skill_base_area_of_effect_radius", 0.1 },
	},
	levels = {
		[1] = { attackSpeedMultiplier = 300, baseMultiplier = 3, levelRequirement = 0, },
		[2] = { attackSpeedMultiplier = 300, baseMultiplier = 3.3, levelRequirement = 0, },
		[3] = { attackSpeedMultiplier = 300, baseMultiplier = 3.63, levelRequirement = 0, },
		[4] = { attackSpeedMultiplier = 300, baseMultiplier = 3.96, levelRequirement = 0, },
		[5] = { attackSpeedMultiplier = 300, baseMultiplier = 4.27, levelRequirement = 0, },
		[6] = { attackSpeedMultiplier = 300, baseMultiplier = 4.59, levelRequirement = 0, },
		[7] = { attackSpeedMultiplier = 300, baseMultiplier = 4.92, levelRequirement = 0, },
		[8] = { attackSpeedMultiplier = 300, baseMultiplier = 5.21, levelRequirement = 0, },
		[9] = { attackSpeedMultiplier = 300, baseMultiplier = 5.47, levelRequirement = 0, },
		[10] = { attackSpeedMultiplier = 300, baseMultiplier = 5.74, levelRequirement = 0, },
		[11] = { attackSpeedMultiplier = 300, baseMultiplier = 6.03, levelRequirement = 0, },
		[12] = { attackSpeedMultiplier = 300, baseMultiplier = 6.33, levelRequirement = 0, },
		[13] = { attackSpeedMultiplier = 300, baseMultiplier = 6.65, levelRequirement = 0, },
		[14] = { attackSpeedMultiplier = 300, baseMultiplier = 6.98, levelRequirement = 0, },
		[15] = { attackSpeedMultiplier = 300, baseMultiplier = 7.33, levelRequirement = 0, },
		[16] = { attackSpeedMultiplier = 300, baseMultiplier = 7.7, levelRequirement = 0, },
		[17] = { attackSpeedMultiplier = 300, baseMultiplier = 8.08, levelRequirement = 0, },
		[18] = { attackSpeedMultiplier = 300, baseMultiplier = 8.49, levelRequirement = 0, },
		[19] = { attackSpeedMultiplier = 300, baseMultiplier = 8.91, levelRequirement = 0, },
		[20] = { attackSpeedMultiplier = 300, baseMultiplier = 9.36, levelRequirement = 0, },
		[21] = { attackSpeedMultiplier = 300, baseMultiplier = 9.82, levelRequirement = 0, },
		[22] = { attackSpeedMultiplier = 300, baseMultiplier = 10.32, levelRequirement = 0, },
		[23] = { attackSpeedMultiplier = 300, baseMultiplier = 10.83, levelRequirement = 0, },
		[24] = { attackSpeedMultiplier = 300, baseMultiplier = 11.37, levelRequirement = 0, },
		[25] = { attackSpeedMultiplier = 300, baseMultiplier = 11.94, levelRequirement = 0, },
		[26] = { attackSpeedMultiplier = 300, baseMultiplier = 12.54, levelRequirement = 0, },
		[27] = { attackSpeedMultiplier = 300, baseMultiplier = 13.17, levelRequirement = 0, },
		[28] = { attackSpeedMultiplier = 300, baseMultiplier = 13.82, levelRequirement = 0, },
		[29] = { attackSpeedMultiplier = 300, baseMultiplier = 14.52, levelRequirement = 0, },
		[30] = { attackSpeedMultiplier = 300, baseMultiplier = 15.24, levelRequirement = 0, },
		[31] = { attackSpeedMultiplier = 300, baseMultiplier = 16, levelRequirement = 0, },
		[32] = { attackSpeedMultiplier = 300, baseMultiplier = 16.8, levelRequirement = 0, },
		[33] = { attackSpeedMultiplier = 300, baseMultiplier = 17.64, levelRequirement = 0, },
		[34] = { attackSpeedMultiplier = 300, baseMultiplier = 18.53, levelRequirement = 0, },
		[35] = { attackSpeedMultiplier = 300, baseMultiplier = 19.45, levelRequirement = 0, },
		[36] = { attackSpeedMultiplier = 300, baseMultiplier = 20.42, levelRequirement = 0, },
		[37] = { attackSpeedMultiplier = 300, baseMultiplier = 21.45, levelRequirement = 0, },
		[38] = { attackSpeedMultiplier = 300, baseMultiplier = 22.52, levelRequirement = 0, },
		[39] = { attackSpeedMultiplier = 300, baseMultiplier = 23.64, levelRequirement = 0, },
		[40] = { attackSpeedMultiplier = 300, baseMultiplier = 24.83, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Requiem",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "rapid_shot_requiem",
			baseFlags = {
				projectile = true,
				area = true,
			},
			constantStats = {
				{ "movement_speed_+%_final_while_performing_action", -70 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 50 },
				{ "active_skill_base_area_of_effect_radius", 16 },
				{ "active_skill_base_secondary_area_of_effect_radius", 24 },
				{ "active_skill_base_physical_damage_%_to_convert_to_cold", 60 },
			},
			stats = {
				"base_is_projectile",
				"action_requires_aiming_stance",
				"should_use_additive_aiming_animation",
				"projectile_uses_contact_position",
				"projectiles_crossbow_barrage",
				"can_perform_skill_while_moving",
				"quality_display_active_skill_base_area_of_effect_radius_is_gem",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["ExplodingPoisonToadPlayer"] = {
	name = "Bursting Fen Toad",
	baseTypeName = "Bursting Fen Toad",
	fromItem = true,
	color = 4,
	description = "A Poisonous Toad leaps towards enemies and explodes, dealing damage in an area and inflicting Poison.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.Area] = true, [SkillType.Chaos] = true, [SkillType.Physical] = true, [SkillType.Triggered] = true, [SkillType.Triggerable] = true, [SkillType.NonWeaponAttack] = true, [SkillType.NoAttackOrCastTime] = true, [SkillType.UseGlobalStats] = true, },
	castTime = 1,
	qualityStats = {
		{ "trigger_toad_spawn_chance_%", 0.5 },
	},
	levels = {
		[1] = { critChance = 5, baseMultiplier = 2.25, levelRequirement = 0, },
		[2] = { critChance = 5, baseMultiplier = 2.48, levelRequirement = 3, },
		[3] = { critChance = 5, baseMultiplier = 2.72, levelRequirement = 6, },
		[4] = { critChance = 5, baseMultiplier = 2.97, levelRequirement = 10, },
		[5] = { critChance = 5, baseMultiplier = 3.2, levelRequirement = 14, },
		[6] = { critChance = 5, baseMultiplier = 3.45, levelRequirement = 18, },
		[7] = { critChance = 5, baseMultiplier = 3.69, levelRequirement = 22, },
		[8] = { critChance = 5, baseMultiplier = 3.91, levelRequirement = 26, },
		[9] = { critChance = 5, baseMultiplier = 4.1, levelRequirement = 31, },
		[10] = { critChance = 5, baseMultiplier = 4.31, levelRequirement = 36, },
		[11] = { critChance = 5, baseMultiplier = 4.52, levelRequirement = 41, },
		[12] = { critChance = 5, baseMultiplier = 4.75, levelRequirement = 46, },
		[13] = { critChance = 5, baseMultiplier = 4.99, levelRequirement = 52, },
		[14] = { critChance = 5, baseMultiplier = 5.24, levelRequirement = 58, },
		[15] = { critChance = 5, baseMultiplier = 5.5, levelRequirement = 64, },
		[16] = { critChance = 5, baseMultiplier = 5.77, levelRequirement = 66, },
		[17] = { critChance = 5, baseMultiplier = 6.06, levelRequirement = 72, },
		[18] = { critChance = 5, baseMultiplier = 6.37, levelRequirement = 78, },
		[19] = { critChance = 5, baseMultiplier = 6.68, levelRequirement = 84, },
		[20] = { critChance = 5, baseMultiplier = 7.02, levelRequirement = 90, },
		[21] = { critChance = 5, baseMultiplier = 7.37, levelRequirement = 90, },
		[22] = { critChance = 5, baseMultiplier = 7.74, levelRequirement = 90, },
		[23] = { critChance = 5, baseMultiplier = 8.12, levelRequirement = 90, },
		[24] = { critChance = 5, baseMultiplier = 8.53, levelRequirement = 90, },
		[25] = { critChance = 5, baseMultiplier = 8.96, levelRequirement = 90, },
		[26] = { critChance = 5, baseMultiplier = 9.4, levelRequirement = 90, },
		[27] = { critChance = 5, baseMultiplier = 9.87, levelRequirement = 90, },
		[28] = { critChance = 5, baseMultiplier = 10.37, levelRequirement = 90, },
		[29] = { critChance = 5, baseMultiplier = 10.89, levelRequirement = 90, },
		[30] = { critChance = 5, baseMultiplier = 11.43, levelRequirement = 90, },
		[31] = { critChance = 5, baseMultiplier = 12, levelRequirement = 90, },
		[32] = { critChance = 5, baseMultiplier = 12.6, levelRequirement = 90, },
		[33] = { critChance = 5, baseMultiplier = 13.23, levelRequirement = 90, },
		[34] = { critChance = 5, baseMultiplier = 13.89, levelRequirement = 90, },
		[35] = { critChance = 5, baseMultiplier = 14.59, levelRequirement = 90, },
		[36] = { critChance = 5, baseMultiplier = 15.32, levelRequirement = 90, },
		[37] = { critChance = 5, baseMultiplier = 16.08, levelRequirement = 90, },
		[38] = { critChance = 5, baseMultiplier = 16.89, levelRequirement = 90, },
		[39] = { critChance = 5, baseMultiplier = 17.73, levelRequirement = 90, },
		[40] = { critChance = 5, baseMultiplier = 18.62, levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Bursting Fen Toad",
			baseEffectiveness = 2,
			incrementalEffectiveness = 0.27349999547005,
			statDescriptionScope = "exploding_poison_toad",
			baseFlags = {
				area = true,
				attack = true,
			},
			constantStats = {
				{ "trigger_toad_spawn_chance_%", 25 },
				{ "base_chance_to_poison_on_hit_%", 100 },
				{ "active_skill_base_area_of_effect_radius", 25 },
			},
			stats = {
				"attack_minimum_added_physical_damage",
				"attack_maximum_added_physical_damage",
				"is_area_damage",
			},
			levels = {
				[1] = { 6, 9, statInterpolation = { 1, 1, }, actorLevel = 1, },
				[2] = { 10, 16, statInterpolation = { 1, 1, }, actorLevel = 3.4519999027252, },
				[3] = { 16, 24, statInterpolation = { 1, 1, }, actorLevel = 6.7670001983643, },
				[4] = { 22, 33, statInterpolation = { 1, 1, }, actorLevel = 10.307999610901, },
				[5] = { 28, 43, statInterpolation = { 1, 1, }, actorLevel = 14.074999809265, },
				[6] = { 35, 53, statInterpolation = { 1, 1, }, actorLevel = 18.068000793457, },
				[7] = { 42, 64, statInterpolation = { 1, 1, }, actorLevel = 22.287000656128, },
				[8] = { 50, 75, statInterpolation = { 1, 1, }, actorLevel = 26.732000350952, },
				[9] = { 58, 87, statInterpolation = { 1, 1, }, actorLevel = 31.40299987793, },
				[10] = { 66, 99, statInterpolation = { 1, 1, }, actorLevel = 36.299999237061, },
				[11] = { 75, 112, statInterpolation = { 1, 1, }, actorLevel = 41.423000335693, },
				[12] = { 84, 126, statInterpolation = { 1, 1, }, actorLevel = 46.771999359131, },
				[13] = { 94, 140, statInterpolation = { 1, 1, }, actorLevel = 52.34700012207, },
				[14] = { 103, 155, statInterpolation = { 1, 1, }, actorLevel = 58.147998809814, },
				[15] = { 114, 170, statInterpolation = { 1, 1, }, actorLevel = 64.175003051758, },
				[16] = { 124, 186, statInterpolation = { 1, 1, }, actorLevel = 70.428001403809, },
				[17] = { 135, 203, statInterpolation = { 1, 1, }, actorLevel = 76.906997680664, },
				[18] = { 147, 220, statInterpolation = { 1, 1, }, actorLevel = 83.611999511719, },
				[19] = { 158, 238, statInterpolation = { 1, 1, }, actorLevel = 90.542999267578, },
				[20] = { 171, 256, statInterpolation = { 1, 1, }, actorLevel = 97.699996948242, },
				[21] = { 183, 275, statInterpolation = { 1, 1, }, actorLevel = 105.08300018311, },
				[22] = { 196, 294, statInterpolation = { 1, 1, }, actorLevel = 112.69200134277, },
				[23] = { 209, 314, statInterpolation = { 1, 1, }, actorLevel = 120.52700042725, },
				[24] = { 223, 335, statInterpolation = { 1, 1, }, actorLevel = 128.58799743652, },
				[25] = { 237, 356, statInterpolation = { 1, 1, }, actorLevel = 136.875, },
				[26] = { 252, 378, statInterpolation = { 1, 1, }, actorLevel = 145.38800048828, },
				[27] = { 267, 400, statInterpolation = { 1, 1, }, actorLevel = 154.12699890137, },
				[28] = { 282, 423, statInterpolation = { 1, 1, }, actorLevel = 163.09199523926, },
				[29] = { 297, 446, statInterpolation = { 1, 1, }, actorLevel = 172.28300476074, },
				[30] = { 313, 470, statInterpolation = { 1, 1, }, actorLevel = 181.69999694824, },
				[31] = { 330, 495, statInterpolation = { 1, 1, }, actorLevel = 191.34300231934, },
				[32] = { 347, 520, statInterpolation = { 1, 1, }, actorLevel = 201.21200561523, },
				[33] = { 364, 546, statInterpolation = { 1, 1, }, actorLevel = 211.30700683594, },
				[34] = { 381, 572, statInterpolation = { 1, 1, }, actorLevel = 221.62800598145, },
				[35] = { 399, 599, statInterpolation = { 1, 1, }, actorLevel = 232.17500305176, },
				[36] = { 418, 626, statInterpolation = { 1, 1, }, actorLevel = 242.94799804688, },
				[37] = { 436, 654, statInterpolation = { 1, 1, }, actorLevel = 253.94700622559, },
				[38] = { 455, 683, statInterpolation = { 1, 1, }, actorLevel = 265.17199707031, },
				[39] = { 475, 712, statInterpolation = { 1, 1, }, actorLevel = 276.62298583984, },
				[40] = { 495, 742, statInterpolation = { 1, 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["PinnacleOfPowerPlayer"] = {
	name = "Pinnacle of Power",
	baseTypeName = "Pinnacle of Power",
	fromItem = true,
	color = 4,
	description = "Consume all Power Charges to master the elements, gaining Elemental Damage and the ability to apply Elemental Ailments with other damage types. Can only be used while you have maximum Power Charges.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Duration] = true, [SkillType.ConsumesCharges] = true, [SkillType.SkillConsumesPowerChargesOnUse] = true, [SkillType.Instant] = true, [SkillType.Cooldown] = true, [SkillType.Buff] = true, [SkillType.Fire] = true, [SkillType.Cold] = true, [SkillType.Lightning] = true, [SkillType.HasUsageCondition] = true, },
	castTime = 0,
	qualityStats = {
		{ "elemental_power_elemental_damage_+%_final_per_power_charge", 0.1 },
	},
	levels = {
		[20] = { storedUses = 1, levelRequirement = 0, cooldown = 4, cost = { Mana = 93, }, },
	},
	statSets = {
		[1] = {
			label = "Pinnacle of Power",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "elemental_power",
			baseFlags = {
				buff = true,
				duration = true,
			},
			constantStats = {
				{ "elemental_power_buff_duration_per_power_charge_ms", 5000 },
				{ "elemental_power_elemental_damage_+%_final_per_power_charge", 8 },
			},
			stats = {
				"base_skill_is_instant",
				"base_deal_no_damage",
				"quality_stat_elemental_power_elemental_damage_+%_final_per_power_charge_is_gem",
			},
			levels = {
				[20] = { actorLevel = 60, },
			},
		},
	}
}
skills["ImpurityPlayer"] = {
	name = "Impurity",
	baseTypeName = "Impurity",
	fromItem = true,
	color = 3,
	description = "Emit an Aura that boosts the Chaos Resistance of you and Allies in your Presence.",
	skillTypes = { [SkillType.Buff] = true, [SkillType.HasReservation] = true, [SkillType.Aura] = true, [SkillType.OngoingSkill] = true, [SkillType.Persistent] = true, [SkillType.Chaos] = true, [SkillType.AffectsPresence] = true, },
	castTime = 0,
	qualityStats = {
		{ "base_skill_buff_chaos_damage_resistance_%_to_apply", 0.3 },
	},
	levels = {
		[1] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 0, },
		[2] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 3, },
		[3] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 6, },
		[4] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 10, },
		[5] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 14, },
		[6] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 18, },
		[7] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 22, },
		[8] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 26, },
		[9] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 31, },
		[10] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 36, },
		[11] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 41, },
		[12] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 46, },
		[13] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 52, },
		[14] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 58, },
		[15] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 64, },
		[16] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 66, },
		[17] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 72, },
		[18] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 78, },
		[19] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 84, },
		[20] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[21] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[22] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[23] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[24] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[25] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[26] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[27] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[28] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[29] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[30] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[31] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[32] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[33] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[34] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[35] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[36] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[37] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[38] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[39] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
		[40] = { reservationMultiplier = -100, manaMultiplier = -100, levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Impurity",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "chaos_resist_aura",
			statMap = {
				["base_skill_buff_chaos_damage_resistance_%_to_apply"] = {
					mod("ChaosResist", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Aura" }),
				},
			},
			baseFlags = {
				aura = true,
			},
			constantStats = {
				{ "skill_desired_amount_override", 1 },
			},
			stats = {
				"base_skill_buff_chaos_damage_resistance_%_to_apply",
				"base_deal_no_damage",
			},
			levels = {
				[1] = { 10, statInterpolation = { 1, }, actorLevel = 1, },
				[2] = { 11, statInterpolation = { 1, }, actorLevel = 3.4519999027252, },
				[3] = { 12, statInterpolation = { 1, }, actorLevel = 6.7670001983643, },
				[4] = { 13, statInterpolation = { 1, }, actorLevel = 10.307999610901, },
				[5] = { 14, statInterpolation = { 1, }, actorLevel = 14.074999809265, },
				[6] = { 15, statInterpolation = { 1, }, actorLevel = 18.068000793457, },
				[7] = { 16, statInterpolation = { 1, }, actorLevel = 22.287000656128, },
				[8] = { 17, statInterpolation = { 1, }, actorLevel = 26.732000350952, },
				[9] = { 18, statInterpolation = { 1, }, actorLevel = 31.40299987793, },
				[10] = { 19, statInterpolation = { 1, }, actorLevel = 36.299999237061, },
				[11] = { 20, statInterpolation = { 1, }, actorLevel = 41.423000335693, },
				[12] = { 21, statInterpolation = { 1, }, actorLevel = 46.771999359131, },
				[13] = { 22, statInterpolation = { 1, }, actorLevel = 52.34700012207, },
				[14] = { 23, statInterpolation = { 1, }, actorLevel = 58.147998809814, },
				[15] = { 24, statInterpolation = { 1, }, actorLevel = 64.175003051758, },
				[16] = { 25, statInterpolation = { 1, }, actorLevel = 70.428001403809, },
				[17] = { 26, statInterpolation = { 1, }, actorLevel = 76.906997680664, },
				[18] = { 27, statInterpolation = { 1, }, actorLevel = 83.611999511719, },
				[19] = { 28, statInterpolation = { 1, }, actorLevel = 90.542999267578, },
				[20] = { 29, statInterpolation = { 1, }, actorLevel = 97.699996948242, },
				[21] = { 30, statInterpolation = { 1, }, actorLevel = 105.08300018311, },
				[22] = { 31, statInterpolation = { 1, }, actorLevel = 112.69200134277, },
				[23] = { 32, statInterpolation = { 1, }, actorLevel = 120.52700042725, },
				[24] = { 33, statInterpolation = { 1, }, actorLevel = 128.58799743652, },
				[25] = { 34, statInterpolation = { 1, }, actorLevel = 136.875, },
				[26] = { 35, statInterpolation = { 1, }, actorLevel = 145.38800048828, },
				[27] = { 36, statInterpolation = { 1, }, actorLevel = 154.12699890137, },
				[28] = { 37, statInterpolation = { 1, }, actorLevel = 163.09199523926, },
				[29] = { 38, statInterpolation = { 1, }, actorLevel = 172.28300476074, },
				[30] = { 39, statInterpolation = { 1, }, actorLevel = 181.69999694824, },
				[31] = { 40, statInterpolation = { 1, }, actorLevel = 191.34300231934, },
				[32] = { 41, statInterpolation = { 1, }, actorLevel = 201.21200561523, },
				[33] = { 42, statInterpolation = { 1, }, actorLevel = 211.30700683594, },
				[34] = { 43, statInterpolation = { 1, }, actorLevel = 221.62800598145, },
				[35] = { 44, statInterpolation = { 1, }, actorLevel = 232.17500305176, },
				[36] = { 45, statInterpolation = { 1, }, actorLevel = 242.94799804688, },
				[37] = { 46, statInterpolation = { 1, }, actorLevel = 253.94700622559, },
				[38] = { 47, statInterpolation = { 1, }, actorLevel = 265.17199707031, },
				[39] = { 48, statInterpolation = { 1, }, actorLevel = 276.62298583984, },
				[40] = { 49, statInterpolation = { 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["HeartOfIcePlayer"] = {
	name = "Heart of Ice",
	baseTypeName = "Heart of Ice",
	fromItem = true,
	color = 3,
	description = "Chills all enemies in your Presence.",
	skillTypes = { [SkillType.Buff] = true, [SkillType.HasReservation] = true, [SkillType.Aura] = true, [SkillType.OngoingSkill] = true, [SkillType.Persistent] = true, [SkillType.AffectsPresence] = true, [SkillType.Cold] = true, [SkillType.Spell] = true, },
	castTime = 0,
	qualityStats = {
		{ "active_skill_chill_effect_+%_final", 1 },
	},
	levels = {
		[1] = { critChance = 11, levelRequirement = 0, },
		[2] = { critChance = 11, levelRequirement = 3, },
		[3] = { critChance = 11, levelRequirement = 6, },
		[4] = { critChance = 11, levelRequirement = 10, },
		[5] = { critChance = 11, levelRequirement = 14, },
		[6] = { critChance = 11, levelRequirement = 18, },
		[7] = { critChance = 11, levelRequirement = 22, },
		[8] = { critChance = 11, levelRequirement = 26, },
		[9] = { critChance = 11, levelRequirement = 31, },
		[10] = { critChance = 11, levelRequirement = 36, },
		[11] = { critChance = 11, levelRequirement = 41, },
		[12] = { critChance = 11, levelRequirement = 46, },
		[13] = { critChance = 11, levelRequirement = 52, },
		[14] = { critChance = 11, levelRequirement = 58, },
		[15] = { critChance = 11, levelRequirement = 64, },
		[16] = { critChance = 11, levelRequirement = 66, },
		[17] = { critChance = 11, levelRequirement = 72, },
		[18] = { critChance = 11, levelRequirement = 78, },
		[19] = { critChance = 11, levelRequirement = 84, },
		[20] = { critChance = 11, levelRequirement = 90, },
		[21] = { critChance = 11, levelRequirement = 90, },
		[22] = { critChance = 11, levelRequirement = 90, },
		[23] = { critChance = 11, levelRequirement = 90, },
		[24] = { critChance = 11, levelRequirement = 90, },
		[25] = { critChance = 11, levelRequirement = 90, },
		[26] = { critChance = 11, levelRequirement = 90, },
		[27] = { critChance = 11, levelRequirement = 90, },
		[28] = { critChance = 11, levelRequirement = 90, },
		[29] = { critChance = 11, levelRequirement = 90, },
		[30] = { critChance = 11, levelRequirement = 90, },
		[31] = { critChance = 11, levelRequirement = 90, },
		[32] = { critChance = 11, levelRequirement = 90, },
		[33] = { critChance = 11, levelRequirement = 90, },
		[34] = { critChance = 11, levelRequirement = 90, },
		[35] = { critChance = 11, levelRequirement = 90, },
		[36] = { critChance = 11, levelRequirement = 90, },
		[37] = { critChance = 11, levelRequirement = 90, },
		[38] = { critChance = 11, levelRequirement = 90, },
		[39] = { critChance = 11, levelRequirement = 90, },
		[40] = { critChance = 11, levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Heart of Ice",
			baseEffectiveness = 3.7999999523163,
			incrementalEffectiveness = 0.14000000059605,
			damageIncrementalEffectiveness = 0.0082000000402331,
			statDescriptionScope = "chilling_aura",
			baseFlags = {
				buff = true,
				aura = true,
			},
			constantStats = {
				{ "skill_desired_amount_override", 1 },
			},
			stats = {
				"spell_minimum_base_cold_damage",
				"spell_maximum_base_cold_damage",
				"base_skill_show_average_damage_instead_of_dps",
				"skill_chill_magnitude_is_aura_magnitude",
			},
			levels = {
				[1] = { 12, 18, statInterpolation = { 1, 1, }, actorLevel = 1, },
				[2] = { 16, 24, statInterpolation = { 1, 1, }, actorLevel = 3.4519999027252, },
				[3] = { 22, 34, statInterpolation = { 1, 1, }, actorLevel = 6.7670001983643, },
				[4] = { 29, 44, statInterpolation = { 1, 1, }, actorLevel = 10.307999610901, },
				[5] = { 37, 56, statInterpolation = { 1, 1, }, actorLevel = 14.074999809265, },
				[6] = { 46, 69, statInterpolation = { 1, 1, }, actorLevel = 18.068000793457, },
				[7] = { 56, 84, statInterpolation = { 1, 1, }, actorLevel = 22.287000656128, },
				[8] = { 67, 101, statInterpolation = { 1, 1, }, actorLevel = 26.732000350952, },
				[9] = { 80, 119, statInterpolation = { 1, 1, }, actorLevel = 31.40299987793, },
				[10] = { 94, 140, statInterpolation = { 1, 1, }, actorLevel = 36.299999237061, },
				[11] = { 109, 164, statInterpolation = { 1, 1, }, actorLevel = 41.423000335693, },
				[12] = { 127, 191, statInterpolation = { 1, 1, }, actorLevel = 46.771999359131, },
				[13] = { 147, 221, statInterpolation = { 1, 1, }, actorLevel = 52.34700012207, },
				[14] = { 170, 254, statInterpolation = { 1, 1, }, actorLevel = 58.147998809814, },
				[15] = { 195, 292, statInterpolation = { 1, 1, }, actorLevel = 64.175003051758, },
				[16] = { 223, 335, statInterpolation = { 1, 1, }, actorLevel = 70.428001403809, },
				[17] = { 255, 383, statInterpolation = { 1, 1, }, actorLevel = 76.906997680664, },
				[18] = { 291, 437, statInterpolation = { 1, 1, }, actorLevel = 83.611999511719, },
				[19] = { 332, 498, statInterpolation = { 1, 1, }, actorLevel = 90.542999267578, },
				[20] = { 378, 567, statInterpolation = { 1, 1, }, actorLevel = 97.699996948242, },
				[21] = { 430, 645, statInterpolation = { 1, 1, }, actorLevel = 105.08300018311, },
				[22] = { 489, 734, statInterpolation = { 1, 1, }, actorLevel = 112.69200134277, },
				[23] = { 556, 834, statInterpolation = { 1, 1, }, actorLevel = 120.52700042725, },
				[24] = { 632, 947, statInterpolation = { 1, 1, }, actorLevel = 128.58799743652, },
				[25] = { 717, 1076, statInterpolation = { 1, 1, }, actorLevel = 136.875, },
				[26] = { 815, 1222, statInterpolation = { 1, 1, }, actorLevel = 145.38800048828, },
				[27] = { 925, 1388, statInterpolation = { 1, 1, }, actorLevel = 154.12699890137, },
				[28] = { 1051, 1577, statInterpolation = { 1, 1, }, actorLevel = 163.09199523926, },
				[29] = { 1195, 1792, statInterpolation = { 1, 1, }, actorLevel = 172.28300476074, },
				[30] = { 1359, 2038, statInterpolation = { 1, 1, }, actorLevel = 181.69999694824, },
				[31] = { 1545, 2318, statInterpolation = { 1, 1, }, actorLevel = 191.34300231934, },
				[32] = { 1759, 2638, statInterpolation = { 1, 1, }, actorLevel = 201.21200561523, },
				[33] = { 2003, 3004, statInterpolation = { 1, 1, }, actorLevel = 211.30700683594, },
				[34] = { 2283, 3424, statInterpolation = { 1, 1, }, actorLevel = 221.62800598145, },
				[35] = { 2603, 3905, statInterpolation = { 1, 1, }, actorLevel = 232.17500305176, },
				[36] = { 2971, 4456, statInterpolation = { 1, 1, }, actorLevel = 242.94799804688, },
				[37] = { 3394, 5090, statInterpolation = { 1, 1, }, actorLevel = 253.94700622559, },
				[38] = { 3880, 5820, statInterpolation = { 1, 1, }, actorLevel = 265.17199707031, },
				[39] = { 4440, 6660, statInterpolation = { 1, 1, }, actorLevel = 276.62298583984, },
				[40] = { 5086, 7629, statInterpolation = { 1, 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["IcestormPlayer"] = {
	name = "Icestorm",
	baseTypeName = "Icestorm",
	fromItem = true,
	color = 3,
	description = "Conjure a hail of icy bolts over the targeted area. Chill and Freeze on enemies in front of you are Consumed to fuel the Icestorm by creating improved bolts.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.Area] = true, [SkillType.Duration] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.Cold] = true, [SkillType.Cascadable] = true, [SkillType.CanRapidFire] = true, [SkillType.AreaSpell] = true, [SkillType.UsableWhileMoving] = true, [SkillType.Limit] = true, [SkillType.Sustained] = true, [SkillType.SkillConsumesFreeze] = true, },
	castTime = 1.2,
	qualityStats = {
		{ "active_skill_base_area_of_effect_radius", 0.1 },
	},
	levels = {
		[1] = { critChance = 11, levelRequirement = 0, cost = { Mana = 13, }, },
		[2] = { critChance = 11, levelRequirement = 3, cost = { Mana = 15, }, },
		[3] = { critChance = 11, levelRequirement = 6, cost = { Mana = 17, }, },
		[4] = { critChance = 11, levelRequirement = 10, cost = { Mana = 19, }, },
		[5] = { critChance = 11, levelRequirement = 14, cost = { Mana = 22, }, },
		[6] = { critChance = 11, levelRequirement = 18, cost = { Mana = 25, }, },
		[7] = { critChance = 11, levelRequirement = 22, cost = { Mana = 28, }, },
		[8] = { critChance = 11, levelRequirement = 26, cost = { Mana = 32, }, },
		[9] = { critChance = 11, levelRequirement = 31, cost = { Mana = 37, }, },
		[10] = { critChance = 11, levelRequirement = 36, cost = { Mana = 41, }, },
		[11] = { critChance = 11, levelRequirement = 41, cost = { Mana = 47, }, },
		[12] = { critChance = 11, levelRequirement = 46, cost = { Mana = 53, }, },
		[13] = { critChance = 11, levelRequirement = 52, cost = { Mana = 60, }, },
		[14] = { critChance = 11, levelRequirement = 58, cost = { Mana = 68, }, },
		[15] = { critChance = 11, levelRequirement = 64, cost = { Mana = 77, }, },
		[16] = { critChance = 11, levelRequirement = 66, cost = { Mana = 88, }, },
		[17] = { critChance = 11, levelRequirement = 72, cost = { Mana = 99, }, },
		[18] = { critChance = 11, levelRequirement = 78, cost = { Mana = 112, }, },
		[19] = { critChance = 11, levelRequirement = 84, cost = { Mana = 127, }, },
		[20] = { critChance = 11, levelRequirement = 90, cost = { Mana = 144, }, },
		[21] = { critChance = 11, levelRequirement = 90, cost = { Mana = 163, }, },
		[22] = { critChance = 11, levelRequirement = 90, cost = { Mana = 185, }, },
		[23] = { critChance = 11, levelRequirement = 90, cost = { Mana = 209, }, },
		[24] = { critChance = 11, levelRequirement = 90, cost = { Mana = 237, }, },
		[25] = { critChance = 11, levelRequirement = 90, cost = { Mana = 268, }, },
		[26] = { critChance = 11, levelRequirement = 90, cost = { Mana = 303, }, },
		[27] = { critChance = 11, levelRequirement = 90, cost = { Mana = 343, }, },
		[28] = { critChance = 11, levelRequirement = 90, cost = { Mana = 388, }, },
		[29] = { critChance = 11, levelRequirement = 90, cost = { Mana = 439, }, },
		[30] = { critChance = 11, levelRequirement = 90, cost = { Mana = 497, }, },
		[31] = { critChance = 11, levelRequirement = 90, cost = { Mana = 563, }, },
		[32] = { critChance = 11, levelRequirement = 90, cost = { Mana = 637, }, },
		[33] = { critChance = 11, levelRequirement = 90, cost = { Mana = 721, }, },
		[34] = { critChance = 11, levelRequirement = 90, cost = { Mana = 816, }, },
		[35] = { critChance = 11, levelRequirement = 90, cost = { Mana = 924, }, },
		[36] = { critChance = 11, levelRequirement = 90, cost = { Mana = 1045, }, },
		[37] = { critChance = 11, levelRequirement = 90, cost = { Mana = 1183, }, },
		[38] = { critChance = 11, levelRequirement = 90, cost = { Mana = 1339, }, },
		[39] = { critChance = 11, levelRequirement = 90, cost = { Mana = 1516, }, },
		[40] = { critChance = 11, levelRequirement = 90, cost = { Mana = 1715, }, },
	},
	statSets = {
		[1] = {
			label = "Storm",
			incrementalEffectiveness = 0.14000000059605,
			damageIncrementalEffectiveness = 0.006800000090152,
			statDescriptionScope = "ice_storm",
			baseFlags = {
				spell = true,
				area = true,
				duration = true,
			},
			constantStats = {
				{ "fire_storm_fireball_delay_ms", 150 },
				{ "firestorm_max_number_of_storms", 3 },
				{ "base_skill_effect_duration", 6000 },
				{ "active_skill_base_area_of_effect_radius", 9 },
				{ "active_skill_base_secondary_area_of_effect_radius", 26 },
				{ "movement_speed_+%_final_while_performing_action", -70 },
				{ "movement_speed_acceleration_+%_per_second_while_performing_action", 160 },
				{ "movement_speed_while_performing_action_locked_duration_%", 60 },
				{ "rain_hit_delay_ms", 150 },
				{ "firestorm_improved_bolts_per_chill_consumed", 1 },
				{ "firestorm_improved_bolts_per_monster_power_of_freeze_consumed", 1 },
			},
			stats = {
				"spell_minimum_base_cold_damage",
				"spell_maximum_base_cold_damage",
				"base_skill_show_average_damage_instead_of_dps",
				"is_area_damage",
				"can_perform_skill_while_moving",
				"cold_damage_cannot_chill",
				"never_freeze",
				"quality_display_active_skill_base_area_of_effect_radius_is_gem",
			},
			levels = {
				[1] = { 3, 5, statInterpolation = { 1, 1, }, actorLevel = 1, },
				[2] = { 4, 6, statInterpolation = { 1, 1, }, actorLevel = 3.4519999027252, },
				[3] = { 6, 9, statInterpolation = { 1, 1, }, actorLevel = 6.7670001983643, },
				[4] = { 8, 11, statInterpolation = { 1, 1, }, actorLevel = 10.307999610901, },
				[5] = { 10, 14, statInterpolation = { 1, 1, }, actorLevel = 14.074999809265, },
				[6] = { 12, 18, statInterpolation = { 1, 1, }, actorLevel = 18.068000793457, },
				[7] = { 14, 21, statInterpolation = { 1, 1, }, actorLevel = 22.287000656128, },
				[8] = { 17, 26, statInterpolation = { 1, 1, }, actorLevel = 26.732000350952, },
				[9] = { 20, 30, statInterpolation = { 1, 1, }, actorLevel = 31.40299987793, },
				[10] = { 23, 35, statInterpolation = { 1, 1, }, actorLevel = 36.299999237061, },
				[11] = { 27, 41, statInterpolation = { 1, 1, }, actorLevel = 41.423000335693, },
				[12] = { 31, 47, statInterpolation = { 1, 1, }, actorLevel = 46.771999359131, },
				[13] = { 36, 54, statInterpolation = { 1, 1, }, actorLevel = 52.34700012207, },
				[14] = { 41, 62, statInterpolation = { 1, 1, }, actorLevel = 58.147998809814, },
				[15] = { 47, 70, statInterpolation = { 1, 1, }, actorLevel = 64.175003051758, },
				[16] = { 53, 80, statInterpolation = { 1, 1, }, actorLevel = 70.428001403809, },
				[17] = { 60, 91, statInterpolation = { 1, 1, }, actorLevel = 76.906997680664, },
				[18] = { 68, 103, statInterpolation = { 1, 1, }, actorLevel = 83.611999511719, },
				[19] = { 77, 116, statInterpolation = { 1, 1, }, actorLevel = 90.542999267578, },
				[20] = { 87, 131, statInterpolation = { 1, 1, }, actorLevel = 97.699996948242, },
				[21] = { 98, 147, statInterpolation = { 1, 1, }, actorLevel = 105.08300018311, },
				[22] = { 110, 165, statInterpolation = { 1, 1, }, actorLevel = 112.69200134277, },
				[23] = { 124, 186, statInterpolation = { 1, 1, }, actorLevel = 120.52700042725, },
				[24] = { 139, 209, statInterpolation = { 1, 1, }, actorLevel = 128.58799743652, },
				[25] = { 156, 234, statInterpolation = { 1, 1, }, actorLevel = 136.875, },
				[26] = { 175, 263, statInterpolation = { 1, 1, }, actorLevel = 145.38800048828, },
				[27] = { 197, 295, statInterpolation = { 1, 1, }, actorLevel = 154.12699890137, },
				[28] = { 221, 331, statInterpolation = { 1, 1, }, actorLevel = 163.09199523926, },
				[29] = { 248, 372, statInterpolation = { 1, 1, }, actorLevel = 172.28300476074, },
				[30] = { 278, 417, statInterpolation = { 1, 1, }, actorLevel = 181.69999694824, },
				[31] = { 312, 468, statInterpolation = { 1, 1, }, actorLevel = 191.34300231934, },
				[32] = { 350, 526, statInterpolation = { 1, 1, }, actorLevel = 201.21200561523, },
				[33] = { 394, 590, statInterpolation = { 1, 1, }, actorLevel = 211.30700683594, },
				[34] = { 442, 663, statInterpolation = { 1, 1, }, actorLevel = 221.62800598145, },
				[35] = { 497, 745, statInterpolation = { 1, 1, }, actorLevel = 232.17500305176, },
				[36] = { 559, 838, statInterpolation = { 1, 1, }, actorLevel = 242.94799804688, },
				[37] = { 628, 943, statInterpolation = { 1, 1, }, actorLevel = 253.94700622559, },
				[38] = { 707, 1061, statInterpolation = { 1, 1, }, actorLevel = 265.17199707031, },
				[39] = { 797, 1195, statInterpolation = { 1, 1, }, actorLevel = 276.62298583984, },
				[40] = { 898, 1347, statInterpolation = { 1, 1, }, actorLevel = 288.29998779297, },
			},
		},
	}
}