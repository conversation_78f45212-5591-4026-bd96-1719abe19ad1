-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games
local itemBases = ...

itemBases["Rattling Sceptre"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Skeletal Warrior Minion",
	implicitModTypes = { },
	req = { },
}
itemBases["Stoic Sceptre"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Discipline",
	implicitModTypes = { },
	req = { },
}
itemBases["Lupine Sceptre"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Skeletal Warrior Minion",
	implicitModTypes = { },
	req = { },
}
itemBases["Omen Sceptre"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Malice",
	implicitModTypes = { },
	req = { },
}
itemBases["Ochre Sceptre"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Skeletal Warrior Minion",
	implicitModTypes = { },
	req = { },
}
itemBases["Shrine Sceptre"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Purity of Fire",
	implicitModTypes = { },
	req = { },
}
itemBases["Shrine Sceptre"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Purity of Ice",
	implicitModTypes = { },
	req = { },
}
itemBases["Shrine Sceptre"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Purity of Lightning",
	implicitModTypes = { },
	req = { },
}
itemBases["Devouring Sceptre"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Skeletal Warrior Minion",
	implicitModTypes = { },
	req = { },
}
itemBases["Clasped Sceptre"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Skeletal Warrior Minion",
	implicitModTypes = { },
	req = { },
}
itemBases["Devotional Sceptre"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Skeletal Warrior Minion",
	implicitModTypes = { },
	req = { },
}
itemBases["Wrath Sceptre"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Skeletal Warrior Minion",
	implicitModTypes = { },
	req = { },
}
itemBases["Aromatic Sceptre"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Skeletal Warrior Minion",
	implicitModTypes = { },
	req = { },
}
itemBases["Pious Sceptre"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Skeletal Warrior Minion",
	implicitModTypes = { },
	req = { },
}
itemBases["Hallowed Sceptre"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Skeletal Warrior Minion",
	implicitModTypes = { },
	req = { },
}

itemBases["Shrine Sceptre"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	hidden = true,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Purity of Fire",
	implicitModTypes = { },
	req = { },
}
itemBases["Shrine Sceptre"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	hidden = true,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Purity of Ice",
	implicitModTypes = { },
	req = { },
}
itemBases["Shrine Sceptre"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	hidden = true,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Purity of Lightning",
	implicitModTypes = { },
	req = { },
}

itemBases["Shrine Sceptre (Purity of Fire)"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Purity of Fire",
	implicitModTypes = { },
	req = { },
}
itemBases["Shrine Sceptre (Purity of Cold)"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Purity of Ice",
	implicitModTypes = { },
	req = { },
}
itemBases["Shrine Sceptre (Purity of Lighting)"] = {
	type = "Sceptre",
	quality = 20,
	spirit = 100,
	socketLimit = 2,
	tags = { default = true, onehand = true, sceptre = true, },
	implicit = "Grants Skill: Level (1-20) Purity of Lightning",
	implicitModTypes = { },
	req = { },
}
