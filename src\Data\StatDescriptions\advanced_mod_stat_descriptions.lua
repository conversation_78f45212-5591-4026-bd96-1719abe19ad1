-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=1,
						[2]=1
					},
					[4]={
						[1]="#",
						[2]="#"
					}
				},
				text="Bathed in the blood of {1} sacrificed in the name of Xibaqua(Ahuana-Xibaqua)\nPassives in radius are Conquered by the Vaal"
			},
			[2]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=2,
						[2]=2
					},
					[4]={
						[1]=0,
						[2]=0
					}
				},
				text="Bathed in the blood of {1} sacrificed in the name of Zerphi(Ahuana-Xibaqua)\nPassives in radius are Conquered by the Vaal"
			},
			[3]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=2,
						[2]=2
					},
					[4]={
						[1]=1,
						[2]="#"
					}
				},
				text="Bathed in the blood of {1} sacrificed in the name of Ahuana(Ahuana-Xibaqua)\nPassives in radius are Conquered by the Vaal"
			},
			[4]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=3,
						[2]=3
					},
					[4]={
						[1]="#",
						[2]="#"
					}
				},
				text="Bathed in the blood of {1} sacrificed in the name of Doryani(Ahuana-Xibaqua)\nPassives in radius are Conquered by the Vaal"
			},
			[5]={
				limit={
					[1]={
						[1]=2,
						[2]=2
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=1,
						[2]=1
					},
					[4]={
						[1]="#",
						[2]="#"
					}
				},
				text="Commanded leadership over {1} warriors under Kaom(Akoya-Rakiata)\nPassives in radius are Conquered by the Karui"
			},
			[6]={
				limit={
					[1]={
						[1]=2,
						[2]=2
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=2,
						[2]=2
					},
					[4]={
						[1]="#",
						[2]="#"
					}
				},
				text="Commanded leadership over {1} warriors under Rakiata(Akoya-Rakiata)\nPassives in radius are Conquered by the Karui"
			},
			[7]={
				limit={
					[1]={
						[1]=2,
						[2]=2
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=3,
						[2]=3
					},
					[4]={
						[1]=0,
						[2]=0
					}
				},
				text="Commanded leadership over {1} warriors under Kiloava(Akoya-Rakiata)\nPassives in radius are Conquered by the Karui"
			},
			[8]={
				limit={
					[1]={
						[1]=2,
						[2]=2
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=3,
						[2]=3
					},
					[4]={
						[1]=1,
						[2]="#"
					}
				},
				text="Commanded leadership over {1} warriors under Akoya(Akoya-Rakiata)\nPassives in radius are Conquered by the Karui"
			},
			[9]={
				limit={
					[1]={
						[1]=3,
						[2]=3
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=1,
						[2]=1
					},
					[4]={
						[1]=0,
						[2]=0
					}
				},
				text="Denoted service of {1} dekhara in the akhara of Deshret(Asenath-Nasima)\nPassives in radius are Conquered by the Maraketh"
			},
			[10]={
				limit={
					[1]={
						[1]=3,
						[2]=3
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=1,
						[2]=1
					},
					[4]={
						[1]=1,
						[2]="#"
					}
				},
				text="Denoted service of {1} dekhara in the akhara of Balbala(Asenath-Nasima)\nPassives in radius are Conquered by the Maraketh"
			},
			[11]={
				limit={
					[1]={
						[1]=3,
						[2]=3
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=2,
						[2]=2
					},
					[4]={
						[1]="#",
						[2]="#"
					}
				},
				text="Denoted service of {1} dekhara in the akhara of Asenath(Asenath-Nasima)\nPassives in radius are Conquered by the Maraketh"
			},
			[12]={
				limit={
					[1]={
						[1]=3,
						[2]=3
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=3,
						[2]=3
					},
					[4]={
						[1]="#",
						[2]="#"
					}
				},
				text="Denoted service of {1} dekhara in the akhara of Nasima(Asenath-Nasima)\nPassives in radius are Conquered by the Maraketh"
			},
			[13]={
				limit={
					[1]={
						[1]=4,
						[2]=4
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=1,
						[2]=1
					},
					[4]={
						[1]=0,
						[2]=0
					}
				},
				text="Carved to glorify {1} new faithful converted by High Templar Venarius(Avarius-Maxarius)\nPassives in radius are Conquered by the Templars"
			},
			[14]={
				limit={
					[1]={
						[1]=4,
						[2]=4
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=1,
						[2]=1
					},
					[4]={
						[1]=1,
						[2]="#"
					}
				},
				text="Carved to glorify {1} new faithful converted by High Templar Maxarius(Avarius-Maxarius)\nPassives in radius are Conquered by the Templars"
			},
			[15]={
				limit={
					[1]={
						[1]=4,
						[2]=4
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=2,
						[2]=2
					},
					[4]={
						[1]="#",
						[2]="#"
					}
				},
				text="Carved to glorify {1} new faithful converted by High Templar Dominus(Avarius-Maxarius)\nPassives in radius are Conquered by the Templars"
			},
			[16]={
				limit={
					[1]={
						[1]=4,
						[2]=4
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=3,
						[2]=3
					},
					[4]={
						[1]="#",
						[2]="#"
					}
				},
				text="Carved to glorify {1} new faithful converted by High Templar Avarius(Avarius-Maxarius)\nPassives in radius are Conquered by the Templars"
			},
			[17]={
				[1]={
					k="times_twenty",
					v=2
				},
				limit={
					[1]={
						[1]=5,
						[2]=5
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=1,
						[2]=1
					},
					[4]={
						[1]="#",
						[2]="#"
					}
				},
				text="Commissioned {1} coins to commemorate Cadiro(Cadiro-Victario)\nPassives in radius are Conquered by the Eternal Empire"
			},
			[18]={
				[1]={
					k="times_twenty",
					v=2
				},
				limit={
					[1]={
						[1]=5,
						[2]=5
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=2,
						[2]=2
					},
					[4]={
						[1]="#",
						[2]="#"
					}
				},
				text="Commissioned {1} coins to commemorate Victario(Cadiro-Victario)\nPassives in radius are Conquered by the Eternal Empire"
			},
			[19]={
				[1]={
					k="times_twenty",
					v=2
				},
				limit={
					[1]={
						[1]=5,
						[2]=5
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=3,
						[2]=3
					},
					[4]={
						[1]=0,
						[2]=0
					}
				},
				text="Commissioned {1} coins to commemorate Chitus(Cadiro-Victario)\nPassives in radius are Conquered by the Eternal Empire"
			},
			[20]={
				[1]={
					k="times_twenty",
					v=2
				},
				limit={
					[1]={
						[1]=5,
						[2]=5
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=3,
						[2]=3
					},
					[4]={
						[1]=1,
						[2]="#"
					}
				},
				text="Commissioned {1} coins to commemorate Caspiro(Cadiro-Victario)\nPassives in radius are Conquered by the Eternal Empire"
			},
			[21]={
				limit={
					[1]={
						[1]=6,
						[2]=6
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=1,
						[2]=1
					},
					[4]={
						[1]="#",
						[2]="#"
					}
				},
				text="Remembrancing {1} songworthy deeds by the line of Vorana(Vorana-Olroth)\nPassives in radius are Conquered by the Kalguur"
			},
			[22]={
				limit={
					[1]={
						[1]=6,
						[2]=6
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=2,
						[2]=2
					},
					[4]={
						[1]="#",
						[2]="#"
					}
				},
				text="Remembrancing {1} songworthy deeds by the line of Medved(Vorana-Olroth)\nPassives in radius are Conquered by the Kalguur"
			},
			[23]={
				limit={
					[1]={
						[1]=6,
						[2]=6
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=3,
						[2]=3
					},
					[4]={
						[1]="#",
						[2]="#"
					}
				},
				text="Remembrancing {1} songworthy deeds by the line of Olroth(Vorana-Olroth)\nPassives in radius are Conquered by the Kalguur"
			}
		},
		stats={
			[1]="local_unique_jewel_alternate_tree_version",
			[2]="local_unique_jewel_alternate_tree_seed",
			[3]="local_unique_jewel_alternate_tree_keystone",
			[4]="local_unique_jewel_alternate_tree_internal_revision"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Only affects Passives in Very Small Ring(Very Small-Massive Ring)"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]=2
					}
				},
				text="Only affects Passives in Small Ring(Very Small-Massive Ring)"
			},
			[3]={
				limit={
					[1]={
						[1]=3,
						[2]=3
					}
				},
				text="Only affects Passives in Medium-Small Ring(Very Small-Massive Ring)"
			},
			[4]={
				limit={
					[1]={
						[1]=4,
						[2]=4
					}
				},
				text="Only affects Passives in Medium Ring(Very Small-Massive Ring)"
			},
			[5]={
				limit={
					[1]={
						[1]=5,
						[2]=5
					}
				},
				text="Only affects Passives in Medium-Large Ring(Very Small-Massive Ring)"
			},
			[6]={
				limit={
					[1]={
						[1]=6,
						[2]=6
					}
				},
				text="Only affects Passives in Large Ring(Very Small-Massive Ring)"
			},
			[7]={
				limit={
					[1]={
						[1]=7,
						[2]=7
					}
				},
				text="Only affects Passives in Very Large Ring(Very Small-Massive Ring)"
			},
			[8]={
				limit={
					[1]={
						[1]=8,
						[2]=8
					}
				},
				text="Only affects Passives in Massive Ring(Very Small-Massive Ring)"
			}
		},
		stats={
			[1]="local_jewel_variable_ring_radius_value"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Area Damage per 10 Devotion"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Area Damage per 10 Devotion"
			}
		},
		stats={
			[1]="area_damage_+%_per_10_devotion"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Channelling Skills deal {0}% increased Damage per 10 Devotion"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Channelling Skills deal {0}% reduced Damage per 10 Devotion"
			}
		},
		stats={
			[1]="channelled_skill_damage_+%_per_10_devotion"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Elemental Damage per 10 Devotion"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Elemental Damage per 10 Devotion"
			}
		},
		stats={
			[1]="elemental_damage_+%_per_10_devotion"
		}
	},
	[6]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0:+d}% to all Elemental Resistances per 10 Devotion"
			}
		},
		stats={
			[1]="elemental_resistance_%_per_10_devotion"
		}
	},
	[7]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=0,
						[2]=0
					},
					[2]={
						[1]="!",
						[2]=0
					}
				},
				text="Non-Aura Hexes expire upon reaching {1:+d}% of base Effect"
			},
			[2]={
				limit={
					[1]={
						[1]="!",
						[2]=0
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="Non-Aura Hexes expire upon reaching 200% of base Effect"
			}
		},
		stats={
			[1]="hexes_expire_on_reaching_200%_effect",
			[2]="hex_remove_at_effect_variance"
		}
	},
	[8]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Mana Cost of Skills per 10 Devotion"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Mana Cost of Skills per 10 Devotion"
			}
		},
		stats={
			[1]="mana_cost_+%_per_10_devotion"
		}
	},
	[9]={
		[1]={
			[1]={
				[1]={
					k="per_minute_to_per_second",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Regenerate {0} Mana per Second per 10 Devotion"
			}
		},
		stats={
			[1]="mana_regeneration_rate_per_minute_per_10_devotion"
		}
	},
	[10]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Minions have +{0} to Accuracy Rating per 10 Devotion"
			}
		},
		stats={
			[1]="minion_accuracy_rating_per_10_devotion"
		}
	},
	[11]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Minion Attack and Cast Speed per 10 Devotion"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Minion Attack and Cast Speed per 10 Devotion"
			}
		},
		stats={
			[1]="minion_attack_and_cast_speed_+%_per_10_devotion"
		}
	},
	[12]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased effect of Non-Curse Auras per 10 Devotion"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced effect of Non-Curse Auras per 10 Devotion"
			}
		},
		stats={
			[1]="non_curse_aura_effect_+%_per_10_devotion"
		}
	},
	[13]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Magnitude of Non-Damaging Ailments you inflict per 10 Devotion"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Magnitude of Non-Damaging Ailments you inflict per 10 Devotion"
			}
		},
		stats={
			[1]="non_damaging_ailment_effect_+%_per_10_devotion"
		}
	},
	[14]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Duration of Curses on you per 10 Devotion"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Duration of Curses on you per 10 Devotion"
			}
		},
		stats={
			[1]="self_curse_duration_+%_per_10_devotion"
		}
	},
	[15]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% reduced Elemental Ailment Duration on you per 10 Devotion"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% increased Elemental Ailment Duration on you per 10 Devotion"
			}
		},
		stats={
			[1]="self_elemental_status_duration_-%_per_10_devotion"
		}
	},
	[16]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Defences from Equipped Shield per 10 Devotion"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Defences from Equipped Shield per 10 Devotion"
			}
		},
		stats={
			[1]="shield_defences_+%_per_10_devotion"
		}
	},
	[17]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Brand Damage per 10 Devotion"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Brand Damage per 10 Devotion"
			}
		},
		stats={
			[1]="sigil_damage_+%_per_10_devotion"
		}
	},
	[18]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Totem Damage per 10 Devotion"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Totem Damage per 10 Devotion"
			}
		},
		stats={
			[1]="totem_damage_+%_per_10_devotion"
		}
	},
	["area_damage_+%_per_10_devotion"]=3,
	["channelled_skill_damage_+%_per_10_devotion"]=4,
	["elemental_damage_+%_per_10_devotion"]=5,
	["elemental_resistance_%_per_10_devotion"]=6,
	["hex_remove_at_effect_variance"]=7,
	["hexes_expire_on_reaching_200%_effect"]=7,
	["local_jewel_variable_ring_radius_value"]=2,
	["local_unique_jewel_alternate_tree_internal_revision"]=1,
	["local_unique_jewel_alternate_tree_keystone"]=1,
	["local_unique_jewel_alternate_tree_seed"]=1,
	["local_unique_jewel_alternate_tree_version"]=1,
	["mana_cost_+%_per_10_devotion"]=8,
	["mana_regeneration_rate_per_minute_per_10_devotion"]=9,
	["minion_accuracy_rating_per_10_devotion"]=10,
	["minion_attack_and_cast_speed_+%_per_10_devotion"]=11,
	["non_curse_aura_effect_+%_per_10_devotion"]=12,
	["non_damaging_ailment_effect_+%_per_10_devotion"]=13,
	parent="stat_descriptions",
	["self_curse_duration_+%_per_10_devotion"]=14,
	["self_elemental_status_duration_-%_per_10_devotion"]=15,
	["shield_defences_+%_per_10_devotion"]=16,
	["sigil_damage_+%_per_10_devotion"]=17,
	["totem_damage_+%_per_10_devotion"]=18
}