-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Shield Bash causes {0}% more Heavy Stun buildup"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Shield Bash causes {0}% less Heavy Stun buildup"
			}
		},
		stats={
			[1]="active_skill_hit_damage_stun_multiplier_+%_final"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Shield Bash always Light Stuns\nShield Bash Dazes if holding a Tower Shield"
			}
		},
		stats={
			[1]="always_light_stun"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Shield Bash can't be Evaded"
			}
		},
		stats={
			[1]="global_always_hit"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Shield Bash deals {0} to {1} Physical Damage per 5 Armour on Shield"
			}
		},
		stats={
			[1]="off_hand_minimum_added_physical_damage_per_5_shield_armour",
			[2]="off_hand_maximum_added_physical_damage_per_5_shield_armour"
		}
	},
	["active_skill_hit_damage_stun_multiplier_+%_final"]=1,
	["always_light_stun"]=2,
	["global_always_hit"]=3,
	["off_hand_maximum_added_physical_damage_per_5_shield_armour"]=4,
	["off_hand_minimum_added_physical_damage_per_5_shield_armour"]=4,
	parent="skill_stat_descriptions"
}