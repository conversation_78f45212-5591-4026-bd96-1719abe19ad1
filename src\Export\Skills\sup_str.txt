-- Path of Building
--
-- Strength support gems
-- Skill data (c) Grinding Gear Games
--
local skills, mod, flag, skill = ...
#skill SupportAftershockChancePlayer
#set SupportAftershockChancePlayer
#mods
#skillEnd

#skill SupportAncestralAidPlayer
#set SupportAncestralAidPlayer
#mods
#skillEnd

#skill SupportAncestralCallPlayer
#set SupportAncestralCallPlayer
#mods
#skillEnd

#skill SupportAncestralUrgencyPlayer
#set SupportAncestralUrgencyPlayer
#mods
#skillEnd

#skill SupportArmourExplosionPlayer
#set SupportArmourExplosionPlayer
#mods
#skillEnd
#skill ArmourExplosionPlayer
#set ArmourExplosion
#mods
#skillEnd

#skill SupportArmsLengthPlayer
#set SupportArmsLengthPlayer
statMap = {
	["support_arms_length_knockback_distance_+%_final"] = {
		mod("EnemyKnockbackDistance", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportAutoReloadPlayer
#set SupportAutoReloadPlayer
#mods
#skillEnd

#skill SupportBarbsPlayer
#set SupportBarbsPlayer
#mods
#skillEnd

#skill SupportBattershoutPlayer
#set SupportBattershoutPlayer
#mods
#set TriggeredBattershoutExplosionPlayer
#mods
#skillEnd

#skill SupportBeheadPlayer
#set SupportBeheadPlayer
#mods
#skillEnd

#skill SupportBloodlustPlayer
#set SupportBloodlustPlayer
statMap = {
	["support_bloodlust_melee_physical_damage_+%_final_vs_bleeding_enemies"] = {
		mod("PhysicalDamage", "MORE", nil, ModFlag.Melee, 0, { type = "ActorCondition", actor = "enemy", var = "Bleeding" }),
	},
},
#mods
#skillEnd

#skill SupportKnockbackPlayer
#set SupportKnockbackPlayer
#mods
#skillEnd

#skill SupportEnduranceChargeOnArmourBreak
#set SupportEnduranceChargeOnArmourBreak
#mods
#skillEnd

#skill SupportBrinkPlayer
#set SupportBrinkPlayer
statMap = {
	["support_brink_damage_+%_final_vs_heavy_stunned_target"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "ActorCondition", actor = "enemy", var = "HeavyStunned" }),
	},
},
#mods
#skillEnd

#skill SupportBrutalityPlayer
#set SupportBrutalityPlayer
statMap = {
	["support_brutality_physical_damage_+%_final"] = {
		mod("PhysicalDamage", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportCannibalismPlayer
#set SupportCannibalismPlayer
statMap = {
	["support_cannibalism_recover_%_maximum_life_on_kill"] = {
		mod("LifeOnKill", "BASE", nil, 0, 0, { type = "PercentStat", stat = "Life", percent = 1 }, { type = "GlobalEffect", effectType = "Buff", effectName = "Cannibalism" }),
	},
},
#mods
#skillEnd

#skill SupportClashPlayer
#set SupportClashPlayer
statMap = {
	["support_melee_damage_+%_final_vs_higher_percent_life_target"] = {
		mod("Damage", "MORE", nil, ModFlag.Melee, 0, { type = "Condition", var = "EnemyHigherLifePercent" }),
	},
	["support_melee_damage_+%_final_vs_lower_percent_life_target"] = {
		mod("Damage", "MORE", nil, ModFlag.Melee, 0, { type = "Condition", var = "EnemyHigherLifePercent", neg = true }),
	},
},
#mods
#skillEnd

#skill SupportConcoctPlayer
#set SupportConcoctPlayer
statMap = {
	["consume_%_of_maximum_life_flask_charges_on_skill_use"] = {
		mod("Multiplier:LifeFlaskMaxChargesPercent", "BASE", nil),
	},
	["support_concoct_bleed_effect_+%_final_per_life_flask_charge_consumed"] = {
		mod("AilmentMagnitude", "MORE", nil, 0, KeywordFlag.Bleed, { type = "Multiplier", var = "LifeFlaskChargeConsumed"}),
	},
},
#baseMod mod("Multiplier:LifeFlaskChargeConsumed", "BASE", 1, 0, 0, { type = "PercentStat", stat = "LifeFlask1MaxCharges", percentVar = "LifeFlaskMaxChargesPercent", floor = true })
#baseMod mod("Multiplier:LifeFlaskChargeConsumed", "BASE", 1, 0, 0, { type = "PercentStat", stat = "LifeFlask2MaxCharges", percentVar = "LifeFlaskMaxChargesPercent", floor = true })
#mods
#skillEnd

#skill SupportCoolheadedPlayer
#set SupportCoolheadedPlayer
statMap = {
	["support_ignite_protection_spirit_cost_ignite_duration_on_self_+%_final"] = {
		mod("SelfIgniteDuration", "MORE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Cool Headed" }),
	},
},
#mods
#skillEnd

#skill SupportCorruptingCryPlayer
#set SupportCorruptingCryPlayer
statMap = {
	["support_corrupting_cry_corrupted_blood_base_physical_damage_per_minute_as_%_of_strength"] = {
		skill("PhysicalDot", nil, { type = "PercentStat", stat = "Str", percent = 1 }),
		div = 60,
	},
	["support_corrupting_cry_warcry_applies_X_stacks_of_corrupted_blood"] = {
		mod("CorruptingCryStagesFromWarcry", nil, 0, KeywordFlag.Warcry)
	},
	["support_corrupting_cry_area_of_effect_+%_final"] = {
		mod("AreaOfEffect", "MORE", nil, 0, KeywordFlag.Warcry)
	},
	["support_corrupting_cry_corrupted_blood_duration_ms"] = {
		skill("durationSecondary", nil),
		div = 1000,
	},
	["support_corrupting_cry_warcry_applies_x_stacks_of_corrupted_blood"] = {
		-- Display only
	},
},
#baseMod skill("debuff", true)
#baseMod flag("dotIsCorruptingBlood")
#baseMod mod("Multiplier:CorruptingCryMaxStages", "BASE", 10)
#baseMod mod("Damage", "MORE", 100, 0, KeywordFlag.PhysicalDot, { type = "Multiplier", var = "CorruptingCryStageAfterFirst"})
#mods
#skillEnd

#skill SupportDauntlessPlayer
#set SupportDauntlessPlayer
statMap = {
	["support_unmoving_damage_+%_final_per_250_ms_stationary"] = {
		mod("Damage", "MORE", nil, ModFlag.Hit, 0, { type = "Condition", var = "Stationary" }, { type = "Multiplier", var = "StationarySeconds", div = 0.25, limitVar = "DauntlessMaxDamage", limitTotal = true }),
	},
	["support_unmoving_damage_multiplier_cap"] = {
		mod("Multiplier:DauntlessMaxDamage", "BASE", nil),
	},
},
#mods
#skillEnd

#skill SupportDazingCryPlayer
#set SupportDazingCryPlayer
#mods
#skillEnd

#skill SupportDeepCutsPlayer
#set SupportDeepCutsPlayer
statMap = {
	["support_deep_cuts_hit_damage_+%_final"] = {
		mod("Damage", "MORE", nil, ModFlag.Hit),
	},
	["support_deep_cuts_bleeding_effect_+%_final"] = {
		mod("AilmentMagnitude", "MORE", nil, 0, KeywordFlag.Bleed),
	},
},
#mods
#skillEnd

#skill SupportDefyPlayer
#set SupportDefyPlayer
#mods
#skillEnd

#skill SupportIncreasedArmourBreakPlayer
#set SupportIncreasedArmourBreakPlayer
statMap = {
	["support_increased_armour_break_armour_break_amount_+%_final"] = {
		mod("ArmourBreakEffect", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportDesperationPlayer
#set SupportDesperationPlayer
#mods
#skillEnd

#skill SupportGroundEffectDurationPlayer
#set SupportGroundEffectDurationPlayer
#mods
#skillEnd

#skill SupportDevastatePlayer
#set SupportDevastatePlayer
statMap = {
	["fully_break_enemies_armour_on_heavy_stun"] = {
		flag("Condition:CanArmourBreak", { type = "GlobalEffect", effectType = "Buff", effectName = "ArmourBreak" } ),
	},
},
#mods
#skillEnd

#skill SupportDirestrikePlayer
#set SupportDirestrikePlayer
statMap = {
	["support_attack_damage_spirit_cost_attack_damage_+%_on_low_life"] = {
		mod("Damage", "INC", nil, ModFlag.Attack, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Direstrike" }),
	},
},
#mods
#skillEnd

#skill SupportDomainPlayer
#set SupportDomainPlayer
#mods
#skillEnd

#skill SupportDoubleBarrelPlayer
#set SupportDoubleBarrelPlayer
statMap = {
	["support_double_barrel_number_of_crossbow_bolts_+"] = {
		mod("CrossbowBoltCount", "BASE", nil),
	},
	["support_double_barrel_crossbow_reload_speed_-%_final"] = {
		mod("ReloadSpeed", "MORE", nil),
		mult = -1
	},
},
#mods
#skillEnd

#skill SupportEnragedWarcryPlayer
#set SupportEnragedWarcryPlayer
#mods
#skillEnd

#skill SupportIgniteDurationPlayer
#set SupportIgniteDurationPlayer
statMap = {
	["support_eternal_flame_chance_to_ignite_+%_final"] = {
		mod("EnemyIgniteChance", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportExecutePlayer
#set SupportExecutePlayer
statMap = {
	["support_executioner_damage_vs_enemies_on_low_life_+%_final"] = {
		mod("Damage", "MORE", nil, ModFlag.Hit, 0, { type = "ActorCondition", actor = "enemy", var = "LowLife"})
	},
},
#mods
#skillEnd

#skill SupportExpeditePlayer
#set SupportExpeditePlayer
#mods
#skillEnd

#skill SupportExploitWeaknessPlayer
#set SupportExploitWeaknessPlayer
statMap = {
	["support_gem_consume_enemy_fully_broken_armour_to_gain_damage_+%_final"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "ActorCondition", actor = "enemy", var = "ArmourFullyBroken"})
	},
},
#mods
#skillEnd

#skill LessDurationSupportPlayer
#set LessDurationSupportPlayer
statMap = {
	["support_reduced_duration_skill_effect_duration_+%_final"] = {
		mod("Duration", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportFigureheadPlayer
#set SupportFigureheadPlayer
#mods
#skillEnd

#skill SupportFireExposurePlayer
#set SupportFireExposurePlayer
statMap = {
	["inflict_fire_exposure_for_x_ms_on_ignite"] = {
		mod("FireExposureChance", "BASE", nil, 0, 0, { type = "ActorCondition", actor = "enemy", var = "Ignited"}),
	},
},
#mods
#skillEnd

#skill SupportAddedFireDamagePlayer
#set SupportAddedFireDamagePlayer
statMap = {
	["support_cold_and_lightning_damage_+%_final"] = {
		mod("ColdDamage", "MORE", nil),
		mod("LightningDamage", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportFirePenetrationPlayer
#set SupportFirePenetrationPlayer
#mods
#skillEnd

#skill SupportFirstBloodPlayer
#set SupportFirstBloodPlayer
#mods
#skillEnd

#skill FistOfWarSupportPlayer
#set FistOfWarSupportPlayer
statMap = {
	["ancestral_slam_interval_duration"] = {
		mod("FistOfWarCooldown", "BASE", nil),
		div = 1000,
	},
},
#baseMod mod("FistOfWarDamageMultiplier", "BASE", 20)
#baseMod mod("FistOfWarMOREAoE", "BASE", 20)
#mods
#skillEnd

#skill SupportFlamepiercePlayer
#set SupportFlamepiercePlayer
#mods
#skillEnd

#skill SupportBloodFountainPlayer
#set SupportBloodFountainPlayer
statMap = {
	["support_blood_fountain_life_regeneration_rate_per_minute_%"] = {
		mod("LifeRegenPercent", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Aura" }),
		div = 60,
	},
},
#mods
#skillEnd

#skill SupportRageFountainPlayer
#set SupportRageFountainPlayer
statMap = {
	["support_rage_fountain_rage_regeneration_per_minute"] = {
		mod("RageRegen", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Aura" }),
		div = 60,
	},
},
#mods
#skillEnd

#skill SupportFreshClipPlayer
#set SupportFreshClipPlayer
statMap = {
	["support_damage_+%_final_per_crossbow_bolt_reloaded_in_past_6_seconds"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "Multiplier", var = "BoltsReloadedPastSixSeconds" } ),
	},
},
#mods
#skillEnd

#skill SupportGreatwoodPlayer
#set SupportGreatwoodPlayer
#mods
#skillEnd

#skill SupportHaemocrystalsPlayer
#set SupportHaemocrystalsPlayer
#mods
#skillEnd

#skill TriggeredHaemocrystalsPlayer
#set SupportHaemocrystalsPlayer
#flags hit area
#mods
#skillEnd

#skill SupportMeleePhysicalDamagePlayer
#set SupportMeleePhysicalDamagePlayer
statMap = {
	["support_melee_physical_damage_+%_final"] = {
		mod("PhysicalDamage", "MORE", nil, ModFlag.Melee),
	},
	["support_melee_physical_damage_attack_speed_+%_final"] = {
		mod("Speed", "MORE", nil, ModFlag.Attack),
	},
},
#mods
#skillEnd

#skill SupportHeftPlayer
#set SupportHeftPlayer
statMap = {
	["support_heft_maximum_physical_damage_+%_final"] = {
		mod("MaxPhysicalDamage", "MORE", nil, ModFlag.Hit),
	},
},
#mods
#skillEnd

#skill SupportHerbalismPlayer
#set SupportHerbalismPlayer
statMap = {
	["support_herbalism_life_recovery_+%_from_life_flasks"] = {
		mod("FlaskLifeRecovery", "INC", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Herbalism" }),
	},
},
#mods
#skillEnd

#skill SupportHolyDescentPlayer
#set SupportHolyDescentPlayer
statMap = {
	["support_holy_descent_consecrated_ground_on_landing"] = {
		-- Display only
	},
	["support_holy_descent_consecrated_ground_base_duration_ms"] = {
		-- Display only
	},
},
#mods
#skillEnd

#skill SupportChanceToIgnitePlayer
#set SupportChanceToIgnitePlayer
statMap = {
	["support_ignition_chance_to_ignite_+%_final"] = {
		mod("EnemyIgniteChance", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportImmolatePlayer
#set SupportImmolatePlayer
#mods
#skillEnd

#skill ImpactShockwaveSupportPlayer
#set ImpactShockwaveSupportPlayer
#mods
#skillEnd

#skill SupportIncisionPlayer
#set SupportIncisionPlayer
statMap = {
	["support_incision_bleeding_effect_+%_final_per_incision_consumed_recently_up_to_30%"] = {
		mod("AilmentMagnitude", "MORE", nil, 0, KeywordFlag.Bleed, { type = "Multiplier", var = "IncisionConsumedRecently", limit = 30, limitTotal = true }),
	},
},
#mods
#skillEnd

#skill SupportInfernalLegionPlayer
#set SupportInfernalLegionPlayer
statMap = {
	["minion_fire_damage_%_of_maximum_life_taken_per_minute"] = {
		mod("MinionModifier", "LIST", { mod = mod("FireDegen", "BASE", nil, 0, 0, { type = "PerStat", stat = "Life" }, { type = "GlobalEffect", effectType = "Buff" }) }),
		div = 6000,
	},
	["support_minion_instability_minion_base_fire_area_damage_per_minute"] = {
		mod("MinionModifier", "LIST", { mod = mod("Multiplier:InfernalLegionBaseDamage", "BASE", nil, 0, 0, { type = "PercentStat", stat = "Life", percent = 1 }) }),
		div = 60,
		mod("ExtraMinionSkill", "LIST", { skillId = "InfernalLegion" }),
	},
},
#mods
#skillEnd

#skill SupportInspirationPlayer
#set SupportInspirationPlayer
statMap = {
	["support_inspiration_cost_+%_final"] = {
		mod("Cost", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportInterludePlayer
#set SupportInterludePlayer
#mods
#skillEnd

#skill SupportIronwoodPlayer
#set SupportIronwoodPlayer
#mods
#skillEnd

#skill SupportJaggedGroundPlayer
#set SupportJaggedGroundPlayer
#mods
#skillEnd

#skill SupportChanceToBleedPlayer
#set SupportChanceToBleedPlayer
#mods
#skillEnd

#skill SupportLifeLeechPlayer
#set SupportLifeLeechPlayer
#mods
#skillEnd

#skill SupportBloodMagicPlayer
#set SupportBloodMagicPlayer
#mods
#skillEnd

#skill SupportLongFusePlayer
#set SupportLongFusePlayer
#mods
#skillEnd

#skill SupportMeatShieldPlayer
#set SupportMeatShieldPlayer
statMap = {
	["support_minion_defensive_stance_minion_damage_taken_+%_final"] = {
		mod("MinionModifier", "LIST", { mod = mod("DamageTaken", "MORE", nil) }),
	},
	["support_meat_shield_minion_damage_+%_final"] = {
		mod("MinionModifier", "LIST", { mod = mod("Damage", "MORE", nil) }),
	},
},
#mods
#skillEnd

#skill SupportOverpowerPlayer
#set SupportOverpowerPlayer
statMap = {
	["support_overpower_hit_damage_stun_multiplier_+%_final"] = {
		mod("StunBuildup", "MORE", nil),
	},
},
#mods
#skillEnd

#skill MoreDurationSupportPlayer
#set MoreDurationSupportPlayer
statMap = {
	["support_more_duration_skill_effect_duration_+%_final"] = {
		mod("Duration", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportEmpoweredDamagePlayer
#set SupportEmpoweredDamagePlayer
statMap = {
	["support_empowered_damage_+%_final"] = {
		mod("ExtraEmpowerMod", "LIST", { mod = mod("Damage", "MORE", nil) }),
	}
},
#mods
#skillEnd

#skill SupportWeaponElementalDamagePlayer
#set SupportWeaponElementalDamagePlayer
statMap = {
	["support_weapon_elemental_damage_+%_final"] = {
		mod("ElementalDamage", "MORE", nil, 0, KeywordFlag.Attack),
	},
},
#mods
#skillEnd

#skill SupportQuillburstPlayer
#set SupportQuillburstPlayer
#mods
#set TriggeredQuillburstPlayer
#mods
#skillEnd

#skill SupportRagePlayer
#set SupportRagePlayer
#mods
#skillEnd

#skill SupportRageforgedPlayer
#set SupportRageforgedPlayer
#mods
#skillEnd

#skill SupportRagingCryPlayer
#set SupportRagingCryPlayer
#mods
#skillEnd

#skill SupportRallyPlayer
#set SupportRallyPlayer
#mods
#skillEnd

#skill SupportRefractionPlayer
#set SupportRefractionPlayer
statMap = {
	["support_tempered_valour_%_armour_to_apply_to_elemental_damage"] = {
		mod("ArmourAppliesToFireDamageTaken", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Refractive Plating" }, { type = "MultiplierThreshold", var = "ValourStacks", thresholdVar = "RefractionMinimumValour" }),
		mod("ArmourAppliesToColdDamageTaken", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Refractive Plating" }, { type = "MultiplierThreshold", var = "ValourStacks", thresholdVar = "RefractionMinimumValour" }),
		mod("ArmourAppliesToLightningDamageTaken", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Refractive Plating"}, { type = "MultiplierThreshold", var = "ValourStacks", thresholdVar = "RefractionMinimumValour" }),
	},
	["support_tempered_valour_minimum_valour_to_apply_buff"] = {
		mod("Multiplier:RefractionMinimumValour", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff"}),
	},
},
#mods
#skillEnd

#skill SupportRetaliatePlayer
#set SupportRetaliatePlayer
#mods
#skillEnd

#skill SupportReveberatePlayer
#set SupportReveberatePlayer
#mods
#skillEnd

#skill SupportRipPlayer
#set SupportRipPlayer
#mods
#skillEnd

#skill SupportRupturePlayer
#set SupportRupturePlayer
#mods
#skillEnd

#skill SupportRustedSpikesPlayer
#set SupportRustedSpikesPlayer
#mods
#skillEnd

#skill RuthlessSupportPlayer
#set RuthlessSupportPlayer
#mods
#skillEnd

#skill SupportDeadlyIgnitesPlayer
#set SupportDeadlyIgnitesPlayer
statMap = {
	["support_stronger_ignites_hit_damage_+%_final"] = {
		mod("Damage", "MORE", nil, ModFlag.Hit),
	},
	["support_stronger_ignites_ignite_effect_+%_final"] = {
		mod("AilmentMagnitude", "MORE", nil, 0, KeywordFlag.Ignite),
	},
},
#mods
#skillEnd

#skill SupportSeeRedPlayer
#set SupportSeeRedPlayer
#mods
#skillEnd

#skill SupportSparPlayer
#set SupportSparPlayer
#mods
#skillEnd

#skill SupportArmourBreakPlayer
#set SupportArmourBreakPlayer
#mods
#skillEnd

#skill SupportSteadfastPlayer
#set SupportSteadfastPlayer
#mods
#skillEnd

#skill SupportStompingGroundPlayer
#set SupportStompingGroundPlayer
#mods
#skillEnd

#skill StompingGroundShockwavePlayer
#set StompingGroundShockwavePlayer
#flags nonWeaponAttack area melee
statMap = {
	["attack_minimum_added_physical_damage_as_%_of_strength"] = {
		skill("PhysicalMin", nil, { type = "PercentStat", stat = "Str", percent = 1 }),
	},
	["attack_maximum_added_physical_damage_as_%_of_strength"] = {
		skill("PhysicalMax", nil, { type = "PercentStat", stat = "Str", percent = 1 }),
	},
},
#baseMod skill("showAverage", true)
#mods
#skillEnd

#skill SupportSyzygyPlayer
#set SupportSyzygyPlayer
#mods
#skillEnd

#skill SupportTearPlayer
#set SupportTearPlayer
#mods
#skillEnd

#skill SupportThornskinPlayer
#set SupportThornskinPlayer
statMap = {
	["support_thorns_spirit_cost_thorns_damage_+%"] = {
		mod("ThornsDamage", "INC", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Thornskin" }),
	},
},
#mods
#skillEnd

#skill SupportTirelessPlayer
#set SupportTirelessPlayer
#mods
#skillEnd

#skill SupportTremorsPlayer
#set SupportTremorsPlayer
statMap = {
	["support_unstable_earth_damage_+%_final"] = {
		mod("Damage", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportUnabatingPlayer
#set SupportUnabatingPlayer
#mods
#skillEnd

#skill UnbreakableSupportPlayer
#set UnbreakableSupportPlayer
statMap = {
	["support_unbreakable_stun_threshold_+%_final_while_performing_action"] = {
		mod("StunThreshold", "MORE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", unscalable = true }),
	},
},
#mods
#skillEnd

#skill SupportUnderminePlayer
#set SupportUnderminePlayer
#mods
#skillEnd

#skill SupportUnsteadyTempoPlayer
#set SupportUnsteadyTempoPlayer
#mods
#skillEnd

#skill SupportUnyieldingPlayer
#set SupportUnyieldingPlayer
#mods
#skillEnd

#skill SupportUpheavalPlayer
#set SupportUpheavalPlayer
statMap = {
	["support_additional_fissures_damage_+%_final"] = {
		mod("Damage", "MORE", nil, ModFlag.Hit),
	},
	["support_additional_fissures_attack_speed_+%_final"] = {
		mod("Speed", "MORE", nil, ModFlag.Attack),
	},
},
#mods
#skillEnd

#skill SupportVitalityPlayer
#set SupportVitalityPlayer
statMap = {
	["support_vitality_life_regeneration_rate_per_minute_%"] = {
		mod("LifeRegenPercent", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Vitality" }),
		div = 60,
	},
},
#mods
#skillEnd

#skill SupportVolcanicEruptionPlayer
#set SupportVolcanicEruptionPlayer
#mods
#skillEnd

#skill TriggeredVolcanicEruptionPlayer
#set TriggeredVolcanicEruptionPlayer
#flags attack area
#mods
#skillEnd
