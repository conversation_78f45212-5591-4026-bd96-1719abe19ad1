-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="total_number_of_projectiles_to_fire"
		}
	},
	[2]={
		stats={
			[1]="active_skill_hit_damage_stun_multiplier_+%_final"
		}
	},
	[3]={
		stats={
			[1]="never_freeze"
		}
	},
	[4]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[5]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fires Bolts at every Enemy within a {0} metre radius"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[6]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Bolt explosion radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Bolt explosion radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_secondary_area_of_effect_radius"
		}
	},
	[7]={
		[1]={
		},
		stats={
			[1]="active_skill_secondary_area_of_effect_radius"
		}
	},
	["active_skill_area_of_effect_radius"]=4,
	["active_skill_base_area_of_effect_radius"]=5,
	["active_skill_base_secondary_area_of_effect_radius"]=6,
	["active_skill_hit_damage_stun_multiplier_+%_final"]=2,
	["active_skill_secondary_area_of_effect_radius"]=7,
	["never_freeze"]=3,
	parent="skill_stat_descriptions",
	["total_number_of_projectiles_to_fire"]=1
}