-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Maximum Buff duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Maximum Buff duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[2]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0}% of repeated <PERSON><PERSON>'s cast time is added to this skill's cooldown"
			}
		},
		stats={
			[1]="staff_unleash_cooldown_%_of_cast_time"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Empowered Spell repeats an additional time"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Empowered Spell repeats {0} times"
			}
		},
		stats={
			[1]="staff_unleash_number_of_seals_for_next_skill"
		}
	},
	["base_skill_effect_duration"]=1,
	parent="skill_stat_descriptions",
	["skill_effect_duration"]=2,
	["staff_unleash_cooldown_%_of_cast_time"]=3,
	["staff_unleash_number_of_seals_for_next_skill"]=4
}