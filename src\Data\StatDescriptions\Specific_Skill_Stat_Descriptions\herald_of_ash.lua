-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Explosion radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Explosion radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[3]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Ignite duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Ignite duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[4]={
		[1]={
			[1]={
				[1]={
					k="per_minute_to_per_second_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="An additional {0:+d}% of Overkill damage contributes to base Ignite damage"
			},
			[2]={
				[1]={
					k="per_minute_to_per_second_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="!",
						[2]=0
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Base Ignite damage is {0}% of Overkill damage"
			}
		},
		stats={
			[1]="herald_of_ash_burning_%_overkill_damage_per_minute",
			[2]="quality_display_herald_of_ash_burning_%_overkill_damage_per_minute_is_gem"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Ignite surrounding enemies if Overkill damage is at least {0}% of enemy's maximum Life"
			}
		},
		stats={
			[1]="herald_of_ash_overkill_threshold_%"
		}
	},
	[6]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	["active_skill_area_of_effect_radius"]=1,
	["active_skill_base_area_of_effect_radius"]=2,
	["base_skill_effect_duration"]=3,
	["herald_of_ash_burning_%_overkill_damage_per_minute"]=4,
	["herald_of_ash_overkill_threshold_%"]=5,
	parent="skill_stat_descriptions",
	["quality_display_herald_of_ash_burning_%_overkill_damage_per_minute_is_gem"]=4,
	["skill_effect_duration"]=6
}