-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="cannot_cause_bleeding"
		}
	},
	[2]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[3]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d} metre to explosion radius"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d} metres to explosion radius"
			},
			[3]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Explosion radius is {0} metre"
			},
			[4]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Explosion radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius",
			[2]="quality_display_active_skill_base_area_of_effect_radius_is_gem"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Deals {0}% of Consumed Blood Loss as additional unscalable Physical Attack Damage"
			}
		},
		stats={
			[1]="blood_hunt_explosion_%_blood_loss_to_deal_unscalable"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0}% more area of effect per {1} Blood Loss Consumed, up to 500%"
			}
		},
		stats={
			[1]="blood_hunt_skill_area_of_effect_+%_final_per_X_blood_loss_consumed_up_to_500%",
			[2]="blood_hunt_X_blood_loss_consumed_for_skill_area_of_effect_+%_final"
		}
	},
	["active_skill_area_of_effect_radius"]=2,
	["active_skill_base_area_of_effect_radius"]=3,
	["blood_hunt_X_blood_loss_consumed_for_skill_area_of_effect_+%_final"]=5,
	["blood_hunt_explosion_%_blood_loss_to_deal_unscalable"]=4,
	["blood_hunt_skill_area_of_effect_+%_final_per_X_blood_loss_consumed_up_to_500%"]=5,
	["cannot_cause_bleeding"]=1,
	parent="skill_stat_descriptions",
	["quality_display_active_skill_base_area_of_effect_radius_is_gem"]=3
}