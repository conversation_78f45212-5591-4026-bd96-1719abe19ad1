-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				[1]={
					k="per_minute_to_per_second_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Life Regeneration granted@{0}"
			}
		},
		stats={
			[1]="skeletal_cleric_grants_base_life_regeneration_rate_per_minute"
		}
	},
	[2]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	parent="skill_stat_descriptions",
	["skeletal_cleric_grants_base_life_regeneration_rate_per_minute"]=1,
	["skill_effect_duration"]=2
}