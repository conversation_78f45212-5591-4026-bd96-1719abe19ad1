-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Aura grants {0}% increased Energy Shield Recharge Rate"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Aura grants {0}% reduced Energy Shield Recharge Rate"
			}
		},
		stats={
			[1]="base_skill_buff_energy_shield_recharge_rate_+%_to_apply"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Aura grants {0:+d} to Total Maximum Energy Shield"
			}
		},
		stats={
			[1]="base_skill_buff_total_maximum_energy_shield_+_to_apply"
		}
	},
	[3]={
		[1]={
		},
		stats={
			[1]="skill_aura_buff_energy_shield_recharge_rate_+%_magnitude_to_apply"
		}
	},
	[4]={
		[1]={
		},
		stats={
			[1]="skill_aura_buff_total_maximum_energy_shield_+_magnitude_to_apply"
		}
	},
	["base_skill_buff_energy_shield_recharge_rate_+%_to_apply"]=1,
	["base_skill_buff_total_maximum_energy_shield_+_to_apply"]=2,
	parent="skill_stat_descriptions",
	["skill_aura_buff_energy_shield_recharge_rate_+%_magnitude_to_apply"]=3,
	["skill_aura_buff_total_maximum_energy_shield_+_magnitude_to_apply"]=4
}