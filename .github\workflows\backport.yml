name: Port changes to PoB1

on:
    pull_request_target:
        types: [closed]

env:
    LABEL_STRING: ${{ join(github.event.pull_request.labels.*.name, ',') }}

jobs:
    backport:
        if: github.event.pull_request.merged == true && contains(github.event.pull_request.labels.*.name, 'pob1')
        runs-on: ubuntu-latest
        steps:
            - name: Notify PathOfBuilding repo
              uses: peter-evans/repository-dispatch@v3
              with:
                token: ${{ secrets.WIRES77_PAT }}
                repository: ${{ github.repository_owner }}/PathOfBuilding
                event-type: port-changes
                client-payload: '{"patch_url": "${{ github.event.pull_request.patch_url }}", "msg": "Apply changes from ${{ github.event.pull_request.html_url }}", "id": ${{ github.event.pull_request.number }}, "title": "${{ github.event.pull_request.title }}", "labels": "${{ env.LABEL_STRING }}", "name": "${{ github.event.pull_request.user.name }}", "user": "${{ github.event.pull_request.user.login }}"}'

