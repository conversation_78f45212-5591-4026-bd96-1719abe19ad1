-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games
local itemBases = ...

itemBases["Withered Wand"] = {
	type = "Wand",
	quality = 20,
	socketLimit = 2,
	tags = { no_fire_spell_mods = true, onehand = true, wand = true, no_cold_spell_mods = true, no_lightning_spell_mods = true, no_physical_spell_mods = true, default = true, },
	implicit = "Grants Skill: Level (1-20) Chaos Bolt",
	implicitModTypes = { },
	req = { },
}
itemBases["Bone Wand"] = {
	type = "Wand",
	quality = 20,
	socketLimit = 2,
	tags = { no_fire_spell_mods = true, onehand = true, wand = true, no_cold_spell_mods = true, no_lightning_spell_mods = true, no_chaos_spell_mods = true, default = true, },
	implicit = "Grants Skill: Level (1-20) Bone Blast",
	implicitModTypes = { },
	req = { },
}
itemBases["Attuned Wand"] = {
	type = "Wand",
	quality = 20,
	socketLimit = 2,
	tags = { default = true, onehand = true, wand = true, },
	implicit = "Grants Skill: Level (1-20) Mana Drain",
	implicitModTypes = { },
	req = { },
}
itemBases["Siphoning Wand"] = {
	type = "Wand",
	quality = 20,
	socketLimit = 2,
	tags = { default = true, onehand = true, wand = true, },
	implicit = "Grants Skill: Level (1-20) Power Siphon",
	implicitModTypes = { },
	req = { },
}
itemBases["Volatile Wand"] = {
	type = "Wand",
	quality = 20,
	socketLimit = 2,
	tags = { no_physical_spell_mods = true, onehand = true, no_cold_spell_mods = true, wand = true, no_lightning_spell_mods = true, no_chaos_spell_mods = true, default = true, },
	implicit = "Grants Skill: Level (1-20) Volatile Dead",
	implicitModTypes = { },
	req = { },
}
itemBases["Galvanic Wand"] = {
	type = "Wand",
	quality = 20,
	socketLimit = 2,
	tags = { no_fire_spell_mods = true, onehand = true, wand = true, no_physical_spell_mods = true, no_cold_spell_mods = true, no_chaos_spell_mods = true, default = true, },
	implicit = "Grants Skill: Level (1-20) Galvanic Field",
	implicitModTypes = { },
	req = { },
}
itemBases["Acrid Wand"] = {
	type = "Wand",
	quality = 20,
	socketLimit = 2,
	tags = { default = true, onehand = true, wand = true, },
	implicit = "Grants Skill: Level (1-20) Decompose",
	implicitModTypes = { },
	req = { },
}
itemBases["Offering Wand"] = {
	type = "Wand",
	quality = 20,
	socketLimit = 2,
	tags = { no_fire_spell_mods = true, onehand = true, wand = true, no_cold_spell_mods = true, no_lightning_spell_mods = true, no_chaos_spell_mods = true, default = true, },
	implicit = "Grants Skill: Level (1-20) Exsanguinate",
	implicitModTypes = { },
	req = { },
}
itemBases["Frigid Wand"] = {
	type = "Wand",
	quality = 20,
	socketLimit = 2,
	tags = { no_fire_spell_mods = true, onehand = true, wand = true, no_physical_spell_mods = true, no_lightning_spell_mods = true, no_chaos_spell_mods = true, default = true, },
	implicit = "Grants Skill: Level (1-20) Chaos Bolt",
	implicitModTypes = { },
	req = { },
}
itemBases["Torture Wand"] = {
	type = "Wand",
	quality = 20,
	socketLimit = 2,
	tags = { default = true, onehand = true, wand = true, },
	implicit = "Grants Skill: Level (1-20) Chaos Bolt",
	implicitModTypes = { },
	req = { },
}
itemBases["Critical Wand"] = {
	type = "Wand",
	quality = 20,
	socketLimit = 2,
	tags = { default = true, onehand = true, wand = true, },
	implicit = "Grants Skill: Level (1-20) Chaos Bolt",
	implicitModTypes = { },
	req = { },
}
itemBases["Primordial Wand"] = {
	type = "Wand",
	quality = 20,
	socketLimit = 2,
	tags = { no_fire_spell_mods = true, onehand = true, wand = true, no_cold_spell_mods = true, no_lightning_spell_mods = true, no_physical_spell_mods = true, default = true, },
	implicit = "Grants Skill: Level (1-20) Wither",
	implicitModTypes = { },
	req = { },
}
itemBases["Dueling Wand"] = {
	type = "Wand",
	quality = 20,
	socketLimit = 2,
	tags = { default = true, onehand = true, wand = true, },
	implicit = "Grants Skill: Level (1-20) Chaos Bolt",
	implicitModTypes = { },
	req = { },
}
itemBases["Random Wand"] = {
	type = "Wand",
	hidden = true,
	socketLimit = 2,
	tags = { wand = true, default = true, },
	implicitModTypes = { },
	req = { },
}
