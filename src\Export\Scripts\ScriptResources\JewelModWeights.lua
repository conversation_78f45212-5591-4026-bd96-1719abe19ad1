local strTypes = {
	-- prefixes
	"AreaOfEffect",
	"GlobalPhysicalDamageReductionRatingPercent",
	"ArmourBreak",
	"AttackDamage",
	"BaseChanceToBleed",
	"IncreasedBlockChance",
	"DamagevsArmourBrokenEnemies",
	"ElementalDamagePercent",
	"ExertedAttackDamage",
	"FireDamagePercentage",
	"FireResistancePenetration",
	"IgniteEffect",
	"IncreasedMaceDamageForJewel",
	"MaximumRage",
	"MeleeDamage",
	"MinionAreaOfEffect",
	"MinionLife",
	"PhysicalDamagePercent",
	"ShieldArmourIncrease",
	"BleedDotMultiplier",
	"ThornsDamageIncrease",
	"TotemDamageForJewel",
	"IncreasedTotemLife",
	"WarcryEffect",
	"WarcryDamage",
	"IncisionChance",
	"BannerArea",
	"PresenceRadius",
	-- suffixes
	"ArmourBreakDuration", 
	"BleedDuration",
	"IgniteChanceIncrease",
	"SkillEffectDuration",
	"KnockbackDistance",
	"LifeCost",
	"LifeLeechAmount",
	"LifeRegenerationRate",
	"MaceStun",
	"MaximumFireResist",
	"MinionPhysicalDamageReduction",
	"RageOnHit",
	"GainRageWhenHit",
	"StunDamageIncrease",
	"IncreasedStunThreshold",
	"SummonTotemCastSpeed",
	"WarcryCooldownSpeed",
	"WarcrySpeed",
	"WeaponSwapSpeed",
	"BannerValourGained",
	"BannerDuration",
}
local dexTypes = {
	-- prefixes
	"IncreasedAccuracyPercent",
	"AilmentEffect",
	"BlindEffect",
	"BowIncreasedAccuracyRating",
	"CharmDamageWhileUsing",
	"CriticalAilmentEffect",
	"AttackDamage",
	"ProjectileDamage",
	"CompanionDamage",
	"HazardDamage",
	"HeraldDamage",
	"CrossbowDamage",
	"SpearDamage",
	"IncreasedBowDamageForJewel",
	"IncreasedStaffDamageForJewel",
	"DamageVsRareOrUnique",
	"ProjectileDamageIfMeleeHitRecently",
	"MeleeDamageIfProjectileHitRecently",
	"ParryDamage",
	"ElementalDamagePercent",
	"LightningDamagePercentage",
	"GlobalEvasionRatingPercent",
	"LightningResistancePenetration",
	"MarkEffect",
	"PoisonEffect",
	"ProjectileSpeed",
	"QuiverModifierEffect",
	"ShockEffect",
	"CompanionLife",
	-- suffixes
	"AilmentChance",
	"IncreasedAilmentThreshold",
	"AttackCriticalStrikeChance",
	"AttackCriticalStrikeMultiplier",
	"IncreasedAttackSpeed",
	"AttacksBlindOnHitChance",
	"BowAttackSpeedForJewel",
	"ChainFromTerrain",
	"CharmDuration",
	"CharmChargesGained",
	"GlobalCooldownRecovery",
	"CrossbowReloadSpeed",
	"CrossbowSpeed",
	"DamagingAilmentDuration",
	"DazeBuildup",
	"DebuffTimePassed",
	"ElementalAilmentDuration",
	"FasterAilmentDamageForJewel",
	"IncreasedFlaskChargesGained",
	"FlaskDuration",
	"ForkingProjectiles",
	"GlobalFlaskLifeRecovery",
	"LifeFlaskChargePercentGeneration",
	"FlaskManaRecovery",
	"ManaFlaskChargePercentGeneration",
	"ManaLeechAmount",
	"MarkCastSpeed",
	"MarkDuration",
	"MaximumLightningResistance",
	"MovementVelocity",
	"ChanceToPierce", 
	"PinBuildup", 
	"BaseChanceToPoison", 
	"PoisonDuration",
	"QuarterstaffFreezeBuildup",
	"StaffAttackSpeedForJewel",
	"SpearAttackSpeed",
	"SpearCriticalDamage",
	"ShockChanceIncrease",
	"ShockDuration",
	"SlowPotency",
	"IncreasedStunThresholdIfNoRecentStun",
	"ParriedDebuffDuration",
	"StunThresholdDuringParry",
}
local intTypes = {
	-- prefixes
	"AilmentEffect",
	"AreaOfEffect",
	"AuraEffectForJewel",
	"IncreasedChaosDamage",
	"ColdDamagePercentage", 
	"ColdResistancePenetration",
	"DamageIfConsumedCorpse",
	"CriticalAilmentEffect",
	"CurseAreaOfEffect",
	"CurseEffectivenessForJewel",
	"ElementalDamagePercent",
	"GlobalEnergyShieldPercent",
	"EnergyShieldDelay",
	"EnergyShieldRegeneration",
	"FocusEnergyShield",
	"IgniteEffect",
	"MinionAccuracyRatingForJewel",
	"MinionDamage",
	"OfferingLife",
	"ShockEffect",
	"WeaponSpellDamage",
	"DamageWithTriggeredSpells",
	"WitheredEffect",
	"PresenceRadius",
	-- suffixes
	"AilmentChance",
	"IncreasedCastSpeed",
	"IncreasedChillDuration",
	"CriticalStrikeChance",
	"CriticalStrikeMultiplier",
	"SpellCritMultiplierForJewel",
	"CurseDelay",
	"BaseCurseDuration",
	"DamagingAilmentDuration",
	"DebuffTimePassed",
	"EnergyGeneration",
	"FasterAilmentDamageForJewel",
	"FreezeDamageIncrease",
	"FreezeThreshold",
	"IgniteChanceIncrease",
	"SkillEffectDuration",
	"MaximumLifeOnKillPercent",
	"LifeRecoupForJewel",
	"ManaGainedOnKillPercentage",
	"ManaRegeneration",
	"MaximumColdResist",
	"MinionAttackSpeedAndCastSpeed",
	"MinionChaosResistance",
	"MinionCriticalStrikeChanceIncrease",
	"MinionCriticalStrikeMultiplier",
	"MinionElementalResistance",
	"OfferingDuration",
	"QuarterstaffFreezeBuildup",
	"ShockChanceIncrease",
	"ShockDuration",
	"SpellCriticalStrikeChance",
	"StunThresholdfromEnergyShield",
	"DamageRemovedFromManaBeforeLife",
	"AilmentThresholdfromEnergyShield",
	"VolatilityOnKillChance",
}

return {
	strTypes, dexTypes, intTypes
}