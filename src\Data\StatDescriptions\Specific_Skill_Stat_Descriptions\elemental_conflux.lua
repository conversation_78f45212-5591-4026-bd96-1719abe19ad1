-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} second duration between Element randomisations"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[2]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Buff grants {0}% more damage with the affected Element"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Buff grants {0}% less damage with the affected Element"
			}
		},
		stats={
			[1]="skill_elemental_conflux_active_element_damage_+%_final"
		}
	},
	["base_skill_effect_duration"]=1,
	parent="skill_stat_descriptions",
	["skill_effect_duration"]=2,
	["skill_elemental_conflux_active_element_damage_+%_final"]=3
}