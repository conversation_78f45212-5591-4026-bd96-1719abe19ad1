-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="base_secondary_skill_effect_duration"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Mark duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Mark duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Marked target releases a blood explosion when Heavy Stunned\nMarked target releases a blood explosion on death if they had at least {0}% Heavy Stun buildup from Blood Loss"
			}
		},
		stats={
			[1]="hunters_mark_%_of_max_threshold_to_trigger_explosion_on_death"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Marked target receives Heavy Stun buildup equal to\n{0}% of Blood Loss"
			}
		},
		stats={
			[1]="hunters_mark_%_of_phys_damage_over_time_as_build_up"
		}
	},
	[5]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	["base_secondary_skill_effect_duration"]=1,
	["base_skill_effect_duration"]=2,
	["hunters_mark_%_of_max_threshold_to_trigger_explosion_on_death"]=3,
	["hunters_mark_%_of_phys_damage_over_time_as_build_up"]=4,
	parent="skill_stat_descriptions",
	["skill_effect_duration"]=5
}