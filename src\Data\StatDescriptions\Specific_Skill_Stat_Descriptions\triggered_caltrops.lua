-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Caltrop radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Caltrop radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[3]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Caltrops land within a {0} metre radius of Projectile impact"
			}
		},
		stats={
			[1]="active_skill_base_secondary_area_of_effect_radius"
		}
	},
	[4]={
		[1]={
		},
		stats={
			[1]="active_skill_secondary_area_of_effect_radius"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]="#",
						[2]="#"
					}
				},
				text="Creates {0} Caltrop"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]="#",
						[2]="#"
					}
				},
				text="Creates {0:d} Caltrops"
			}
		},
		stats={
			[1]="base_number_of_projectiles",
			[2]="skill_can_fire_arrows",
			[3]="quality_display_base_number_of_projectiles_is_gem"
		}
	},
	[6]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Caltrop duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Caltrop duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[7]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Deals {0}% of your Dexterity as Physical Damage on Hit"
			}
		},
		stats={
			[1]="main_hand_base_physical_damage_from_%_dex"
		}
	},
	[8]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	[9]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Maximum {0} active Caltrops"
			}
		},
		stats={
			[1]="virtual_maximum_caltrops_allowed"
		}
	},
	["active_skill_area_of_effect_radius"]=1,
	["active_skill_base_area_of_effect_radius"]=2,
	["active_skill_base_secondary_area_of_effect_radius"]=3,
	["active_skill_secondary_area_of_effect_radius"]=4,
	["base_number_of_projectiles"]=5,
	["base_skill_effect_duration"]=6,
	["main_hand_base_physical_damage_from_%_dex"]=7,
	parent="skill_stat_descriptions",
	["quality_display_base_number_of_projectiles_is_gem"]=5,
	["skill_can_fire_arrows"]=5,
	["skill_effect_duration"]=8,
	["virtual_maximum_caltrops_allowed"]=9
}