-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Twister radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Twister radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[3]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Twister duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Twister duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Twister Hits at {0}% of Attack speed"
			}
		},
		stats={
			[1]="sandstorm_swipe_hit_frequency_attack_speed_%"
		}
	},
	[5]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	[6]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Consumes Whirlwinds to create an additional twister and deal {0}% more damage per Whirlwind stage"
			}
		},
		stats={
			[1]="twister_damage_+%_final_per_whirling_slash_stage"
		}
	},
	[7]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Elemental twisters Gain {0}% of damage as damage of the corresponding Type"
			}
		},
		stats={
			[1]="twister_gain_%_elemental_damage_of_corresponding_type"
		}
	},
	[8]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Twisters fired at the same time can Hit the same\ntarget no more than once per second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Twisters fired at the same time can Hit the same\ntarget no more than once every {0} seconds"
			}
		},
		stats={
			[1]="twister_hit_interval_ms"
		}
	},
	[9]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% chance for an additional twister"
			}
		},
		stats={
			[1]="twister_%_chance_for_additional_twister"
		}
	},
	[10]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Twister Hit interval@{0}s"
			}
		},
		stats={
			[1]="virtual_sandstorm_swipe_hit_interval"
		}
	},
	["active_skill_area_of_effect_radius"]=1,
	["active_skill_base_area_of_effect_radius"]=2,
	["base_skill_effect_duration"]=3,
	parent="skill_stat_descriptions",
	["sandstorm_swipe_hit_frequency_attack_speed_%"]=4,
	["skill_effect_duration"]=5,
	["twister_%_chance_for_additional_twister"]=9,
	["twister_damage_+%_final_per_whirling_slash_stage"]=6,
	["twister_gain_%_elemental_damage_of_corresponding_type"]=7,
	["twister_hit_interval_ms"]=8,
	["virtual_sandstorm_swipe_hit_interval"]=10
}