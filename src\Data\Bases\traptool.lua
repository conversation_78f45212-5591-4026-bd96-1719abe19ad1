-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games
local itemBases = ...


itemBases["Clay Trap"] = {
	type = "TrapTool",
	quality = 20,
	tags = { twohand = true, trap = true, default = true, },
	implicitModTypes = { },
	req = { },
}
itemBases["Clamping Trap"] = {
	type = "TrapTool",
	quality = 20,
	tags = { twohand = true, trap = true, default = true, },
	implicitModTypes = { },
	req = { },
}
itemBases["Shrapnel Trap"] = {
	type = "TrapTool",
	quality = 20,
	tags = { twohand = true, trap = true, default = true, },
	implicitModTypes = { },
	req = { },
}
itemBases["Urn Trap"] = {
	type = "TrapTool",
	quality = 20,
	tags = { twohand = true, trap = true, default = true, },
	implicitModTypes = { },
	req = { },
}
itemBases["Incense Trap"] = {
	type = "TrapTool",
	quality = 20,
	tags = { twohand = true, trap = true, default = true, },
	implicit = "(20-30)% increased Cooldown Recovery Rate for throwing Traps",
	implicitModTypes = { {  }, },
	req = { },
}
itemBases["Bladed Trap"] = {
	type = "TrapTool",
	quality = 20,
	tags = { twohand = true, trap = true, default = true, },
	implicitModTypes = { },
	req = { },
}
itemBases["Dart Trap"] = {
	type = "TrapTool",
	quality = 20,
	tags = { twohand = true, trap = true, default = true, },
	implicitModTypes = { },
	req = { },
}
itemBases["Spike Trap"] = {
	type = "TrapTool",
	quality = 20,
	tags = { twohand = true, trap = true, default = true, },
	implicitModTypes = { },
	req = { },
}
itemBases["Coiled Trap"] = {
	type = "TrapTool",
	quality = 20,
	tags = { twohand = true, trap = true, default = true, },
	implicitModTypes = { },
	req = { },
}
itemBases["Lead Trap"] = {
	type = "TrapTool",
	quality = 20,
	tags = { twohand = true, trap = true, default = true, },
	implicitModTypes = { },
	req = { },
}
itemBases["Medallion Trap"] = {
	type = "TrapTool",
	quality = 20,
	tags = { twohand = true, trap = true, default = true, },
	implicitModTypes = { },
	req = { },
}
itemBases["Intricate Trap"] = {
	type = "TrapTool",
	quality = 20,
	tags = { twohand = true, trap = true, default = true, },
	implicitModTypes = { },
	req = { },
}
itemBases["Refined Trap"] = {
	type = "TrapTool",
	quality = 20,
	tags = { twohand = true, trap = true, default = true, },
	implicitModTypes = { },
	req = { },
}
