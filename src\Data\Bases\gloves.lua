-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games
local itemBases = ...


itemBases["Stocky Mitts"] = {
	type = "Gloves",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, str_armour = true, armour = true, ezomyte_basetype = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 15, },
	req = { },
}
itemBases["Riveted Mitts"] = {
	type = "Gloves",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, str_armour = true, armour = true, ezomyte_basetype = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 31, },
	req = { level = 11, str = 19, },
}
itemBases["Tempered Mitts"] = {
	type = "Gloves",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, gloves = true, str_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 40, },
	req = { level = 16, str = 27, },
}
itemBases["Bolstered Mitts"] = {
	type = "Gloves",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, gloves = true, str_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 58, },
	req = { level = 27, str = 44, },
}
itemBases["Moulded Mitts"] = {
	type = "Gloves",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, str_armour = true, armour = true, vaal_basetype = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 68, },
	req = { level = 33, str = 53, },
}
itemBases["Detailed Mitts"] = {
	type = "Gloves",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 88, },
	req = { level = 45, str = 73, },
}
itemBases["Titan Mitts"] = {
	type = "Gloves",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 100, },
	req = { level = 52, str = 83, },
}
itemBases["Grand Mitts"] = {
	type = "Gloves",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 122, },
	req = { level = 65, str = 104, },
}
itemBases["Plated Mitts"] = {
	type = "Gloves",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 88, },
	req = { level = 45, str = 73, },
}
itemBases["Elegant Mitts"] = {
	type = "Gloves",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 98, },
	req = { level = 51, str = 82, },
}
itemBases["Ancient Mitts"] = {
	type = "Gloves",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 105, },
	req = { level = 55, str = 88, },
}
itemBases["Feathered Mitts"] = {
	type = "Gloves",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 111, },
	req = { level = 59, str = 95, },
}
itemBases["Knightly Mitts"] = {
	type = "Gloves",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 122, },
	req = { level = 65, str = 104, },
}
itemBases["Ornate Mitts"] = {
	type = "Gloves",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 134, },
	req = { level = 70, str = 113, },
}
itemBases["Vaal Mitts"] = {
	type = "Gloves",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 146, },
	req = { level = 75, str = 122, },
}

itemBases["Suede Bracers"] = {
	type = "Gloves",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, ezomyte_basetype = true, dex_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 10, },
	req = { },
}
itemBases["Firm Bracers"] = {
	type = "Gloves",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, ezomyte_basetype = true, dex_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 26, },
	req = { level = 11, dex = 19, },
}
itemBases["Bound Bracers"] = {
	type = "Gloves",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, gloves = true, dex_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 33, },
	req = { level = 16, dex = 27, },
}
itemBases["Sectioned Bracers"] = {
	type = "Gloves",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, gloves = true, dex_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 52, },
	req = { level = 28, dex = 46, },
}
itemBases["Spined Bracers"] = {
	type = "Gloves",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, dex_armour = true, armour = true, vaal_basetype = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 60, },
	req = { level = 33, dex = 53, },
}
itemBases["Fine Bracers"] = {
	type = "Gloves",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 79, },
	req = { level = 45, dex = 73, },
}
itemBases["Hardened Bracers"] = {
	type = "Gloves",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 90, },
	req = { level = 52, dex = 83, },
}
itemBases["Engraved Bracers"] = {
	type = "Gloves",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 111, },
	req = { level = 65, dex = 104, },
}
itemBases["Hunting Bracers"] = {
	type = "Gloves",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 79, },
	req = { level = 45, dex = 73, },
}
itemBases["Swift Bracers"] = {
	type = "Gloves",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 89, },
	req = { level = 51, dex = 82, },
}
itemBases["Refined Bracers"] = {
	type = "Gloves",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 95, },
	req = { level = 55, dex = 88, },
}
itemBases["Spiked Bracers"] = {
	type = "Gloves",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 101, },
	req = { level = 59, dex = 95, },
}
itemBases["Stalking Bracers"] = {
	type = "Gloves",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 111, },
	req = { level = 65, dex = 104, },
}
itemBases["Grand Bracers"] = {
	type = "Gloves",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 122, },
	req = { level = 70, dex = 113, },
}
itemBases["Barbed Bracers"] = {
	type = "Gloves",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 133, },
	req = { level = 75, dex = 122, },
}

itemBases["Torn Gloves"] = {
	type = "Gloves",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, ezomyte_basetype = true, int_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 9, },
	req = { },
}
itemBases["Sombre Gloves"] = {
	type = "Gloves",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, ezomyte_basetype = true, int_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 15, },
	req = { level = 12, int = 20, },
}
itemBases["Stitched Gloves"] = {
	type = "Gloves",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, gloves = true, int_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 17, },
	req = { level = 16, int = 27, },
}
itemBases["Jewelled Gloves"] = {
	type = "Gloves",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, gloves = true, int_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 22, },
	req = { level = 26, int = 43, },
}
itemBases["Intricate Gloves"] = {
	type = "Gloves",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, int_armour = true, armour = true, vaal_basetype = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 26, },
	req = { level = 33, int = 53, },
}
itemBases["Pauascale Gloves"] = {
	type = "Gloves",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 32, },
	req = { level = 45, int = 73, },
}
itemBases["Embroidered Gloves"] = {
	type = "Gloves",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 35, },
	req = { level = 52, int = 83, },
}
itemBases["Adorned Gloves"] = {
	type = "Gloves",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 42, },
	req = { level = 65, int = 104, },
}
itemBases["Ominous Gloves"] = {
	type = "Gloves",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 32, },
	req = { level = 45, int = 73, },
}
itemBases["Embroidered Gloves"] = {
	type = "Gloves",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 35, },
	req = { level = 51, int = 82, },
}
itemBases["Baroque Gloves"] = {
	type = "Gloves",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 37, },
	req = { level = 55, int = 88, },
}
itemBases["Gold Gloves"] = {
	type = "Gloves",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 39, },
	req = { level = 59, int = 95, },
}
itemBases["Grim Gloves"] = {
	type = "Gloves",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 42, },
	req = { level = 65, int = 104, },
}
itemBases["Opulent Gloves"] = {
	type = "Gloves",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 46, },
	req = { level = 70, int = 113, },
}
itemBases["Vaal Gloves"] = {
	type = "Gloves",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 50, },
	req = { level = 75, int = 122, },
}

itemBases["Ringmail Gauntlets"] = {
	type = "Gloves",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, ezomyte_basetype = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 13, Evasion = 10, },
	req = { level = 6, str = 7, dex = 7, },
}
itemBases["Layered Gauntlets"] = {
	type = "Gloves",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, str_dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 22, Evasion = 18, },
	req = { level = 16, str = 15, dex = 15, },
}
itemBases["Doubled Gauntlets"] = {
	type = "Gloves",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, vaal_basetype = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 37, Evasion = 33, },
	req = { level = 33, str = 30, dex = 30, },
}
itemBases["Plate Gauntlets"] = {
	type = "Gloves",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 48, Evasion = 44, },
	req = { level = 45, str = 40, dex = 40, },
}
itemBases["Burnished Gauntlets"] = {
	type = "Gloves",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 55, Evasion = 50, },
	req = { level = 52, str = 46, dex = 46, },
}
itemBases["Ornate Gauntlets"] = {
	type = "Gloves",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 67, Evasion = 61, },
	req = { level = 65, str = 57, dex = 57, },
}
itemBases["Ironmail Gauntlets"] = {
	type = "Gloves",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 48, Evasion = 44, },
	req = { level = 45, str = 40, dex = 40, },
}
itemBases["Captain Gauntlets"] = {
	type = "Gloves",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 54, Evasion = 49, },
	req = { level = 51, str = 46, dex = 46, },
}
itemBases["Zealot Gauntlets"] = {
	type = "Gloves",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 61, Evasion = 56, },
	req = { level = 59, str = 52, dex = 52, },
}
itemBases["Steelmail Gauntlets"] = {
	type = "Gloves",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 67, Evasion = 61, },
	req = { level = 65, str = 57, dex = 57, },
}
itemBases["Commander Gauntlets"] = {
	type = "Gloves",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 74, Evasion = 67, },
	req = { level = 70, str = 62, dex = 62, },
}
itemBases["Cultist Gauntlets"] = {
	type = "Gloves",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 80, Evasion = 73, },
	req = { level = 75, str = 67, dex = 67, },
}

itemBases["Rope Cuffs"] = {
	type = "Gloves",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, ezomyte_basetype = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 12, EnergyShield = 6, },
	req = { level = 5, str = 6, int = 6, },
}
itemBases["Aged Cuffs"] = {
	type = "Gloves",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, str_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 22, EnergyShield = 9, },
	req = { level = 16, str = 15, int = 15, },
}
itemBases["Goldcast Cuffs"] = {
	type = "Gloves",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, vaal_basetype = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 37, EnergyShield = 14, },
	req = { level = 33, str = 30, int = 30, },
}
itemBases["Diviner Cuffs"] = {
	type = "Gloves",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 48, EnergyShield = 17, },
	req = { level = 45, str = 40, int = 40, },
}
itemBases["Righteous Cuffs"] = {
	type = "Gloves",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 55, EnergyShield = 19, },
	req = { level = 52, str = 46, int = 46, },
}
itemBases["Signet Cuffs"] = {
	type = "Gloves",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 67, EnergyShield = 23, },
	req = { level = 65, str = 57, int = 57, },
}
itemBases["Braided Cuffs"] = {
	type = "Gloves",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 48, EnergyShield = 17, },
	req = { level = 45, str = 40, int = 40, },
}
itemBases["Heirloom Cuffs"] = {
	type = "Gloves",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 54, EnergyShield = 19, },
	req = { level = 51, str = 46, int = 46, },
}
itemBases["Ornate Cuffs"] = {
	type = "Gloves",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 61, EnergyShield = 21, },
	req = { level = 59, str = 52, int = 52, },
}
itemBases["Bound Cuffs"] = {
	type = "Gloves",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 67, EnergyShield = 23, },
	req = { level = 65, str = 57, int = 57, },
}
itemBases["Ancient Cuffs"] = {
	type = "Gloves",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 74, EnergyShield = 25, },
	req = { level = 70, str = 62, int = 62, },
}
itemBases["Gleaming Cuffs"] = {
	type = "Gloves",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 80, EnergyShield = 28, },
	req = { level = 75, str = 67, int = 67, },
}

itemBases["Gauze Wraps"] = {
	type = "Gloves",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, ezomyte_basetype = true, armour = true, default = true, dex_int_armour = true, },
	implicitModTypes = { },
	armour = { Evasion = 8, EnergyShield = 6, },
	req = { dex = 6, int = 6, },
}
itemBases["Linen Wraps"] = {
	type = "Gloves",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, gloves = true, armour = true, default = true, dex_int_armour = true, },
	implicitModTypes = { },
	armour = { Evasion = 18, EnergyShield = 9, },
	req = { level = 16, dex = 15, int = 15, },
}
itemBases["Spiral Wraps"] = {
	type = "Gloves",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { gloves = true, vaal_basetype = true, armour = true, default = true, dex_int_armour = true, },
	implicitModTypes = { },
	armour = { Evasion = 33, EnergyShield = 14, },
	req = { level = 33, dex = 30, int = 30, },
}
itemBases["Buckled Wraps"] = {
	type = "Gloves",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { dex_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 44, EnergyShield = 17, },
	req = { level = 45, dex = 40, int = 40, },
}
itemBases["Furtive Wraps"] = {
	type = "Gloves",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { dex_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 50, EnergyShield = 19, },
	req = { level = 52, dex = 46, int = 46, },
}
itemBases["Utility Wraps"] = {
	type = "Gloves",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { dex_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 61, EnergyShield = 23, },
	req = { level = 65, dex = 57, int = 57, },
}
itemBases["Bandage Wraps"] = {
	type = "Gloves",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { dex_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 44, EnergyShield = 17, },
	req = { level = 45, dex = 40, int = 40, },
}
itemBases["Cambric Wraps"] = {
	type = "Gloves",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { dex_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 49, EnergyShield = 19, },
	req = { level = 51, dex = 46, int = 46, },
}
itemBases["Adorned Wraps"] = {
	type = "Gloves",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { dex_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 56, EnergyShield = 21, },
	req = { level = 59, dex = 52, int = 52, },
}
itemBases["War Wraps"] = {
	type = "Gloves",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { dex_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 61, EnergyShield = 23, },
	req = { level = 65, dex = 57, int = 57, },
}
itemBases["Elegant Wraps"] = {
	type = "Gloves",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { dex_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 67, EnergyShield = 25, },
	req = { level = 70, dex = 62, int = 62, },
}
itemBases["Vaal Wraps"] = {
	type = "Gloves",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { dex_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 73, EnergyShield = 28, },
	req = { level = 75, dex = 67, int = 67, },
}

itemBases["Grand Manchettes"] = {
	type = "Gloves",
	subType = "Armour/Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_int_armour = true, armour = true, gloves = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 44, Evasion = 40, EnergyShield = 15, },
	req = { level = 65, str = 40, dex = 40, int = 40, },
}


