-- Export World Areas and possible Spectres found in each area.

local importedSpectres = {}

local file = io.open("../Data/Spectres.lua", "r")
if file then
	for line in file:lines() do
		-- Try to capture the name line inside the spectre table:
		local name = line:match('name%s*=%s*"(.-)"')
		if name then
			importedSpectres[name] = true
		end
	end
	file:close()
end

-- Step 1: Build packId -> monster names
local packIdToMonsters = {}
for entry in dat("MonsterPackEntries"):Rows() do
	if entry.MonsterPacksKey and entry.MonsterVarietiesKey then
		local packId = entry.MonsterPacksKey.Id
		local monVar = entry.MonsterVarietiesKey
		if packId and monVar and monVar.Name and monVar.Name ~= "" then
			packIdToMonsters[packId] = packIdToMonsters[packId] or {}
			table.insert(packIdToMonsters[packId], monVar.Name)
		end
	end
end

for pack in dat("MonsterPacks"):Rows() do
	local packId = pack.Id
	local list = packIdToMonsters[packId] or {}
	local seen = {}
	for _, name in ipairs(list) do
		seen[name] = true
	end

	local function addIfSpectre(mon)
		if mon.Name ~= "" and not seen[mon.Name] then
			table.insert(list, mon.Name)
			seen[mon.Name] = true
		end
	end

	if pack.AdditionalMonsters then
		for _, mon in ipairs(pack.AdditionalMonsters) do
			addIfSpectre(mon)
		end
	end
	if pack.BossMonsters then
		for _, mon in ipairs(pack.BossMonsters) do
			addIfSpectre(mon)
		end
	end

	packIdToMonsters[packId] = list
end

-- Step 2: areaId -> monster names
local areaIdToMonsters = {}

for pack in dat("MonsterPacks"):Rows() do
	if pack.WorldAreas then
		for _, areaRef in ipairs(pack.WorldAreas) do
			local areaId = areaRef.Id
			if areaId then
				areaIdToMonsters[areaId] = areaIdToMonsters[areaId] or {}
				local seen = areaIdToMonsters[areaId .. "_seen"] or {}
				for _, name in ipairs(packIdToMonsters[pack.Id] or {}) do
					if not seen[name] then
						table.insert(areaIdToMonsters[areaId], name)
						seen[name] = true
					end
				end
				areaIdToMonsters[areaId .. "_seen"] = seen
			end
		end
	end
end

-- Step 3: EndGameMaps
for map in dat("EndGameMaps"):Rows() do
	local areaRefs = {}
	if map.BossVersion then
		table.insert(areaRefs, map.BossVersion)
	end
	if map.NonBossVersion then
		table.insert(areaRefs, map.NonBossVersion)
	end
	for _, area in ipairs(areaRefs) do
		local areaId = area.Id
		areaIdToMonsters[areaId] = areaIdToMonsters[areaId] or {}
		local seen = areaIdToMonsters[areaId .. "_seen"] or {}
		if map.NativePacks then
			for _, pack in ipairs(map.NativePacks) do
				for _, name in ipairs(packIdToMonsters[pack.Id] or {}) do
					if not seen[name] then
						table.insert(areaIdToMonsters[areaId], name)
						seen[name] = true
					end
				end
			end
		end
		areaIdToMonsters[areaId .. "_seen"] = seen

		-- Attach FlavourText as description for this area if present
		if map.FlavourText and map.FlavourText ~= "" then
			-- Hideouts have 2 lines, remove second line
			if areaId:sub(-10) == "_Claimable" then
				local firstSentence = map.FlavourText:match("([^%.%!%?]+[%.%!%?])")
				if firstSentence then
					areaIdToMonsters[areaId .. "_desc"] = firstSentence:gsub("%s+$", "")
				else
					areaIdToMonsters[areaId .. "_desc"] = map.FlavourText
				end
			else
				areaIdToMonsters[areaId .. "_desc"] = map.FlavourText
			end
		end
	end
end

-- Combine _NoBoss monsters into their corresponding boss map
for areaId, monsters in pairs(areaIdToMonsters) do
	if type(areaId) == "string" and areaId:sub(-7) == "_NoBoss" then
		local bossAreaId = areaId:sub(1, -8)
		areaIdToMonsters[bossAreaId] = areaIdToMonsters[bossAreaId] or {}
		local seen = {}
		for _, name in ipairs(areaIdToMonsters[bossAreaId]) do
			seen[name] = true
		end
		for _, name in ipairs(monsters) do
			if not seen[name] then
				table.insert(areaIdToMonsters[bossAreaId], name)
				seen[name] = true
			end
		end
	end
end

-- Step 4: Output
local out = io.open("../Data/WorldAreas.lua", "w")
out:write('-- This file is automatically generated, do not edit!\n')
out:write('-- Path of Building\n')
out:write('-- World Area Data (c) Grinding Gear Games\n\n')
out:write('local worldAreas, _ = ...\n\n')

for area in dat("WorldAreas"):Rows() do
	if area.Name and area.Name ~= "NULL" and area.Id then
		-- Skip areas ending with _NoBoss
		if area.Id:sub(-7) == "_NoBoss" then
			goto continue
		end
		local monsters = areaIdToMonsters[area.Id] or {}
		local tags = {}
		local isMap = false
		if area.Tags and #area.Tags > 0 then
			for _, tag in ipairs(area.Tags) do
				table.insert(tags, '"' .. tag.Id .. '"')
				if tag.Id == "map" then
					isMap = true
				end
			end
		end
		out:write('worldAreas["' .. area.Id .. '"] = {\n')
		local suffix = ""
		if isMap then
			suffix = " (Map)"
		elseif area.Id:match("^Sanctum_(%d+)") then
			local floorNum = area.Id:match("^Sanctum_(%d+)")
			suffix = " (Floor " .. floorNum .. ")"
		elseif area.Act and area.Act ~= 10 then
			if area.Act >= 5 then
				suffix = " (Act " .. tostring(area.Act - 3) .. ")"
			else
				suffix = " (Act " .. tostring(area.Act) .. ")"
			end
		end
		out:write('\tname = "' .. area.Name .. suffix .. '",\n')
		out:write('\tbaseName = "' .. area.Name .. '",\n')
		local desc = area.Description
		if (not desc or desc == "") and areaIdToMonsters[area.Id .. "_desc"] then
			desc = areaIdToMonsters[area.Id .. "_desc"]
		end
		if desc and desc ~= "" then
			out:write('\tdescription = "' .. desc .. '",\n')
		end
		out:write('\ttags = { ' .. table.concat(tags, ", ") .. ' },\n')
		out:write('\tact = ' .. tostring(area.Act or 0) .. ',\n')
		out:write('\tlevel = ' .. tostring(area.AreaLevel or 1) .. ',\n')
		out:write('\tisMap = ' .. tostring(isMap) .. ',\n')
		out:write('\tisHideout = ' .. tostring(area.IsHideout) .. ',\n')
		-- Normal monster list
		out:write('\tmonsterVarieties = {\n')
		local seen = {}
		table.sort(monsters)
		for _, name in ipairs(monsters) do
			if importedSpectres[name] and not seen[name] then
				out:write('\t\t"' .. name .. '",\n')
				seen[name] = true
			end
		end
		out:write('\t},\n')
		-- Bosses section
		if area.Bosses and #area.Bosses > 0 then
			out:write('\tbossVarieties = {\n')
			local bossSeen = {}
			for _, boss in ipairs(area.Bosses) do
				if boss.Id and boss.Id ~= "" then
					local bossVariety = dat("MonsterVarieties"):GetRow("Id", boss.Id)
					if bossVariety and bossVariety.Name and bossVariety.Name ~= "" and not bossVariety.Name:find("DNT") then
						local bossName = bossVariety.Name
						if not bossSeen[bossName] then
							out:write('\t\t"' .. bossName .. '",\n')
							bossSeen[bossName] = true
						end
					end
				end
			end
			out:write('\t},\n')
		end
		out:write('}\n\n')
	end
	::continue::
end

out:write('return worldAreas\n')
out:close()

print("World Areas exported.")
