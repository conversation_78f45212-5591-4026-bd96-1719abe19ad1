-- This file is automatically generated, do not edit!
-- Jewel data (c) Grinding Gear Games

return {
	jewels = {
		["Small Cluster Jewel"] = {
			size = "Small",
			sizeIndex = 0,
			minNodes = 2,
			maxNodes = 3,
			smallIndicies = { 0, 4, 2 },
			notableIndicies = { 4 },
			socketIndicies = { 4 },
			totalIndicies = 6,
			skills = {
				["affliction_maximum_life"] = {
					name = "Life",
					icon = "Art/2DArt/SkillIcons/passives/IncreasedMaximumLifeNode.png",
					tag = "affliction_maximum_life",
					stats = { "4% increased maximum Life" },
					enchant = {
						"Added Small Passive Skills grant: 4% increased maximum Life",
					},
				},
				["affliction_maximum_energy_shield"] = {
					name = "Energy Shield",
					icon = "Art/2DArt/SkillIcons/passives/EnergyShieldNode.png",
					tag = "affliction_maximum_energy_shield",
					stats = { "6% increased maximum Energy Shield" },
					enchant = {
						"Added Small Passive Skills grant: 6% increased maximum Energy Shield",
					},
				},
				["affliction_maximum_mana"] = {
					name = "Mana",
					icon = "Art/2DArt/SkillIcons/passives/MaxManaNode.png",
					tag = "affliction_maximum_mana",
					stats = { "6% increased maximum Mana" },
					enchant = {
						"Added Small Passive Skills grant: 6% increased maximum Mana",
					},
				},
				["affliction_armour"] = {
					name = "Armour",
					icon = "Art/2DArt/SkillIcons/passives/ArmourNode.png",
					tag = "affliction_armour",
					stats = { "15% increased Armour" },
					enchant = {
						"Added Small Passive Skills grant: 15% increased Armour",
					},
				},
				["affliction_evasion"] = {
					name = "Evasion",
					icon = "Art/2DArt/SkillIcons/passives/EvasionNode.png",
					tag = "affliction_evasion",
					stats = { "15% increased Evasion Rating" },
					enchant = {
						"Added Small Passive Skills grant: 15% increased Evasion Rating",
					},
				},
				["affliction_chance_to_block_attack_damage"] = {
					name = "Chance to Block Attack Damage",
					icon = "Art/2DArt/SkillIcons/passives/BlockAttackDmgNode.png",
					tag = "affliction_chance_to_block",
					stats = { "+2% Chance to Block Attack Damage" },
					enchant = {
						"Added Small Passive Skills grant: +2% Chance to Block Attack Damage",
					},
				},
				["affliction_chance_to_block_spell_damage"] = {
					name = "Chance to Block Spell Damage",
					icon = "Art/2DArt/SkillIcons/passives/BlockSpellDmgNode.png",
					tag = "affliction_chance_to_block",
					stats = { "2% Chance to Block Spell Damage" },
					enchant = {
						"Added Small Passive Skills grant: 2% Chance to Block Spell Damage",
					},
				},
				["affliction_fire_resistance"] = {
					name = "Fire Resistance",
					icon = "Art/2DArt/SkillIcons/passives/FireResistNode.png",
					tag = "affliction_fire_resistance",
					stats = { "+15% to Fire Resistance" },
					enchant = {
						"Added Small Passive Skills grant: +15% to Fire Resistance",
					},
				},
				["affliction_cold_resistance"] = {
					name = "Cold Resistance",
					icon = "Art/2DArt/SkillIcons/passives/ColdResistNode.png",
					tag = "affliction_cold_resistance",
					stats = { "+15% to Cold Resistance" },
					enchant = {
						"Added Small Passive Skills grant: +15% to Cold Resistance",
					},
				},
				["affliction_lightning_resistance"] = {
					name = "Lightning Resistance",
					icon = "Art/2DArt/SkillIcons/passives/LightningResistNode.png",
					tag = "affliction_lightning_resistance",
					stats = { "+15% to Lightning Resistance" },
					enchant = {
						"Added Small Passive Skills grant: +15% to Lightning Resistance",
					},
				},
				["affliction_chaos_resistance"] = {
					name = "Chaos Resistance",
					icon = "Art/2DArt/SkillIcons/passives/ChaosResistNode.png",
					tag = "affliction_chaos_resistance",
					stats = { "+12% to Chaos Resistance" },
					enchant = {
						"Added Small Passive Skills grant: +12% to Chaos Resistance",
					},
				},
				["affliction_chance_to_dodge_attacks"] = {
					name = "Chance to Suppress Spell Damage",
					icon = "Art/2DArt/SkillIcons/passives/DodgeAtksNode.png",
					tag = "affliction_chance_to_dodge_attacks",
					stats = { "+4% chance to Suppress Spell Damage" },
					enchant = {
						"Added Small Passive Skills grant: +4% chance to Suppress Spell Damage",
					},
				},
				["affliction_strength"] = {
					name = "Strength",
					icon = "Art/2DArt/SkillIcons/passives/plusstrength.png",
					tag = "affliction_strength",
					stats = { "+10 to Strength" },
					enchant = {
						"Added Small Passive Skills grant: +10 to Strength",
					},
				},
				["affliction_dexterity"] = {
					name = "Dexterity",
					icon = "Art/2DArt/SkillIcons/passives/plusdexterity.png",
					tag = "affliction_dexterity",
					stats = { "+10 to Dexterity" },
					enchant = {
						"Added Small Passive Skills grant: +10 to Dexterity",
					},
				},
				["affliction_intelligence"] = {
					name = "Intelligence",
					icon = "Art/2DArt/SkillIcons/passives/plusintelligence.png",
					tag = "affliction_intelligence",
					stats = { "+10 to Intelligence" },
					enchant = {
						"Added Small Passive Skills grant: +10 to Intelligence",
					},
				},
				["affliction_reservation_efficiency_small"] = {
					name = "Reservation Efficiency",
					icon = "Art/2DArt/SkillIcons/passives/AuraEffectNode.png",
					tag = "affliction_reservation_efficiency_small",
					stats = { "6% increased Mana Reservation Efficiency of Skills" },
					enchant = {
						"Added Small Passive Skills grant: 6% increased Mana Reservation Efficiency of Skills",
					},
				},
				["affliction_curse_effect_small"] = {
					name = "Curse Effect",
					icon = "Art/2DArt/SkillIcons/passives/CurseEffectNode.png",
					tag = "affliction_curse_effect_small",
					stats = { "2% increased Effect of your Curses" },
					enchant = {
						"Added Small Passive Skills grant: 2% increased Effect of your Curses",
					},
				},
			},
		},
		["Medium Cluster Jewel"] = {
			size = "Medium",
			sizeIndex = 1,
			minNodes = 4,
			maxNodes = 6,
			smallIndicies = { 0, 6, 8, 4, 10, 2 },
			notableIndicies = { 6, 10, 2, 0 },
			socketIndicies = { 6 },
			totalIndicies = 12,
			skills = {
				["affliction_fire_damage_over_time_multiplier"] = {
					name = "Fire Damage over Time",
					icon = "Art/2DArt/SkillIcons/passives/FireDamageOverTimeNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltFireDamageMastery.png",
					tag = "affliction_fire_damage_over_time_multiplier",
					stats = { "12% increased Burning Damage" },
					enchant = {
						"Added Small Passive Skills grant: 12% increased Burning Damage",
					},
				},
				["affliction_chaos_damage_over_time_multiplier"] = {
					name = "Chaos Damage over Time",
					icon = "Art/2DArt/SkillIcons/passives/ChaosDamageOverTimeNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltChaosDamageMastery.png",
					tag = "affliction_chaos_damage_over_time_multiplier",
					stats = { "12% increased Chaos Damage over Time" },
					enchant = {
						"Added Small Passive Skills grant: 12% increased Chaos Damage over Time",
					},
				},
				["affliction_physical_damage_over_time_multiplier"] = {
					name = "Physical Damage over Time",
					icon = "Art/2DArt/SkillIcons/passives/PhysicalDamageOverTimeNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltBloodMastery.png",
					tag = "affliction_physical_damage_over_time_multiplier",
					stats = { "12% increased Physical Damage over Time" },
					enchant = {
						"Added Small Passive Skills grant: 12% increased Physical Damage over Time",
					},
				},
				["affliction_cold_damage_over_time_multiplier"] = {
					name = "Cold Damage over Time",
					icon = "Art/2DArt/SkillIcons/passives/ColdDamageOverTimeNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltColdDamageMastery.png",
					tag = "affliction_cold_damage_over_time_multiplier",
					stats = { "12% increased Cold Damage over Time" },
					enchant = {
						"Added Small Passive Skills grant: 12% increased Cold Damage over Time",
					},
				},
				["affliction_damage_over_time_multiplier"] = {
					name = "Damage over Time",
					icon = "Art/2DArt/SkillIcons/passives/DamageOverTimeNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltDamageOverTimeMultiplierMastery.png",
					tag = "affliction_damage_over_time_multiplier",
					stats = { "10% increased Damage over Time" },
					enchant = {
						"Added Small Passive Skills grant: 10% increased Damage over Time",
					},
				},
				["affliction_effect_of_non-damaging_ailments"] = {
					name = "Effect of Non-Damaging Ailments",
					icon = "Art/2DArt/SkillIcons/passives/IncreasedNonDamageAilmentNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltNonDamagingAilmentsMastery.png",
					tag = "affliction_effect_of_non-damaging_ailments",
					stats = { "10% increased Effect of Non-Damaging Ailments" },
					enchant = {
						"Added Small Passive Skills grant: 10% increased Effect of Non-Damaging Ailments",
					},
				},
				["affliction_aura_effect"] = {
					name = "Aura Effect (Legacy)",
					icon = "Art/2DArt/SkillIcons/passives/AuraEffectNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryAuras.png",
					tag = "old_do_not_use_affliction_aura_effect",
					stats = { "3% increased effect of Non-Curse Auras from your Skills" },
					enchant = {
						"Added Small Passive Skills grant: 3% increased effect of Non-Curse Auras from your Skills",
					},
				},
				["affliction_curse_effect"] = {
					name = "Curse Effect (Legacy)",
					icon = "Art/2DArt/SkillIcons/passives/CurseEffectNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryCurse.png",
					tag = "old_do_not_use_affliction_curse_effect",
					stats = { "2% increased Effect of your Curses" },
					enchant = {
						"Added Small Passive Skills grant: 2% increased Effect of your Curses",
					},
				},
				["affliction_damage_while_you_have_a_herald"] = {
					name = "Damage while you have a Herald",
					icon = "Art/2DArt/SkillIcons/passives/DmgHeraldSkillsNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltDamageWithHeraldMastery.png",
					tag = "affliction_damage_while_you_have_a_herald",
					stats = { "10% increased Damage while affected by a Herald" },
					enchant = {
						"Added Small Passive Skills grant: 10% increased Damage while affected by a Herald",
					},
				},
				["affliction_minion_damage_while_you_have_a_herald"] = {
					name = "Minion Damage while you have a Herald",
					icon = "Art/2DArt/SkillIcons/passives/MinionDmgHeraldSkillsNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMinionDamageHeraldMastery.png",
					tag = "affliction_minion_damage_while_you_have_a_herald",
					stats = { "Minions deal 10% increased Damage while you are affected by a Herald" },
					enchant = {
						"Added Small Passive Skills grant: Minions deal 10% increased Damage while you are affected by a Herald",
					},
				},
				["affliction_warcry_buff_effect"] = {
					name = "Exerted Attack Damage",
					icon = "Art/2DArt/SkillIcons/passives/IncreasedWarcryNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltWarcryMastery.png",
					tag = "affliction_warcry_buff_effect",
					stats = { "Exerted Attacks deal 20% increased Damage" },
					enchant = {
						"Added Small Passive Skills grant: Exerted Attacks deal 20% increased Damage",
					},
				},
				["affliction_critical_chance"] = {
					name = "Critical Chance",
					icon = "Art/2DArt/SkillIcons/passives/IncreaseCritChanceNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryGroupCrit.png",
					tag = "affliction_critical_chance",
					stats = { "15% increased Critical Strike Chance" },
					enchant = {
						"Added Small Passive Skills grant: 15% increased Critical Strike Chance",
					},
				},
				["affliction_minion_life"] = {
					name = "Minion Life",
					icon = "Art/2DArt/SkillIcons/passives/IncreaseMinionLifeNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryGroupMinions.png",
					tag = "affliction_minion_life",
					stats = { "Minions have 12% increased maximum Life" },
					enchant = {
						"Added Small Passive Skills grant: Minions have 12% increased maximum Life",
					},
				},
				["affliction_area_damage"] = {
					name = "Area Damage",
					icon = "Art/2DArt/SkillIcons/passives/AreaDmgNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltAreaDamageMastery.png",
					tag = "affliction_area_damage",
					stats = { "10% increased Area Damage" },
					enchant = {
						"Added Small Passive Skills grant: 10% increased Area Damage",
					},
				},
				["affliction_projectile_damage"] = {
					name = "Projectile Damage",
					icon = "Art/2DArt/SkillIcons/passives/ProjectileDmgNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryProjectiles.png",
					tag = "affliction_projectile_damage",
					stats = { "10% increased Projectile Damage" },
					enchant = {
						"Added Small Passive Skills grant: 10% increased Projectile Damage",
					},
				},
				["affliction_trap_and_mine_damage"] = {
					name = "Trap and Mine Damage",
					icon = "Art/2DArt/SkillIcons/passives/TrapAndMineDmgNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryTraps.png",
					tag = "affliction_trap_and_mine_damage",
					stats = { "12% increased Trap Damage", "12% increased Mine Damage" },
					enchant = {
						"Added Small Passive Skills grant: 12% increased Trap Damage",
						"Added Small Passive Skills grant: 12% increased Mine Damage",
					},
				},
				["affliction_totem_damage"] = {
					name = "Totem Damage",
					icon = "Art/2DArt/SkillIcons/passives/TotemDmgNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryTotem.png",
					tag = "affliction_totem_damage",
					stats = { "12% increased Totem Damage" },
					enchant = {
						"Added Small Passive Skills grant: 12% increased Totem Damage",
					},
				},
				["affliction_brand_damage"] = {
					name = "Brand Damage",
					icon = "Art/2DArt/SkillIcons/passives/BrandDmgNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryBrand.png",
					tag = "affliction_brand_damage",
					stats = { "12% increased Brand Damage" },
					enchant = {
						"Added Small Passive Skills grant: 12% increased Brand Damage",
					},
				},
				["affliction_channelling_skill_damage"] = {
					name = "Channelling Skill Damage",
					icon = "Art/2DArt/SkillIcons/passives/DmgWhenChannelSkillsNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryChannelling.png",
					tag = "affliction_channelling_skill_damage",
					stats = { "Channelling Skills deal 12% increased Damage" },
					enchant = {
						"Added Small Passive Skills grant: Channelling Skills deal 12% increased Damage",
					},
				},
				["affliction_flask_duration"] = {
					name = "Flask Duration",
					icon = "Art/2DArt/SkillIcons/passives/FlaskDurationnode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryFlasks.png",
					tag = "affliction_flask_duration",
					stats = { "6% increased Flask Effect Duration" },
					enchant = {
						"Added Small Passive Skills grant: 6% increased Flask Effect Duration",
					},
				},
				["affliction_life_and_mana_recovery_from_flasks"] = {
					name = "Life and Mana recovery from Flasks",
					icon = "Art/2DArt/SkillIcons/passives/LifeManaFlasksrecoverynode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryFlasks.png",
					tag = "affliction_life_and_mana_recovery_from_flasks",
					stats = { "10% increased Life Recovery from Flasks", "10% increased Mana Recovery from Flasks" },
					enchant = {
						"Added Small Passive Skills grant: 10% increased Life Recovery from Flasks",
						"Added Small Passive Skills grant: 10% increased Mana Recovery from Flasks",
					},
				},
			},
		},
		["Large Cluster Jewel"] = {
			size = "Large",
			sizeIndex = 2,
			minNodes = 8,
			maxNodes = 12,
			smallIndicies = { 0, 4, 6, 8, 10, 2, 7, 5, 9, 3, 11, 1 },
			notableIndicies = { 6, 4, 8, 10, 2 },
			socketIndicies = { 4, 8, 6 },
			totalIndicies = 12,
			skills = {
				["affliction_axe_and_sword_damage"] = {
					name = "Axe and Sword Damage",
					icon = "Art/2DArt/SkillIcons/passives/NodeAxeandSwordDamage.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryGroupSwordAndAxe.png",
					tag = "affliction_axe_and_sword_damage",
					stats = { "Axe Attacks deal 12% increased Damage with Hits and Ailments", "Sword Attacks deal 12% increased Damage with Hits and Ailments" },
					enchant = {
						"Added Small Passive Skills grant: Axe Attacks deal 12% increased Damage with Hits and Ailments",
						"Added Small Passive Skills grant: Sword Attacks deal 12% increased Damage with Hits and Ailments",
					},
				},
				["affliction_mace_and_staff_damage"] = {
					name = "Mace and Staff Damage",
					icon = "Art/2DArt/SkillIcons/passives/NodeMaceandStaffDamage.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryGroupMaceAndStaff.png",
					tag = "affliction_mace_and_staff_damage",
					stats = { "Staff Attacks deal 12% increased Damage with Hits and Ailments", "Mace or Sceptre Attacks deal 12% increased Damage with Hits and Ailments" },
					enchant = {
						"Added Small Passive Skills grant: Staff Attacks deal 12% increased Damage with Hits and Ailments",
						"Added Small Passive Skills grant: Mace or Sceptre Attacks deal 12% increased Damage with Hits and Ailments",
					},
				},
				["affliction_dagger_and_claw_damage"] = {
					name = "Dagger and Claw Damage",
					icon = "Art/2DArt/SkillIcons/passives/NodeDaggerandClawDamage.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltDaggerClawDamageMastery.png",
					tag = "affliction_dagger_and_claw_damage",
					stats = { "Claw Attacks deal 12% increased Damage with Hits and Ailments", "Dagger Attacks deal 12% increased Damage with Hits and Ailments" },
					enchant = {
						"Added Small Passive Skills grant: Claw Attacks deal 12% increased Damage with Hits and Ailments",
						"Added Small Passive Skills grant: Dagger Attacks deal 12% increased Damage with Hits and Ailments",
					},
				},
				["affliction_bow_damage"] = {
					name = "Bow Damage",
					icon = "Art/2DArt/SkillIcons/passives/NodeBowDamage.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryGroupBow.png",
					tag = "affliction_bow_damage",
					stats = { "12% increased Damage with Bows", "12% increased Damage Over Time with Bow Skills" },
					enchant = {
						"Added Small Passive Skills grant: 12% increased Damage with Bows",
						"Added Small Passive Skills grant: 12% increased Damage Over Time with Bow Skills",
					},
				},
				["affliction_wand_damage"] = {
					name = "Wand Damage",
					icon = "Art/2DArt/SkillIcons/passives/NodeWandDamage.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryGroupWand.png",
					tag = "affliction_wand_damage",
					stats = { "Wand Attacks deal 12% increased Damage with Hits and Ailments" },
					enchant = {
						"Added Small Passive Skills grant: Wand Attacks deal 12% increased Damage with Hits and Ailments",
					},
				},
				["affliction_damage_with_two_handed_melee_weapons"] = {
					name = "Damage with Two Handed Weapons",
					icon = "Art/2DArt/SkillIcons/passives/NodeTwoHandedMeleeDamage.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryGroupTwoHands.png",
					tag = "affliction_damage_with_two_handed_melee_weapons",
					stats = { "12% increased Damage with Two Handed Weapons" },
					enchant = {
						"Added Small Passive Skills grant: 12% increased Damage with Two Handed Weapons",
					},
				},
				["affliction_attack_damage_while_dual_wielding_"] = {
					name = "Attack Damage while Dual Wielding",
					icon = "Art/2DArt/SkillIcons/passives/NodeDualWieldingDamage.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryGroupDualWield.png",
					tag = "affliction_attack_damage_while_dual_wielding_",
					stats = { "12% increased Attack Damage while Dual Wielding" },
					enchant = {
						"Added Small Passive Skills grant: 12% increased Attack Damage while Dual Wielding",
					},
				},
				["affliction_attack_damage_while_holding_a_shield"] = {
					name = "Attack Damage while holding a Shield",
					icon = "Art/2DArt/SkillIcons/passives/NodeHoldingShieldDamage.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryGroupShield.png",
					tag = "affliction_attack_damage_while_holding_a_shield",
					stats = { "12% increased Attack Damage while holding a Shield" },
					enchant = {
						"Added Small Passive Skills grant: 12% increased Attack Damage while holding a Shield",
					},
				},
				["affliction_attack_damage_"] = {
					name = "Attack Damage",
					icon = "Art/2DArt/SkillIcons/passives/IncreasedAttackDamageNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltAttackDamageMastery.png",
					tag = "affliction_attack_damage_",
					stats = { "10% increased Attack Damage" },
					enchant = {
						"Added Small Passive Skills grant: 10% increased Attack Damage",
					},
				},
				["affliction_spell_damage"] = {
					name = "Spell Damage",
					icon = "Art/2DArt/SkillIcons/passives/IncreasedSpellDamageNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryGroupCast.png",
					tag = "affliction_spell_damage",
					stats = { "10% increased Spell Damage" },
					enchant = {
						"Added Small Passive Skills grant: 10% increased Spell Damage",
					},
				},
				["affliction_elemental_damage"] = {
					name = "Elemental Damage",
					icon = "Art/2DArt/SkillIcons/passives/ElementalDamagenode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryElementalDamage.png",
					tag = "affliction_elemental_damage",
					stats = { "10% increased Elemental Damage" },
					enchant = {
						"Added Small Passive Skills grant: 10% increased Elemental Damage",
					},
				},
				["affliction_physical_damage"] = {
					name = "Physical Damage",
					icon = "Art/2DArt/SkillIcons/passives/PhysicalDamagenode2.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryPhysicalDamage.png",
					tag = "affliction_physical_damage",
					stats = { "12% increased Physical Damage" },
					enchant = {
						"Added Small Passive Skills grant: 12% increased Physical Damage",
					},
				},
				["affliction_fire_damage"] = {
					name = "Fire Damage",
					icon = "Art/2DArt/SkillIcons/passives/FireDamagenode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryGroupFire.png",
					tag = "affliction_fire_damage",
					stats = { "12% increased Fire Damage" },
					enchant = {
						"Added Small Passive Skills grant: 12% increased Fire Damage",
					},
				},
				["affliction_lightning_damage"] = {
					name = "Lightning Damage",
					icon = "Art/2DArt/SkillIcons/passives/LightningDamagenode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryGroupLightning.png",
					tag = "affliction_lightning_damage",
					stats = { "12% increased Lightning Damage" },
					enchant = {
						"Added Small Passive Skills grant: 12% increased Lightning Damage",
					},
				},
				["affliction_cold_damage"] = {
					name = "Cold Damage",
					icon = "Art/2DArt/SkillIcons/passives/ColdDamagenode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryGroupCold.png",
					tag = "affliction_cold_damage",
					stats = { "12% increased Cold Damage" },
					enchant = {
						"Added Small Passive Skills grant: 12% increased Cold Damage",
					},
				},
				["affliction_chaos_damage"] = {
					name = "Chaos Damage",
					icon = "Art/2DArt/SkillIcons/passives/ChaosDamagenode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryChaos.png",
					tag = "affliction_chaos_damage",
					stats = { "12% increased Chaos Damage" },
					enchant = {
						"Added Small Passive Skills grant: 12% increased Chaos Damage",
					},
				},
				["affliction_minion_damage"] = {
					name = "Minion Damage",
					icon = "Art/2DArt/SkillIcons/passives/IncreasedMinionDamageNode.png",
					masteryIcon = "Art/2DArt/SkillIcons/passives/AltMasteryGroupMinions.png",
					tag = "affliction_minion_damage",
					stats = { "Minions deal 10% increased Damage" },
					enchant = {
						"Added Small Passive Skills grant: Minions deal 10% increased Damage",
					},
				},
			},
		},
	},
	notableSortOrder = {
		["Prodigious Defence"] = 11075,
		["Advance Guard"] = 11076,
		["Gladiatorial Combat"] = 11077,
		["Strike Leader"] = 11078,
		["Powerful Ward"] = 11079,
		["Enduring Ward"] = 11080,
		["Gladiator's Fortitude"] = 11081,
		["Precise Retaliation"] = 11082,
		["Veteran Defender"] = 11083,
		["Iron Breaker"] = 11084,
		["Deep Cuts"] = 11085,
		["Master the Fundamentals"] = 11086,
		["Force Multiplier"] = 11087,
		["Furious Assault"] = 11088,
		["Vicious Skewering"] = 11089,
		["Grim Oath"] = 11090,
		["Battle-Hardened"] = 11091,
		["Replenishing Presence"] = 11092,
		["Master of Command"] = 11093,
		["Spiteful Presence"] = 11094,
		["Purposeful Harbinger"] = 11095,
		["Destructive Aspect"] = 11096,
		["Electric Presence"] = 11097,
		["Volatile Presence"] = 11098,
		["Righteous Path"] = 11099,
		["Skullbreaker"] = 11100,
		["Pressure Points"] = 11101,
		["Overwhelming Malice"] = 11102,
		["Magnifier"] = 11103,
		["Savage Response"] = 11104,
		["Eye of the Storm"] = 11105,
		["Basics of Pain"] = 11106,
		["Quick Getaway"] = 11107,
		["Assert Dominance"] = 11108,
		["Vast Power"] = 11109,
		["Powerful Assault"] = 11110,
		["Intensity"] = 11111,
		["Titanic Swings"] = 11112,
		["Towering Threat"] = 11113,
		["Ancestral Echo"] = 11114,
		["Ancestral Reach"] = 11115,
		["Ancestral Might"] = 11116,
		["Ancestral Preservation"] = 11117,
		["Snaring Spirits"] = 11118,
		["Sleepless Sentries"] = 11119,
		["Ancestral Guidance"] = 11120,
		["Ancestral Inspiration"] = 11121,
		["Vital Focus"] = 11122,
		["Unrestrained Focus"] = 11123,
		["Unwavering Focus"] = 11124,
		["Enduring Focus"] = 11125,
		["Precise Focus"] = 11126,
		["Stoic Focus"] = 11127,
		["Hex Breaker"] = 11128,
		["Arcane Adept"] = 11129,
		["Distilled Perfection"] = 11130,
		["Spiked Concoction"] = 11131,
		["Fasting"] = 11132,
		["Mender's Wellspring"] = 11133,
		["Special Reserve"] = 11134,
		["Numbing Elixir"] = 11135,
		["Mob Mentality"] = 11136,
		["Cry Wolf"] = 11137,
		["Haunting Shout"] = 11138,
		["Lead By Example"] = 11139,
		["Provocateur"] = 11140,
		["Warning Call"] = 11141,
		["Rattling Bellow"] = 11142,
		["Bloodscent"] = 11143,
		["Run Through"] = 11144,
		["Wound Aggravation"] = 11145,
		["Overlord"] = 11146,
		["Expansive Might"] = 11147,
		["Weight Advantage"] = 11148,
		["Wind-up"] = 11149,
		["Fan of Blades"] = 11150,
		["Disease Vector"] = 11151,
		["Arcing Shot"] = 11152,
		["Tempered Arrowheads"] = 11153,
		["Broadside"] = 11154,
		["Explosive Force"] = 11155,
		["Opportunistic Fusilade"] = 11156,
		["Storm's Hand"] = 11157,
		["Battlefield Dominator"] = 11158,
		["Martial Mastery"] = 11159,
		["Surefooted Striker"] = 11160,
		["Graceful Execution"] = 11161,
		["Brutal Infamy"] = 11162,
		["Fearsome Warrior"] = 11163,
		["Combat Rhythm"] = 11164,
		["Hit and Run"] = 11165,
		["Insatiable Killer"] = 11166,
		["Mage Bane"] = 11167,
		["Martial Momentum"] = 11168,
		["Deadly Repartee"] = 11169,
		["Quick and Deadly"] = 11170,
		["Smite the Weak"] = 11171,
		["Heavy Hitter"] = 11172,
		["Martial Prowess"] = 11173,
		["Calamitous"] = 11174,
		["Devastator"] = 11175,
		["Fuel the Fight"] = 11176,
		["Drive the Destruction"] = 11177,
		["Feed the Fury"] = 11178,
		["Seal Mender"] = 11179,
		["Conjured Wall"] = 11180,
		["Arcane Heroism"] = 11181,
		["Practiced Caster"] = 11182,
		["Burden Projection"] = 11183,
		["Thaumophage"] = 11184,
		["Essence Rush"] = 11185,
		["Sap Psyche"] = 11186,
		["Sadist"] = 11187,
		["Corrosive Elements"] = 11188,
		["Doryani's Lesson"] = 11189,
		["Disorienting Display"] = 11190,
		["Prismatic Heart"] = 11191,
		["Widespread Destruction"] = 11192,
		["Master of Fire"] = 11193,
		["Smoking Remains"] = 11194,
		["Cremator"] = 11195,
		["Snowstorm"] = 11196,
		["Storm Drinker"] = 11197,
		["Paralysis"] = 11198,
		["Supercharge"] = 11199,
		["Blanketed Snow"] = 11200,
		["Cold to the Core"] = 11201,
		["Cold-Blooded Killer"] = 11202,
		["Touch of Cruelty"] = 11203,
		["Unwaveringly Evil"] = 11204,
		["Unspeakable Gifts"] = 11205,
		["Dark Ideation"] = 11206,
		["Unholy Grace"] = 11207,
		["Wicked Pall"] = 11208,
		["Renewal"] = 11209,
		["Raze and Pillage"] = 11210,
		["Rotten Claws"] = 11211,
		["Call to the Slaughter"] = 11212,
		["Skeletal Atrophy"] = 11499,
		["Hulking Corpses"] = 11213,
		["Vicious Bite"] = 11214,
		["Primordial Bond"] = 11215,
		["Blowback"] = 11216,
		["Fan the Flames"] = 11217,
		["Cooked Alive"] = 11218,
		["Burning Bright"] = 11219,
		["Wrapped in Flame"] = 11220,
		["Vivid Hues"] = 11221,
		["Rend"] = 11222,
		["Disorienting Wounds"] = 11223,
		["Compound Injury"] = 11224,
		["Blood Artist"] = 13885,
		["Phlebotomist"] = 13886,
		["Septic Spells"] = 11225,
		["Low Tolerance"] = 11226,
		["Steady Torment"] = 11227,
		["Eternal Suffering"] = 11228,
		["Eldritch Inspiration"] = 11229,
		["Wasting Affliction"] = 11230,
		["Haemorrhage"] = 11231,
		["Flow of Life"] = 11232,
		["Exposure Therapy"] = 11233,
		["Brush with Death"] = 11234,
		["Vile Reinvigoration"] = 11235,
		["Circling Oblivion"] = 11236,
		["Brewed for Potency"] = 11237,
		["Astonishing Affliction"] = 11238,
		["Cold Conduction"] = 11239,
		["Inspired Oppression"] = 11240,
		["Chilling Presence"] = 11241,
		["Deep Chill"] = 11242,
		["Blast-Freeze"] = 11243,
		["Thunderstruck"] = 11244,
		["Stormrider"] = 11245,
		["Overshock"] = 11246,
		["Evil Eye"] = 11247,
		["Evil Eye"] = 11248,
		["Forbidden Words"] = 11249,
		["Doedre's Spite"] = 11250,
		["Victim Maker"] = 11251,
		["Master of Fear"] = 11252,
		["Wish for Death"] = 11253,
		["Heraldry"] = 11254,
		["Endbringer"] = 11255,
		["Cult-Leader"] = 11256,
		["Empowered Envoy"] = 11257,
		["Dark Messenger"] = 11258,
		["Agent of Destruction"] = 11259,
		["Lasting Impression"] = 11260,
		["Self-Fulfilling Prophecy"] = 11261,
		["Invigorating Portents"] = 11262,
		["Pure Agony"] = 11263,
		["Disciples"] = 11264,
		["Dread March"] = 11265,
		["Blessed Rebirth"] = 11266,
		["Life from Death"] = 11267,
		["Feasting Fiends"] = 11268,
		["Bodyguards"] = 11269,
		["Follow-Through"] = 11270,
		["Streamlined"] = 11271,
		["Shrieking Bolts"] = 11272,
		["Eye to Eye"] = 11273,
		["Repeater"] = 11274,
		["Aerodynamics"] = 11275,
		["Chip Away"] = 11276,
		["Seeker Runes"] = 11277,
		["Remarkable"] = 11278,
		["Brand Loyalty"] = 11279,
		["Holy Conquest"] = 11280,
		["Grand Design"] = 11281,
		["Set and Forget"] = 11282,
		["Expert Sabotage"] = 11283,
		["Guerilla Tactics"] = 11284,
		["Expendability"] = 11285,
		["Arcane Pyrotechnics"] = 11286,
		["Surprise Sabotage"] = 11287,
		["Careful Handling"] = 11288,
		["Peak Vigour"] = 11289,
		["Fettle"] = 11290,
		["Feast of Flesh"] = 11291,
		["Sublime Sensation"] = 11292,
		["Surging Vitality"] = 11293,
		["Peace Amidst Chaos"] = 11294,
		["Adrenaline"] = 11295,
		["Wall of Muscle"] = 11296,
		["Mindfulness"] = 11297,
		["Liquid Inspiration"] = 11298,
		["Openness"] = 11299,
		["Daring Ideas"] = 11300,
		["Clarity of Purpose"] = 11301,
		["Scintillating Idea"] = 11302,
		["Holistic Health"] = 11303,
		["Genius"] = 11304,
		["Improvisor"] = 11305,
		["Stubborn Student"] = 11306,
		["Savour the Moment"] = 11307,
		["Energy From Naught"] = 11308,
		["Will Shaper"] = 11309,
		["Spring Back"] = 11310,
		["Conservation of Energy"] = 11311,
		["Heart of Iron"] = 11312,
		["Prismatic Carapace"] = 11313,
		["Militarism"] = 11314,
		["Second Skin"] = 11315,
		["Dragon Hunter"] = 11316,
		["Enduring Composure"] = 11317,
		["Prismatic Dance"] = 11318,
		["Natural Vigour"] = 11319,
		["Untouchable"] = 11320,
		["Shifting Shadow"] = 11321,
		["Readiness"] = 11322,
		["Confident Combatant"] = 11323,
		["Flexible Sentry"] = 11324,
		["Vicious Guard"] = 11325,
		["Mystical Ward"] = 11326,
		["Rote Reinforcement"] = 11327,
		["Mage Hunter"] = 11328,
		["Riot Queller"] = 11329,
		["One with the Shield"] = 11330,
		["Aerialist"] = 11331,
		["Elegant Form"] = 11332,
		["Darting Movements"] = 11333,
		["No Witnesses"] = 11334,
		["Molten One's Mark"] = 11335,
		["Fire Attunement"] = 11336,
		["Pure Might"] = 11337,
		["Blacksmith"] = 11338,
		["Non-Flammable"] = 11339,
		["Winter Prowler"] = 11340,
		["Hibernator"] = 11341,
		["Pure Guile"] = 11342,
		["Alchemist"] = 11343,
		["Antifreeze"] = 11344,
		["Wizardry"] = 11345,
		["Capacitor"] = 11346,
		["Pure Aptitude"] = 11347,
		["Sage"] = 11348,
		["Insulated"] = 11349,
		["Born of Chaos"] = 11350,
		["Antivenom"] = 11351,
		["Rot-Resistant"] = 11352,
		["Blessed"] = 11353,
		["Student of Decay"] = 11354,
		["Lord of Drought"] = 12193,
		["Blizzard Caller"] = 12194,
		["Tempt the Storm"] = 12195,
		["Misery Everlasting"] = 12196,
		["Exploit Weakness"] = 12197,
		["Self-Control"] = 12202,
		["Uncompromising"] = 12203,
		["Sublime Form"] = 12204,
		["Mortifying Aspect"] = 12260,
		["Frantic Aspect"] = 12261,
		["Introspection"] = 12262,
		["Hound's Mark"] = 12198,
		["Doedre's Gluttony"] = 12199,
		["Doedre's Apathy"] = 12200,
		["Master of the Maelstrom"] = 12201,
		["Aggressive Defence"] = 15336,
	},
	keystones = {
		"Disciple of Kitava",
		"Lone Messenger",
		"Nature's Patience",
		"Secrets of Suffering",
		"Kineticism",
		"Veteran's Awareness",
		"Hollow Palm Technique",
		"Pitfighter",
	},
	orbitOffsets = {
		[2627120085] = {
			[0] = 3,
			[1] = 5,
			[2] = 5,
		},
		[2627101230] = {
			[0] = 0,
			[1] = 11,
			[2] = 11,
		},
		[2627106371] = {
			[0] = 2,
			[1] = 3,
			[2] = 3,
		},
		[2627104746] = {
			[0] = 1,
			[1] = 1,
			[2] = 1,
		},
		[2627124228] = {
			[0] = 5,
			[1] = 9,
			[2] = 9,
		},
		[2627094852] = {
			[0] = 4,
			[1] = 7,
			[2] = 7,
		},
		[2627131802] = {
			[0] = 2,
			[1] = 3,
		},
		[2627102757] = {
			[0] = 3,
			[1] = 5,
		},
		[2627089297] = {
			[0] = 3,
			[1] = 7,
		},
		[2627116210] = {
			[0] = 1,
		},
		[2627094457] = {
			[0] = 2,
		},
		[2627084052] = {
			[0] = 3,
		},
		[2627127329] = {
			[0] = 5,
			[1] = 9,
		},
		[2627133290] = {
			[0] = 5,
			[1] = 11,
		},
		[2627111949] = {
			[0] = 0,
			[1] = 1,
		},
		[2627111409] = {
			[0] = 4,
		},
		[2627120566] = {
			[0] = 5,
		},
		[2627113243] = {
			[0] = 0,
		},
		[2627101537] = {
			[0] = 1,
			[1] = 1,
		},
		[2627104114] = {
			[0] = 2,
			[1] = 3,
		},
		[2627129299] = {
			[0] = 3,
			[1] = 5,
		},
		[2627079950] = {
			[0] = 0,
		},
		[2627126047] = {
			[0] = 1,
		},
		[2627098142] = {
			[0] = 2,
		},
		[2627113994] = {
			[0] = 5,
			[1] = 11,
		},
		[2627140262] = {
			[0] = 1,
			[1] = 1,
		},
		[2627134451] = {
			[0] = 2,
			[1] = 3,
		},
		[2627124224] = {
			[0] = 5,
		},
		[2627103571] = {
			[0] = 0,
		},
		[2627111166] = {
			[0] = 1,
		},
		[2627112022] = {
			[0] = 4,
			[1] = 7,
		},
		[2627109929] = {
			[0] = 5,
			[1] = 9,
		},
		[2627126275] = {
			[0] = 5,
			[1] = 11,
		},
		[2627112510] = {
			[0] = 3,
		},
		[2627086739] = {
			[0] = 4,
		},
		[2627132535] = {
			[0] = 5,
		},
		[2627134290] = {
			[0] = 3,
			[1] = 5,
		},
		[2627110109] = {
			[0] = 4,
			[1] = 7,
		},
		[2627100548] = {
			[0] = 5,
			[1] = 9,
		},
		[2627139850] = {
			[0] = 2,
		},
		[2627130696] = {
			[0] = 3,
		},
		[2627103915] = {
			[0] = 4,
		},
	},
}