-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[2]={
		stats={
			[1]="virtual_number_of_marks_allowed_per_type"
		}
	},
	[3]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Orb radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Orb radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_secondary_area_of_effect_radius"
		}
	},
	[4]={
		[1]={
		},
		stats={
			[1]="active_skill_secondary_area_of_effect_radius"
		}
	},
	[5]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Orb duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Orb duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[6]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Limit {0} Orb of Storms"
			}
		},
		stats={
			[1]="orb_of_storms_base_maximum_number_of_orbs"
		}
	},
	[7]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Orb Limit@{0}"
			}
		},
		stats={
			[1]="orb_of_storms_maximum_number_of_orbs"
		}
	},
	[8]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	["active_skill_base_area_of_effect_radius"]=1,
	["active_skill_base_secondary_area_of_effect_radius"]=3,
	["active_skill_secondary_area_of_effect_radius"]=4,
	["base_skill_effect_duration"]=5,
	["orb_of_storms_base_maximum_number_of_orbs"]=6,
	["orb_of_storms_maximum_number_of_orbs"]=7,
	parent="skill_stat_descriptions",
	["skill_effect_duration"]=8,
	["virtual_number_of_marks_allowed_per_type"]=2
}