-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Buff duration is {0} seconds per Power Charge Consumed"
			}
		},
		stats={
			[1]="elemental_power_buff_duration_per_power_charge_ms"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d}% more Elemental Damage per Power Charge Consumed"
			},
			[2]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0}% more Elemental Damage per Power Charge Consumed\nBuff causes all Elemental Damage to Contribute to Ignite chance and Magnitude, Shock chance, and Freeze buildup"
			}
		},
		stats={
			[1]="elemental_power_elemental_damage_+%_final_per_power_charge",
			[2]="quality_stat_elemental_power_elemental_damage_+%_final_per_power_charge_is_gem"
		}
	},
	["elemental_power_buff_duration_per_power_charge_ms"]=1,
	["elemental_power_elemental_damage_+%_final_per_power_charge"]=2,
	parent="skill_stat_descriptions",
	["quality_stat_elemental_power_elemental_damage_+%_final_per_power_charge_is_gem"]=2
}