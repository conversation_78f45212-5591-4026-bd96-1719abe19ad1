-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="skill_can_fire_arrows"
		}
	},
	[2]={
		stats={
			[1]="cannot_pierce"
		}
	},
	[3]={
		stats={
			[1]="base_projectiles_cannot_chain"
		}
	},
	[4]={
		stats={
			[1]="projectiles_cannot_split"
		}
	},
	[5]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[6]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fires lightning bolts at targets within {0} metre radius"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[7]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="Fires {0:+d} bolt"
			},
			[2]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="Fires {0:+d} bolts"
			},
			[3]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fires {0} bolt"
			},
			[4]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fires {0} bolts"
			}
		},
		stats={
			[1]="base_number_of_projectiles",
			[2]="quality_display_base_number_of_projectiles_is_gem"
		}
	},
	["active_skill_area_of_effect_radius"]=5,
	["active_skill_base_area_of_effect_radius"]=6,
	["base_number_of_projectiles"]=7,
	["base_projectiles_cannot_chain"]=3,
	["cannot_pierce"]=2,
	parent="skill_stat_descriptions",
	["projectiles_cannot_split"]=4,
	["quality_display_base_number_of_projectiles_is_gem"]=7,
	["skill_can_fire_arrows"]=1
}