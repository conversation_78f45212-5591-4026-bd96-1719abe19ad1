-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Explosion radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Explosion radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_secondary_area_of_effect_radius"
		}
	},
	[2]={
		[1]={
		},
		stats={
			[1]="active_skill_secondary_area_of_effect_radius"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Spears that have existed for at\nleast 0.5 seconds deal {0}% more Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Spears that have existed for at\nleast 0.5 seconds deal {0}% less Damage"
			}
		},
		stats={
			[1]="spearfield_spear_damage_+%_final_after_half_seconds"
		}
	},
	["active_skill_base_secondary_area_of_effect_radius"]=1,
	["active_skill_secondary_area_of_effect_radius"]=2,
	parent="skill_stat_descriptions",
	["spearfield_spear_damage_+%_final_after_half_seconds"]=3
}