-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Rain radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Rain radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[3]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Arrow impact radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Arrow impact radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_secondary_area_of_effect_radius"
		}
	},
	[4]={
		[1]={
		},
		stats={
			[1]="active_skill_secondary_area_of_effect_radius"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Maximum {0} arrow rain"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Maximum {0} arrow rains"
			}
		},
		stats={
			[1]="base_number_of_arrow_rains_allowed"
		}
	},
	[6]={
		[1]={
		},
		stats={
			[1]="number_of_arrow_rains_allowed"
		}
	},
	[7]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="One arrow lands every {0} seconds"
			}
		},
		stats={
			[1]="rain_of_arrows_delay_per_arrow"
		}
	},
	[8]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fires {0}% more Arrows if a Frenzy Charge was consumed"
			}
		},
		stats={
			[1]="rain_of_arrows_projectile_count_multiplier_if_any_frenzy_charge_spent"
		}
	},
	[9]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fires {0}% more Arrows per Frenzy Charge consumed"
			}
		},
		stats={
			[1]="rain_of_arrows_projectile_count_multiplier_per_frenzy_charge"
		}
	},
	[10]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Modifiers to Projectile speed apply to arrow landing frequency"
			}
		},
		stats={
			[1]="skill_specific_stat_description_mode"
		}
	},
	[11]={
		[1]={
		},
		stats={
			[1]="virtual_rain_of_arrows_delay_per_arrow_display_ms"
		}
	},
	["active_skill_area_of_effect_radius"]=1,
	["active_skill_base_area_of_effect_radius"]=2,
	["active_skill_base_secondary_area_of_effect_radius"]=3,
	["active_skill_secondary_area_of_effect_radius"]=4,
	["base_number_of_arrow_rains_allowed"]=5,
	["number_of_arrow_rains_allowed"]=6,
	parent="skill_stat_descriptions",
	["rain_of_arrows_delay_per_arrow"]=7,
	["rain_of_arrows_projectile_count_multiplier_if_any_frenzy_charge_spent"]=8,
	["rain_of_arrows_projectile_count_multiplier_per_frenzy_charge"]=9,
	["skill_specific_stat_description_mode"]=10,
	["virtual_rain_of_arrows_delay_per_arrow_display_ms"]=11
}