-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Totem uses its own Two Handed Mace, dealing\n{0} to {1} base Physical damage"
			}
		},
		stats={
			[1]="alt_attack_container_main_hand_weapon_minimum_physical_damage",
			[2]="alt_attack_container_main_hand_weapon_maximum_physical_damage"
		}
	},
	["alt_attack_container_main_hand_weapon_maximum_physical_damage"]=1,
	["alt_attack_container_main_hand_weapon_minimum_physical_damage"]=1,
	parent="skill_stat_descriptions"
}