if not loadStatFile then
	dofile("statdesc.lua")
end
loadStatFile("stat_descriptions.csd")

local out = io.open("../Data/ModScalability.lua", "w")
out:write('-- This file is automatically generated, do not edit!\n')
out:write('-- Item data (c) Grinding Gear Games\n\nreturn {\n')
local scalabilityLines = describeScalability("stat_descriptions.csd")
local lines = { }
for line, _ in pairs(scalabilityLines) do
    table.insert(lines, line)
end
table.sort(lines)
for _, line in ipairs(lines) do
    local scalability = scalabilityLines[line]
    out:write('\t["', line, '"] = { ')
    for i, scalable in ipairs(scalability) do
        out:write("{ isScalable = "..tostring(scalable.isScalable))
        if scalable.formats then 
            out:write(', formats = { ')
            for j, format in ipairs(scalable.formats) do
                out:write('"'..format..'"')
                if j < #scalable.formats then out:write(", ") end
            end
            out:write(" }")
        end
        out:write(" }")
        if i < #scalability then out:write(", ") end
    end
    out:write(" },\n")
end

out:write('}')
out:close()

print("Stat mod scalability exported.")
