-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=2
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]=10,
						[2]=10
					}
				},
				text="Minions Ignite Enemies within a radius of {1} metre as though dealt Base Fire Damage equal to {0}% of Minion's Maximum Life"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=2
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Minions Ignite Enemies within a radius of {1} metres as though dealt Base Fire Damage equal to {0}% of Minion's Maximum Life"
			}
		},
		stats={
			[1]="infernal_familiar_minion_burns_for_%_max_life",
			[2]="infernal_familiar_minion_burn_radius"
		}
	},
	["infernal_familiar_minion_burn_radius"]=1,
	["infernal_familiar_minion_burns_for_%_max_life"]=1,
	parent="skill_stat_descriptions"
}