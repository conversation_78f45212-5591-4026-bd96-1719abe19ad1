-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="frost_wall_maximum_life"
		}
	},
	[2]={
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[3]={
		stats={
			[1]="skill_ground_effect_duration"
		}
	},
	[4]={
		stats={
			[1]="base_number_of_frozen_locus_allowed"
		}
	},
	[5]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[6]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Explosion and Chilled Ground radius are {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Explosion and Chilled Ground radius are {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	["active_skill_area_of_effect_radius"]=5,
	["active_skill_base_area_of_effect_radius"]=6,
	["base_number_of_frozen_locus_allowed"]=4,
	["base_skill_effect_duration"]=2,
	["frost_wall_maximum_life"]=1,
	parent="specific_skill_stat_descriptions/ice_ambush_statset_0",
	["skill_ground_effect_duration"]=3
}