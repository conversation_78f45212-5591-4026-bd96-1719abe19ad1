-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Impact radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Impact radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[3]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Jagged Ground radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Jagged Ground radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_tertiary_area_of_effect_radius"
		}
	},
	[4]={
		[1]={
		},
		stats={
			[1]="active_skill_tertiary_area_of_effect_radius"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d} maximum Jagged Ground patch"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d} maximum Jagged Ground patches"
			},
			[3]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Maximum {0} Jagged Ground patch"
			},
			[4]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Maximum {0} Jagged Ground patches"
			}
		},
		stats={
			[1]="base_number_of_earthquakes_allowed",
			[2]="quality_display_earthquake_is_gem"
		}
	},
	[6]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Maximum Jagged Ground patches@{0}"
			}
		},
		stats={
			[1]="number_of_earthquakes_allowed"
		}
	},
	[7]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	[8]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Jagged Ground duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Jagged Ground duration is {0} seconds"
			}
		},
		stats={
			[1]="skill_jagged_ground_base_duration_ms"
		}
	},
	["active_skill_area_of_effect_radius"]=1,
	["active_skill_base_area_of_effect_radius"]=2,
	["active_skill_base_tertiary_area_of_effect_radius"]=3,
	["active_skill_tertiary_area_of_effect_radius"]=4,
	["base_number_of_earthquakes_allowed"]=5,
	["number_of_earthquakes_allowed"]=6,
	parent="skill_stat_descriptions",
	["quality_display_earthquake_is_gem"]=5,
	["skill_effect_duration"]=7,
	["skill_jagged_ground_base_duration_ms"]=8
}