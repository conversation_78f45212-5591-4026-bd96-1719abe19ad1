-- This file is automatically generated, do not edit!
-- Path of Building
-- World Area Data (c) Grinding Gear Games

local worldAreas, _ = ...

worldAreas["CharacterSelect"] = {
	name = "Character Select (Act 1)",
	baseName = "Character Select",
	tags = {  },
	act = 1,
	level = 0,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["PersonalHideout"] = {
	name = "Your Hideout (Act 1)",
	baseName = "Your Hideout",
	tags = {  },
	act = 1,
	level = 0,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["GuildHideout__"] = {
	name = "Your Guild Hideout (Act 1)",
	baseName = "Your Guild Hideout",
	tags = {  },
	act = 1,
	level = 0,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["HideoutCave"] = {
	name = "Submerged Hideout (Act 1)",
	baseName = "Submerged Hideout",
	tags = { "area_with_water" },
	act = 1,
	level = 20,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutShipgraveyard"] = {
	name = "Shipwreck Hideout (Act 1)",
	baseName = "Shipwreck Hideout",
	tags = { "area_with_water" },
	act = 1,
	level = 20,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutSpace"] = {
	name = "Celestial Nebula Hideout (Act 1)",
	baseName = "Celestial Nebula Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutArenaGraveyardTrio"] = {
	name = "Entombed Hideout (Act 1)",
	baseName = "Entombed Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutPillarsOfArun"] = {
	name = "Towering Hideout (Act 1)",
	baseName = "Towering Hideout",
	tags = {  },
	act = 1,
	level = 20,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutAtziriArena"] = {
	name = "Corrupted Hideout (Act 1)",
	baseName = "Corrupted Hideout",
	tags = {  },
	act = 1,
	level = 20,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutInnocenceArena"] = {
	name = "Innocent Hideout (Act 1)",
	baseName = "Innocent Hideout",
	tags = {  },
	act = 1,
	level = 20,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutShaperArena"] = {
	name = "Shaped Hideout (Act 1)",
	baseName = "Shaped Hideout",
	tags = {  },
	act = 1,
	level = 20,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutKaomArena"] = {
	name = "Furious Hideout (Act 1)",
	baseName = "Furious Hideout",
	tags = {  },
	act = 1,
	level = 20,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutDaressoArena"] = {
	name = "Champion's Hideout (Act 1)",
	baseName = "Champion's Hideout",
	tags = {  },
	act = 1,
	level = 20,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutDominusArena"] = {
	name = "Indomitable Hideout (Act 1)",
	baseName = "Indomitable Hideout",
	tags = {  },
	act = 1,
	level = 20,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutPietyArena"] = {
	name = "Morbid Hideout (Act 1)",
	baseName = "Morbid Hideout",
	tags = {  },
	act = 1,
	level = 20,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutLunarisSolarisArena"] = {
	name = "Eclipsed Hideout (Act 1)",
	baseName = "Eclipsed Hideout",
	tags = {  },
	act = 1,
	level = 20,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutKitavaArenaAct10"] = {
	name = "Ravenous Hideout (Act 1)",
	baseName = "Ravenous Hideout",
	tags = {  },
	act = 1,
	level = 20,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutDoomguard"] = {
	name = "Doomguard Hideout (Act 1)",
	baseName = "Doomguard Hideout",
	tags = { "area_with_water" },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutSunspire"] = {
	name = "Sunspire Hideout (Act 1)",
	baseName = "Sunspire Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutDarkwood"] = {
	name = "Darkwood Hideout (Act 1)",
	baseName = "Darkwood Hideout",
	tags = { "area_with_water" },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutArenaElder"] = {
	name = "Void Hideout (Act 1)",
	baseName = "Void Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutChiyouSpring"] = {
	name = "Chiyou Hideout (Act 1)",
	baseName = "Chiyou Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutBlankAbyss"] = {
	name = "Infinite Abyss Hideout (Act 1)",
	baseName = "Infinite Abyss Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutBlankCity"] = {
	name = "Urban Sprawl Hideout (Act 1)",
	baseName = "Urban Sprawl Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutBlankClouds"] = {
	name = "Boundless Skies Hideout (Act 1)",
	baseName = "Boundless Skies Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutBlankDesert"] = {
	name = "Endless Sands Hideout (Act 1)",
	baseName = "Endless Sands Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutBlankDirt"] = {
	name = "Eternal Wasteland Hideout (Act 1)",
	baseName = "Eternal Wasteland Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutBlankGrass"] = {
	name = "Vast Plains Hideout (Act 1)",
	baseName = "Vast Plains Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutBlankSea"] = {
	name = "All at Sea Hideout (Act 1)",
	baseName = "All at Sea Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutBlankSnow"] = {
	name = "Glacial Expanse Hideout (Act 1)",
	baseName = "Glacial Expanse Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutThaumaturgy"] = {
	name = "Thaumaturgical Hideout (Act 1)",
	baseName = "Thaumaturgical Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutYaochi"] = {
	name = "Yaochi Hideout (Act 1)",
	baseName = "Yaochi Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutHumanoidPet"] = {
	name = "Humanoid Pet Hideout (Act 1)",
	baseName = "Humanoid Pet Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutRitualLeague"] = {
	name = "Ritualist's Hideout (Act 1)",
	baseName = "Ritualist's Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutArenaMaven"] = {
	name = "Crucible Hideout (Act 1)",
	baseName = "Crucible Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutHasina"] = {
	name = "Tavern Hideout (Act 1)",
	baseName = "Tavern Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutAzuriteCave"] = {
	name = "Azurite Cavern Hideout (Act 1)",
	baseName = "Azurite Cavern Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutSynthesisHub"] = {
	name = "Synthesis Hideout (Act 1)",
	baseName = "Synthesis Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutVeritaniaArena"] = {
	name = "Redeemer's Hideout (Act 1)",
	baseName = "Redeemer's Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutBlankBlack"] = {
	name = "Black Void Hideout (Act 1)",
	baseName = "Black Void Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutTencentApocalypse"] = {
	name = "Cataclysmic Hideout (Act 1)",
	baseName = "Cataclysmic Hideout",
	tags = { "area_with_water" },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutOlrothArena"] = {
	name = "Ancestral Hideout (Act 1)",
	baseName = "Ancestral Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutLight"] = {
	name = "Timekeeper's Hideout (Act 1)",
	baseName = "Timekeeper's Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutDark"] = {
	name = "Ghost-lit Graveyard Hideout (Act 1)",
	baseName = "Ghost-lit Graveyard Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutArenaBlackStar_"] = {
	name = "Polaric Hideout (Act 1)",
	baseName = "Polaric Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutArenaInfiniteHunger"] = {
	name = "Seething Hideout (Act 1)",
	baseName = "Seething Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutCosmicAtlas"] = {
	name = "Atlas Hideout (Act 1)",
	baseName = "Atlas Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutArenaEaterOfWorlds"] = {
	name = "Tangled Hideout (Act 1)",
	baseName = "Tangled Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutArenaSearingExarch"] = {
	name = "Searing Hideout (Act 1)",
	baseName = "Searing Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutBeaconOfSalvation"] = {
	name = "Beacon of Salvation Hideout (Act 1)",
	baseName = "Beacon of Salvation Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutCanopy"] = {
	name = "Canopy Hideout (Act 1)",
	baseName = "Canopy Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutDreadnought"] = {
	name = "The Dreadnought Hideout (Act 1)",
	baseName = "The Dreadnought Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutFelled"] = {
	name = "Felled Hideout (Act 1)",
	baseName = "Felled Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutShrine"] = {
	name = "Shrine Hideout (Act 1)",
	baseName = "Shrine Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutLimestone"] = {
	name = "Limestone Hideout (Act 1)",
	baseName = "Limestone Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutCanal"] = {
	name = "Canal Hideout (Act 1)",
	baseName = "Canal Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutMonolith"] = {
	name = "Monolith Hideout (Act 1)",
	baseName = "Monolith Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutTwisted"] = {
	name = "Twisted Hideout (Act 1)",
	baseName = "Twisted Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutTranscendent"] = {
	name = "Transcendent Hideout (Act 1)",
	baseName = "Transcendent Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutHourglass"] = {
	name = "Hourglass Hideout (Act 1)",
	baseName = "Hourglass Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutChaos"] = {
	name = "Chaos Hideout (Act 1)",
	baseName = "Chaos Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutSummit"] = {
	name = "Plateau of the Gods Hideout (Act 1)",
	baseName = "Plateau of the Gods Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["HideoutRacetrack"] = {
	name = "Vastiri Racecourse Hideout (Act 1)",
	baseName = "Vastiri Racecourse Hideout",
	tags = {  },
	act = 1,
	level = 65,
	isMap = false,
	isHideout = true,
	monsterVarieties = {
	},
}

worldAreas["G_login"] = {
	name = "Login Scene (Act 1)",
	baseName = "Login Scene",
	tags = {  },
	act = 1,
	level = 0,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["G1_WorldMap"] = {
	name = "Act 1 (Act 1)",
	baseName = "Act 1",
	tags = {  },
	act = 1,
	level = 0,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["G2_WorldMap"] = {
	name = "Act 2 (Act 2)",
	baseName = "Act 2",
	tags = {  },
	act = 2,
	level = 0,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["G3_WorldMap"] = {
	name = "Act 3 (Act 3)",
	baseName = "Act 3",
	tags = {  },
	act = 3,
	level = 0,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["TN_WorldMap"] = {
	name = "Atlas",
	baseName = "Atlas",
	tags = {  },
	act = 10,
	level = 0,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["G1_town"] = {
	name = "Clearfell Encampment (Act 1)",
	baseName = "Clearfell Encampment",
	description = "A bastion of hope for those who survive",
	tags = {  },
	act = 1,
	level = 15,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["G1_1"] = {
	name = "The Riverbank (Act 1)",
	baseName = "The Riverbank",
	description = "The drowned stare through muddied branches",
	tags = { "EzomyteStrongbox" },
	act = 1,
	level = 1,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Drowned",
		"Porcupine Crab",
	},
	bossVarieties = {
		"The Bloated Miller",
	},
}

worldAreas["G1_2"] = {
	name = "Clearfell (Act 1)",
	baseName = "Clearfell",
	description = "A sickness has befallen Ogham",
	tags = { "EzomyteStrongbox" },
	act = 1,
	level = 2,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Lumbering Dead",
		"Rotten Wolf",
		"Vile Hag",
		"Vile Imp",
	},
	bossVarieties = {
		"Beira of the Rotten Pack",
	},
}

worldAreas["G1_3"] = {
	name = "Mud Burrow (Act 1)",
	baseName = "Mud Burrow",
	description = "The tunnels of a tormented creature",
	tags = { "EzomyteStrongbox" },
	act = 1,
	level = 3,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Flesh Larva",
		"Mud Simulacrum",
		"Wretched Rattler",
	},
	bossVarieties = {
		"The Devourer",
	},
}

worldAreas["G1_4"] = {
	name = "The Grelwood (Act 1)",
	baseName = "The Grelwood",
	description = "Forest of the Old Magicks",
	tags = { "EzomyteStrongbox" },
	act = 1,
	level = 4,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Fungal Proliferator",
		"Fungal Rattler",
		"Fungal Zombie",
		"Pack Werewolf",
		"Vile Hag",
		"Vile Imp",
		"Werewolf Prowler",
	},
	bossVarieties = {
		"The Brambleghast",
	},
}

worldAreas["G1_5"] = {
	name = "The Red Vale (Act 1)",
	baseName = "The Red Vale",
	description = "Haunted battleground of the Phaaryl Wars",
	tags = { "EzomyteStrongbox" },
	act = 1,
	level = 5,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Ancient Ezomyte",
		"Bloom Serpent",
		"Maw Demon",
		"Risen Arbalest",
	},
	bossVarieties = {
		"The Rust King",
	},
}

worldAreas["G1_6"] = {
	name = "The Grim Tangle (Act 1)",
	baseName = "The Grim Tangle",
	description = "The sickness spreads forth underground",
	tags = { "EzomyteStrongbox" },
	act = 1,
	level = 6,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Fungal Artillery",
		"Fungal Proliferator",
		"Fungal Rattler",
		"Fungal Wolf",
		"Fungal Zombie",
	},
	bossVarieties = {
		"The Rotten Druid",
	},
}

worldAreas["G1_7"] = {
	name = "Cemetery of the Eternals (Act 1)",
	baseName = "Cemetery of the Eternals",
	description = "Built atop the Ezomyte clan graves beneath",
	tags = { "EzomyteStrongbox" },
	act = 1,
	level = 7,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Bearer of Penitence",
		"Burdened Wretch",
		"Death Knight",
		"Frost Wraith",
		"Hungering Stalker",
		"Risen Rattler",
		"Undertaker",
	},
	bossVarieties = {
		"Lachlann of Endless Lament",
	},
}

worldAreas["G1_8"] = {
	name = "Mausoleum of the Praetor (Act 1)",
	baseName = "Mausoleum of the Praetor",
	description = "Resting place of Draven Sentari",
	tags = { "EzomyteStrongbox" },
	act = 1,
	level = 8,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Blood Cretin",
		"Courtesan",
		"Eternal Knight",
		"Ghoul Commander",
		"Lightning Wraith",
		"Risen Rattler",
		"Skulking Ghoul",
		"Wheelbound Hag",
	},
	bossVarieties = {
		"Draven, the Eternal Praetor",
	},
}

worldAreas["G1_9"] = {
	name = "Tomb of the Consort (Act 1)",
	baseName = "Tomb of the Consort",
	description = "Resting place of Asinia Sentari",
	tags = { "EzomyteStrongbox" },
	act = 1,
	level = 8,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Bone Stalker",
		"Dread Servant",
		"Eternal Knight",
		"Knight-Gaunt",
		"Risen Rattler",
	},
	bossVarieties = {
		"Asinia, the Praetor's Consort",
	},
}

worldAreas["G1_10"] = {
	name = "Root Hollow (Act 1)",
	baseName = "Root Hollow",
	tags = { "EzomyteStrongbox" },
	act = 1,
	level = 15,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
	bossVarieties = {
		"The Rotten Druid",
	},
}

worldAreas["G1_11"] = {
	name = "Hunting Grounds (Act 1)",
	baseName = "Hunting Grounds",
	description = "Wild bounty of Ogham",
	tags = { "EzomyteStrongbox" },
	act = 1,
	level = 10,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Bramble Ape",
		"Bramble Burrower",
		"Bramble Hulk",
		"Bramble Rhoa",
		"Venomous Crab",
		"Venomous Crab Matriarch",
	},
	bossVarieties = {
		"The Crowbell",
	},
}

worldAreas["G1_12"] = {
	name = "Freythorn (Act 1)",
	baseName = "Freythorn",
	description = "The Clanless Enclave",
	tags = { "EzomyteStrongbox" },
	act = 1,
	level = 11,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Cultist Archer",
		"Cultist Brute",
		"Cultist Daggerdancer",
		"Cultist Warrior",
		"Cultist Witch",
		"Ribrattle",
		"Skeleton Spriggan",
		"Skullslinger",
		"Spinesnatcher",
	},
}

worldAreas["G1_13_1"] = {
	name = "Ogham Farmlands (Act 1)",
	baseName = "Ogham Farmlands",
	description = "Diseased crops of Ogham",
	tags = { "EzomyteStrongbox" },
	act = 1,
	level = 12,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Decrepit Mercenary",
		"Iron Guard",
		"Iron Thaumaturgist",
		"Pack Werewolf",
		"Rabid Dog",
		"Risen Farmhand",
		"Rotting Crow",
		"Scarecrow Beast",
		"Voracious Werewolf",
		"Werewolf Prowler",
	},
}

worldAreas["G1_13_2"] = {
	name = "Ogham Village (Act 1)",
	baseName = "Ogham Village",
	description = "The burning tragedy",
	tags = { "EzomyteStrongbox" },
	act = 1,
	level = 13,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Blood Collector",
		"Blood Cretin",
		"Burning Dead",
		"Decrepit Mercenary",
		"Voracious Werewolf",
		"Werewolf Prowler",
	},
	bossVarieties = {
		"The Executioner",
	},
}

worldAreas["G1_14"] = {
	name = "The Manor Ramparts (Act 1)",
	baseName = "The Manor Ramparts",
	description = "Surrounding walls of the Wolf's Den",
	tags = { "EzomyteStrongbox" },
	act = 1,
	level = 14,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Blood Carrier",
		"Blood Collector",
		"Blood Cretin",
		"Courtesan",
		"Death Knight",
		"Decrepit Mercenary",
		"Gargoyle Demon",
		"Iron Guard",
		"Iron Spearman",
		"Iron Thaumaturgist",
	},
}

worldAreas["G1_15"] = {
	name = "Ogham Manor (Act 1)",
	baseName = "Ogham Manor",
	description = "Den of the Mad Wolf",
	tags = { "EzomyteStrongbox" },
	act = 1,
	level = 15,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Blood Carrier",
		"Blood Collector",
		"Blood Cretin",
		"Courtesan",
		"Iron Enforcer",
		"Iron Guard",
		"Iron Sharpshooter",
		"Iron Spearman",
		"Iron Thaumaturgist",
		"Tendril Prowler",
		"Tendril Sentinel",
	},
	bossVarieties = {
		"Candlemass, the Living Rite",
		"Count Geonor",
	},
}

worldAreas["G2_town"] = {
	name = "The Ardura Caravan (Act 2)",
	baseName = "The Ardura Caravan",
	description = "Pride of Sekhema Asala",
	tags = {  },
	act = 2,
	level = 32,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["G2_1"] = {
	name = "Vastiri Outskirts (Act 2)",
	baseName = "Vastiri Outskirts",
	description = "Blood-red and unforgiving sands",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 16,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Brimstone Crab",
		"Crag Leaper",
		"Hyena Demon",
		"Rotting Hulk",
		"Sandscoured Dead",
		"Sun Clan Scavenger",
	},
	bossVarieties = {
		"Rathbreaker",
	},
}

worldAreas["G2_2"] = {
	name = "Traitor's Passage (Act 2)",
	baseName = "Traitor's Passage",
	description = "The pride and fall of Balbala",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 19,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Desiccated Lich",
		"Quake Golem",
		"Risen Arbalest",
		"Risen Maraketh",
		"Skitter Golem",
		"Tombshrieker",
		"Vault Lurker",
	},
	bossVarieties = {
		"Balbala, the Traitor",
	},
}

worldAreas["G2_3"] = {
	name = "The Halani Gates (Act 2)",
	baseName = "The Halani Gates",
	description = "Gateway of the Second River",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 20,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Boulder Ant",
		"Faridun Bladedancer",
		"Faridun Fledgling",
		"Faridun Heavy Infantry",
		"Faridun Infantry",
		"Faridun Javelineer",
		"Faridun Neophyte",
		"Faridun Spearman",
		"Faridun Spearwoman",
		"Faridun Swordsman",
		"Faridun Wind-slicer",
	},
	bossVarieties = {
		"Jamanra, the Risen King",
	},
}

worldAreas["G2_3a"] = {
	name = "The Halani Gates (Act 2)",
	baseName = "The Halani Gates",
	description = "Gateway of the Second River",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 20,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["G2_3s"] = {
	name = "The Halani Gates (Act 2)",
	baseName = "The Halani Gates",
	description = "Gateway of the Second River",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 20,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["G2_4_1"] = {
	name = "Keth (Act 2)",
	baseName = "Keth",
	description = "Jewel of the Vastiri",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 21,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Desiccated Lich",
		"Living Sand",
		"Risen Maraketh",
		"Serpent Clan",
		"Serpent Shaman",
		"Tarnished Beetle",
		"Tarnished Scarab",
	},
	bossVarieties = {
		"Kabala, Constrictor Queen",
	},
}

worldAreas["G2_4_2"] = {
	name = "The Lost City (Act 2)",
	baseName = "The Lost City",
	description = "The glory of Keth knew no bounds",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Adorned Beetle",
		"Adorned Scarab",
		"Risen Arbalest",
		"Risen Maraketh",
		"Sand Spirit",
		"Serpent Clan",
		"Serpent Shaman",
		"Tarnished Beetle",
	},
}

worldAreas["G2_4_3"] = {
	name = "Buried Shrines (Act 2)",
	baseName = "Buried Shrines",
	description = "Sands settle where water once flowed",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 23,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Mar Acolyte",
		"Risen Arbalest",
		"Risen Maraketh",
		"Sand Spirit",
		"Vesper Bat",
	},
	bossVarieties = {
		"Azarian, the Forsaken Son",
	},
}

worldAreas["G2_5_1"] = {
	name = "Mastodon Badlands (Act 2)",
	baseName = "Mastodon Badlands",
	description = "Territory of the Lost-Men",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 21,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Gilded Cobra",
		"Lost-men Brute",
		"Lost-men Necromancer",
		"Lost-men Zealot",
		"Ribrattle",
		"Sabre Spider",
		"Skullslinger",
		"Spinesnatcher",
	},
}

worldAreas["G2_5_2"] = {
	name = "The Bone Pits (Act 2)",
	baseName = "The Bone Pits",
	description = "Necromantic ash tarnish the sands",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Drudge Osseodon",
		"Gilded Cobra",
		"Hyena Demon",
		"Lost-men Brute",
		"Lost-men Necromancer",
		"Lost-men Subjugator",
		"Lost-men Zealot",
		"Ribrattle",
		"Skullslinger",
		"Spinesnatcher",
		"Sun Clan Scavenger",
	},
	bossVarieties = {
		"Ekbab, Ancient Steed",
	},
}

worldAreas["G2_6"] = {
	name = "Valley of the Titans (Act 2)",
	baseName = "Valley of the Titans",
	description = "They remain where they slept",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 21,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Desiccated Lich",
		"Dune Lurker",
		"Mantis Rat",
		"Quake Golem",
		"Risen Arbalest",
		"Risen Maraketh",
		"Skitter Golem",
		"Walking Goliath",
	},
}

worldAreas["G2_7"] = {
	name = "The Titan Grotto (Act 2)",
	baseName = "The Titan Grotto",
	description = "Their echoes rattled the world",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Goliath",
		"Sandflesh Mage",
		"Sandflesh Skeleton",
		"Sandflesh Warrior",
		"Winged Horror",
	},
	bossVarieties = {
		"Zalmarath, the Colossus",
	},
}

worldAreas["G2_8"] = {
	name = "Deshar (Act 2)",
	baseName = "Deshar",
	description = "The City of the Dead",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 28,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Maraketh Undead",
		"Porcupine Goliath",
		"Rasp Scavenger",
		"Regurgitating Vulture",
		"Sabre Spider",
		"Vile Vulture",
	},
}

worldAreas["G2_8a"] = {
	name = "Deshar (Act 2)",
	baseName = "Deshar",
	description = "The City of the Dead",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 28,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["G2_9_1"] = {
	name = "Path of Mourning (Act 2)",
	baseName = "Path of Mourning",
	description = "They climb to mourn their Honoured Dead",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 29,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Maraketh Undead",
		"Risen Maraketh",
		"Risen Tale-woman",
	},
}

worldAreas["G2_9_2"] = {
	name = "The Spires of Deshar (Act 2)",
	baseName = "The Spires of Deshar",
	description = "Where the Honoured Dead lie buried in the sky",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 30,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Faridun Bladedancer",
		"Faridun Fledgling",
		"Faridun Heavy Infantry",
		"Faridun Impaler",
		"Faridun Infantry",
		"Faridun Javelineer",
		"Faridun Neophyte",
		"Faridun Spearman",
		"Faridun Spearwoman",
		"Faridun Swordsman",
		"Faridun Wind-slicer",
		"Maraketh Undead",
		"Winged Fiend",
	},
	bossVarieties = {
		"Tor Gul, the Defiler",
	},
}

worldAreas["G2_10_1"] = {
	name = "Mawdun Quarry (Act 2)",
	baseName = "Mawdun Quarry",
	description = "The hills of Mawdun became the Faridun foothold",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 17,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Armoured Rhex",
		"Corrupted Corpse",
		"Faridun Crawler",
		"Forsaken Hulk",
		"Forsaken Miner",
		"Plague Harvester",
		"Plague Swarm",
	},
}

worldAreas["G2_10_2"] = {
	name = "Mawdun Mine (Act 2)",
	baseName = "Mawdun Mine",
	description = "Where metal veins bled for the tools of war",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 18,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Corrupted Corpse",
		"Faridun Crawler",
		"Forgotten Crawler",
		"Forgotten Satyr",
		"Forgotten Stalker",
		"Forsaken Miner",
		"Mantis Rat",
		"Plague Nymph",
	},
	bossVarieties = {
		"Rudja, the Dread Engineer",
	},
}

worldAreas["G2_11"] = {
	name = "The Dreadnought's Wake (Act 2)",
	baseName = "The Dreadnought's Wake",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 30,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Corrupted Corpse",
		"Plague Harvester",
		"Plague Nymph",
		"Plague Swarm",
		"Porcupine Goliath",
		"Rasp Scavenger",
		"Rhex",
	},
}

worldAreas["G2_12_1"] = {
	name = "The Dreadnought (Act 2)",
	baseName = "The Dreadnought",
	description = "War Caravan of the Faridun",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 31,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Faridun Bladedancer",
		"Faridun Crawler",
		"Faridun Fledgling",
		"Faridun Heavy Infantry",
		"Faridun Infantry",
		"Faridun Javelineer",
		"Faridun Neophyte",
		"Faridun Plaguebringer",
		"Faridun Spearman",
		"Faridun Spearwoman",
		"Faridun Swordsman",
		"Faridun Wind-slicer",
		"Plague Harvester",
		"Plague Swarm",
	},
}

worldAreas["G2_12_2"] = {
	name = "Dreadnought Vanguard (Act 2)",
	baseName = "Dreadnought Vanguard",
	description = "Forward carts of the Risen King",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 32,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Faridun Bladedancer",
		"Faridun Butcher",
		"Faridun Fledgling",
		"Faridun Heavy Infantry",
		"Faridun Infantry",
		"Faridun Javelineer",
		"Faridun Neophyte",
		"Faridun Spearman",
		"Faridun Spearwoman",
		"Faridun Swordsman",
		"Faridun Wind-slicer",
	},
	bossVarieties = {
		"Jamanra, the Abomination",
	},
}

worldAreas["G2_13"] = {
	name = "Trial of the Sekhemas (Act 2)",
	baseName = "Trial of the Sekhemas",
	description = "The Winter Sekhema designed a Great Trial to challenge the worthy",
	tags = { "MarakethStrongbox" },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["Sanctum_1"] = {
	name = "Trial of the Sekhemas (Floor 1)",
	baseName = "Trial of the Sekhemas",
	tags = {  },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Boulder Ant",
		"Brimstone Crab",
		"Quake Golem",
		"Rasp Scavenger",
		"Serpent Clan",
		"Serpent Shaman",
		"Skitter Golem",
		"Tombshrieker",
		"Vault Lurker",
	},
	bossVarieties = {
		"Rattlecage, the Earthbreaker",
	},
}

worldAreas["Sanctum_1_Foyer_1"] = {
	name = "Trial of the Sekhemas (Floor 1)",
	baseName = "Trial of the Sekhemas",
	tags = {  },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["Sanctum_1_Foyer_2"] = {
	name = "Trial of the Sekhemas (Floor 1)",
	baseName = "Trial of the Sekhemas",
	tags = {  },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["Sanctum_1_Foyer_3"] = {
	name = "Trial of the Sekhemas (Floor 1)",
	baseName = "Trial of the Sekhemas",
	tags = {  },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["Sanctum_2"] = {
	name = "Trial of the Sekhemas (Floor 2)",
	baseName = "Trial of the Sekhemas",
	tags = {  },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Adorned Beetle",
		"Adorned Scarab",
		"Desiccated Lich",
		"Mar Acolyte",
		"Risen Arbalest",
		"Risen Maraketh",
		"Risen Tale-woman",
		"Sand Spirit",
		"Urnwalker",
	},
	bossVarieties = {
		"Rafiq of the Frozen Spring",
		"Hadi of the Flaming River",
	},
}

worldAreas["Sanctum_2_Foyer_1"] = {
	name = "Trial of the Sekhemas (Floor 2)",
	baseName = "Trial of the Sekhemas",
	tags = {  },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["Sanctum_2_Foyer_2"] = {
	name = "Trial of the Sekhemas (Floor 2)",
	baseName = "Trial of the Sekhemas",
	tags = {  },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["Sanctum_2_Foyer_3"] = {
	name = "Trial of the Sekhemas (Floor 2)",
	baseName = "Trial of the Sekhemas",
	tags = {  },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["Sanctum_3"] = {
	name = "Trial of the Sekhemas (Floor 3)",
	baseName = "Trial of the Sekhemas",
	tags = {  },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Adorned Beetle",
		"Adorned Scarab",
		"Brimstone Crab",
		"Desiccated Lich",
		"Dune Lurker",
		"Porcupine Goliath",
		"Rasp Scavenger",
		"Sand Spirit",
		"Serpent Clan",
		"Serpent Shaman",
		"Urnwalker",
		"Vesper Bat",
		"Walking Goliath",
	},
	bossVarieties = {
		"Ashar, the Sand Mother",
	},
}

worldAreas["Sanctum_3_Foyer_1"] = {
	name = "Trial of the Sekhemas (Floor 3)",
	baseName = "Trial of the Sekhemas",
	tags = {  },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["Sanctum_3_Foyer_2"] = {
	name = "Trial of the Sekhemas (Floor 3)",
	baseName = "Trial of the Sekhemas",
	tags = {  },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["Sanctum_3_Foyer_3"] = {
	name = "Trial of the Sekhemas (Floor 3)",
	baseName = "Trial of the Sekhemas",
	tags = {  },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["Sanctum_4"] = {
	name = "Trial of the Sekhemas (Floor 4)",
	baseName = "Trial of the Sekhemas",
	tags = {  },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Desiccated Lich",
		"Frost Wraith",
		"Goliath",
		"Risen Tale-woman",
		"Sand Spirit",
		"Sandflesh Mage",
		"Urnwalker",
		"Walking Goliath",
		"Winged Horror",
	},
	bossVarieties = {
		"Zarokh, the Temporal",
	},
}

worldAreas["Sanctum_4_Foyer_1"] = {
	name = "Trial of the Sekhemas (Floor 4)",
	baseName = "Trial of the Sekhemas",
	tags = {  },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["Sanctum_4_Foyer_2"] = {
	name = "Trial of the Sekhemas (Floor 4)",
	baseName = "Trial of the Sekhemas",
	tags = {  },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["Sanctum_4_Foyer_3"] = {
	name = "Trial of the Sekhemas (Floor 4)",
	baseName = "Trial of the Sekhemas",
	tags = {  },
	act = 2,
	level = 22,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["G3_town"] = {
	name = "Ziggurat Encampment (Act 3)",
	baseName = "Ziggurat Encampment",
	description = "A haven provides refuge from the jungle",
	tags = { "area_with_water" },
	act = 3,
	level = 44,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["G3_1"] = {
	name = "Sandswept Marsh (Act 3)",
	baseName = "Sandswept Marsh",
	description = "Where thick waters stir with the rising dead",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 3,
	level = 33,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Bloodthief Queen",
		"Bloodthief Wasp",
		"Bogfelled Commoner",
		"Bogfelled Slave",
		"Dredge Fiend",
		"Orok Fleshstabber",
		"Orok Hunter",
		"Orok Shaman",
		"Orok Throatcutter",
		"Rotting Hulk",
	},
	bossVarieties = {
		"Rootdredge",
	},
}

worldAreas["G3_2_1"] = {
	name = "Infested Barrens (Act 3)",
	baseName = "Infested Barrens",
	description = "Where the jungle gives way to colonies and hives",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 3,
	level = 35,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Antlion Charger",
		"Bane Sapling",
		"Diretusk Boar",
		"Ill-fated Explorer",
	},
}

worldAreas["G3_2_2"] = {
	name = "The Matlan Waterways (Act 3)",
	baseName = "The Matlan Waterways",
	description = "Seed of Utzaal's birth and death",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 3,
	level = 39,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Azak Brute",
		"Azak Fleshstabber",
		"Azak Mongrelmaster",
		"Azak Shaman",
		"Azak Spearthrower",
		"Azak Stalker",
		"Azak Throatcutter",
		"Chaw Mongrel",
		"Chyme Skitterer",
		"River Drake",
	},
}

worldAreas["G3_3"] = {
	name = "Jungle Ruins (Act 3)",
	baseName = "Jungle Ruins",
	description = "The jungle thrives amongst overgrown structures",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 3,
	level = 34,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Alpha Primate",
		"Antlion Charger",
		"Bane Sapling",
		"Constricted Shambler",
		"Constricted Spitter",
		"Entwined Hulk",
		"Feral Primate",
		"Quadrilla",
		"Snakethroat Shambler",
	},
	bossVarieties = {
		"Mighty Silverfist",
	},
}

worldAreas["G3_4"] = {
	name = "The Venom Crypts (Act 3)",
	baseName = "The Venom Crypts",
	description = "Graves that once held nobility now slither and bite",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 3,
	level = 35,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Constricted Shambler",
		"Constricted Spitter",
		"Entrailhome Shambler",
		"Entwined Hulk",
		"Rotted Rat",
		"Scorpion Monkey",
		"Slitherspitter",
		"Snakethroat Shambler",
	},
}

worldAreas["G3_5"] = {
	name = "Chimeral Wetlands (Act 3)",
	baseName = "Chimeral Wetlands",
	description = "Where beauty and death become one",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 3,
	level = 36,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Bloom Serpent",
		"Diretusk Boar",
		"Ill-fated Explorer",
		"Prowling Chimeral",
		"River Drake",
	},
	bossVarieties = {
		"Xyclucian, the Chimera",
	},
}

worldAreas["G3_6_1"] = {
	name = "Jiquani's Machinarium (Act 3)",
	baseName = "Jiquani's Machinarium",
	description = "Where the Architect of Industry bestowed life unto stone",
	tags = { "dungeon", "VaalStrongbox" },
	act = 3,
	level = 37,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Crawler Sentinel",
		"Pale-stitched Stalker",
		"Rotted Rat",
		"Rusted Dyna Golem",
		"Rusted Reconstructor",
		"Vaal Skeletal Archer",
		"Vaal Skeletal Priest",
		"Vaal Skeletal Squire",
		"Vaal Skeletal Warrior",
	},
	bossVarieties = {
		"Blackjaw, the Remnant",
	},
}

worldAreas["G3_6_2"] = {
	name = "Jiquani's Sanctum (Act 3)",
	baseName = "Jiquani's Sanctum",
	description = "Seat of Jiquani's rise to power",
	tags = { "dungeon", "VaalStrongbox" },
	act = 3,
	level = 38,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Pale-stitched Stalker",
		"Prowling Shade",
		"Undead Vaal Bladedancer",
		"Undead Vaal Guard",
		"Vaal Skeletal Archer",
		"Vaal Skeletal Priest",
		"Vaal Skeletal Squire",
		"Vaal Skeletal Warrior",
	},
	bossVarieties = {
		"Zicoatl, Warden of the Core",
	},
}

worldAreas["G3_7"] = {
	name = "The Azak Bog (Act 3)",
	baseName = "The Azak Bog",
	description = "Home of the merciless Azak savages",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 3,
	level = 36,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Azak Brute",
		"Azak Fledgling",
		"Azak Fleshstabber",
		"Azak Mauler",
		"Azak Shaman",
		"Azak Spearthrower",
		"Azak Stalker",
		"Azak Throatcutter",
		"Azak Torchbearer",
		"Chaw Mongrel",
	},
	bossVarieties = {
		"Ignagduk, the Bog Witch",
	},
}

worldAreas["G3_8"] = {
	name = "The Drowned City (Act 3)",
	baseName = "The Drowned City",
	description = "Utzaal the drowned is now revealed",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 3,
	level = 40,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Chyme Skitterer",
		"Drowned Crawler",
		"Drowned Explorer",
		"Filthy Crone",
		"Filthy First-born",
		"Filthy Lobber",
		"Flathead Clubber",
		"Flathead Warrior",
		"Foul Blacksmith",
		"Foul Mauler",
		"Foul Sage",
		"Hunchback Clubber",
		"River Drake",
		"River Hag",
	},
}

worldAreas["G3_9"] = {
	name = "The Molten Vault (Act 3)",
	baseName = "The Molten Vault",
	description = "Forge of the forgotten wealth of Kamasa",
	tags = { "dungeon", "VaalStrongbox" },
	act = 3,
	level = 41,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Gold-Melted Sentinel",
		"Gold-Melted Shambler",
		"Gold-melted Blacksmith",
		"Vaal Embalmed Archer",
		"Vaal Embalmed Axeman",
		"Vaal Embalmed Bearer",
		"Vaal Embalmed Rogue",
		"Vaal Embalmed Spearman",
	},
	bossVarieties = {
		"Mektul, the Forgemaster",
	},
}

worldAreas["G3_10"] = {
	name = "The Trial of Chaos (Act 3)",
	baseName = "The Trial of Chaos",
	description = "Where the Trialmaster tests challengers in the name of Chaos",
	tags = { "dungeon", "VaalStrongbox" },
	act = 3,
	level = 38,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
	bossVarieties = {
		"Uxmal, the Beastlord",
		"Chetza, the Feathered Plague",
		"Bahlak, the Sky Seer",
		"The Trialmaster",
	},
}

worldAreas["G3_10_Airlock"] = {
	name = "The Temple of Chaos (Act 3)",
	baseName = "The Temple of Chaos",
	description = "Testing grounds for the Vaal High Priests",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 3,
	level = 38,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["G3_11"] = {
	name = "Apex of Filth (Act 3)",
	baseName = "Apex of Filth",
	description = "The rot and mire of unspeakable centuries",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 3,
	level = 41,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Filthy Crone",
		"Filthy First-born",
		"Filthy Lobber",
		"Flathead Clubber",
		"Flathead Warrior",
		"Flathead Youngling",
		"Foul Blacksmith",
		"Foul Mauler",
		"Foul Sage",
		"Hunchback Clubber",
		"Pyromushroom Cultivator",
	},
	bossVarieties = {
		"The Queen of Filth",
	},
}

worldAreas["G3_12"] = {
	name = "Temple of Kopec (Act 3)",
	baseName = "Temple of Kopec",
	description = "Unfathomable energy resides within the Ziggurat",
	tags = { "dungeon", "VaalStrongbox" },
	act = 3,
	level = 42,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Adorned Miscreation",
		"Bloodrite Guard",
		"Bloodrite Priest",
		"Priest of the Sun",
	},
	bossVarieties = {
		"Ketzuli, High Priest of the Sun",
	},
}

worldAreas["G_Endgame_Town"] = {
	name = "The Ziggurat Refuge",
	baseName = "The Ziggurat Refuge",
	description = "Reinforced hideaway from the Cataclysm",
	tags = {  },
	act = 10,
	level = 65,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Adorned Beetle",
		"Adorned Miscreation",
		"Adorned Scarab",
		"Alpha Primate",
		"Ancient Ezomyte",
		"Antlion Charger",
		"Armoured Rhex",
		"Azak Brute",
		"Azak Fledgling",
		"Azak Fleshstabber",
		"Azak Mauler",
		"Azak Shaman",
		"Azak Spearthrower",
		"Azak Stalker",
		"Azak Throatcutter",
		"Bearer of Penitence",
		"Bladelash Transcendent",
		"Blood Carrier",
		"Blood Collector",
		"Blood Cretin",
		"Blood Priest",
		"Blood Priestess",
		"Blood Zealot",
		"Bloodrite Guard",
		"Bloodrite Priest",
		"Bloodthief Queen",
		"Bloodthief Wasp",
		"Bloom Serpent",
		"Bogfelled Commoner",
		"Bogfelled Slave",
		"Bone Stalker",
		"Boulder Ant",
		"Bramble Ape",
		"Bramble Burrower",
		"Bramble Hulk",
		"Bramble Rhoa",
		"Brimstone Crab",
		"Brutal Transcendent",
		"Burdened Wretch",
		"Burning Dead",
		"Chaotic Zealot",
		"Chaw Mongrel",
		"Chyme Skitterer",
		"Constricted Shambler",
		"Constricted Spitter",
		"Corrupted Corpse",
		"Courtesan",
		"Crag Leaper",
		"Cultist Archer",
		"Cultist Brute",
		"Cultist Daggerdancer",
		"Cultist Warrior",
		"Cultist Witch",
		"Cultivated Grove",
		"Death Knight",
		"Decrepit Mercenary",
		"Desiccated Lich",
		"Diretusk Boar",
		"Doryani's Elite",
		"Dread Servant",
		"Dredge Fiend",
		"Drowned",
		"Drowned Crawler",
		"Drowned Explorer",
		"Drudge Osseodon",
		"Dune Lurker",
		"Entrailhome Shambler",
		"Entwined Hulk",
		"Eternal Knight",
		"Faridun Bladedancer",
		"Faridun Butcher",
		"Faridun Crawler",
		"Faridun Fledgling",
		"Faridun Heavy Infantry",
		"Faridun Infantry",
		"Faridun Javelineer",
		"Faridun Neophyte",
		"Faridun Plaguebringer",
		"Faridun Spearman",
		"Faridun Swordsman",
		"Faridun Wind-slicer",
		"Feral Primate",
		"Fiery Zealot",
		"Filthy Crone",
		"Filthy First-born",
		"Filthy Lobber",
		"Flathead Clubber",
		"Flathead Warrior",
		"Flathead Youngling",
		"Flesh Larva",
		"Forgotten Crawler",
		"Forgotten Satyr",
		"Forgotten Stalker",
		"Forsaken Hulk",
		"Forsaken Miner",
		"Foul Blacksmith",
		"Foul Mauler",
		"Foul Sage",
		"Frost Wraith",
		"Fungal Artillery",
		"Fungal Proliferator",
		"Fungal Rattler",
		"Fungal Wolf",
		"Fungal Zombie",
		"Fused Swordsman",
		"Gargoyle Demon",
		"Gelid Zealot",
		"Gilded Cobra",
		"Gold-Melted Sentinel",
		"Gold-Melted Shambler",
		"Gold-melted Blacksmith",
		"Goliath",
		"Goliath Transcendent",
		"Hunchback Clubber",
		"Hyena Demon",
		"Ill-fated Explorer",
		"Iron Enforcer",
		"Iron Guard",
		"Iron Sharpshooter",
		"Iron Spearman",
		"Iron Thaumaturgist",
		"Knight-Gaunt",
		"Lightning Wraith",
		"Living Sand",
		"Lost-men Brute",
		"Lost-men Necromancer",
		"Lost-men Subjugator",
		"Lost-men Zealot",
		"Mantis Rat",
		"Mar Acolyte",
		"Maraketh Undead",
		"Mud Simulacrum",
		"Orok Fleshstabber",
		"Orok Hunter",
		"Orok Shaman",
		"Orok Throatcutter",
		"Pack Werewolf",
		"Pale-stitched Stalker",
		"Plague Harvester",
		"Plague Nymph",
		"Plague Swarm",
		"Porcupine Crab",
		"Porcupine Goliath",
		"Powered Zealot",
		"Priest of the Sun",
		"Prowling Chimeral",
		"Prowling Shade",
		"Pyromushroom Cultivator",
		"Quadrilla",
		"Quake Golem",
		"Rabid Dog",
		"Rasp Scavenger",
		"Rattling Gibbet",
		"Regurgitating Vulture",
		"Rhex",
		"Ribrattle",
		"Risen Arbalest",
		"Risen Farmhand",
		"Risen Maraketh",
		"Risen Rattler",
		"Risen Tale-woman",
		"River Drake",
		"River Hag",
		"Rotted Rat",
		"Rotten Wolf",
		"Rotting Crow",
		"Rotting Hulk",
		"Rusted Dyna Golem",
		"Sabre Spider",
		"Sand Spirit",
		"Sandflesh Mage",
		"Sandflesh Skeleton",
		"Sandflesh Warrior",
		"Sandscoured Dead",
		"Scarecrow Beast",
		"Scorpion Monkey",
		"Serpent Clan",
		"Serpent Shaman",
		"Shielded Transcendent",
		"Skeleton Spriggan",
		"Skitter Golem",
		"Skullslinger",
		"Slitherspitter",
		"Snakethroat Shambler",
		"Spinesnatcher",
		"Sun Clan Scavenger",
		"Surgical Experimentalist",
		"Swamp Golem",
		"Tarnished Beetle",
		"Tarnished Scarab",
		"Tendril Prowler",
		"Tendril Sentinel",
		"Tombshrieker",
		"Undead Vaal Bladedancer",
		"Undead Vaal Guard",
		"Undertaker",
		"Vaal Axeman",
		"Vaal Embalmed Archer",
		"Vaal Embalmed Axeman",
		"Vaal Embalmed Bearer",
		"Vaal Embalmed Rogue",
		"Vaal Embalmed Spearman",
		"Vaal Excoriator",
		"Vaal Formshifter",
		"Vaal Goliath",
		"Vaal Guard",
		"Vaal Overseer",
		"Vaal Skeletal Archer",
		"Vaal Skeletal Priest",
		"Vaal Skeletal Squire",
		"Vaal Skeletal Warrior",
		"Vault Lurker",
		"Venomous Crab",
		"Venomous Crab Matriarch",
		"Vile Hag",
		"Vile Imp",
		"Vile Vulture",
		"Viper Legionnaire",
		"Walking Goliath",
		"Warrior Transcendent",
		"Werewolf Prowler",
		"Winged Fiend",
		"Winged Horror",
		"Wretched Rattler",
	},
}

worldAreas["G3_14"] = {
	name = "Utzaal (Act 3)",
	baseName = "Utzaal",
	description = "The Cradle of Vaal Ambition",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 3,
	level = 43,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Chaotic Zealot",
		"Gelid Zealot",
		"Loyal Jaguar",
		"Vaal Excoriator",
		"Vaal Goliath",
		"Vaal Guard",
		"Vaal Overseer",
		"Viper Legionnaire",
	},
	bossVarieties = {
		"Viper Napuatzi",
	},
}

worldAreas["G3_15"] = {
	name = "Library of Kamasa (Act 3)",
	baseName = "Library of Kamasa",
	tags = { "dungeon", "VaalStrongbox" },
	act = 3,
	level = 43,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Vaal Enforcer",
		"Vaal Excoriator",
		"Vaal Guard",
		"Vaal Overseer",
		"Vaal Researcher",
	},
}

worldAreas["G3_16"] = {
	name = "Aggorat (Act 3)",
	baseName = "Aggorat",
	description = "Salvation sought in beauty and blood",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 3,
	level = 44,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Bannerbearing Zealot",
		"Blood Priest",
		"Blood Priestess",
		"Blood Zealot",
		"Chaotic Zealot",
		"Fiery Zealot",
		"Gelid Zealot",
		"Vaal Axeman",
		"Vaal Formshifter",
		"Vaal Goliath",
	},
}

worldAreas["G3_17"] = {
	name = "The Black Chambers (Act 3)",
	baseName = "The Black Chambers",
	description = "Doryani toiled in her name and his own",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 3,
	level = 45,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Bladelash Transcendent",
		"Brutal Transcendent",
		"Doryani's Elite",
		"Fused Swordsman",
		"Goliath Transcendent",
		"Shielded Transcendent",
		"Surgical Experimentalist",
		"Warrior Transcendent",
	},
	bossVarieties = {
		"Doryani, Royal Thaumaturge",
		"Doryani's Triumph",
	},
}

worldAreas["C_G1_town"] = {
	name = "Clearfell Encampment (Act 4)",
	baseName = "Clearfell Encampment",
	description = "A bastion of hope for those who survive",
	tags = {  },
	act = 7,
	level = 51,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["C_G1_1"] = {
	name = "The Riverbank (Act 4)",
	baseName = "The Riverbank",
	description = "The drowned stare through muddied branches",
	tags = { "EzomyteStrongbox" },
	act = 7,
	level = 45,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Drowned",
		"Drowned Crawler",
		"Porcupine Crab",
		"River Hag",
	},
	bossVarieties = {
		"The Bloated Miller",
	},
}

worldAreas["C_G1_2"] = {
	name = "Clearfell (Act 4)",
	baseName = "Clearfell",
	description = "A sickness has befallen Ogham",
	tags = { "EzomyteStrongbox" },
	act = 7,
	level = 45,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Drowned",
		"Rotten Wolf",
		"Vile Hag",
		"Vile Imp",
	},
	bossVarieties = {
		"Beira of the Rotten Pack",
	},
}

worldAreas["C_G1_3"] = {
	name = "Mud Burrow (Act 4)",
	baseName = "Mud Burrow",
	description = "The tunnels of a tormented creature",
	tags = { "EzomyteStrongbox" },
	act = 7,
	level = 46,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Flesh Larva",
		"Mud Simulacrum",
		"Plague Nymph",
		"Rotted Rat",
		"Wretched Rattler",
	},
	bossVarieties = {
		"The Devourer",
	},
}

worldAreas["C_G1_4"] = {
	name = "The Grelwood (Act 4)",
	baseName = "The Grelwood",
	description = "Forest of the Old Magicks",
	tags = { "EzomyteStrongbox" },
	act = 7,
	level = 46,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Cultivated Grove",
		"Fungal Artillery",
		"Fungal Proliferator",
		"Fungal Rattler",
		"Fungal Zombie",
		"Pack Werewolf",
		"Skeleton Spriggan",
		"Vile Hag",
		"Vile Imp",
		"Werewolf Prowler",
	},
	bossVarieties = {
		"The Brambleghast",
	},
}

worldAreas["C_G1_5"] = {
	name = "The Red Vale (Act 4)",
	baseName = "The Red Vale",
	description = "Haunted battleground of the Phaaryl Wars",
	tags = { "EzomyteStrongbox" },
	act = 7,
	level = 47,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Ancient Ezomyte",
		"Bloom Serpent",
		"Knight-Gaunt",
		"Maw Demon",
		"Risen Arbalest",
	},
	bossVarieties = {
		"The Rust King",
	},
}

worldAreas["C_G1_6"] = {
	name = "The Grim Tangle (Act 4)",
	baseName = "The Grim Tangle",
	description = "The sickness spreads forth underground",
	tags = { "EzomyteStrongbox" },
	act = 7,
	level = 47,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Fungal Artillery",
		"Fungal Proliferator",
		"Fungal Rattler",
		"Fungal Wolf",
		"Fungal Zombie",
	},
	bossVarieties = {
		"The Rotten Druid",
	},
}

worldAreas["C_G1_7"] = {
	name = "Cemetery of the Eternals (Act 4)",
	baseName = "Cemetery of the Eternals",
	description = "Built atop the Ezomyte clan graves beneath",
	tags = { "EzomyteStrongbox" },
	act = 7,
	level = 47,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Bearer of Penitence",
		"Burdened Wretch",
		"Death Knight",
		"Frost Wraith",
		"Hungering Stalker",
		"Risen Rattler",
		"Undertaker",
	},
	bossVarieties = {
		"Lachlann of Endless Lament",
	},
}

worldAreas["C_G1_8"] = {
	name = "Mausoleum of the Praetor (Act 4)",
	baseName = "Mausoleum of the Praetor",
	description = "Resting place of Draven Sentari",
	tags = { "EzomyteStrongbox" },
	act = 7,
	level = 48,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Blood Cretin",
		"Courtesan",
		"Eternal Knight",
		"Ghoul Commander",
		"Lightning Wraith",
		"Risen Rattler",
		"Skulking Ghoul",
		"Wheelbound Hag",
	},
	bossVarieties = {
		"Draven, the Eternal Praetor",
	},
}

worldAreas["C_G1_9"] = {
	name = "Tomb of the Consort (Act 4)",
	baseName = "Tomb of the Consort",
	description = "Resting place of Asinia Sentari",
	tags = { "EzomyteStrongbox" },
	act = 7,
	level = 48,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Bone Stalker",
		"Dread Servant",
		"Eternal Knight",
		"Knight-Gaunt",
		"Risen Rattler",
	},
	bossVarieties = {
		"Asinia, the Praetor's Consort",
	},
}

worldAreas["C_G1_10"] = {
	name = "Root Hollow (Act 4)",
	baseName = "Root Hollow",
	tags = { "EzomyteStrongbox" },
	act = 7,
	level = 51,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
	bossVarieties = {
		"The Rotten Druid",
	},
}

worldAreas["C_G1_11"] = {
	name = "Hunting Grounds (Act 4)",
	baseName = "Hunting Grounds",
	description = "Wild bounty of Ogham",
	tags = { "EzomyteStrongbox" },
	act = 7,
	level = 49,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Bramble Ape",
		"Bramble Burrower",
		"Bramble Hulk",
		"Bramble Rhoa",
		"Venomous Crab",
		"Venomous Crab Matriarch",
	},
	bossVarieties = {
		"The Crowbell",
	},
}

worldAreas["C_G1_12"] = {
	name = "Freythorn (Act 4)",
	baseName = "Freythorn",
	description = "The Clanless Enclave",
	tags = { "EzomyteStrongbox" },
	act = 7,
	level = 49,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Cultist Archer",
		"Cultist Brute",
		"Cultist Daggerdancer",
		"Cultist Warrior",
		"Cultist Witch",
		"Ribrattle",
		"Skeleton Spriggan",
		"Skullslinger",
		"Spinesnatcher",
	},
}

worldAreas["C_G1_13_1"] = {
	name = "Ogham Farmlands (Act 4)",
	baseName = "Ogham Farmlands",
	description = "Diseased crops of Ogham",
	tags = { "EzomyteStrongbox" },
	act = 7,
	level = 49,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Decrepit Mercenary",
		"Iron Guard",
		"Iron Thaumaturgist",
		"Pack Werewolf",
		"Rabid Dog",
		"Risen Farmhand",
		"Rotting Crow",
		"Scarecrow Beast",
		"Voracious Werewolf",
		"Werewolf Prowler",
	},
}

worldAreas["C_G1_13_2"] = {
	name = "Ogham Village (Act 4)",
	baseName = "Ogham Village",
	description = "The burning tragedy",
	tags = { "EzomyteStrongbox" },
	act = 7,
	level = 50,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Blood Collector",
		"Blood Cretin",
		"Burning Dead",
		"Courtesan",
		"Decrepit Mercenary",
		"Voracious Werewolf",
		"Werewolf Prowler",
	},
	bossVarieties = {
		"The Executioner",
	},
}

worldAreas["C_G1_14"] = {
	name = "The Manor Ramparts (Act 4)",
	baseName = "The Manor Ramparts",
	description = "Surrounding walls of the Wolf's Den",
	tags = { "EzomyteStrongbox" },
	act = 7,
	level = 50,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Blood Carrier",
		"Blood Collector",
		"Blood Cretin",
		"Courtesan",
		"Death Knight",
		"Decrepit Mercenary",
		"Gargoyle Demon",
		"Iron Guard",
		"Iron Spearman",
		"Iron Thaumaturgist",
	},
}

worldAreas["C_G1_15"] = {
	name = "Ogham Manor (Act 4)",
	baseName = "Ogham Manor",
	description = "Den of the Mad Wolf",
	tags = { "EzomyteStrongbox" },
	act = 7,
	level = 51,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Blood Carrier",
		"Blood Collector",
		"Blood Cretin",
		"Courtesan",
		"Iron Enforcer",
		"Iron Guard",
		"Iron Sharpshooter",
		"Iron Spearman",
		"Iron Thaumaturgist",
		"Tendril Prowler",
		"Tendril Sentinel",
	},
	bossVarieties = {
		"Candlemass, the Living Rite",
		"Count Geonor",
	},
}

worldAreas["C_G2_town"] = {
	name = "The Ardura Caravan (Act 5)",
	baseName = "The Ardura Caravan",
	description = "Pride of Sekhema Asala",
	tags = {  },
	act = 8,
	level = 57,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["C_G2_1"] = {
	name = "Vastiri Outskirts (Act 5)",
	baseName = "Vastiri Outskirts",
	description = "Blood-red and unforgiving sands",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 51,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Brimstone Crab",
		"Crag Leaper",
		"Hyena Demon",
		"Rotting Hulk",
		"Sandscoured Dead",
		"Sun Clan Scavenger",
	},
	bossVarieties = {
		"Rathbreaker",
	},
}

worldAreas["C_G2_2"] = {
	name = "Traitor's Passage (Act 5)",
	baseName = "Traitor's Passage",
	description = "The pride and fall of Balbala",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 52,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Desiccated Lich",
		"Quake Golem",
		"Risen Arbalest",
		"Risen Maraketh",
		"Skitter Golem",
		"Tombshrieker",
		"Vault Lurker",
	},
	bossVarieties = {
		"Balbala, the Traitor",
	},
}

worldAreas["C_G2_3"] = {
	name = "The Halani Gates (Act 5)",
	baseName = "The Halani Gates",
	description = "Gateway of the Second River",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 53,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Boulder Ant",
		"Faridun Bladedancer",
		"Faridun Fledgling",
		"Faridun Heavy Infantry",
		"Faridun Infantry",
		"Faridun Javelineer",
		"Faridun Neophyte",
		"Faridun Spearman",
		"Faridun Spearwoman",
		"Faridun Swordsman",
		"Faridun Wind-slicer",
	},
	bossVarieties = {
		"Jamanra, the Risen King",
	},
}

worldAreas["C_G2_3a"] = {
	name = "The Halani Gates (Act 5)",
	baseName = "The Halani Gates",
	description = "Gateway of the Second River",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 53,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["C_G2_3s"] = {
	name = "The Halani Gates (Act 5)",
	baseName = "The Halani Gates",
	description = "Gateway of the Second River",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 53,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["C_G2_4_1"] = {
	name = "Keth (Act 5)",
	baseName = "Keth",
	description = "Jewel of the Vastiri",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 53,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Desiccated Lich",
		"Living Sand",
		"Risen Maraketh",
		"Serpent Clan",
		"Serpent Shaman",
		"Tarnished Beetle",
		"Tarnished Scarab",
	},
	bossVarieties = {
		"Kabala, Constrictor Queen",
	},
}

worldAreas["C_G2_4_2"] = {
	name = "The Lost City (Act 5)",
	baseName = "The Lost City",
	description = "The glory of Keth knew no bounds",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 54,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Adorned Beetle",
		"Adorned Scarab",
		"Risen Arbalest",
		"Risen Maraketh",
		"Serpent Clan",
		"Serpent Shaman",
		"Tarnished Beetle",
	},
}

worldAreas["C_G2_4_3"] = {
	name = "Buried Shrines (Act 5)",
	baseName = "Buried Shrines",
	description = "Sands settle where water once flowed",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 54,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Mar Acolyte",
		"Risen Arbalest",
		"Risen Maraketh",
		"Sand Spirit",
		"Vesper Bat",
	},
	bossVarieties = {
		"Azarian, the Forsaken Son",
	},
}

worldAreas["C_G2_5_1"] = {
	name = "Mastodon Badlands (Act 5)",
	baseName = "Mastodon Badlands",
	description = "Territory of the Lost-Men",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 53,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Gilded Cobra",
		"Lost-men Brute",
		"Lost-men Necromancer",
		"Lost-men Zealot",
		"Ribrattle",
		"Sabre Spider",
		"Skullslinger",
		"Spinesnatcher",
	},
}

worldAreas["C_G2_5_2"] = {
	name = "The Bone Pits (Act 5)",
	baseName = "The Bone Pits",
	description = "Necromantic ash tarnish the sands",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 54,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Drudge Osseodon",
		"Gilded Cobra",
		"Hyena Demon",
		"Lost-men Brute",
		"Lost-men Necromancer",
		"Lost-men Subjugator",
		"Lost-men Zealot",
		"Ribrattle",
		"Skullslinger",
		"Spinesnatcher",
		"Sun Clan Scavenger",
	},
	bossVarieties = {
		"Ekbab, Ancient Steed",
	},
}

worldAreas["C_G2_6"] = {
	name = "Valley of the Titans (Act 5)",
	baseName = "Valley of the Titans",
	description = "They remain where they slept",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 53,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Desiccated Lich",
		"Dune Lurker",
		"Mantis Rat",
		"Quake Golem",
		"Risen Arbalest",
		"Risen Maraketh",
		"Skitter Golem",
		"Walking Goliath",
	},
}

worldAreas["C_G2_7"] = {
	name = "The Titan Grotto (Act 5)",
	baseName = "The Titan Grotto",
	description = "Their echoes rattled the world",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 54,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Goliath",
		"Sandflesh Mage",
		"Sandflesh Skeleton",
		"Sandflesh Warrior",
		"Winged Horror",
	},
	bossVarieties = {
		"Zalmarath, the Colossus",
	},
}

worldAreas["C_G2_8"] = {
	name = "Deshar (Act 5)",
	baseName = "Deshar",
	description = "The City of the Dead",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 56,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Maraketh Undead",
		"Porcupine Goliath",
		"Rasp Scavenger",
		"Regurgitating Vulture",
		"Sabre Spider",
		"Vile Vulture",
	},
}

worldAreas["C_G2_9_1"] = {
	name = "Path of Mourning (Act 5)",
	baseName = "Path of Mourning",
	description = "They climb to mourn their Honoured Dead",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 56,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Maraketh Undead",
		"Risen Maraketh",
		"Risen Tale-woman",
	},
}

worldAreas["C_G2_9_2_"] = {
	name = "The Spires of Deshar (Act 5)",
	baseName = "The Spires of Deshar",
	description = "Where the Honoured Dead lie buried in the sky",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 57,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Faridun Bladedancer",
		"Faridun Fledgling",
		"Faridun Heavy Infantry",
		"Faridun Impaler",
		"Faridun Infantry",
		"Faridun Javelineer",
		"Faridun Neophyte",
		"Faridun Spearman",
		"Faridun Spearwoman",
		"Faridun Swordsman",
		"Faridun Wind-slicer",
		"Maraketh Undead",
		"Winged Fiend",
	},
	bossVarieties = {
		"Tor Gul, the Defiler",
	},
}

worldAreas["C_G2_10_1"] = {
	name = "Mawdun Quarry (Act 5)",
	baseName = "Mawdun Quarry",
	description = "The hills of Mawdun became the Faridun foothold",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 51,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Armoured Rhex",
		"Corrupted Corpse",
		"Faridun Crawler",
		"Forsaken Hulk",
		"Forsaken Miner",
		"Plague Harvester",
		"Plague Swarm",
	},
}

worldAreas["C_G2_10_2"] = {
	name = "Mawdun Mine (Act 5)",
	baseName = "Mawdun Mine",
	description = "Where metal veins bled for the tools of war",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 52,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Corrupted Corpse",
		"Faridun Crawler",
		"Forgotten Crawler",
		"Forgotten Satyr",
		"Forgotten Stalker",
		"Forsaken Miner",
		"Mantis Rat",
		"Plague Nymph",
	},
	bossVarieties = {
		"Rudja, the Dread Engineer",
	},
}

worldAreas["C_G2_11"] = {
	name = "The Dreadnought's Wake (Act 5)",
	baseName = "The Dreadnought's Wake",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 57,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Corrupted Corpse",
		"Plague Harvester",
		"Plague Nymph",
		"Plague Swarm",
		"Porcupine Goliath",
		"Rasp Scavenger",
		"Rhex",
	},
}

worldAreas["C_G2_12_1"] = {
	name = "The Dreadnought (Act 5)",
	baseName = "The Dreadnought",
	description = "War Caravan of the Faridun",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 57,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Faridun Bladedancer",
		"Faridun Crawler",
		"Faridun Fledgling",
		"Faridun Heavy Infantry",
		"Faridun Infantry",
		"Faridun Javelineer",
		"Faridun Neophyte",
		"Faridun Plaguebringer",
		"Faridun Spearman",
		"Faridun Spearwoman",
		"Faridun Swordsman",
		"Faridun Wind-slicer",
		"Plague Harvester",
		"Plague Swarm",
	},
}

worldAreas["C_G2_12_2"] = {
	name = "Dreadnought Vanguard (Act 5)",
	baseName = "Dreadnought Vanguard",
	description = "Forward carts of the Risen King",
	tags = { "MarakethStrongbox" },
	act = 8,
	level = 57,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Faridun Bladedancer",
		"Faridun Butcher",
		"Faridun Fledgling",
		"Faridun Heavy Infantry",
		"Faridun Infantry",
		"Faridun Javelineer",
		"Faridun Neophyte",
		"Faridun Spearman",
		"Faridun Spearwoman",
		"Faridun Swordsman",
		"Faridun Wind-slicer",
	},
	bossVarieties = {
		"Jamanra, the Abomination",
	},
}

worldAreas["C_G3_town"] = {
	name = "Ziggurat Encampment (Act 6)",
	baseName = "Ziggurat Encampment",
	description = "A haven provides refuge from the jungle ",
	tags = { "area_with_water" },
	act = 9,
	level = 64,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["C_G3_1"] = {
	name = "Sandswept Marsh (Act 6)",
	baseName = "Sandswept Marsh",
	description = "Where thick waters stir with the rising dead",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 9,
	level = 58,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Bloodthief Queen",
		"Bloodthief Wasp",
		"Bogfelled Commoner",
		"Bogfelled Slave",
		"Dredge Fiend",
		"Orok Fleshstabber",
		"Orok Hunter",
		"Orok Shaman",
		"Orok Throatcutter",
		"Rotting Hulk",
	},
	bossVarieties = {
		"Rootdredge",
	},
}

worldAreas["C_G3_2_1"] = {
	name = "Infested Barrens (Act 6)",
	baseName = "Infested Barrens",
	description = "Where the jungle gives way to colonies and hives",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 9,
	level = 59,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Antlion Charger",
		"Bane Sapling",
		"Diretusk Boar",
		"Ill-fated Explorer",
	},
}

worldAreas["C_G3_2_2"] = {
	name = "The Matlan Waterways (Act 6)",
	baseName = "The Matlan Waterways",
	description = "Seed of Utzaal's birth and death",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 9,
	level = 61,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Azak Brute",
		"Azak Fleshstabber",
		"Azak Mongrelmaster",
		"Azak Shaman",
		"Azak Spearthrower",
		"Azak Stalker",
		"Azak Throatcutter",
		"Chaw Mongrel",
		"Chyme Skitterer",
		"River Drake",
	},
}

worldAreas["C_G3_3"] = {
	name = "Jungle Ruins (Act 6)",
	baseName = "Jungle Ruins",
	description = "The jungle thrives amongst overgrown structures",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 9,
	level = 58,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Alpha Primate",
		"Antlion Charger",
		"Bane Sapling",
		"Constricted Shambler",
		"Constricted Spitter",
		"Entwined Hulk",
		"Feral Primate",
		"Quadrilla",
		"Snakethroat Shambler",
	},
	bossVarieties = {
		"Mighty Silverfist",
	},
}

worldAreas["C_G3_4"] = {
	name = "The Venom Crypts (Act 6)",
	baseName = "The Venom Crypts",
	description = "Graves that once held nobility now slither and bite",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 9,
	level = 59,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Constricted Shambler",
		"Constricted Spitter",
		"Entrailhome Shambler",
		"Entwined Hulk",
		"Rotted Rat",
		"Scorpion Monkey",
		"Slitherspitter",
		"Snakethroat Shambler",
	},
}

worldAreas["C_G3_5"] = {
	name = "Chimeral Wetlands (Act 6)",
	baseName = "Chimeral Wetlands",
	description = "Where beauty and death become one",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 9,
	level = 59,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Bloom Serpent",
		"Diretusk Boar",
		"Ill-fated Explorer",
		"Prowling Chimeral",
		"River Drake",
	},
	bossVarieties = {
		"Xyclucian, the Chimera",
	},
}

worldAreas["C_G3_6_1"] = {
	name = "Jiquani's Machinarium (Act 6)",
	baseName = "Jiquani's Machinarium",
	description = "Where the Architect of Industry bestowed life unto stone",
	tags = { "dungeon", "VaalStrongbox" },
	act = 9,
	level = 60,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Crawler Sentinel",
		"Pale-stitched Stalker",
		"Rotted Rat",
		"Rusted Dyna Golem",
		"Rusted Reconstructor",
		"Vaal Skeletal Archer",
		"Vaal Skeletal Priest",
		"Vaal Skeletal Squire",
		"Vaal Skeletal Warrior",
	},
	bossVarieties = {
		"Blackjaw, the Remnant",
	},
}

worldAreas["C_G3_6_2"] = {
	name = "Jiquani's Sanctum (Act 6)",
	baseName = "Jiquani's Sanctum",
	description = "Seat of Jiquani's rise to power",
	tags = { "dungeon", "VaalStrongbox" },
	act = 9,
	level = 60,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Pale-stitched Stalker",
		"Prowling Shade",
		"Undead Vaal Bladedancer",
		"Undead Vaal Guard",
		"Vaal Skeletal Archer",
		"Vaal Skeletal Priest",
		"Vaal Skeletal Squire",
		"Vaal Skeletal Warrior",
	},
	bossVarieties = {
		"Zicoatl, Warden of the Core",
	},
}

worldAreas["C_G3_7"] = {
	name = "The Azak Bog (Act 6)",
	baseName = "The Azak Bog",
	description = "Home of the merciless Azak savages",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 9,
	level = 60,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Azak Brute",
		"Azak Fledgling",
		"Azak Fleshstabber",
		"Azak Mauler",
		"Azak Shaman",
		"Azak Spearthrower",
		"Azak Stalker",
		"Azak Throatcutter",
		"Azak Torchbearer",
		"Chaw Mongrel",
	},
	bossVarieties = {
		"Ignagduk, the Bog Witch",
	},
}

worldAreas["C_G3_8"] = {
	name = "The Drowned City (Act 6)",
	baseName = "The Drowned City",
	description = "Utzaal the drowned is now revealed",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 9,
	level = 61,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Chyme Skitterer",
		"Drowned Crawler",
		"Drowned Explorer",
		"Filthy Crone",
		"Filthy First-born",
		"Filthy Lobber",
		"Flathead Clubber",
		"Flathead Warrior",
		"Foul Blacksmith",
		"Foul Mauler",
		"Foul Sage",
		"Hunchback Clubber",
		"River Drake",
		"River Hag",
	},
}

worldAreas["C_G3_9"] = {
	name = "The Molten Vault (Act 6)",
	baseName = "The Molten Vault",
	description = "Forge of the forgotten wealth of Kamasa",
	tags = { "dungeon", "VaalStrongbox" },
	act = 9,
	level = 62,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Gold-Melted Sentinel",
		"Gold-Melted Shambler",
		"Gold-melted Blacksmith",
		"Vaal Embalmed Archer",
		"Vaal Embalmed Axeman",
		"Vaal Embalmed Bearer",
		"Vaal Embalmed Rogue",
		"Vaal Embalmed Spearman",
	},
	bossVarieties = {
		"Mektul, the Forgemaster",
	},
}

worldAreas["C_G3_10_Airlock"] = {
	name = "The Temple of Chaos (Act 6)",
	baseName = "The Temple of Chaos",
	description = "Testing grounds for the Vaal High Priests",
	tags = {  },
	act = 9,
	level = 60,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["C_G3_11"] = {
	name = "Apex of Filth (Act 6)",
	baseName = "Apex of Filth",
	description = "The rot and mire of unspeakable centuries",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 9,
	level = 61,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Filthy Crone",
		"Filthy First-born",
		"Filthy Lobber",
		"Flathead Clubber",
		"Flathead Warrior",
		"Flathead Youngling",
		"Foul Blacksmith",
		"Foul Mauler",
		"Foul Sage",
		"Hunchback Clubber",
		"Pyromushroom Cultivator",
	},
	bossVarieties = {
		"The Queen of Filth",
	},
}

worldAreas["C_G3_12"] = {
	name = "Temple of Kopec (Act 6)",
	baseName = "Temple of Kopec",
	description = "Unfathomable energy resides within the Ziggurat",
	tags = { "dungeon", "VaalStrongbox" },
	act = 9,
	level = 62,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Adorned Miscreation",
		"Bloodrite Guard",
		"Bloodrite Priest",
		"Priest of the Sun",
	},
	bossVarieties = {
		"Ketzuli, High Priest of the Sun",
	},
}

worldAreas["C_G3_14"] = {
	name = "Utzaal (Act 6)",
	baseName = "Utzaal",
	description = "The Cradle of Vaal Ambition",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 9,
	level = 62,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Chaotic Zealot",
		"Gelid Zealot",
		"Loyal Jaguar",
		"Vaal Excoriator",
		"Vaal Goliath",
		"Vaal Guard",
		"Vaal Overseer",
		"Viper Legionnaire",
	},
	bossVarieties = {
		"Viper Napuatzi",
	},
}

worldAreas["C_G3_15"] = {
	name = "Library of Kamasa (Act 6)",
	baseName = "Library of Kamasa",
	tags = { "dungeon", "VaalStrongbox" },
	act = 9,
	level = 63,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Vaal Enforcer",
		"Vaal Excoriator",
		"Vaal Guard",
		"Vaal Overseer",
		"Vaal Researcher",
	},
}

worldAreas["C_G3_16_"] = {
	name = "Aggorat (Act 6)",
	baseName = "Aggorat",
	description = "Salvation sought in beauty and blood",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 9,
	level = 63,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Bannerbearing Zealot",
		"Blood Priest",
		"Blood Priestess",
		"Blood Zealot",
		"Chaotic Zealot",
		"Fiery Zealot",
		"Gelid Zealot",
		"Vaal Axeman",
		"Vaal Formshifter",
		"Vaal Goliath",
	},
}

worldAreas["C_G3_17"] = {
	name = "The Black Chambers (Act 6)",
	baseName = "The Black Chambers",
	description = "Doryani toiled in her name and his own",
	tags = { "area_with_water", "VaalStrongbox" },
	act = 9,
	level = 64,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Bladelash Transcendent",
		"Brutal Transcendent",
		"Doryani's Elite",
		"Fused Swordsman",
		"Goliath Transcendent",
		"Shielded Transcendent",
		"Surgical Experimentalist",
		"Warrior Transcendent",
	},
	bossVarieties = {
		"Doryani, Royal Thaumaturge",
		"Doryani's Triumph",
	},
}

worldAreas["MapLeaguePortal"] = {
	name = "The Realmgate",
	baseName = "The Realmgate",
	tags = {  },
	act = 10,
	level = 65,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapVoidReliquary"] = {
	name = "The Reliquary Vault (Map)",
	baseName = "The Reliquary Vault",
	tags = { "map" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapSpiderJungle"] = {
	name = "Spider Jungle (Map)",
	baseName = "Spider Jungle",
	tags = { "map", "forest", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
	bossVarieties = {
	},
}

worldAreas["MapRustbowl"] = {
	name = "Rustbowl (Map)",
	baseName = "Rustbowl",
	description = "Aeons of wear have rotted steel to its core.",
	tags = { "map", "desert_biome", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Ancient Ezomyte",
		"Risen Arbalest",
	},
	bossVarieties = {
		"Gozen, Rebellious Rustlord",
	},
}

worldAreas["MapBackwash"] = {
	name = "Backwash (Map)",
	baseName = "Backwash",
	description = "Humid air chokes and twists everything it touches.",
	tags = { "map", "forest_biome", "swamp_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Filthy Crone",
		"Filthy First-born",
		"Filthy Lobber",
		"Flathead Clubber",
		"Flathead Warrior",
		"Foul Blacksmith",
		"Foul Mauler",
		"Foul Sage",
		"Pyromushroom Cultivator",
	},
	bossVarieties = {
		"Yaota, the Loathsome",
	},
}

worldAreas["MapBurialBog"] = {
	name = "Burial Bog (Map)",
	baseName = "Burial Bog",
	description = "The land returns the dead as easily as it received them.",
	tags = { "map", "swamp_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Bogfelled Commoner",
		"Bogfelled Slave",
		"Dredge Fiend",
	},
	bossVarieties = {
		"Grudgelash, Vile Thorn",
	},
}

worldAreas["MapInferno"] = {
	name = "Inferno (Map)",
	baseName = "Inferno",
	tags = { "map", "forest_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Burning Dead",
	},
	bossVarieties = {
	},
}

worldAreas["MapWetlands"] = {
	name = "Wetlands (Map)",
	baseName = "Wetlands",
	description = "Mud and air seethes with warped life.",
	tags = { "map", "swamp_biome", "EzomyteStrongbox", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Bramble Burrower",
		"Venomous Crab Matriarch",
	},
	bossVarieties = {
		"Gorian, the Moving Earth",
	},
}

worldAreas["MapBloomingField"] = {
	name = "Blooming Field (Map)",
	baseName = "Blooming Field",
	description = "Bright colours hide the rot beneath.",
	tags = { "map", "forest_biome", "grass_biome", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Bloom Serpent",
	},
	bossVarieties = {
		"The Black Crow",
	},
}

worldAreas["MapCrimsonShores"] = {
	name = "Crimson Shores (Map)",
	baseName = "Crimson Shores",
	description = "Fishermen once reaped a rich bounty here.",
	tags = { "map", "water_biome", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Brimstone Crab",
	},
	bossVarieties = {
		"Rattlecage, the Earthbreaker",
	},
}

worldAreas["MapCenotes"] = {
	name = "Cenotes (Map)",
	baseName = "Cenotes",
	tags = { "map", "mountain_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Bogfelled Commoner",
		"Bogfelled Slave",
		"Rotting Hulk",
		"Swamp Golem",
	},
	bossVarieties = {
		"Bahlak, the Sky Seer",
	},
}

worldAreas["MapSavanna"] = {
	name = "Savannah (Map)",
	baseName = "Savannah",
	description = "Wild lands spurn those who claim to rule them.",
	tags = { "map", "grass_biome", "desert_biome", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Hyena Demon",
		"Sun Clan Scavenger",
	},
	bossVarieties = {
		"Caedron, the Hyena Lord",
	},
}

worldAreas["MapFortress"] = {
	name = "Fortress (Map)",
	baseName = "Fortress",
	description = "Time overwhelms even the sturdiest walls.",
	tags = { "map", "desert_biome", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Vaal Skeletal Priest",
		"Vaal Skeletal Squire",
	},
	bossVarieties = {
		"Pirasha, the Forgotten Prisoner",
	},
}

worldAreas["MapPenitentiary"] = {
	name = "Penitentiary (Map)",
	baseName = "Penitentiary",
	description = "Restless prisoners yearn for freedom.",
	tags = { "map", "grass_biome", "ezomyte_city", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Gilded Cobra",
		"Lost-men Zealot",
	},
	bossVarieties = {
		"Incarnation of Death",
	},
}

worldAreas["MapLostTowers"] = {
	name = "Lost Towers (Map)",
	baseName = "Lost Towers",
	description = "The grandest of monuments, standing proudly before an audience of none.",
	tags = { "map", "map_tower", "forest_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Blood Priest",
		"Blood Priestess",
		"Blood Zealot",
		"Vaal Formshifter",
	},
	bossVarieties = {
		"Chetza, the Feathered Plague",
	},
}

worldAreas["MapBloodwood"] = {
	name = "Bloodwood (Map)",
	baseName = "Bloodwood",
	description = " Poisoned trees bear pestilent fruits.",
	tags = { "map", "forest_biome", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Blood Collector",
		"Blood Cretin",
		"Courtesan",
	},
	bossVarieties = {
		"Gorian, the Moving Earth",
	},
}

worldAreas["MapSandspit"] = {
	name = "Sandspit (Map)",
	baseName = "Sandspit",
	tags = { "map", "water_biome", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Venomous Crab",
		"Venomous Crab Matriarch",
	},
	bossVarieties = {
	},
}

worldAreas["MapForge"] = {
	name = "Forge (Map)",
	baseName = "Forge",
	description = "No living hands ever stoked these flames.",
	tags = { "map", "mountain_biome", "desert_biome", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Goliath",
	},
	bossVarieties = {
		"Vastweld, the Colossal Guardian",
	},
}

worldAreas["MapSulphuricCaverns"] = {
	name = "Sulphuric Caverns (Map)",
	baseName = "Sulphuric Caverns",
	description = "Beasts of many kinds sought shelter one final time.",
	tags = { "map", "mountain_biome", "swamp_biome", "desert_biome", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Orok Fleshstabber",
		"Orok Hunter",
		"Orok Shaman",
		"Orok Throatcutter",
	},
	bossVarieties = {
		"The Bone Colossus",
		"Lord of the Pit",
	},
}

worldAreas["MapMire"] = {
	name = "Mire (Map)",
	baseName = "Mire",
	description = "These waters devour the same souls they feed.",
	tags = { "map", "forest_biome", "swamp_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Cultist Archer",
		"Cultist Daggerdancer",
	},
	bossVarieties = {
		"Riona, Winter's Cackle",
	},
}

worldAreas["MapAugury"] = {
	name = "Augury (Map)",
	baseName = "Augury",
	description = "They watched the birds to foretell what any fool could see.",
	tags = { "map", "grass_biome", "forest_biome", "swamp_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Constricted Spitter",
		"Slitherspitter",
	},
	bossVarieties = {
		"Gressor-Kul, the Apex",
	},
}

worldAreas["MapWoodland"] = {
	name = "Woodland (Map)",
	baseName = "Woodland",
	description = "The woods give their leaves to the seasons. Man takes the rest.",
	tags = { "map", "forest_biome", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Antlion Charger",
		"Ill-fated Explorer",
	},
	bossVarieties = {
		"Tierney, the Hateful",
	},
}

worldAreas["MapSump"] = {
	name = "Sump (Map)",
	baseName = "Sump",
	description = "Humanity trapped within a cage of desperation and agony.",
	tags = { "map", "swamp_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Diretusk Boar",
		"Ill-fated Explorer",
	},
	bossVarieties = {
		"Brakka, the Withered Crone",
	},
}

worldAreas["MapWillow"] = {
	name = "Willow (Map)",
	baseName = "Willow",
	description = "Leaves cling to trees as souls cling to life.",
	tags = { "map", "forest_biome", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Lightning Wraith",
		"Risen Rattler",
	},
	bossVarieties = {
		"Connal, the Tormented",
	},
}

worldAreas["MapHive"] = {
	name = "Hive (Map)",
	baseName = "Hive",
	tags = { "map", "desert_biome", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Bane Sapling",
	},
	bossVarieties = {
		"The Fungus Behemoth",
	},
}

worldAreas["MapHeadland"] = {
	name = "Headland (Map)",
	baseName = "Headland",
	description = "Sturdy walls held out an armada, but not the famine it brought.",
	tags = { "map", "mountain_biome", "faridun_city", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Pale-stitched Stalker",
	},
	bossVarieties = {
		"Hask, the Fallen Son",
	},
}

worldAreas["MapLoftySummit"] = {
	name = "Lofty Summit (Map)",
	baseName = "Lofty Summit",
	description = "The last vestiges of earth, lost beyond the sky.",
	tags = { "map", "mountain_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Frost Wraith",
		"Risen Rattler",
	},
	bossVarieties = {
		"Oloton, the Remorseless",
	},
}

worldAreas["MapNecropolis"] = {
	name = "Necropolis (Map)",
	baseName = "Necropolis",
	description = "Silent stones mark ancient graves and forgotten sorrows.",
	tags = { "map", "forest_biome", "ezomyte_city", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Death Knight",
		"Risen Rattler",
	},
	bossVarieties = {
		"Tycho, the Black Praetor",
	},
}

worldAreas["MapCrypt"] = {
	name = "Crypt (Map)",
	baseName = "Crypt",
	description = "Those killed in battle do not rest peacefully.",
	tags = { "map", "mountain_biome", "grass_biome", "desert_biome", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Knight-Gaunt",
		"Risen Rattler",
	},
	bossVarieties = {
		"Meltwax, Mockery of Faith",
	},
}

worldAreas["MapHiddenGrotto"] = {
	name = "Hidden Grotto (Map)",
	baseName = "Hidden Grotto",
	description = "Shafts of light raise life where they fall.",
	tags = { "map", "mountain_biome", "grass_biome", "forest_biome", "swamp_biome", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Quake Golem",
		"Skitter Golem",
	},
	bossVarieties = {
		"Zar Wali, the Bone Tyrant",
	},
}

worldAreas["MapSteamingSprings"] = {
	name = "Steaming Springs (Map)",
	baseName = "Steaming Springs",
	description = "The tears of a ravaged earth.",
	tags = { "map", "mountain_biome", "grass_biome", "forest_biome", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Bramble Rhoa",
	},
	bossVarieties = {
		"Manassa, the Serpent Queen",
	},
}

worldAreas["MapSeepage"] = {
	name = "Seepage (Map)",
	baseName = "Seepage",
	description = "The fetid home of foul generations.",
	tags = { "map", "grass_biome", "forest_biome", "swamp_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Fungal Artillery",
		"Fungal Proliferator",
		"Fungal Rattler",
	},
	bossVarieties = {
		"The Fungus Behemoth",
	},
}

worldAreas["MapRiverside"] = {
	name = "Riverside (Map)",
	baseName = "Riverside",
	description = "Rushing waters threaten to move the earth.",
	tags = { "map", "forest_biome", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Quadrilla",
		"Scorpion Monkey",
	},
	bossVarieties = {
		"Zekoa, the Headcrusher",
	},
}

worldAreas["MapRavine"] = {
	name = "Ravine (Map)",
	baseName = "Ravine",
	description = "A wound carved into the world, never to heal.",
	tags = { "map", "mountain_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Constricted Spitter",
		"Snakethroat Shambler",
	},
	bossVarieties = {
		"Tetzcatl, the Blazing Guardian",
	},
}

worldAreas["MapSpiderWoods"] = {
	name = "Spider Woods (Map)",
	baseName = "Spider Woods",
	description = "Vast lairs of silk span the treetops.",
	tags = { "map", "forest_biome", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Vault Lurker",
	},
	bossVarieties = {
		"Rootgrasp, the Hateful Forest",
	},
}

worldAreas["MapAbyss"] = {
	name = "Abyss (Map)",
	baseName = "Abyss",
	description = "Darkness enshrouds these endless chasms.",
	tags = { "map", "mountain_biome", "desert_biome", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Mar Acolyte",
		"Risen Arbalest",
		"Risen Maraketh",
	},
	bossVarieties = {
		"Zar Wali, the Bone Tyrant",
	},
}

worldAreas["MapGrimhaven"] = {
	name = "Grimhaven (Map)",
	baseName = "Grimhaven",
	description = "Avarice in the conqueror builds contempt among the conquered.",
	tags = { "map", "grass_biome", "ezomyte_city", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Decrepit Mercenary",
		"Iron Thaumaturgist",
	},
	bossVarieties = {
		"Saphira, The Dread Consort",
	},
}

worldAreas["MapVaalVillage"] = {
	name = "Vaal Village (Map)",
	baseName = "Vaal Village",
	description = "Vice wears a mask of simplicity.",
	tags = { "map", "swamp_biome", "vaal_city", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Vaal Goliath",
	},
	bossVarieties = {
	},
}

worldAreas["MapVaalOutskirts"] = {
	name = "Vaal Outskirts (Map)",
	baseName = "Vaal Outskirts",
	tags = { "map", "lightning", "maraketh", "bloodbather", "area_with_water", "machinarium", "giant", "earth_elemental", "construct", "bones", "reptile_beast", "beach", "vaal", "devourer", "rodent_beast", "insect", "demon", "rust", "cultist", "mutewind", "avian_beast", "spider", "stone_construct", "corrupted", "chaos", "gardens", "desert_area", "inca", "forest", "undead", "mammal_beast", "primate_beast", "cenobite", "amphibian_beast", "skeleton", "snake", "cavern", "feline_beast", "crustacean_beast", "canine_beast", "urban", "cold", "fire", "swamp", "ghost", "werewolf" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
	bossVarieties = {
	},
}

worldAreas["MapSlick"] = {
	name = "Slick (Map)",
	baseName = "Slick",
	tags = { "map", "mountain_biome", "desert_biome", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Undead Vaal Guard",
	},
	bossVarieties = {
		"Vorrik, The Infernal Engineer",
	},
}

worldAreas["MapVaalCity"] = {
	name = "Vaal City (Map)",
	baseName = "Vaal City",
	description = "Hubris convinces men they can survive the mistakes of their forebears.",
	tags = { "map", "swamp_biome", "vaal_city", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Viper Legionnaire",
	},
	bossVarieties = {
		"Viper Napuatzi",
	},
}

worldAreas["MapSteppe"] = {
	name = "Steppe (Map)",
	baseName = "Steppe",
	tags = { "map", "grass_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Diretusk Boar",
	},
	bossVarieties = {
		"Gozen, Rebellious Rustlord",
	},
}

worldAreas["MapSwampTower"] = {
	name = "Sinking Spire (Map)",
	baseName = "Sinking Spire",
	description = "This Vaal structure is not lost in the jungle. Not yet.",
	tags = { "map", "swamp_biome", "map_tower", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Filthy Crone",
		"Flathead Clubber",
		"Hunchback Clubber",
	},
	bossVarieties = {
		"Stormgore",
	},
}

worldAreas["MapRockpools"] = {
	name = "Rockpools (Map)",
	baseName = "Rockpools",
	tags = { "map", "forest_biome", "swamp_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Constricted Shambler",
		"Constricted Spitter",
		"Snakethroat Shambler",
	},
	bossVarieties = {
	},
}

worldAreas["MapCreek"] = {
	name = "Creek (Map)",
	baseName = "Creek",
	description = "Dark energies congeal the lifeblood of the forest.",
	tags = { "map", "forest_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"River Drake",
	},
	bossVarieties = {
		"Tierney, the Hateful",
	},
}

worldAreas["MapOutlands"] = {
	name = "Outlands (Map)",
	baseName = "Outlands",
	description = "Stone shelters brace against the doom of the desert.",
	tags = { "map", "desert_biome", "faridun_city", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Faridun Heavy Infantry",
		"Faridun Neophyte",
		"Faridun Spearman",
		"Faridun Swordsman",
	},
}

worldAreas["MapBastille"] = {
	name = "Bastille (Map)",
	baseName = "Bastille",
	description = "An orchestra of chains and screams produces a discordant cacophony.",
	tags = { "map", "mountain_biome", "grass_biome", "desert_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Decrepit Mercenary",
		"Iron Guard",
	},
	bossVarieties = {
		"Tierney, the Hateful",
	},
}

worldAreas["MapDecay"] = {
	name = "Decay (Map)",
	baseName = "Decay",
	description = "Spores dance through the air in search of new hosts.",
	tags = { "map", "grass_biome", "forest_biome", "swamp_biome", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Fungal Artillery",
		"Fungal Proliferator",
		"Fungal Zombie",
	},
	bossVarieties = {
		"The Fungus Behemoth",
	},
}

worldAreas["MapMineshaft"] = {
	name = "Mineshaft (Map)",
	baseName = "Mineshaft",
	description = "A dark labyrinth of steel and stone.",
	tags = { "map", "mountain_biome", "desert_biome", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Forsaken Miner",
	},
	bossVarieties = {
		"Vorrik, The Infernal Engineer",
	},
}

worldAreas["MapDeserted"] = {
	name = "Deserted (Map)",
	baseName = "Deserted",
	description = "A city ravaged by time and sands.",
	tags = { "map", "desert_biome", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Desiccated Lich",
		"Living Sand",
	},
	bossVarieties = {
		"Lord of the Pit",
	},
}

worldAreas["MapOasis"] = {
	name = "Oasis (Map)",
	baseName = "Oasis",
	description = "Hidden amongst sunbleached wastes lies a mockery of paradise.",
	tags = { "map", "desert_biome", "faridun_city", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Faridun Heavy Infantry",
		"Faridun Neophyte",
		"Faridun Spearman",
		"Faridun Swordsman",
	},
	bossVarieties = {
		"Ishtaroth, the Perennial",
	},
}

worldAreas["MapBastion"] = {
	name = "Bastion (Map)",
	baseName = "Bastion",
	tags = { "map", "faridun_city", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
	bossVarieties = {
	},
}

worldAreas["MapRefuge"] = {
	name = "Refuge (Map)",
	baseName = "Refuge",
	tags = { "map", "undead", "mammal_beast", "forest_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
	bossVarieties = {
	},
}

worldAreas["MapAlpineRidge"] = {
	name = "Alpine Ridge (Map)",
	baseName = "Alpine Ridge",
	description = "The path grows treacherous as the world falls away.",
	tags = { "map", "mountain_biome", "map_tower", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Winged Fiend",
	},
	bossVarieties = {
		"Ignatia, the Flame-Sworn",
		"Gelida, the Frost-Tongue",
	},
}

worldAreas["MapSunTemple"] = {
	name = "Sun Temple (Map)",
	baseName = "Sun Temple",
	description = "Wet stone emanates an inner warmth. Vaal brilliance lies in wait.",
	tags = { "map", "vaal_city", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Bloodrite Guard",
		"Bloodrite Priest",
		"Priest of the Sun",
	},
	bossVarieties = {
		"Tonqui, Seer of the Sun",
	},
}

worldAreas["MapChannel"] = {
	name = "Channel (Map)",
	baseName = "Channel",
	description = "The waters have returned, but no empire remains to greet them.",
	tags = { "map", "desert_biome", "faridun_city", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Mar Acolyte",
		"Sand Spirit",
	},
	bossVarieties = {
		"Hask, the Fallen Son",
	},
}

worldAreas["MapVaalFoundry"] = {
	name = "Vaal Foundry (Map)",
	baseName = "Vaal Foundry",
	description = "The cult of Kamasa exploited Utzaal long before its fall.",
	tags = { "map", "vaal_city", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Gold-Melted Sentinel",
		"Gold-Melted Shambler",
		"Gold-melted Blacksmith",
	},
	bossVarieties = {
		"Gulzal, the Living Furnace ",
	},
}

worldAreas["MapVaalFactory"] = {
	name = "Vaal Factory (Map)",
	baseName = "Vaal Factory",
	description = "Remnants of Vaal artifice still remain.",
	tags = { "map", "vaal_city", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
	bossVarieties = {
		"Tetzcatl, the Blazing Guardian",
	},
}

worldAreas["MapMesa"] = {
	name = "Mesa (Map)",
	baseName = "Mesa",
	description = "Bleak heights overlook a devastated land.",
	tags = { "map", "map_tower", "desert_biome", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Hyena Demon",
		"Sun Clan Scavenger",
	},
	bossVarieties = {
		"Karash, The Dune Dweller",
	},
}

worldAreas["MapBluff"] = {
	name = "Bluff (Map)",
	baseName = "Bluff",
	description = "Life still clings to the highest places.",
	tags = { "map", "map_tower", "grass_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Constricted Spitter",
		"Entrailhome Shambler",
		"Slitherspitter",
	},
	bossVarieties = {
		"Gressor-Kul, the Apex",
	},
}

worldAreas["MapPerch"] = {
	name = "Perch (Map)",
	baseName = "Perch",
	tags = { "map", "mountain_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
	bossVarieties = {
	},
}

worldAreas["MapUniqueUntaintedParadise"] = {
	name = "Untainted Paradise (Map)",
	baseName = "Untainted Paradise",
	description = "Life grows strong in this realm of plenty.",
	tags = { "map" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Bramble Ape",
		"Bramble Burrower",
		"Bramble Hulk",
		"Bramble Rhoa",
		"Caustic Crab",
		"Quill Crab",
	},
}

worldAreas["MapUniqueVault"] = {
	name = "Vaults of Kamasa (Map)",
	baseName = "Vaults of Kamasa",
	description = "By that era, Kamasa was just a name. Gold was their true god.",
	tags = { "map" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapUniqueCastaway"] = {
	name = "Castaway (Map)",
	baseName = "Castaway",
	description = "Hulls crash and splinter upon the shores.",
	tags = { "map" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Bloated Anchorman",
		"Drowned Bearer",
		"Drowned Crawler",
		"Drowned Explorer",
		"Gull Shrike",
		"Man o' War",
		"Rotting Cannoneer",
		"Rotting Demolitionist",
		"Rotting Grenadier",
		"Rotting Soulcatcher",
		"Searot Ensnarer",
		"Searot Harpooner",
		"Searot Skeleton",
		"Searot Sniper",
	},
	bossVarieties = {
		"Torrek of the Drowned Fleet",
	},
}

worldAreas["MapUniqueMegalith"] = {
	name = "The Phaaryl Megalith (Map)",
	baseName = "The Phaaryl Megalith",
	description = "The songs tell of a great thunderstorm that ravaged the valley. A beleaugered tribe appeared in its wake, seeking refuge, and bringing knowledge of runes.",
	tags = { "map" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Ancient Ezomyte",
		"Pack Werewolf",
		"Risen Arbalest",
		"Skeleton Spriggan",
		"Vile Hag",
		"Vile Imp",
		"Werewolf Prowler",
	},
}

worldAreas["MapUniqueLake"] = {
	name = "The Fractured Lake (Map)",
	baseName = "The Fractured Lake",
	description = "A mirror is a perfect prison for one's sense of self... until it cracks.",
	tags = { "map", "water_biome" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapUniqueSelenite"] = {
	name = "The Silent Cave (Map)",
	baseName = "The Silent Cave",
	description = "The prismatic patterns of Time shimmer and coalesce in vast geodes hidden from sight.",
	tags = { "map", "mountain_biome" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapUniqueMerchant01_Chimeral"] = {
	name = "Merchant's Campsite (Map)",
	baseName = "Merchant's Campsite",
	description = "A travelling merchant offers wares in perilous times.",
	tags = { "map" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapUniqueMerchant01_Oasis"] = {
	name = "Merchant's Campsite (Map)",
	baseName = "Merchant's Campsite",
	description = "A travelling merchant offers wares in perilous times.",
	tags = { "map" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapUniqueMerchant01_Sandswept"] = {
	name = "Merchant's Campsite (Map)",
	baseName = "Merchant's Campsite",
	description = "A travelling merchant offers wares in perilous times.",
	tags = { "map" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapUniqueMerchant02_Crimson"] = {
	name = "Merchant's Campsite (Map)",
	baseName = "Merchant's Campsite",
	description = "A travelling merchant offers wares in perilous times.",
	tags = { "map" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapUniqueMerchant02_Farmland"] = {
	name = "Merchant's Campsite (Map)",
	baseName = "Merchant's Campsite",
	description = "A travelling merchant offers wares in perilous times.",
	tags = { "map" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapUniqueMerchant02_Riverbank"] = {
	name = "Merchant's Campsite (Map)",
	baseName = "Merchant's Campsite",
	description = "A travelling merchant offers wares in perilous times.",
	tags = { "map" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapUniqueMerchant03_Beach"] = {
	name = "Moment of Zen (Map)",
	baseName = "Moment of Zen",
	description = "A travelling merchant offers wares in perilous times.",
	tags = { "map" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapUniqueMerchant03_Tropical"] = {
	name = "Moment of Zen (Map)",
	baseName = "Moment of Zen",
	description = "A travelling merchant offers wares in perilous times.",
	tags = { "map" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapUniqueMerchant03_Raft"] = {
	name = "Moment of Zen (Map)",
	baseName = "Moment of Zen",
	description = "A travelling merchant offers wares in perilous times.",
	tags = { "map" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapUniqueMerchant04_PirateShip"] = {
	name = "The Voyage (Map)",
	baseName = "The Voyage",
	tags = { "map" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapUniqueWildwood"] = {
	name = "The Viridian Wildwood (Map)",
	baseName = "The Viridian Wildwood",
	description = "As separate worlds draw ever closer, the Nameless gather at the edge of existence.",
	tags = { "map" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Cultist Archer",
		"Cultist Brute",
		"Cultist Daggerdancer",
		"Cultist Warrior",
		"Cultist Witch",
		"Forgotten Stag",
		"Nameless Burrower",
		"Nameless Dweller",
		"Nameless Horror",
		"Nameless Hulk",
		"Nameless Lurker",
		"Nameless Vermin",
		"Treant Foulspawn",
		"Treant Fungalreaver",
		"Treant Hookhorror",
		"Treant Mystic",
		"Treant Sage",
		"Treant Spriggan",
	},
}

worldAreas["MapUberBoss_IronCitadel"] = {
	name = "The Iron Citadel (Map)",
	baseName = "The Iron Citadel",
	description = "A heart of corruption, borne of steel.",
	tags = { "map", "ezomyte_city", "EzomyteStrongbox" },
	act = 10,
	level = 80,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Iron Sharpshooter",
		"Iron Spearman",
	},
	bossVarieties = {
		"Count Geonor",
		"Geonor, the Putrid Wolf",
	},
}

worldAreas["MapUberBoss_CopperCitadel"] = {
	name = "The Copper Citadel (Map)",
	baseName = "The Copper Citadel",
	description = "A heart of corruption, borne of copper.",
	tags = { "map", "faridun_city", "MarakethStrongbox" },
	act = 10,
	level = 80,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Faridun Butcher",
		"Faridun Infantry",
	},
	bossVarieties = {
		"Jamanra, the Abomination",
	},
}

worldAreas["MapUberBoss_StoneCitadel"] = {
	name = "The Stone Citadel (Map)",
	baseName = "The Stone Citadel",
	description = "A heart of corruption, borne of stone.",
	tags = { "map", "vaal_city", "VaalStrongbox" },
	act = 10,
	level = 80,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
		"Bladelash Transcendent",
		"Brutal Transcendent",
		"Surgical Experimentalist",
		"Warrior Transcendent",
	},
	bossVarieties = {
		"Doryani, Royal Thaumaturge",
		"Doryani's Triumph",
	},
}

worldAreas["MapUberBoss_Monolith"] = {
	name = "The Burning Monolith (Map)",
	baseName = "The Burning Monolith",
	description = "Flaming rite of the Fourth Edict",
	tags = { "map", "lightning", "maraketh", "bloodbather", "area_with_water", "machinarium", "giant", "earth_elemental", "construct", "bones", "reptile_beast", "beach", "vaal", "devourer", "rodent_beast", "insect", "demon", "rust", "cultist", "mutewind", "avian_beast", "spider", "stone_construct", "corrupted", "chaos", "gardens", "desert_area", "inca", "forest", "undead", "mammal_beast", "primate_beast", "cenobite", "amphibian_beast", "skeleton", "snake", "cavern", "feline_beast", "crustacean_beast", "canine_beast", "urban", "cold", "fire", "swamp", "ghost", "werewolf" },
	act = 10,
	level = 80,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
	bossVarieties = {
		"The Arbiter of Ash",
	},
}

worldAreas["ExpeditionLogBook_Peninsula"] = {
	name = "Craggy Peninsula",
	baseName = "Craggy Peninsula",
	tags = { "area_with_water", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["ExpeditionLogBook_Tropical"] = {
	name = "Lush Isle",
	baseName = "Lush Isle",
	tags = { "area_with_water", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["ExpeditionLogBook_Tundra"] = {
	name = "Frigid Bluffs",
	baseName = "Frigid Bluffs",
	tags = { "area_with_water", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"Blood Zealot",
		"Gelid Zealot",
	},
}

worldAreas["ExpeditionLogBook_Atoll"] = {
	name = "Barren Atoll",
	baseName = "Barren Atoll",
	tags = { "area_with_water", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["ExpeditionLogBook_Digsite"] = {
	name = "Abandonded Excavation",
	baseName = "Abandonded Excavation",
	tags = { "area_with_water", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["ExpeditionSubArea_Cavern"] = {
	name = "Smuggler's Den",
	baseName = "Smuggler's Den",
	tags = {  },
	act = 10,
	level = 65,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["ExpeditionSubArea_Kalguur"] = {
	name = "Kalguuran Tomb",
	baseName = "Kalguuran Tomb",
	tags = {  },
	act = 10,
	level = 65,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["ExpeditionSubArea_OlrothBoss"] = {
	name = "Kalguuran Tomb",
	baseName = "Kalguuran Tomb",
	tags = {  },
	act = 10,
	level = 65,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
	bossVarieties = {
		"Olroth, Origin of the Fall",
	},
}

worldAreas["ExpeditionSubArea_Shrike"] = {
	name = "Rancid Nest",
	baseName = "Rancid Nest",
	tags = {  },
	act = 10,
	level = 65,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["ExpeditionSubArea_Siren"] = {
	name = "Hidden Aquifer",
	baseName = "Hidden Aquifer",
	tags = {  },
	act = 10,
	level = 65,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["ExpeditionSubArea_Volcano"] = {
	name = "Sulphur Mines",
	baseName = "Sulphur Mines",
	tags = {  },
	act = 10,
	level = 65,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["Delirium_Act1Town"] = {
	name = "Clearfell Lumbermill",
	baseName = "Clearfell Lumbermill",
	tags = {  },
	act = 10,
	level = 80,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["BreachDomain_01"] = {
	name = "Twisted Domain",
	baseName = "Twisted Domain",
	tags = {  },
	act = 10,
	level = 80,
	isMap = false,
	isHideout = false,
	monsterVarieties = {
		"It That Controls",
		"It That Crawls",
		"It That Creeps",
		"It That Grasps",
		"It That Guards",
		"It That Hates",
		"It That Hunts",
		"It That Lashes",
		"It That Shreds",
		"It That Stalks",
		"It That Watches",
	},
	bossVarieties = {
		"Xesht, We That Are One",
	},
}

worldAreas["RitualLeagueBoss"] = {
	name = "Crux of Nothingness (Map)",
	baseName = "Crux of Nothingness",
	tags = { "map" },
	act = 10,
	level = 80,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
	bossVarieties = {
		"The King in the Mists",
	},
}

worldAreas["MapHideoutFelled_Claimable"] = {
	name = "Felled Hideout (Map)",
	baseName = "Felled Hideout",
	description = "A fortress of fallen wood.",
	tags = { "map", "forest_biome", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapHideoutLimestone_Claimable"] = {
	name = "Limestone Hideout (Map)",
	baseName = "Limestone Hideout",
	description = "A forgotten grotto, lost to the world.",
	tags = { "map", "water_biome", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapHideoutShrine_Claimable"] = {
	name = "Shrine Hideout (Map)",
	baseName = "Shrine Hideout",
	description = "A fragment of a glorious past.",
	tags = { "map", "desert_biome", "MarakethStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapHideoutCanal_Claimable"] = {
	name = "Canal Hideout (Map)",
	baseName = "Canal Hideout",
	description = "A moment in time, on the eve of the end.",
	tags = { "map", "grass_biome", "VaalStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapAzmerianRanges"] = {
	name = "Azmerian Ranges (Map)",
	baseName = "Azmerian Ranges",
	tags = { "map", "forest_biome", "mountain_biome", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
	bossVarieties = {
		"The King in the Mists",
		"The Brambleghast",
	},
}

worldAreas["MapTrenches"] = {
	name = "Trenches (Map)",
	baseName = "Trenches",
	tags = { "map", "forest_biome", "swamp_biome", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
	bossVarieties = {
		"Gorian, the Moving Earth",
	},
}

worldAreas["MapTrenches_Noboss"] = {
	name = "Trenches (Map)",
	baseName = "Trenches",
	description = "The Cataclysm tore the land asunder.",
	tags = { "map", "forest_biome", "swamp_biome", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
}

worldAreas["MapFrozenFalls"] = {
	name = "Frozen Falls (Map)",
	baseName = "Frozen Falls",
	description = "Beware a chill colder than death itself.",
	tags = { "map", "mountain_biome", "water_biome", "EzomyteStrongbox" },
	act = 10,
	level = 65,
	isMap = true,
	isHideout = false,
	monsterVarieties = {
	},
	bossVarieties = {
		"Riona, Winter's Cackle",
	},
}

return worldAreas
