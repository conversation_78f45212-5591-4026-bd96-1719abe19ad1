local out = io.open("../Data/Misc.lua", "w")
out:write("-- This file is automatically generated, do not edit!\n\n")
out:write('local data = ...\n')
local evasion = ""
local accuracy = ""
local life = ""
local allyLife = ""
local allyDamage = ""
local damage = ""
local armour = ""
local ailmentThreshold = ""
local minionLevel = ""
for stats in dat("DefaultMonsterStats"):Rows() do
	evasion = evasion .. stats.Evasion .. ", "
	accuracy = accuracy .. stats.Accuracy .. ", "
	life = life .. stats.MonsterLife .. ", "
	allyLife = allyLife .. stats.MinionLife .. ", "
	damage = damage .. stats.Damage .. ", "
	allyDamage = allyDamage .. stats.MinionDamage .. ", "
	armour = armour .. stats.Armour .. ", "
	ailmentThreshold = ailmentThreshold .. stats.AilmentThreshold .. ", "
end
-- Table was incorrect is PoE 1 but seems to be correct for PoE 2. Keeping here just in case
--for i = 1, 100 do
	--armour = armour .. math.floor((dat("GameConstants"):GetRow("Id", "MonsterArmourBase").Value + 2.5 * i ) * ( ( 1 + dat("GameConstants"):GetRow("Id", "MonsterArmourImprovement").Value / dat("GameConstants"):GetRow("Id", "MonsterArmourImprovement").Divisor / 100 ) ^ i)) .. ", "
--end
out:write('-- From DefaultMonsterStats.dat\n')
out:write('data.monsterEvasionTable = { '..evasion..'}\n') --This table is off by about 0.5% in some cases but is quicker than generating the value at runtime
out:write('data.monsterAccuracyTable = { '..accuracy..'}\n')
out:write('data.monsterLifeTable = { '..life..'}\n')
out:write('data.monsterAllyLifeTable = { '..allyLife..'}\n')
out:write('data.monsterDamageTable = { '..damage..'}\n')
out:write('data.monsterAllyDamageTable = { '..allyDamage..'}\n')
out:write('data.monsterArmourTable = { '..armour..'}\n')
out:write('data.monsterAilmentThresholdTable = { '..ailmentThreshold..'}\n')
out:write('\n')

out:write('-- From MinionGemLevelScaling.dat\n')
for level in dat("MinionGemLevelScaling"):Rows() do
		minionLevel = minionLevel .. level.MinionLevel .. ", "
end
out:write('data.minionLevelTable = { '..minionLevel..'}\n')
out:write('\n')

out:write('-- From GameConstants.dat\n')
out:write('data.gameConstants = {\n')
for row in dat("GameConstants"):Rows() do
	out:write('\t["' .. row.Id .. '"] = ' .. row.Value / row.Divisor .. ',\n')
end
out:write('}\n')

out:write('-- From Metadata/Characters/Character.ot\n')
out:write('data.characterConstants = {\n')
local file = getFile("Metadata/Characters/Character.ot")
if not file then return nil end
local text = convertUTF16to8(file)
local inWantedBlock = false
for line in text:gmatch("[^\r\n]+") do
	-- Detect start of a block
	if line:match("^Stats") or line:match("^Pathfinding") then
		inWantedBlock = true
	elseif inWantedBlock and line:match("^}") then
		inWantedBlock = false
	elseif inWantedBlock and line:find("=") then
		local key, value = line:gsub("%s+",""):match("^(.-)=(.+)$")
		if key and value then
			out:write('\t["' .. key .. '"] = ' .. value .. ',\n')
		end
	end
end
out:write('}\n')

out:write('-- From Metadata/Monsters/Monster.ot\n')
out:write('data.monsterConstants = {\n')
local file = getFile("Metadata/Monsters/Monster.ot")
if not file then return nil end
local text = convertUTF16to8(file)
local inWantedBlock = false
for line in text:gmatch("[^\r\n]+") do
	-- Detect start of a block
	if line:match("^Stats") then
		inWantedBlock = true
	elseif inWantedBlock and line:match("^}") then
		inWantedBlock = false
	elseif inWantedBlock and line:find("=") then
		local key, value = line:gsub("%s+",""):match("^(.-)=(.+)$")
		if key and value then
			out:write('\t["' .. key .. '"] = ' .. value .. ',\n')
		end
	end
end
out:write('}\n')

out:write('-- From PlayerMinionIntrinsicStats.dat\n')
out:write('data.playerMinionIntrinsicStats = {\n')
for row in dat("PlayerMinionIntrinsicStats"):Rows() do
	out:write('\t["' .. row.Id.Id .. '"] = ' .. row.Value .. ',\n')
end
out:write('}\n')

local totemMult = ""
local keys = { }
for var in dat("SkillTotemVariations"):Rows() do
	if not keys[var.SkillTotem] then
		keys[var.SkillTotem] = true
		totemMult = totemMult .. "[" .. var.SkillTotem .. "] = " .. var.MonsterVariety.LifeMultiplier / 100 .. ", "
	end
end
out:write('-- From MonsterVarieties.dat combined with SkillTotemVariations.dat\n')
out:write('data.totemLifeMult = { '..totemMult..'}\n')

out:write('data.monsterVarietyLifeMult = {\n')
local cachedEntry = { }
for row in dat("MonsterVarieties"):Rows() do
	for _, mod in ipairs(row.Mods) do
		if mod.Id == "MonsterNecromancerRaisable" and not cachedEntry['"' .. row.Name .. '"'] then
			out:write('\t["' .. row.Name .. '"] = ' .. row.LifeMultiplier/100 .. ',\n')
			cachedEntry['"' .. row.Name .. '"'] = true
			break
		end
	end
end
out:write('}\n')

out:write('-- From MonsterMapDifficulty.dat\n')
out:write('data.mapLevelLifeMult = { ')
for row in dat("MonsterMapDifficulty"):Rows() do
	out:write('[' .. row.AreaLevel .. '] = ' .. (1+row.LifePercentIncrease/100) .. ', ')
end
out:write('}\n')

local mapBossLifeMult = ""
local mapBossAilmentMult = ""
for var in dat("MonsterMapBossDifficulty"):Rows() do
	mapBossLifeMult = mapBossLifeMult .. "[" .. var.AreaLevel .. "] = " .. 1 + var.BossLifePercentIncrease / 100 .. ", "
	mapBossAilmentMult = mapBossAilmentMult .. "[" .. var.AreaLevel .. "] = " .. (100 + var.BossAilmentPercentDecrease) / 100 .. ", "
end

out:write('-- From MonsterMapBossDifficulty.dat\n')
out:write('data.mapLevelBossLifeMult = { '..mapBossLifeMult..'}\n')
out:write('data.mapLevelBossAilmentMult = { '..mapBossAilmentMult..'}\n')

out:close()

print("Misc data exported.")
