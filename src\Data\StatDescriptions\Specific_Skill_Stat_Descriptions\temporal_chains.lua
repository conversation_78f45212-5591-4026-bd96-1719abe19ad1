-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Curse radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Curse radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[3]={
		[1]={
			[1]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Curse Slows targets by {0}%"
			}
		},
		stats={
			[1]="base_skill_debuff_action_speed_+%_final_to_inflict"
		}
	},
	[4]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Curse duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Curse duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Curse makes other effects on targets expire {0}% faster"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Curse makes other effects on targets expire {0}% slower"
			}
		},
		stats={
			[1]="base_temporal_chains_other_buff_time_passed_+%_to_apply"
		}
	},
	[6]={
		[1]={
		},
		stats={
			[1]="skill_curse_action_speed_+%_final_magnitude_to_inflict"
		}
	},
	[7]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	[8]={
		[1]={
		},
		stats={
			[1]="temporal_chains_other_buff_time_passed_+%_magnitude_to_apply"
		}
	},
	["active_skill_area_of_effect_radius"]=1,
	["active_skill_base_area_of_effect_radius"]=2,
	["base_skill_debuff_action_speed_+%_final_to_inflict"]=3,
	["base_skill_effect_duration"]=4,
	["base_temporal_chains_other_buff_time_passed_+%_to_apply"]=5,
	parent="skill_stat_descriptions",
	["skill_curse_action_speed_+%_final_magnitude_to_inflict"]=6,
	["skill_effect_duration"]=7,
	["temporal_chains_other_buff_time_passed_+%_magnitude_to_apply"]=8
}