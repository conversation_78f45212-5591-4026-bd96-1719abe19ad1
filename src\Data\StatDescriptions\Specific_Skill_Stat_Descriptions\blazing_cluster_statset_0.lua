-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Hover duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Hover duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Can accumulate up to {0} Embers\nModifiers to number of Projectiles instead\napply to number of Embers accumulated"
			}
		},
		stats={
			[1]="blazing_cluster_maximum_number_of_projectiles_allowed"
		}
	},
	[3]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	[4]={
		[1]={
		},
		stats={
			[1]="total_number_of_projectiles_to_fire"
		}
	},
	["base_skill_effect_duration"]=1,
	["blazing_cluster_maximum_number_of_projectiles_allowed"]=2,
	parent="skill_stat_descriptions",
	["skill_effect_duration"]=3,
	["total_number_of_projectiles_to_fire"]=4
}