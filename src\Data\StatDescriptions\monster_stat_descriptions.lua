-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Curse Immune"
			}
		},
		stats={
			[1]="immune_to_curses"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Hexproof"
			}
		},
		stats={
			[1]="hexproof"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Burns Ground on Death"
			}
		},
		stats={
			[1]="monster_ground_fire_on_death_%_max_damage_to_deal_per_minute"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Chills Ground on Death"
			}
		},
		stats={
			[1]="monster_ground_ice_on_death_base_area_of_effect_radius"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Spreads Tar on Death"
			}
		},
		stats={
			[1]="monster_ground_tar_on_death_base_area_of_effect_radius"
		}
	},
	[6]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Spreads Caustic Ground on Death"
			}
		},
		stats={
			[1]="monster_caustic_cloud_on_death_%_max_damage_to_deal_per_minute"
		}
	},
	[7]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Can raise Magic monsters"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]=2
					}
				},
				text="Can raise Rare or Magic monsters"
			},
			[3]={
				limit={
					[1]={
						[1]=3,
						[2]=3
					}
				},
				text="Can raise Unique, Rare or Magic monsters"
			}
		},
		stats={
			[1]="necromancer_additional_rarity_levels_can_be_raised"
		}
	},
	[8]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]=99
					}
				},
				text="Avoids Frozen"
			},
			[2]={
				limit={
					[1]={
						[1]=100,
						[2]="#"
					}
				},
				text="Cannot be Frozen"
			}
		},
		stats={
			[1]="base_avoid_freeze_%"
		}
	},
	[9]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]=99
					}
				},
				text="Avoids Chilled"
			},
			[2]={
				limit={
					[1]={
						[1]=100,
						[2]="#"
					}
				},
				text="Cannot be Chilled"
			}
		},
		stats={
			[1]="base_avoid_chill_%"
		}
	},
	[10]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]=99
					}
				},
				text="Avoids Ignited"
			},
			[2]={
				limit={
					[1]={
						[1]=100,
						[2]="#"
					}
				},
				text="Cannot be Ignited"
			}
		},
		stats={
			[1]="base_avoid_ignite_%"
		}
	},
	[11]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]=99
					}
				},
				text="Avoids Shocked"
			},
			[2]={
				limit={
					[1]={
						[1]=100,
						[2]="#"
					}
				},
				text="Cannot be Shocked"
			}
		},
		stats={
			[1]="base_avoid_shock_%"
		}
	},
	[12]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Spikes on Death"
			}
		},
		stats={
			[1]="display_monster_spike_nova_on_death_text"
		}
	},
	[13]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Ice Spears on Death"
			}
		},
		stats={
			[1]="display_monster_ice_spear_nova_on_death_text"
		}
	},
	[14]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Casts Lightning Nova"
			}
		},
		stats={
			[1]="display_monster_casts_lightning_nova_text"
		}
	},
	[15]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Casts Fire Nova"
			}
		},
		stats={
			[1]="monster_casts_fire_nova_text"
		}
	},
	[16]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Removes Flask Charges"
			}
		},
		stats={
			[1]="monster_casts_flask_charge_nova_text"
		}
	},
	[17]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Causes Bleeding"
			}
		},
		stats={
			[1]="monster_casts_bleed_nova_text"
		}
	},
	[18]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Enemies take increased Damage"
			}
		},
		stats={
			[1]="monster_has_damage_taken_aura_text"
		}
	},
	[19]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Enemies are affected by Temporal Chains"
			}
		},
		stats={
			[1]="monster_has_temporal_chains_aura_text"
		}
	},
	[20]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Enemies take Damage when using Movement skills"
			}
		},
		stats={
			[1]="monster_has_movement_skill_damage_aura_text"
		}
	},
	[21]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Enrages on Low Life"
			}
		},
		stats={
			[1]="monster_enrages_on_low_life_text"
		}
	},
	[22]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Cannot be Slowed"
			}
		},
		stats={
			[1]="action_speed_cannot_be_reduced_below_base"
		}
	},
	[23]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Cannot be Life-Leeched"
			}
		},
		stats={
			[1]="cannot_have_life_leeched_from"
		}
	},
	[24]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Ignores Summoning Towers"
			}
		},
		stats={
			[1]="is_blight_chaos_monster"
		}
	},
	[25]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Resilient to Cold Towers"
			}
		},
		stats={
			[1]="is_blight_cold_monster"
		}
	},
	[26]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Resilient to Fire Towers"
			}
		},
		stats={
			[1]="is_blight_fire_monster"
		}
	},
	[27]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Resilient to Lightning Towers"
			}
		},
		stats={
			[1]="is_blight_lightning_monster"
		}
	},
	[28]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Resilient to Physical Towers"
			}
		},
		stats={
			[1]="is_blight_physical_monster"
		}
	},
	[29]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Empowered by Molten Gold"
			}
		},
		stats={
			[1]="monster_immune_to_damage_in_lava_text"
		}
	},
	[30]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Slowing Potency of Debuffs on me"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Slowing Potency of Debuffs on me"
			}
		},
		stats={
			[1]="monster_slow_potency_+%_final"
		}
	},
	[31]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Area of Effect"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Area of Effect"
			}
		},
		stats={
			[1]="rare_monster_mod_area_of_effect_+%_final"
		}
	},
	[32]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Blocks Projectiles while charging"
			}
		},
		stats={
			[1]="trigger_charge_additional_block_chance_against_projectiles_%"
		}
	},
	["action_speed_cannot_be_reduced_below_base"]=22,
	["base_avoid_chill_%"]=9,
	["base_avoid_freeze_%"]=8,
	["base_avoid_ignite_%"]=10,
	["base_avoid_shock_%"]=11,
	["cannot_have_life_leeched_from"]=23,
	["display_monster_casts_lightning_nova_text"]=14,
	["display_monster_ice_spear_nova_on_death_text"]=13,
	["display_monster_spike_nova_on_death_text"]=12,
	["hexproof"]=2,
	["immune_to_curses"]=1,
	["is_blight_chaos_monster"]=24,
	["is_blight_cold_monster"]=25,
	["is_blight_fire_monster"]=26,
	["is_blight_lightning_monster"]=27,
	["is_blight_physical_monster"]=28,
	["monster_casts_bleed_nova_text"]=17,
	["monster_casts_fire_nova_text"]=15,
	["monster_casts_flask_charge_nova_text"]=16,
	["monster_caustic_cloud_on_death_%_max_damage_to_deal_per_minute"]=6,
	["monster_enrages_on_low_life_text"]=21,
	["monster_ground_fire_on_death_%_max_damage_to_deal_per_minute"]=3,
	["monster_ground_ice_on_death_base_area_of_effect_radius"]=4,
	["monster_ground_tar_on_death_base_area_of_effect_radius"]=5,
	["monster_has_damage_taken_aura_text"]=18,
	["monster_has_movement_skill_damage_aura_text"]=20,
	["monster_has_temporal_chains_aura_text"]=19,
	["monster_immune_to_damage_in_lava_text"]=29,
	["monster_slow_potency_+%_final"]=30,
	["necromancer_additional_rarity_levels_can_be_raised"]=7,
	["rare_monster_mod_area_of_effect_+%_final"]=31,
	["trigger_charge_additional_block_chance_against_projectiles_%"]=32
}