if not loadStatFile then
	dofile("statdesc.lua")
end
loadStatFile("stat_descriptions.csd")

function table.containsId(table, element)
  for _, value in pairs(table) do
    if value.Id == element then
      return true
    end
  end
  return false
end

local s_format = string.format

local directiveTable = { }

directiveTable.type = function(state, args, out)
	state.type = args
end

directiveTable.base = function(state, args, out)
	local baseTypeId, displayName = args:match("([%w/_]+) (.+)")
	if not baseTypeId then
		baseTypeId = args
	end
	local baseItemType = dat("BaseItemTypes"):GetRow("Id", baseTypeId)
	if not baseItemType then
		printf("Invalid Id %s", baseTypeId)
		return
	end
	if not displayName then
		displayName = baseItemType.Name
	end
	displayName = displayName:gsub("\195\182","o")
	displayName = displayName:gsub("^%s*(.-)%s*$", "%1") -- trim spaces GGG might leave in by accident
	
	-- Special handling of Runes and SoulCores
	local function addRuneStats(stats, slotType, modLines)
		local stats, orders = describeStats(stats)
		if #orders > 0 then
			local out = {
				type = "Rune",
				slotType = slotType,
				label = stats,
				statOrder = orders,
			}
			table.insert(modLines, out)
		end
	end

	local function writeModLines(modLines, out)
		for _, modLine in ipairs(modLines) do
			out:write('\t\t["'..modLine.slotType..'"] = {\n')
			out:write('\t\t\t\ttype = "Rune",\n')
			-- for j, label in ipairs(modLine.label) do
			out:write('\t\t\t\t"'..table.concat(modLine.label, '",\n\t\t\t\t"')..'",\n')
			out:write('\t\t\t\tstatOrder = { '..table.concat(modLine.statOrder, ', ')..' },\n')
			-- end
			out:write('\t\t},\n')
		end
	end

	-- Check for Standard Weapon, Armour, Caster Runes
	local soulCores = dat("SoulCores"):GetRow("BaseItemTypes", baseItemType)
	local soulCoresPerClass = dat("SoulCoresPerClass"):GetRow("BaseItemType", baseItemType)
	out:write('\t["', displayName, '"] = {\n')
	local modLines = { }
	if soulCores then
		-- weapons
		local stats = { }
		for i, statKey in ipairs(soulCores.StatsKeysWeapon) do
			local statValue = soulCores["StatsValuesWeapon"][i]
			stats[statKey.Id] = { min = statValue, max = statValue }
		end
		if next(stats) then
			addRuneStats(stats, "weapon", modLines)
		end

		-- armour
		stats = { }  -- reset stats to empty
		for i, statKey in ipairs(soulCores.StatsKeysArmour) do
			local statValue = soulCores["StatsValuesArmour"][i]
			stats[statKey.Id] = { min = statValue, max = statValue }
		end
		if next(stats) then
			addRuneStats(stats, "armour", modLines)
		end

		-- caster check (wand & staff)
		stats = { }  -- reset stats to empty
		for i, statKey in ipairs(soulCores.StatsKeysCaster) do
			local statValue = soulCores["StatsValuesCaster"][i]
			stats[statKey.Id] = { min = statValue, max = statValue }
		end
		if next(stats) then
			addRuneStats(stats, "caster", modLines)
		end

		-- Check if the row is an Attribute rune which can go in all slots
		if soulCores.StatsKeysAttributes then
			stats = { }  -- reset stats to empty
			for i, statKey in ipairs(soulCores.StatsKeysAttributes) do
				local statValue = soulCores["StatsValuesAttributes"][i]
				stats[statKey.Id] = { min = statValue, max = statValue }
			end
			if next(stats) then
				addRuneStats(stats, "weapon", modLines)
				addRuneStats(stats, "armour", modLines)
				addRuneStats(stats, "caster", modLines)
			end
		end
	end

	-- Handle special case of new runes on specific item types
	if soulCoresPerClass then
		local stats = { }  -- reset stats to empty
		for i, statKey in ipairs(soulCoresPerClass.Stats) do
			local statValue = soulCoresPerClass["StatsValues"][i]
			stats[statKey.Id] = { min = statValue, max = statValue }
		end
		local itemClassId = soulCoresPerClass.ItemClass.Id
		if next(stats) then
			addRuneStats(stats, itemClassId:lower(), modLines)
		end
	end
	writeModLines(modLines, out)
	out:write('\t},\n')
end

directiveTable.baseMatch = function(state, argstr, out)
	-- Default to look at the Id column for matching
	local key = "Id"
	local args = {}
	for i in string.gmatch(argstr, "%S+") do
		table.insert(args, i)
	end
	local value = args[1]
	-- If column name is specified, use that
	if args[2] then
		key = args[1]
		value = args[2]
	end
	for i, baseItemType in ipairs(dat("BaseItemTypes"):GetRowList(key, value, true)) do
		directiveTable.base(state, baseItemType.Id, out)
	end
end

local out = io.open("../Data/ModRunes.lua", "w")
out:write('-- This file is automatically generated, do not edit!\n')
out:write('-- Item data (c) Grinding Gear Games\n\nreturn {\n')

local state = { }
for line in io.lines("Bases/soulcore.txt") do
	local spec, args = line:match("#(%a+) ?(.*)")
	if spec then
		if directiveTable[spec] then
			directiveTable[spec](state, args, out)
		else
			printf("Unknown directive '%s'", spec)
		end
	end
end

out:write("}")
out:close()

print("Soul Cores exported.")