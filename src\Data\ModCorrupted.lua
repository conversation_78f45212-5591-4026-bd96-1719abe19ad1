-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	["CorruptionLocalIncreasedPhysicalDamageReductionRatingPercent1"] = { type = "Corrupted", affix = "", "(15-25)% increased Armour", statOrder = { 837 }, level = 1, group = "LocalPhysicalDamageReductionRatingPercent", weightKey = { "str_armour", "default" }, weightVal = { 1,  0 }, modTags = { "armour", "defences" }, tradeHash = 1062208444, },
	["CorruptionLocalIncreasedEvasionRatingPercent1"] = { type = "Corrupted", affix = "", "(15-25)% increased Evasion Rating", statOrder = { 838 }, level = 1, group = "LocalEvasionRatingIncreasePercent", weightKey = { "dex_armour", "default" }, weightVal = { 1,  0 }, modTags = { "evasion", "defences" }, tradeHash = 124859000, },
	["CorruptionLocalIncreasedEnergyShieldPercent1"] = { type = "Corrupted", affix = "", "(15-25)% increased Energy Shield", statOrder = { 839 }, level = 1, group = "LocalEnergyShieldPercent", weightKey = { "int_armour", "default" }, weightVal = { 1,  0 }, modTags = { "energy_shield", "defences" }, tradeHash = 4015621042, },
	["CorruptionLocalIncreasedArmourAndEvasion1"] = { type = "Corrupted", affix = "", "(15-25)% increased Armour and Evasion", statOrder = { 840 }, level = 1, group = "LocalArmourAndEvasion", weightKey = { "str_dex_armour", "default" }, weightVal = { 1,  0 }, modTags = { "armour", "evasion", "defences" }, tradeHash = 2451402625, },
	["CorruptionLocalIncreasedArmourAndEnergyShield1"] = { type = "Corrupted", affix = "", "(15-25)% increased Armour and Energy Shield", statOrder = { 841 }, level = 1, group = "LocalArmourAndEnergyShield", weightKey = { "str_int_armour", "default" }, weightVal = { 1,  0 }, modTags = { "armour", "energy_shield", "defences" }, tradeHash = 3321629045, },
	["CorruptionLocalIncreasedEvasionAndEnergyShield1"] = { type = "Corrupted", affix = "", "(15-25)% increased Evasion and Energy Shield", statOrder = { 842 }, level = 1, group = "LocalEvasionAndEnergyShield", weightKey = { "dex_int_armour", "default" }, weightVal = { 1,  0 }, modTags = { "energy_shield", "evasion", "defences" }, tradeHash = 1999113824, },
	["CorruptionReducedLocalAttributeRequirements1"] = { type = "Corrupted", affix = "", "(10-20)% reduced Attribute Requirements", statOrder = { 921 }, level = 1, group = "LocalAttributeRequirements", weightKey = { "onehand", "armour", "twohand", "default" }, weightVal = { 1, 1, 1,  0 }, modTags = {  }, tradeHash = 3639275092, },
	["CorruptionAdditionalPhysicalDamageReduction1"] = { type = "Corrupted", affix = "", "(3-5)% additional Physical Damage Reduction", statOrder = { 949 }, level = 1, group = "ReducedPhysicalDamageTaken", weightKey = { "body_armour", "default" }, weightVal = { 1,  0 }, modTags = { "physical" }, tradeHash = 3771516363, },
	["CorruptionDamageTakenGainedAsLife1"] = { type = "Corrupted", affix = "", "(10-20)% of Damage taken Recouped as Life", statOrder = { 965 }, level = 1, group = "DamageTakenGainedAsLife", weightKey = { "body_armour", "default" }, weightVal = { 1,  0 }, modTags = { "resource", "life" }, tradeHash = 1444556985, },
	["CorruptionDamageTakenGainedAsMana1"] = { type = "Corrupted", affix = "", "(10-20)% of Damage taken Recouped as Mana", statOrder = { 972 }, level = 1, group = "PercentDamageGoesToMana", weightKey = { "body_armour", "default" }, weightVal = { 1,  0 }, modTags = { "resource", "life", "mana" }, tradeHash = 472520716, },
	["CorruptionLifeLeech1"] = { type = "Corrupted", affix = "", "Leech 3% of Physical Attack Damage as Life", statOrder = { 966 }, level = 1, group = "LifeLeechPermyriad", weightKey = { "amulet", "default" }, weightVal = { 1,  0 }, modTags = { "resource", "life", "physical", "attack" }, tradeHash = 2557965901, },
	["CorruptionManaLeech1"] = { type = "Corrupted", affix = "", "Leech 2% of Physical Attack Damage as Mana", statOrder = { 974 }, level = 1, group = "ManaLeechPermyriad", weightKey = { "amulet", "default" }, weightVal = { 1,  0 }, modTags = { "resource", "mana", "physical", "attack" }, tradeHash = 707457662, },
	["CorruptionMaximumElementalResistance1"] = { type = "Corrupted", affix = "", "+1% to all Maximum Elemental Resistances", statOrder = { 950 }, level = 1, group = "MaximumElementalResistance", weightKey = { "body_armour", "amulet", "default" }, weightVal = { 1, 1,  0 }, modTags = { "elemental", "resistance" }, tradeHash = 1978899297, },
	["CorruptionIncreasedLife1"] = { type = "Corrupted", affix = "", "+(30-40) to maximum Life", statOrder = { 872 }, level = 1, group = "IncreasedLife", weightKey = { "body_armour", "belt", "default" }, weightVal = { 1, 1,  0 }, modTags = { "resource", "life" }, tradeHash = 3299347043, },
	["CorruptionIncreasedMana1"] = { type = "Corrupted", affix = "", "+(20-25) to maximum Mana", statOrder = { 874 }, level = 1, group = "IncreasedMana", weightKey = { "focus", "quiver", "ring", "default" }, weightVal = { 1, 1, 1,  0 }, modTags = { "resource", "mana" }, tradeHash = 1050105434, },
	["CorruptionIncreasedPhysicalDamageReductionRatingPercent1"] = { type = "Corrupted", affix = "", "(15-25)% increased Armour", statOrder = { 867 }, level = 1, group = "GlobalPhysicalDamageReductionRatingPercent", weightKey = { "belt", "default" }, weightVal = { 1,  0 }, modTags = { "armour", "defences" }, tradeHash = 2866361420, },
	["CorruptionIncreasedEvasionRatingPercent1"] = { type = "Corrupted", affix = "", "(15-25)% increased Evasion Rating", statOrder = { 869 }, level = 1, group = "GlobalEvasionRatingPercent", weightKey = { "belt", "default" }, weightVal = { 1,  0 }, modTags = { "evasion", "defences" }, tradeHash = 2106365538, },
	["CorruptionIncreasedEnergyShieldPercent1"] = { type = "Corrupted", affix = "", "(15-25)% increased maximum Energy Shield", statOrder = { 871 }, level = 1, group = "GlobalEnergyShieldPercent", weightKey = { "belt", "default" }, weightVal = { 1,  0 }, modTags = { "energy_shield", "defences" }, tradeHash = 2482852589, },
	["CorruptionThornsDamageIncrease1"] = { type = "Corrupted", affix = "", "(40-50)% increased Thorns damage", statOrder = { 9211 }, level = 1, group = "ThornsDamageIncrease", weightKey = { "body_armour", "shield", "default" }, weightVal = { 1, 1,  0 }, modTags = { "damage" }, tradeHash = 1315743832, },
	["CorruptionChaosResistance1"] = { type = "Corrupted", affix = "", "+(13-19)% to Chaos Resistance", statOrder = { 959 }, level = 1, group = "ChaosResistance", weightKey = { "body_armour", "ring", "default" }, weightVal = { 1, 1,  0 }, modTags = { "chaos", "resistance" }, tradeHash = 2923486259, },
	["CorruptionFireResistance1"] = { type = "Corrupted", affix = "", "+(20-25)% to Fire Resistance", statOrder = { 956 }, level = 1, group = "FireResistance", weightKey = { "belt", "boots", "default" }, weightVal = { 1, 1,  0 }, modTags = { "elemental", "fire", "resistance" }, tradeHash = 3372524247, },
	["CorruptionColdResistance1"] = { type = "Corrupted", affix = "", "+(20-25)% to Cold Resistance", statOrder = { 957 }, level = 1, group = "ColdResistance", weightKey = { "belt", "boots", "default" }, weightVal = { 1, 1,  0 }, modTags = { "elemental", "cold", "resistance" }, tradeHash = 4220027924, },
	["CorruptionLightningResistance1"] = { type = "Corrupted", affix = "", "+(20-25)% to Lightning Resistance", statOrder = { 958 }, level = 1, group = "LightningResistance", weightKey = { "belt", "boots", "default" }, weightVal = { 1, 1,  0 }, modTags = { "elemental", "lightning", "resistance" }, tradeHash = 1671376347, },
	["CorruptionMaximumFireResistance1"] = { type = "Corrupted", affix = "", "+(1-3)% to Maximum Fire Resistance", statOrder = { 951 }, level = 1, group = "MaximumFireResist", weightKey = { "belt", "default" }, weightVal = { 1,  0 }, modTags = { "elemental", "fire", "resistance" }, tradeHash = 4095671657, },
	["CorruptionMaximumColdResistance1"] = { type = "Corrupted", affix = "", "+(1-3)% to Maximum Cold Resistance", statOrder = { 952 }, level = 1, group = "MaximumColdResist", weightKey = { "helmet", "default" }, weightVal = { 1,  0 }, modTags = { "elemental", "cold", "resistance" }, tradeHash = 3676141501, },
	["CorruptionMaximumLightningResistance1"] = { type = "Corrupted", affix = "", "+(1-3)% to Maximum Lightning Resistance", statOrder = { 953 }, level = 1, group = "MaximumLightningResistance", weightKey = { "boots", "default" }, weightVal = { 1,  0 }, modTags = { "elemental", "lightning", "resistance" }, tradeHash = 1011760251, },
	["CorruptionIncreasedSpirit1"] = { type = "Corrupted", affix = "", "+(20-30) to Spirit", statOrder = { 877 }, level = 1, group = "BaseSpirit", weightKey = { "helmet", "default" }, weightVal = { 1,  0 }, modTags = {  }, tradeHash = 3981240776, },
	["CorruptionFirePenetration1"] = { type = "Corrupted", affix = "", "Damage Penetrates (10-15)% Fire Resistance", statOrder = { 2643 }, level = 1, group = "FireResistancePenetration", weightKey = { "gloves", "default" }, weightVal = { 1,  0 }, modTags = { "elemental_damage", "damage", "elemental", "fire" }, tradeHash = 2653955271, },
	["CorruptionColdPenetration1"] = { type = "Corrupted", affix = "", "Damage Penetrates (10-15)% Cold Resistance", statOrder = { 2644 }, level = 1, group = "ColdResistancePenetration", weightKey = { "gloves", "default" }, weightVal = { 1,  0 }, modTags = { "elemental_damage", "damage", "elemental", "cold" }, tradeHash = 3417711605, },
	["CorruptionLightningPenetration1"] = { type = "Corrupted", affix = "", "Damage Penetrates (10-15)% Lightning Resistance", statOrder = { 2645 }, level = 1, group = "LightningResistancePenetration", weightKey = { "gloves", "default" }, weightVal = { 1,  0 }, modTags = { "elemental_damage", "damage", "elemental", "lightning" }, tradeHash = 818778753, },
	["CorruptionArmourBreak1"] = { type = "Corrupted", affix = "", "Break (10-15)% increased Armour", statOrder = { 4275 }, level = 1, group = "ArmourBreak", weightKey = { "gloves", "default" }, weightVal = { 1,  0 }, modTags = {  }, tradeHash = 1776411443, },
	["CorruptionGoldFoundIncrease1"] = { type = "Corrupted", affix = "", "(5-10)% increased Quantity of Gold Dropped by Slain Enemies", statOrder = { 6281 }, level = 1, group = "GoldFoundIncrease", weightKey = { "default" }, weightVal = {  0 }, modTags = { "drop" }, tradeHash = 3175163625, },
	["CorruptionMaximumEnduranceCharges1"] = { type = "Corrupted", affix = "", "+1 to Maximum Endurance Charges", statOrder = { 1500 }, level = 1, group = "MaximumEnduranceCharges", weightKey = { "belt", "default" }, weightVal = { 1,  0 }, modTags = { "endurance_charge" }, tradeHash = 1515657623, },
	["CorruptionMaximumFrenzyCharges1"] = { type = "Corrupted", affix = "", "+1 to Maximum Frenzy Charges", statOrder = { 1505 }, level = 1, group = "MaximumFrenzyCharges", weightKey = { "gloves", "default" }, weightVal = { 1,  0 }, modTags = { "frenzy_charge" }, tradeHash = 4078695, },
	["CorruptionMaximumPowerCharges1"] = { type = "Corrupted", affix = "", "+1 to Maximum Power Charges", statOrder = { 1510 }, level = 1, group = "MaximumPowerCharges", weightKey = { "helmet", "default" }, weightVal = { 1,  0 }, modTags = { "power_charge" }, tradeHash = 227523295, },
	["CorruptionIncreasedAccuracy1"] = { type = "Corrupted", affix = "", "+(50-100) to Accuracy Rating", statOrder = { 864 }, level = 1, group = "IncreasedAccuracy", weightKey = { "quiver", "helmet", "default" }, weightVal = { 1, 1,  0 }, modTags = { "attack" }, tradeHash = 803737631, },
	["CorruptionMovementVelocity1"] = { type = "Corrupted", affix = "", "(3-5)% increased Movement Speed", statOrder = { 829 }, level = 1, group = "MovementVelocity", weightKey = { "boots", "default" }, weightVal = { 1,  0 }, modTags = { "speed" }, tradeHash = 2250533757, },
	["CorruptionIncreasedStunThreshold1"] = { type = "Corrupted", affix = "", "(20-30)% increased Stun Threshold", statOrder = { 2914 }, level = 1, group = "IncreasedStunThreshold", weightKey = { "boots", "default" }, weightVal = { 1,  0 }, modTags = {  }, tradeHash = 680068163, },
	["CorruptionIncreasedFreezeThreshold1"] = { type = "Corrupted", affix = "", "(20-30)% increased Freeze Threshold", statOrder = { 2915 }, level = 1, group = "FreezeThreshold", weightKey = { "boots", "default" }, weightVal = { 1,  0 }, modTags = { "damage" }, tradeHash = 3780644166, },
	["CorruptionSlowPotency1"] = { type = "Corrupted", affix = "", "(20-30)% reduced Slowing Potency of Debuffs on You", statOrder = { 4548 }, level = 1, group = "SlowPotency", weightKey = { "boots", "default" }, weightVal = { 1,  0 }, modTags = {  }, tradeHash = 924253255, },
	["CorruptionLifeRegenerationPercent1"] = { type = "Corrupted", affix = "", "Regenerate (1-2)% of maximum Life per second", statOrder = { 1635 }, level = 1, group = "LifeRegenerationRatePercentage", weightKey = { "ring", "helmet", "default" }, weightVal = { 1, 1,  0 }, modTags = { "resource", "life" }, tradeHash = 836936635, },
	["CorruptionLifeRegenerationRate1"] = { type = "Corrupted", affix = "", "(15-25)% increased Life Regeneration rate", statOrder = { 964 }, level = 1, group = "LifeRegenerationRate", weightKey = { "default" }, weightVal = {  0 }, modTags = { "resource", "life" }, tradeHash = 44972811, },
	["CorruptionManaRegeneration1"] = { type = "Corrupted", affix = "", "(20-30)% increased Mana Regeneration Rate", statOrder = { 971 }, level = 1, group = "ManaRegeneration", weightKey = { "ring", "helmet", "default" }, weightVal = { 1, 1,  0 }, modTags = { "resource", "mana" }, tradeHash = 789117908, },
	["CorruptionLocalBlockChance1"] = { type = "Corrupted", affix = "", "(20-30)% increased Block chance", statOrder = { 832 }, level = 1, group = "LocalIncreasedBlockPercentage", weightKey = { "shield", "default" }, weightVal = { 1,  0 }, modTags = { "block" }, tradeHash = 2481353198, },
	["CorruptionMaximumBlockChance1"] = { type = "Corrupted", affix = "", "+3% to maximum Block chance", statOrder = { 1676 }, level = 1, group = "MaximumBlockChance", weightKey = { "shield", "default" }, weightVal = { 1,  0 }, modTags = { "block" }, tradeHash = 480796730, },
	["CorruptionGainLifeOnBlock1"] = { type = "Corrupted", affix = "", "(20-25) Life gained when you Block", statOrder = { 1459 }, level = 1, group = "GainLifeOnBlock", weightKey = { "shield", "default" }, weightVal = { 1,  0 }, modTags = { "block", "resource", "life" }, tradeHash = 762600725, },
	["CorruptionGainManaOnBlock1"] = { type = "Corrupted", affix = "", "(10-15) Mana gained when you Block", statOrder = { 1460 }, level = 1, group = "GainManaOnBlock", weightKey = { "shield", "default" }, weightVal = { 1,  0 }, modTags = { "block", "resource", "mana" }, tradeHash = 2122183138, },
	["CorruptionAllResistances1"] = { type = "Corrupted", affix = "", "+(5-10)% to all Elemental Resistances", statOrder = { 955 }, level = 1, group = "AllResistances", weightKey = { "ring", "amulet", "default" }, weightVal = { 1, 1,  0 }, modTags = { "elemental", "fire", "cold", "lightning", "resistance" }, tradeHash = 2901986750, },
	["CorruptionGlobalFireSpellGemsLevel1"] = { type = "Corrupted", affix = "", "+1 to Level of all Fire Spell Skills", statOrder = { 923 }, level = 1, group = "GlobalIncreaseFireSpellSkillGemLevel", weightKey = { "staff", "wand", "default" }, weightVal = { 1, 1,  0 }, modTags = { "elemental", "fire", "caster", "gem" }, tradeHash = 591105508, },
	["CorruptionGlobalColdSpellGemsLevel1"] = { type = "Corrupted", affix = "", "+1 to Level of all Cold Spell Skills", statOrder = { 924 }, level = 1, group = "GlobalIncreaseColdSpellSkillGemLevel", weightKey = { "staff", "wand", "default" }, weightVal = { 1, 1,  0 }, modTags = { "elemental", "cold", "caster", "gem" }, tradeHash = 2254480358, },
	["CorruptionGlobalLightningSpellGemsLevel1"] = { type = "Corrupted", affix = "", "+1 to Level of all Lightning Spell Skills", statOrder = { 925 }, level = 1, group = "GlobalIncreaseLightningSpellSkillGemLevel", weightKey = { "staff", "wand", "default" }, weightVal = { 1, 1,  0 }, modTags = { "elemental", "lightning", "caster", "gem" }, tradeHash = 1545858329, },
	["CorruptionGlobalChaosSpellGemsLevel1"] = { type = "Corrupted", affix = "", "+1 to Level of all Chaos Spell Skills", statOrder = { 926 }, level = 1, group = "GlobalIncreaseChaosSpellSkillGemLevel", weightKey = { "staff", "wand", "default" }, weightVal = { 1, 1,  0 }, modTags = { "chaos", "caster", "gem" }, tradeHash = 4226189338, },
	["CorruptionGlobalPhysicalSpellGemsLevel1"] = { type = "Corrupted", affix = "", "+1 to Level of all Physical Spell Skills", statOrder = { 1416 }, level = 1, group = "GlobalIncreasePhysicalSpellSkillGemLevel", weightKey = { "staff", "wand", "default" }, weightVal = { 1, 1,  0 }, modTags = { "physical", "caster", "gem" }, tradeHash = 1600707273, },
	["CorruptionGlobalMinionSkillGemsLevel1"] = { type = "Corrupted", affix = "", "+1 to Level of all Minion Skills", statOrder = { 929 }, level = 1, group = "GlobalIncreaseMinionSpellSkillGemLevel", weightKey = { "helmet", "default" }, weightVal = { 1,  0 }, modTags = { "minion", "gem" }, tradeHash = 2162097452, },
	["CorruptionGlobalMeleeSkillGemsLevel1"] = { type = "Corrupted", affix = "", "+1 to Level of all Melee Skills", statOrder = { 927 }, level = 1, group = "GlobalIncreaseMeleeSkillGemLevel", weightKey = { "gloves", "default" }, weightVal = { 1,  0 }, modTags = { "attack" }, tradeHash = 9187492, },
	["CorruptionGlobalTrapSkillGemsLevel1"] = { type = "Corrupted", affix = "", "+1 to Level of all Trap Skill Gems", statOrder = { 930 }, level = 1, group = "GlobalIncreaseTrapSkillGemLevel", weightKey = { "default" }, weightVal = {  0 }, modTags = {  }, tradeHash = 239953100, },
	["CorruptionItemFoundRarityIncrease1"] = { type = "Corrupted", affix = "", "(10-15)% increased Rarity of Items found", statOrder = { 916 }, level = 1, group = "ItemFoundRarityIncrease", weightKey = { "ring", "amulet", "default" }, weightVal = { 1, 1,  0 }, modTags = { "drop" }, tradeHash = 3917489142, },
	["CorruptionAllDamage1"] = { type = "Corrupted", affix = "", "(20-30)% increased Damage", statOrder = { 1095 }, level = 1, group = "AllDamage", weightKey = { "quiver", "ring", "default" }, weightVal = { 1, 1,  0 }, modTags = { "damage" }, tradeHash = 2154246560, },
	["CorruptionIncreasedSkillSpeed1"] = { type = "Corrupted", affix = "", "(4-6)% increased Skill Speed", statOrder = { 830 }, level = 1, group = "IncreasedSkillSpeed", weightKey = { "quiver", "ring", "default" }, weightVal = { 1, 1,  0 }, modTags = { "speed" }, tradeHash = 970213192, },
	["CorruptionCriticalStrikeMultiplier1"] = { type = "Corrupted", affix = "", "(15-20)% increased Critical Damage Bonus", statOrder = { 935 }, level = 1, group = "CriticalStrikeMultiplier", weightKey = { "quiver", "ring", "default" }, weightVal = { 1, 1,  0 }, modTags = { "damage", "critical" }, tradeHash = 3556824919, },
	["CorruptionGlobalSkillGemLevel1"] = { type = "Corrupted", affix = "", "+1 to Level of all Skills", statOrder = { 4180 }, level = 1, group = "GlobalSkillGemLevel", weightKey = { "amulet", "default" }, weightVal = { 1,  0 }, modTags = { "gem" }, tradeHash = 4283407333, },
	["CorruptionStrength1"] = { type = "Corrupted", affix = "", "+(10-15) to Strength", statOrder = { 945 }, level = 1, group = "Strength", weightKey = { "belt", "ring", "amulet", "default" }, weightVal = { 1, 1, 1,  0 }, modTags = { "attribute" }, tradeHash = 4080418644, },
	["CorruptionDexterity1"] = { type = "Corrupted", affix = "", "+(10-15) to Dexterity", statOrder = { 946 }, level = 1, group = "Dexterity", weightKey = { "belt", "ring", "amulet", "default" }, weightVal = { 1, 1, 1,  0 }, modTags = { "attribute" }, tradeHash = 3261801346, },
	["CorruptionIntelligence1"] = { type = "Corrupted", affix = "", "+(10-15) to Intelligence", statOrder = { 947 }, level = 1, group = "Intelligence", weightKey = { "belt", "ring", "amulet", "default" }, weightVal = { 1, 1, 1,  0 }, modTags = { "attribute" }, tradeHash = 328541901, },
	["CorruptionIncreasedSlowEffect1"] = { type = "Corrupted", affix = "", "Debuffs you inflict have (20-30)% increased Slow Magnitude", statOrder = { 4512 }, level = 1, group = "SlowEffect", weightKey = { "gloves", "default" }, weightVal = { 1,  0 }, modTags = {  }, tradeHash = 3650992555, },
	["CorruptionWeaponSwapSpeed1"] = { type = "Corrupted", affix = "", "(20-30)% increased Weapon Swap Speed", statOrder = { 9436 }, level = 1, group = "WeaponSwapSpeed", weightKey = { "gloves", "default" }, weightVal = { 1,  0 }, modTags = { "attack", "speed" }, tradeHash = 3233599707, },
	["CorruptionLifeFlaskChargeGeneration1"] = { type = "Corrupted", affix = "", "Life Flasks gain (0.08-0.17) charges per Second", statOrder = { 6260 }, level = 1, group = "LifeFlaskChargeGeneration", weightKey = { "amulet", "default" }, weightVal = { 1,  0 }, modTags = {  }, tradeHash = 1102738251, },
	["CorruptionManaFlaskChargeGeneration1"] = { type = "Corrupted", affix = "", "Mana Flasks gain (0.08-0.17) charges per Second", statOrder = { 6261 }, level = 1, group = "ManaFlaskChargeGeneration", weightKey = { "amulet", "default" }, weightVal = { 1,  0 }, modTags = {  }, tradeHash = 2200293569, },
	["CorruptionCharmChargeGeneration1"] = { type = "Corrupted", affix = "", "Charms gain (0.08-0.17) charges per Second", statOrder = { 6258 }, level = 1, group = "CharmChargeGeneration", weightKey = { "amulet", "default" }, weightVal = { 1,  0 }, modTags = {  }, tradeHash = 185580205, },
	["CorruptionLocalIncreasedPhysicalDamagePercent1"] = { type = "Corrupted", affix = "", "(15-25)% increased Physical Damage", statOrder = { 823 }, level = 1, group = "LocalPhysicalDamagePercent", weightKey = { "weapon", "default" }, weightVal = { 1,  0 }, modTags = { "physical_damage", "damage", "physical", "attack" }, tradeHash = 1805374733, },
	["CorruptionSpellDamageOnWeapon1"] = { type = "Corrupted", affix = "", "(20-30)% increased Spell Damage", statOrder = { 855 }, level = 1, group = "WeaponSpellDamage", weightKey = { "focus", "wand", "default" }, weightVal = { 1, 1,  0 }, modTags = { "caster_damage", "damage", "caster" }, tradeHash = 2974417149, },
	["CorruptionSpellDamageOnTwoHandWeapon1"] = { type = "Corrupted", affix = "", "(40-50)% increased Spell Damage", statOrder = { 855 }, level = 1, group = "WeaponSpellDamage", weightKey = { "staff", "default" }, weightVal = { 1,  0 }, modTags = { "caster_damage", "damage", "caster" }, tradeHash = 2974417149, },
	["CorruptionLocalIncreasedSpiritPercent1"] = { type = "Corrupted", affix = "", "(15-25)% increased Spirit", statOrder = { 844 }, level = 1, group = "LocalIncreasedSpiritPercent", weightKey = { "sceptre", "default" }, weightVal = { 1,  0 }, modTags = {  }, tradeHash = 3984865854, },
	["CorruptionLocalAddedFireDamage1"] = { type = "Corrupted", affix = "", "Adds (9-14) to (15-22) Fire Damage", statOrder = { 825 }, level = 1, group = "LocalFireDamage", weightKey = { "bow", "one_hand_weapon", "default" }, weightVal = { 1, 1,  0 }, modTags = { "elemental_damage", "damage", "elemental", "fire", "attack" }, tradeHash = 709508406, },
	["CorruptionLocalAddedFireDamageTwoHand1"] = { type = "Corrupted", affix = "", "Adds (13-20) to (21-31) Fire Damage", statOrder = { 825 }, level = 1, group = "LocalFireDamage", weightKey = { "bow", "two_hand_weapon", "default" }, weightVal = { 0, 1,  0 }, modTags = { "elemental_damage", "damage", "elemental", "fire", "attack" }, tradeHash = 709508406, },
	["CorruptionLocalAddedColdDamage1"] = { type = "Corrupted", affix = "", "Adds (8-12) to (13-19) Cold Damage", statOrder = { 826 }, level = 1, group = "LocalColdDamage", weightKey = { "bow", "one_hand_weapon", "default" }, weightVal = { 1, 1,  0 }, modTags = { "elemental_damage", "damage", "elemental", "cold", "attack" }, tradeHash = 1037193709, },
	["CorruptionLocalAddedColdDamageTwoHand1"] = { type = "Corrupted", affix = "", "Adds (11-17) to (18-26) Cold Damage", statOrder = { 826 }, level = 1, group = "LocalColdDamage", weightKey = { "bow", "two_hand_weapon", "default" }, weightVal = { 0, 1,  0 }, modTags = { "elemental_damage", "damage", "elemental", "cold", "attack" }, tradeHash = 1037193709, },
	["CorruptionLocalAddedLightningDamage1"] = { type = "Corrupted", affix = "", "Adds (1-2) to (29-43) Lightning Damage", statOrder = { 827 }, level = 1, group = "LocalLightningDamage", weightKey = { "bow", "one_hand_weapon", "default" }, weightVal = { 1, 1,  0 }, modTags = { "elemental_damage", "damage", "elemental", "lightning", "attack" }, tradeHash = 3336890334, },
	["CorruptionLocalAddedLightningDamageTwoHand1"] = { type = "Corrupted", affix = "", "Adds (1-3) to (41-61) Lightning Damage", statOrder = { 827 }, level = 1, group = "LocalLightningDamage", weightKey = { "bow", "two_hand_weapon", "default" }, weightVal = { 0, 1,  0 }, modTags = { "elemental_damage", "damage", "elemental", "lightning", "attack" }, tradeHash = 3336890334, },
	["CorruptionLocalAddedChaosDamage1"] = { type = "Corrupted", affix = "", "Adds (7-11) to (12-18) Chaos damage", statOrder = { 1235 }, level = 1, group = "LocalChaosDamage", weightKey = { "bow", "one_hand_weapon", "default" }, weightVal = { 1, 1,  0 }, modTags = { "chaos_damage", "damage", "chaos", "attack" }, tradeHash = 2223678961, },
	["CorruptionLocalAddedChaosDamageTwoHand1"] = { type = "Corrupted", affix = "", "Adds (10-16) to (17-25) Chaos damage", statOrder = { 1235 }, level = 1, group = "LocalChaosDamage", weightKey = { "bow", "two_hand_weapon", "default" }, weightVal = { 0, 1,  0 }, modTags = { "chaos_damage", "damage", "chaos", "attack" }, tradeHash = 2223678961, },
	["CorruptionLocalIncreasedAttackSpeed1"] = { type = "Corrupted", affix = "", "(6-8)% increased Attack Speed", statOrder = { 919 }, level = 1, group = "LocalIncreasedAttackSpeed", weightKey = { "weapon", "default" }, weightVal = { 1,  0 }, modTags = { "attack", "speed" }, tradeHash = 210067635, },
	["CorruptionLocalCriticalStrikeMultiplier1"] = { type = "Corrupted", affix = "", "+(10-15)% to Critical Damage Bonus", statOrder = { 918 }, level = 1, group = "LocalCriticalStrikeMultiplier", weightKey = { "weapon", "default" }, weightVal = { 1,  0 }, modTags = { "damage", "attack", "critical" }, tradeHash = 2694482655, },
	["CorruptionLocalStunDamageIncrease1"] = { type = "Corrupted", affix = "", "Causes (20-30)% increased Stun Buildup", statOrder = { 980 }, level = 1, group = "LocalStunDamageIncrease", weightKey = { "mace", "warstaff", "default" }, weightVal = { 1, 1,  0 }, modTags = {  }, tradeHash = 791928121, },
	["CorruptionLocalWeaponRangeIncrease1"] = { type = "Corrupted", affix = "", "(10-20)% increased Melee Strike Range with this weapon", statOrder = { 6883 }, level = 1, group = "LocalWeaponRangeIncrease", weightKey = { "mace", "warstaff", "default" }, weightVal = { 1, 1,  0 }, modTags = { "attack" }, tradeHash = 548198834, },
	["CorruptionLocalChanceToBleed1"] = { type = "Corrupted", affix = "", "(10-15)% chance to cause Bleeding on Hit", statOrder = { 2178 }, level = 1, group = "LocalChanceToBleed", weightKey = { "mace", "default" }, weightVal = { 1,  0 }, modTags = { "bleed", "physical", "attack", "ailment" }, tradeHash = 1519615863, },
	["CorruptionLocalChanceToPoison1"] = { type = "Corrupted", affix = "", "(10-15)% chance to Poison on Hit with this weapon", statOrder = { 7059 }, level = 1, group = "LocalChanceToPoisonOnHit", weightKey = { "warstaff", "default" }, weightVal = { 1,  0 }, modTags = { "poison", "chaos", "attack", "ailment" }, tradeHash = 3885634897, },
	["CorruptionLocalRageOnHit1"] = { type = "Corrupted", affix = "", "Gain 1 Rage on Hit", statOrder = { 6989 }, level = 1, group = "LocalRageOnHit", weightKey = { "mace", "warstaff", "default" }, weightVal = { 1, 1,  0 }, modTags = {  }, tradeHash = 1725749947, },
	["CorruptionLocalChanceToMaim1"] = { type = "Corrupted", affix = "", "(10-15)% chance to Maim on Hit", statOrder = { 7044 }, level = 1, group = "LocalChanceToMaim", weightKey = { "bow", "crossbow", "default" }, weightVal = { 1, 1,  0 }, modTags = { "attack" }, tradeHash = 2763429652, },
	["CorruptionLocalChanceToBlind1"] = { type = "Corrupted", affix = "", "(5-10)% chance to Blind Enemies on hit", statOrder = { 1964 }, level = 1, group = "BlindingHit", weightKey = { "bow", "crossbow", "default" }, weightVal = { 1, 1,  0 }, modTags = {  }, tradeHash = 2301191210, },
	["CorruptionWeaponElementalDamage1"] = { type = "Corrupted", affix = "", "(20-30)% increased Elemental Damage with Attacks", statOrder = { 861 }, level = 1, group = "IncreasedWeaponElementalDamagePercent", weightKey = { "bow", "one_hand_weapon", "default" }, weightVal = { 1, 1,  0 }, modTags = { "elemental_damage", "damage", "elemental", "attack" }, tradeHash = 387439868, },
	["CorruptionWeaponElementalDamageTwoHand1"] = { type = "Corrupted", affix = "", "(40-50)% increased Elemental Damage with Attacks", statOrder = { 861 }, level = 1, group = "IncreasedWeaponElementalDamagePercent", weightKey = { "bow", "two_hand_weapon", "default" }, weightVal = { 0, 1,  0 }, modTags = { "elemental_damage", "damage", "elemental", "attack" }, tradeHash = 387439868, },
	["CorruptionAdditionalArrows1"] = { type = "Corrupted", affix = "", "Bow Attacks fire an additional Arrow", statOrder = { 943 }, level = 1, group = "AdditionalArrows", weightKey = { "bow", "default" }, weightVal = { 1,  0 }, modTags = { "attack" }, tradeHash = 3885405204, },
	["CorruptionAdditionalAmmo1"] = { type = "Corrupted", affix = "", "Loads an additional bolt", statOrder = { 941 }, level = 1, group = "AdditionalAmmo", weightKey = { "crossbow", "default" }, weightVal = { 1,  0 }, modTags = { "attack" }, tradeHash = 1039380318, },
	["CorruptionIgniteChanceIncrease1"] = { type = "Corrupted", affix = "", "(20-30)% increased chance to Ignite", statOrder = { 984 }, level = 1, group = "IgniteChanceIncrease", weightKey = { "staff", "wand", "default" }, weightVal = { 1, 1,  0 }, modTags = {  }, tradeHash = 2968503605, },
	["CorruptionFreezeDamageIncrease1"] = { type = "Corrupted", affix = "", "(20-30)% increased Freeze Buildup", statOrder = { 986 }, level = 1, group = "FreezeDamageIncrease", weightKey = { "staff", "wand", "default" }, weightVal = { 1, 1,  0 }, modTags = {  }, tradeHash = 473429811, },
	["CorruptionShockChanceIncrease1"] = { type = "Corrupted", affix = "", "(20-30)% increased chance to Shock", statOrder = { 988 }, level = 1, group = "ShockChanceIncrease", weightKey = { "staff", "wand", "default" }, weightVal = { 1, 1,  0 }, modTags = {  }, tradeHash = 293638271, },
	["CorruptionSpellCriticalStrikeChance1"] = { type = "Corrupted", affix = "", "(20-30)% increased Critical Hit Chance for Spells", statOrder = { 933 }, level = 1, group = "SpellCriticalStrikeChance", weightKey = { "staff", "wand", "default" }, weightVal = { 1, 1,  0 }, modTags = { "caster", "critical" }, tradeHash = 737908626, },
	["CorruptionLifeGainedFromEnemyDeath1"] = { type = "Corrupted", affix = "", "Gain (20-25) Life per Enemy Killed", statOrder = { 970 }, level = 1, group = "LifeGainedFromEnemyDeath", weightKey = { "quiver", "staff", "wand", "default" }, weightVal = { 1, 1, 1,  0 }, modTags = { "resource", "life" }, tradeHash = 3695891184, },
	["CorruptionManaGainedFromEnemyDeath1"] = { type = "Corrupted", affix = "", "Gain (10-15) Mana per Enemy Killed", statOrder = { 975 }, level = 1, group = "ManaGainedFromEnemyDeath", weightKey = { "quiver", "staff", "wand", "default" }, weightVal = { 1, 1, 1,  0 }, modTags = { "resource", "mana" }, tradeHash = 1368271171, },
	["CorruptionIncreasedCastSpeed1"] = { type = "Corrupted", affix = "", "(10-15)% increased Cast Speed", statOrder = { 940 }, level = 1, group = "IncreasedCastSpeed", weightKey = { "staff", "wand", "default" }, weightVal = { 1, 1,  0 }, modTags = { "caster", "speed" }, tradeHash = 2891184298, },
	["CorruptionEnergyShieldDelay1"] = { type = "Corrupted", affix = "", "(20-30)% faster start of Energy Shield Recharge", statOrder = { 1379 }, level = 1, group = "EnergyShieldDelay", weightKey = { "focus", "default" }, weightVal = { 1,  0 }, modTags = { "energy_shield", "defences" }, tradeHash = 1782086450, },
	["CorruptionAlliesInPresenceAllDamage1"] = { type = "Corrupted", affix = "", "Allies in your Presence deal (20-30)% increased Damage", statOrder = { 884 }, level = 1, group = "AlliesInPresenceAllDamage", weightKey = { "sceptre", "default" }, weightVal = { 1,  0 }, modTags = { "damage" }, tradeHash = 1798257884, },
	["CorruptionAlliesInPresenceIncreasedAttackSpeed1"] = { type = "Corrupted", affix = "", "Allies in your Presence have (5-10)% increased Attack Speed", statOrder = { 893 }, level = 1, group = "AlliesInPresenceIncreasedAttackSpeed", weightKey = { "sceptre", "default" }, weightVal = { 1,  0 }, modTags = { "attack", "speed" }, tradeHash = 1998951374, },
	["CorruptionAlliesInPresenceIncreasedCastSpeed1"] = { type = "Corrupted", affix = "", "Allies in your Presence have (5-10)% increased Cast Speed", statOrder = { 894 }, level = 1, group = "AlliesInPresenceIncreasedCastSpeed", weightKey = { "sceptre", "default" }, weightVal = { 1,  0 }, modTags = { "caster", "speed" }, tradeHash = 289128254, },
	["CorruptionAlliesInPresenceCriticalStrikeMultiplier1"] = { type = "Corrupted", affix = "", "Allies in your Presence have (10-15)% increased Critical Damage Bonus", statOrder = { 892 }, level = 1, group = "AlliesInPresenceCriticalStrikeMultiplier", weightKey = { "sceptre", "default" }, weightVal = { 1,  0 }, modTags = { "damage", "critical" }, tradeHash = 3057012405, },
	["CorruptionChanceToPierce1"] = { type = "Corrupted", affix = "", "(20-30)% chance to Pierce an Enemy", statOrder = { 997 }, level = 1, group = "ChanceToPierce", weightKey = { "quiver", "default" }, weightVal = { 1,  0 }, modTags = {  }, tradeHash = 2321178454, },
	["CorruptionChainFromTerrain1"] = { type = "Corrupted", affix = "", "Projectiles have (10-20)% chance to Chain an additional time from terrain", statOrder = { 8581 }, level = 1, group = "ChainFromTerrain", weightKey = { "quiver", "default" }, weightVal = { 1,  0 }, modTags = {  }, tradeHash = 4081947835, },
	["CorruptionJewelStrength1"] = { type = "Corrupted", affix = "", "+(4-6) to Strength", statOrder = { 945 }, level = 1, group = "Strength", weightKey = { "jewel", "default" }, weightVal = { 1,  0 }, modTags = { "attribute" }, tradeHash = 4080418644, },
	["CorruptionJewelDexterity1"] = { type = "Corrupted", affix = "", "+(4-6) to Dexterity", statOrder = { 946 }, level = 1, group = "Dexterity", weightKey = { "jewel", "default" }, weightVal = { 1,  0 }, modTags = { "attribute" }, tradeHash = 3261801346, },
	["CorruptionJewelIntelligence1"] = { type = "Corrupted", affix = "", "+(4-6) to Intelligence", statOrder = { 947 }, level = 1, group = "Intelligence", weightKey = { "jewel", "default" }, weightVal = { 1,  0 }, modTags = { "attribute" }, tradeHash = 328541901, },
	["CorruptionJewelFireResist1"] = { type = "Corrupted", affix = "", "+(5-10)% to Fire Resistance", statOrder = { 956 }, level = 1, group = "FireResistance", weightKey = { "jewel", "default" }, weightVal = { 1,  0 }, modTags = { "elemental", "fire", "resistance" }, tradeHash = 3372524247, },
	["CorruptionJewelColdResist1"] = { type = "Corrupted", affix = "", "+(5-10)% to Cold Resistance", statOrder = { 957 }, level = 1, group = "ColdResistance", weightKey = { "jewel", "default" }, weightVal = { 1,  0 }, modTags = { "elemental", "cold", "resistance" }, tradeHash = 4220027924, },
	["CorruptionJewelLightningResist1"] = { type = "Corrupted", affix = "", "+(5-10)% to Lightning Resistance", statOrder = { 958 }, level = 1, group = "LightningResistance", weightKey = { "jewel", "default" }, weightVal = { 1,  0 }, modTags = { "elemental", "lightning", "resistance" }, tradeHash = 1671376347, },
	["CorruptionJewelChaosResist1"] = { type = "Corrupted", affix = "", "+(3-7)% to Chaos Resistance", statOrder = { 959 }, level = 1, group = "ChaosResistance", weightKey = { "jewel", "default" }, weightVal = { 1,  0 }, modTags = { "chaos", "resistance" }, tradeHash = 2923486259, },
	["CorruptionJewelMaimImmunity1"] = { type = "Corrupted", affix = "", "Immune to Maim", statOrder = { 6624 }, level = 1, group = "ImmuneToMaim", weightKey = { "jewel", "default" }, weightVal = { 1,  0 }, modTags = {  }, tradeHash = 3429557654, },
	["CorruptionJewelHinderImmunity1"] = { type = "Corrupted", affix = "", "You cannot be Hindered", statOrder = { 9482 }, level = 1, group = "YouCannotBeHindered", weightKey = { "jewel", "default" }, weightVal = { 1,  0 }, modTags = { "blue_herring" }, tradeHash = 721014846, },
	["CorruptionJewelCorruptedBloodImmunity1"] = { type = "Corrupted", affix = "", "Corrupted Blood cannot be inflicted on you", statOrder = { 4832 }, level = 1, group = "CorruptedBloodImmunity", weightKey = { "jewel", "default" }, weightVal = { 1,  0 }, modTags = { "bleed", "physical", "ailment" }, tradeHash = 1658498488, },
	["CorruptionJewelBlindImmunity1"] = { type = "Corrupted", affix = "", "Cannot be Blinded", statOrder = { 2638 }, level = 1, group = "ImmunityToBlind", weightKey = { "jewel", "default" }, weightVal = { 1,  0 }, modTags = {  }, tradeHash = 1436284579, },
	["SpecialCorruptionWarcrySpeed1"] = { type = "SpecialCorrupted", affix = "", "(15-25)% increased Warcry Speed", statOrder = { 2920 }, level = 1, group = "WarcrySpeed", weightKey = { "helmet", "default" }, weightVal = { 1,  0 }, modTags = { "speed" }, tradeHash = 1316278494, },
	["SpecialCorruptionCurseEffect1"] = { type = "SpecialCorrupted", affix = "", "(5-10)% increased Curse Magnitudes", statOrder = { 2288 }, level = 1, group = "CurseEffectiveness", weightKey = { "helmet", "default" }, weightVal = { 1,  0 }, modTags = { "caster", "curse" }, tradeHash = 2353576063, },
	["SpecialCorruptionAreaOfEffect1"] = { type = "SpecialCorrupted", affix = "", "(15-25)% increased Area of Effect", statOrder = { 1571 }, level = 1, group = "AreaOfEffect", weightKey = { "helmet", "default" }, weightVal = { 1,  0 }, modTags = {  }, tradeHash = 280731498, },
	["SpecialCorruptionPresenceRadius1"] = { type = "SpecialCorrupted", affix = "", "(15-25)% increased Presence Area of Effect", statOrder = { 998 }, level = 1, group = "PresenceRadius", weightKey = { "helmet", "default" }, weightVal = { 1,  0 }, modTags = {  }, tradeHash = 101878827, },
	["SpecialCorruptionCooldownRecovery1"] = { type = "SpecialCorrupted", affix = "", "(8-12)% increased Cooldown Recovery Rate", statOrder = { 4504 }, level = 1, group = "GlobalCooldownRecovery", weightKey = { "helmet", "default" }, weightVal = { 1,  0 }, modTags = {  }, tradeHash = 1004011302, },
	["SpecialCorruptionSkillEffectDuration1"] = { type = "SpecialCorrupted", affix = "", "(15-25)% increased Skill Effect Duration", statOrder = { 1586 }, level = 1, group = "SkillEffectDuration", weightKey = { "helmet", "default" }, weightVal = { 1,  0 }, modTags = {  }, tradeHash = 3377888098, },
	["SpecialCorruptionEnergyGeneration1"] = { type = "SpecialCorrupted", affix = "", "Meta Skills gain (20-30)% increased Energy", statOrder = { 5847 }, level = 1, group = "EnergyGeneration", weightKey = { "helmet", "default" }, weightVal = { 1,  0 }, modTags = {  }, tradeHash = 4236566306, },
	["SpecialCorruptionDamageGainedAsChaos1"] = { type = "SpecialCorrupted", affix = "", "Gain (5-8)% of Damage as Extra Chaos Damage", statOrder = { 1620 }, level = 1, group = "DamageGainedAsChaos", weightKey = { "helmet", "default" }, weightVal = { 1,  0 }, modTags = { "chaos_damage", "damage", "chaos" }, tradeHash = 3398787959, },
}