-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Chains once per Frenzy Charge Consumed"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Chains {0} times per Frenzy Charge Consumed"
			}
		},
		stats={
			[1]="spiral_volley_X_chains_per_frenzy_charge_consumed"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Damage per Frenzy Charge Consumed"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Damage per Frenzy Charge Consumed"
			}
		},
		stats={
			[1]="spiral_volley_damage_+%_final_per_frenzy_charge_consumed"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Damage if a Frenzy Charge was Consumed"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Damage if a Frenzy Charge was Consumed"
			}
		},
		stats={
			[1]="spiral_volley_damage_+%_final_when_frenzy_charges_consumed"
		}
	},
	parent="skill_stat_descriptions",
	["spiral_volley_X_chains_per_frenzy_charge_consumed"]=1,
	["spiral_volley_damage_+%_final_per_frenzy_charge_consumed"]=2,
	["spiral_volley_damage_+%_final_when_frenzy_charges_consumed"]=3
}