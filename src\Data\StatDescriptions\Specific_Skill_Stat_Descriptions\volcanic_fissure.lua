-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="virtual_number_of_marks_allowed_per_type"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d} second to Fissure duration"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d} seconds to Fissure duration"
			},
			[3]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fissure duration is {0} second"
			},
			[4]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fissure duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration",
			[2]="quality_display_base_skill_effect_duration_is_gem"
		}
	},
	[3]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Limit {0} Fissure"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Limit {0} Fissures"
			}
		},
		stats={
			[1]="volcanic_fissure_base_maximum_number_of_fissures"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fissure Limit@{0}"
			}
		},
		stats={
			[1]="volcanic_fissure_maximum_number_of_fissures"
		}
	},
	["base_skill_effect_duration"]=2,
	parent="skill_stat_descriptions",
	["quality_display_base_skill_effect_duration_is_gem"]=2,
	["skill_effect_duration"]=3,
	["virtual_number_of_marks_allowed_per_type"]=1,
	["volcanic_fissure_base_maximum_number_of_fissures"]=4,
	["volcanic_fissure_maximum_number_of_fissures"]=5
}