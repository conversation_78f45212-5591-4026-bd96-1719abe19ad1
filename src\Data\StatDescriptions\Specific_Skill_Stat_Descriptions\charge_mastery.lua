-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Critical Hit Chance while\nyou have a Power Charge"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Critical Hit Chance while\nyou have a Power Charge"
			}
		},
		stats={
			[1]="charge_mastery_crit_chance_+%_final_with_power_charges"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Defences while you have an Endurance Charge"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Defences while you have an Endurance Charge"
			}
		},
		stats={
			[1]="charge_mastery_defences_+%_final_with_endurance_charges"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Skill Speed while you have a Frenzy Charge"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Skill Speed while you have a Frenzy Charge"
			}
		},
		stats={
			[1]="charge_mastery_skill_speed_+%_final_with_frenzy_charges"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Consumes one of each Charge every second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Consumes one of each Charge every {0} seconds"
			}
		},
		stats={
			[1]="consume_frenzy_power_and_endurance_charge_every_x_ms"
		}
	},
	["charge_mastery_crit_chance_+%_final_with_power_charges"]=1,
	["charge_mastery_defences_+%_final_with_endurance_charges"]=2,
	["charge_mastery_skill_speed_+%_final_with_frenzy_charges"]=3,
	["consume_frenzy_power_and_endurance_charge_every_x_ms"]=4,
	parent="skill_stat_descriptions"
}