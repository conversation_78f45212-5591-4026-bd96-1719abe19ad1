-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="skill_can_fire_arrows"
		}
	},
	[2]={
		stats={
			[1]="cannot_pierce"
		}
	},
	[3]={
		stats={
			[1]="base_projectiles_cannot_chain"
		}
	},
	[4]={
		stats={
			[1]="total_number_of_projectiles_to_fire"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Spear cannot Pierce, Fork, Chain or Return\nModifiers to the number of Projectiles fired only affect\nthe maximum number of lightning bolt Projectiles"
			}
		},
		stats={
			[1]="lightning_burst_display"
		}
	},
	[6]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Splits towards {0} targets when Consuming a Frenzy Charge"
			}
		},
		stats={
			[1]="lightning_spear_additional_number_to_split_when_charged"
		}
	},
	[7]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Deals {0}% more damage when Consuming a Frenzy Charge"
			}
		},
		stats={
			[1]="lightning_spear_damage_+%_final_when_charged"
		}
	},
	["base_projectiles_cannot_chain"]=3,
	["cannot_pierce"]=2,
	["lightning_burst_display"]=5,
	["lightning_spear_additional_number_to_split_when_charged"]=6,
	["lightning_spear_damage_+%_final_when_charged"]=7,
	parent="skill_stat_descriptions",
	["skill_can_fire_arrows"]=1,
	["total_number_of_projectiles_to_fire"]=4
}