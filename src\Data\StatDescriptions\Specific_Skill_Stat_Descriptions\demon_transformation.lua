-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Cast Speed while in Demon Form"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% decreased Cast Speed while in Demon Form"
			}
		},
		stats={
			[1]="demon_form_grants_cast_speed_+%"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0:+d} to Level of all Spell Skills while in Demon Form"
			}
		},
		stats={
			[1]="demon_form_grants_spell_gem_level_+"
		}
	},
	[3]={
		[1]={
			[1]={
				[1]={
					k="per_minute_to_per_second",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Lose {0}% of maximum Life per second per Demonflame"
			}
		},
		stats={
			[1]="demon_form_life_loss_%_per_minute_per_stack"
		}
	},
	[4]={
		[1]={
			[1]={
				[1]={
					k="per_minute_to_per_second",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} Life Loss per second per Demonflame"
			}
		},
		stats={
			[1]="demon_form_life_loss_per_minute_per_stack"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Deal {0}% increased Spell damage per Demonflame"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Deal {0}% reduced Spell damage per Demonflame"
			}
		},
		stats={
			[1]="demon_form_spell_damage_+%_per_stack"
		}
	},
	[6]={
		[1]={
		},
		stats={
			[1]="max_demon_form_stacks"
		}
	},
	["demon_form_grants_cast_speed_+%"]=1,
	["demon_form_grants_spell_gem_level_+"]=2,
	["demon_form_life_loss_%_per_minute_per_stack"]=3,
	["demon_form_life_loss_per_minute_per_stack"]=4,
	["demon_form_spell_damage_+%_per_stack"]=5,
	["max_demon_form_stacks"]=6,
	parent="skill_stat_descriptions"
}