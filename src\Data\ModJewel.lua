-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	["JewelAccuracy"] = { type = "Prefix", affix = "Accurate", "(5-10)% increased Accuracy Rating", statOrder = { 1276 }, level = 1, group = "IncreasedAccuracyPercent", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack" }, tradeHash = 624954515, },
	["JewelAilmentChance"] = { type = "Suffix", affix = "of Ailing", "(5-15)% increased chance to inflict Ailments", statOrder = { 4155 }, level = 1, group = "AilmentChance", weightKey = { "dexjewel", "intjewel", "default" }, weightVal = { 1, 1, 0 }, modTags = { "ailment" }, tradeHash = 1772247089, },
	["JewelAilmentEffect"] = { type = "Prefix", affix = "Acrimonious", "(5-15)% increased Magnitude of Ailments you inflict", statOrder = { 4156 }, level = 1, group = "AilmentEffect", weightKey = { "dexjewel", "intjewel", "default" }, weightVal = { 1, 1, 0 }, modTags = { "damage", "ailment" }, tradeHash = 1303248024, },
	["JewelAilmentThreshold"] = { type = "Suffix", affix = "of Enduring", "(10-20)% increased Elemental Ailment Threshold", statOrder = { 4162 }, level = 1, group = "IncreasedAilmentThreshold", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 3544800472, },
	["JewelAreaofEffect"] = { type = "Prefix", affix = "Blasting", "(4-6)% increased Area of Effect", statOrder = { 1571 }, level = 1, group = "AreaOfEffect", weightKey = { "strjewel", "intjewel", "default" }, weightVal = { 1, 1, 0 }, modTags = {  }, tradeHash = 280731498, },
	["JewelArmour"] = { type = "Prefix", affix = "Armoured", "(10-20)% increased Armour", statOrder = { 867 }, level = 1, group = "GlobalPhysicalDamageReductionRatingPercent", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "armour", "defences" }, tradeHash = 2866361420, },
	["JewelArmourBreak"] = { type = "Prefix", affix = "Shattering", "Break (5-15)% increased Armour", statOrder = { 4275 }, level = 1, group = "ArmourBreak", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1776411443, },
	["JewelArmourBreakDuration"] = { type = "Suffix", affix = "Resonating", "(10-20)% increased Armour Break Duration", statOrder = { 4278 }, level = 1, group = "ArmourBreakDuration", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2215233444, },
	["JewelAttackCriticalChance"] = { type = "Suffix", affix = "of Deadliness", "(6-16)% increased Critical Hit Chance for Attacks", statOrder = { 932 }, level = 1, group = "AttackCriticalStrikeChance", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "critical" }, tradeHash = 2194114101, },
	["JewelAttackCriticalDamage"] = { type = "Suffix", affix = "of Demolishing", "(10-20)% increased Critical Damage Bonus for Attack Damage", statOrder = { 936 }, level = 1, group = "AttackCriticalStrikeMultiplier", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "critical" }, tradeHash = 3714003708, },
	["JewelAttackDamage"] = { type = "Prefix", affix = "Combat", "(5-15)% increased Attack Damage", statOrder = { 1101 }, level = 1, group = "AttackDamage", weightKey = { "strjewel", "dexjewel", "default" }, weightVal = { 1, 1, 0 }, modTags = { "damage", "attack" }, tradeHash = 2843214518, },
	["JewelAttackSpeed"] = { type = "Suffix", affix = "of Alacrity", "(2-4)% increased Attack Speed", statOrder = { 939 }, level = 1, group = "IncreasedAttackSpeed", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "speed" }, tradeHash = 681332047, },
	["JewelAuraEffect"] = { type = "Prefix", affix = "Commanding", "Aura Skills have (3-7)% increased Magnitudes", statOrder = { 2498 }, level = 1, group = "AuraEffectForJewel", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "aura" }, tradeHash = 315791320, },
	["JewelAxeDamage"] = { type = "Prefix", affix = "Sinister", "(5-15)% increased Damage with Axes", statOrder = { 1178 }, level = 1, group = "IncreasedAxeDamageForJewel", weightKey = { "default" }, weightVal = { 0 }, modTags = { "damage", "attack" }, tradeHash = 3314142259, },
	["JewelAxeSpeed"] = { type = "Suffix", affix = "of Cleaving", "(2-4)% increased Attack Speed with Axes", statOrder = { 1263 }, level = 1, group = "AxeAttackSpeedForJewel", weightKey = { "default" }, weightVal = { 0 }, modTags = { "attack", "speed" }, tradeHash = 3550868361, },
	["JewelBleedingChance"] = { type = "Prefix", affix = "Bleeding", "(3-7)% chance to inflict Bleeding on Hit", statOrder = { 4498 }, level = 1, group = "BaseChanceToBleed", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2174054121, },
	["JewelBleedingDuration"] = { type = "Suffix", affix = "of Haemophilia", "(5-10)% increased Bleeding Duration", statOrder = { 4491 }, level = 1, group = "BleedDuration", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "bleed", "physical", "attack", "ailment" }, tradeHash = 1459321413, },
	["JewelBlindEffect"] = { type = "Prefix", affix = "Stifling", "(5-10)% increased Blind Effect", statOrder = { 4715 }, level = 1, group = "BlindEffect", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1585769763, },
	["JewelBlindonHit"] = { type = "Suffix", affix = "of Blinding", "(3-7)% chance to Blind Enemies on Hit with Attacks", statOrder = { 4428 }, level = 1, group = "AttacksBlindOnHitChance", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack" }, tradeHash = 318953428, },
	["JewelBlock"] = { type = "Prefix", affix = "Protecting", "(3-7)% increased Block chance", statOrder = { 1074 }, level = 1, group = "IncreasedBlockChance", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "block" }, tradeHash = 4147897060, },
	["JewelDamageVsRareOrUnique"] = { type = "Prefix", affix = "Slaying", "(10-20)% increased Damage with Hits against Rare and Unique Enemies", statOrder = { 2855 }, level = 1, group = "DamageVsRareOrUnique", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, tradeHash = 1852872083, },
	["JewelBowAccuracyRating"] = { type = "Prefix", affix = "Precise", "(5-15)% increased Accuracy Rating with Bows", statOrder = { 1285 }, level = 1, group = "BowIncreasedAccuracyRating", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack" }, tradeHash = 169946467, },
	["JewelBowDamage"] = { type = "Prefix", affix = "Perforating", "(6-16)% increased Damage with Bows", statOrder = { 1198 }, level = 1, group = "IncreasedBowDamageForJewel", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, tradeHash = 4188894176, },
	["JewelBowSpeed"] = { type = "Suffix", affix = "of Nocking", "(2-4)% increased Attack Speed with Bows", statOrder = { 1268 }, level = 1, group = "BowAttackSpeedForJewel", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "speed" }, tradeHash = 3759735052, },
	["JewelCastSpeed"] = { type = "Suffix", affix = "of Enchanting", "(2-4)% increased Cast Speed", statOrder = { 940 }, level = 1, group = "IncreasedCastSpeed", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "caster", "speed" }, tradeHash = 2891184298, },
	["JewelChainFromTerrain"] = { type = "Suffix", affix = "of Chaining", "Projectiles have (3-5)% chance to Chain an additional time from terrain", statOrder = { 8581 }, level = 1, group = "ChainFromTerrain", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 4081947835, },
	["JewelCharmDuration"] = { type = "Suffix", affix = "of the Woodland", "(5-15)% increased Charm Effect Duration", statOrder = { 881 }, level = 1, group = "CharmDuration", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1389754388, },
	["JewelCharmChargesGained"] = { type = "Suffix", affix = "of the Thicker", "(5-15)% increased Charm Charges gained", statOrder = { 5146 }, level = 1, group = "CharmChargesGained", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 3585532255, },
	["JewelCharmDamageWhileUsing"] = { type = "Prefix", affix = "Verdant", "(10-20)% increased Damage while you have an active Charm", statOrder = { 5511 }, level = 1, group = "CharmDamageWhileUsing", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 627767961, },
	["JewelChaosDamage"] = { type = "Prefix", affix = "Chaotic", "(6-12)% increased Chaos Damage", statOrder = { 860 }, level = 1, group = "IncreasedChaosDamage", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "chaos_damage", "damage", "chaos" }, tradeHash = 736967255, },
	["JewelChillDuration"] = { type = "Suffix", affix = "of Frost", "(15-25)% increased Chill Duration on Enemies", statOrder = { 1553 }, level = 1, group = "IncreasedChillDuration", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental", "cold", "ailment" }, tradeHash = 3485067555, },
	["JewelColdDamage"] = { type = "Prefix", affix = "Chilling", "(5-15)% increased Cold Damage", statOrder = { 858 }, level = 1, group = "ColdDamagePercentage", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental_damage", "damage", "elemental", "cold" }, tradeHash = 3291658075, },
	["JewelColdPenetration"] = { type = "Prefix", affix = "Numbing", "Damage Penetrates (5-10)% Cold Resistance", statOrder = { 2644 }, level = 1, group = "ColdResistancePenetration", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental_damage", "damage", "elemental", "cold" }, tradeHash = 3417711605, },
	["JewelCooldownSpeed"] = { type = "Suffix", affix = "of Chronomancy", "(3-5)% increased Cooldown Recovery Rate", statOrder = { 4504 }, level = 1, group = "GlobalCooldownRecovery", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1004011302, },
	["JewelCorpses"] = { type = "Prefix", affix = "Necromantic", "(10-20)% increased Damage if you have Consumed a Corpse Recently", statOrder = { 3850 }, level = 1, group = "DamageIfConsumedCorpse", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, tradeHash = 2118708619, },
	["JewelCriticalAilmentEffect"] = { type = "Prefix", affix = "Rancorous", "(10-20)% increased Magnitude of Damaging Ailments you inflict with Critical Hits", statOrder = { 5327 }, level = 1, group = "CriticalAilmentEffect", weightKey = { "dexjewel", "intjewel", "default" }, weightVal = { 1, 1, 0 }, modTags = { "damage", "critical", "ailment" }, tradeHash = 440490623, },
	["JewelCriticalChance"] = { type = "Suffix", affix = "of Annihilation", "(5-15)% increased Critical Hit Chance", statOrder = { 931 }, level = 1, group = "CriticalStrikeChance", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "critical" }, tradeHash = 587431675, },
	["JewelCriticalDamage"] = { type = "Suffix", affix = "of Potency", "(10-20)% increased Critical Damage Bonus", statOrder = { 935 }, level = 1, group = "CriticalStrikeMultiplier", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "critical" }, tradeHash = 3556824919, },
	["JewelSpellCriticalDamage"] = { type = "Suffix", affix = "of Unmaking", "(10-20)% increased Critical Spell Damage Bonus", statOrder = { 937 }, level = 1, group = "SpellCritMultiplierForJewel", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "caster_damage", "damage", "caster", "critical" }, tradeHash = 274716455, },
	["JewelCrossbowDamage"] = { type = "Prefix", affix = "Bolting", "(6-16)% increased Damage with Crossbows", statOrder = { 3897 }, level = 1, group = "CrossbowDamage", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, tradeHash = 427684353, },
	["JewelCrossbowReloadSpeed"] = { type = "Suffix", affix = "of Reloading", "(10-15)% increased Crossbow Reload Speed", statOrder = { 8749 }, level = 1, group = "CrossbowReloadSpeed", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "speed" }, tradeHash = 3192728503, },
	["JewelCrossbowSpeed"] = { type = "Suffix", affix = "of Rapidity", "(2-4)% increased Attack Speed with Crossbows", statOrder = { 3901 }, level = 1, group = "CrossbowSpeed", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "speed" }, tradeHash = 1135928777, },
	["JewelCurseArea"] = { type = "Prefix", affix = "Expanding", "(15-25)% increased Area of Effect of Curses", statOrder = { 1893 }, level = 1, group = "CurseAreaOfEffect", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "caster", "curse" }, tradeHash = 153777645, },
	["JewelCurseDelay"] = { type = "Suffix", affix = "of Chanting", "(5-15)% faster Curse Activation", statOrder = { 5425 }, level = 1, group = "CurseDelay", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "curse" }, tradeHash = 1104825894, },
	["JewelCurseDuration"] = { type = "Suffix", affix = "of Continuation", "(15-25)% increased Curse Duration", statOrder = { 1480 }, level = 1, group = "BaseCurseDuration", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "curse" }, tradeHash = 3824372849, },
	["JewelCurseEffect"] = { type = "Prefix", affix = "Hexing", "(2-4)% increased Curse Magnitudes", statOrder = { 2288 }, level = 1, group = "CurseEffectivenessForJewel", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "caster", "curse" }, tradeHash = 2353576063, },
	["JewelDaggerCriticalChance"] = { type = "Suffix", affix = "of Backstabbing", "(6-16)% increased Critical Hit Chance with Daggers", statOrder = { 1307 }, level = 1, group = "CritChanceWithDaggerForJewel", weightKey = { "default" }, weightVal = { 0 }, modTags = { "attack", "critical" }, tradeHash = 4018186542, },
	["JewelDaggerDamage"] = { type = "Prefix", affix = "Lethal", "(6-16)% increased Damage with Daggers", statOrder = { 1190 }, level = 1, group = "IncreasedDaggerDamageForJewel", weightKey = { "default" }, weightVal = { 0 }, modTags = { "damage", "attack" }, tradeHash = 3586984690, },
	["JewelDaggerSpeed"] = { type = "Suffix", affix = "of Slicing", "(2-4)% increased Attack Speed with Daggers", statOrder = { 1266 }, level = 1, group = "DaggerAttackSpeedForJewel", weightKey = { "default" }, weightVal = { 0 }, modTags = { "attack", "speed" }, tradeHash = 2538566497, },
	["JewelDamagefromMana"] = { type = "Suffix", affix = "of Mind", "(2-4)% of Damage is taken from Mana before Life", statOrder = { 2384 }, level = 1, group = "DamageRemovedFromManaBeforeLife", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life", "mana" }, tradeHash = 458438597, },
	["JewelDamagevsArmourBrokenEnemies"] = { type = "Prefix", affix = "Exploiting", "(15-25)% increased Damage against Enemies with Fully Broken Armour", statOrder = { 5445 }, level = 1, group = "DamagevsArmourBrokenEnemies", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, tradeHash = 2301718443, },
	["JewelDamagingAilmentDuration"] = { type = "Suffix", affix = "of Suffusion", "(5-10)% increased Duration of Damaging Ailments on Enemies", statOrder = { 5550 }, level = 1, group = "DamagingAilmentDuration", weightKey = { "dexjewel", "intjewel", "default" }, weightVal = { 1, 1, 0 }, modTags = { "ailment" }, tradeHash = 1829102168, },
	["JewelDazeBuildup"] = { type = "Suffix", affix = "of Dazing", "(10-20)% increased Daze Buildup", statOrder = { 5560 }, level = 1, group = "DazeBuildup", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1949833742, },
	["JewelDebuffExpiry"] = { type = "Suffix", affix = "of Diminishing", "Debuffs on you expire (5-10)% faster", statOrder = { 5583 }, level = 1, group = "DebuffTimePassed", weightKey = { "dexjewel", "intjewel", "default" }, weightVal = { 1, 1, 0 }, modTags = {  }, tradeHash = 1238227257, },
	["JewelElementalAilmentDuration"] = { type = "Suffix", affix = "of Suffering", "(5-10)% increased Duration of Ignite, Shock and Chill on Enemies", statOrder = { 6598 }, level = 1, group = "ElementalAilmentDuration", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental", "ailment" }, tradeHash = 1062710370, },
	["JewelElementalDamage"] = { type = "Prefix", affix = "Prismatic", "(5-15)% increased Elemental Damage", statOrder = { 1668 }, level = 1, group = "ElementalDamagePercent", weightKey = { "strjewel", "dexjewel", "intjewel", "default" }, weightVal = { 1, 1, 1, 0 }, modTags = { "elemental_damage", "damage", "elemental" }, tradeHash = 3141070085, },
	["JewelEmpoweredAttackDamage"] = { type = "Prefix", affix = "Empowering", "Empowered Attacks deal (10-20)% increased Damage", statOrder = { 5778 }, level = 1, group = "ExertedAttackDamage", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, tradeHash = 1569101201, },
	["JewelEnergy"] = { type = "Suffix", affix = "of Generation", "Meta Skills gain (4-8)% increased Energy", statOrder = { 5847 }, level = 1, group = "EnergyGeneration", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 4236566306, },
	["JewelEnergyShield"] = { type = "Prefix", affix = "Shimmering", "(10-20)% increased maximum Energy Shield", statOrder = { 871 }, level = 1, group = "GlobalEnergyShieldPercent", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "energy_shield", "defences" }, tradeHash = 2482852589, },
	["JewelEnergyShieldDelay"] = { type = "Prefix", affix = "Serene", "(10-15)% faster start of Energy Shield Recharge", statOrder = { 1379 }, level = 1, group = "EnergyShieldDelay", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "energy_shield", "defences" }, tradeHash = 1782086450, },
	["JewelEnergyShieldRecharge"] = { type = "Prefix", affix = "Fevered", "(10-20)% increased Energy Shield Recharge Rate", statOrder = { 962 }, level = 1, group = "EnergyShieldRegeneration", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "energy_shield", "defences" }, tradeHash = 2339757871, },
	["JewelEvasion"] = { type = "Prefix", affix = "Evasive", "(10-20)% increased Evasion Rating", statOrder = { 869 }, level = 1, group = "GlobalEvasionRatingPercent", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "evasion", "defences" }, tradeHash = 2106365538, },
	["JewelFasterAilments"] = { type = "Suffix", affix = "of Decrepifying", "Damaging Ailments deal damage (3-7)% faster", statOrder = { 5552 }, level = 1, group = "FasterAilmentDamageForJewel", weightKey = { "dexjewel", "intjewel", "default" }, weightVal = { 1, 1, 0 }, modTags = { "ailment" }, tradeHash = 538241406, },
	["JewelFireDamage"] = { type = "Prefix", affix = "Flaming", "(5-15)% increased Fire Damage", statOrder = { 857 }, level = 1, group = "FireDamagePercentage", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental_damage", "damage", "elemental", "fire" }, tradeHash = 3962278098, },
	["JewelFirePenetration"] = { type = "Prefix", affix = "Searing", "Damage Penetrates (5-10)% Fire Resistance", statOrder = { 2643 }, level = 1, group = "FireResistancePenetration", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental_damage", "damage", "elemental", "fire" }, tradeHash = 2653955271, },
	["JewelFlailCriticalChance"] = { type = "Suffix", affix = "of Thrashing", "(6-16)% increased Critical Hit Chance with Flails", statOrder = { 3891 }, level = 1, group = "FlailCriticalChance", weightKey = { "default" }, weightVal = { 0 }, modTags = { "attack", "critical" }, tradeHash = 1484710594, },
	["JewelFlailDamage"] = { type = "Prefix", affix = "Flailing", "(6-16)% increased Damage with Flails", statOrder = { 3886 }, level = 1, group = "FlailDamage", weightKey = { "default" }, weightVal = { 0 }, modTags = { "damage", "attack" }, tradeHash = 1731242173, },
	["JewelFlaskChargesGained"] = { type = "Suffix", affix = "of Gathering", "(5-10)% increased Flask Charges gained", statOrder = { 6050 }, level = 1, group = "IncreasedFlaskChargesGained", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 1836676211, },
	["JewelFlaskDuration"] = { type = "Suffix", affix = "of Prolonging", "(5-10)% increased Flask Effect Duration", statOrder = { 882 }, level = 1, group = "FlaskDuration", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3741323227, },
	["JewelFocusEnergyShield"] = { type = "Prefix", affix = "Focusing", "(30-50)% increased Energy Shield from Equipped Focus", statOrder = { 5857 }, level = 1, group = "FocusEnergyShield", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "energy_shield", "defences" }, tradeHash = 3174700878, },
	["JewelForkingProjectiles"] = { type = "Suffix", affix = "of Forking", "Projectiles have (10-15)% chance for an additional Projectile when Forking", statOrder = { 5064 }, level = 1, group = "ForkingProjectiles", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 3003542304, },
	["JewelFreezeAmount"] = { type = "Suffix", affix = "of Freezing", "(10-20)% increased Freeze Buildup", statOrder = { 986 }, level = 1, group = "FreezeDamageIncrease", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 473429811, },
	["JewelFreezeThreshold"] = { type = "Suffix", affix = "of Snowbreathing", "(18-32)% increased Freeze Threshold", statOrder = { 2915 }, level = 1, group = "FreezeThreshold", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, tradeHash = 3780644166, },
	["JewelHeraldDamage"] = { type = "Prefix", affix = "Heralding", "Herald Skills deal (15-25)% increased Damage", statOrder = { 5516 }, level = 1, group = "HeraldDamage", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 21071013, },
	["JewelIgniteChance"] = { type = "Suffix", affix = "of Ignition", "(10-20)% increased chance to Ignite", statOrder = { 984 }, level = 1, group = "IgniteChanceIncrease", weightKey = { "strjewel", "intjewel", "default" }, weightVal = { 1, 1, 0 }, modTags = {  }, tradeHash = 2968503605, },
	["JewelIgniteEffect"] = { type = "Prefix", affix = "Burning", "(5-15)% increased Magnitude of Ignite you inflict", statOrder = { 6596 }, level = 1, group = "IgniteEffect", weightKey = { "strjewel", "intjewel", "default" }, weightVal = { 1, 1, 0 }, modTags = {  }, tradeHash = 3791899485, },
	["JewelIncreasedDuration"] = { type = "Suffix", affix = "of Lengthening", "(5-10)% increased Skill Effect Duration", statOrder = { 1586 }, level = 1, group = "SkillEffectDuration", weightKey = { "strjewel", "intjewel", "default" }, weightVal = { 1, 1, 0 }, modTags = {  }, tradeHash = 3377888098, },
	["JewelKnockback"] = { type = "Suffix", affix = "of Fending", "(5-15)% increased Knockback Distance", statOrder = { 1688 }, level = 1, group = "KnockbackDistance", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 565784293, },
	["JewelLifeCost"] = { type = "Suffix", affix = "of Sacrifice", "(4-6)% of Skill Mana Costs Converted to Life Costs", statOrder = { 4545 }, level = 1, group = "LifeCost", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life" }, tradeHash = 2480498143, },
	["JewelLifeFlaskRecovery"] = { type = "Suffix", affix = "of Recovery", "(5-15)% increased Life Recovery from Flasks", statOrder = { 1738 }, level = 1, group = "GlobalFlaskLifeRecovery", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "life" }, tradeHash = 821241191, },
	["JewelLifeFlaskChargeGen"] = { type = "Suffix", affix = "of Pathfinding", "(10-20)% increased Life Flask Charges gained", statOrder = { 6734 }, level = 1, group = "LifeFlaskChargePercentGeneration", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 4009879772, },
	["JewelLifeLeech"] = { type = "Suffix", affix = "of Frenzy", "(5-15)% increased amount of Life Leeched", statOrder = { 1839 }, level = 1, group = "LifeLeechAmount", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2112395885, },
	["JewelLifeonKill"] = { type = "Suffix", affix = "of Success", "Recover (1-2)% of maximum Life on Kill", statOrder = { 1451 }, level = 1, group = "MaximumLifeOnKillPercent", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life" }, tradeHash = 2023107756, },
	["JewelLifeRecoup"] = { type = "Suffix", affix = "of Infusion", "(2-3)% of Damage taken Recouped as Life", statOrder = { 965 }, level = 1, group = "LifeRecoupForJewel", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life" }, tradeHash = 1444556985, },
	["JewelLifeRegeneration"] = { type = "Suffix", affix = "of Regeneration", "(5-10)% increased Life Regeneration rate", statOrder = { 964 }, level = 1, group = "LifeRegenerationRate", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life" }, tradeHash = 44972811, },
	["JewelLightningDamage"] = { type = "Prefix", affix = "Humming", "(5-15)% increased Lightning Damage", statOrder = { 859 }, level = 1, group = "LightningDamagePercentage", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental_damage", "damage", "elemental", "lightning" }, tradeHash = 2231156303, },
	["JewelLightningPenetration"] = { type = "Prefix", affix = "Surging", "Damage Penetrates (5-10)% Lightning Resistance", statOrder = { 2645 }, level = 1, group = "LightningResistancePenetration", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental_damage", "damage", "elemental", "lightning" }, tradeHash = 818778753, },
	["JewelMaceDamage"] = { type = "Prefix", affix = "Beating", "(6-16)% increased Damage with Maces", statOrder = { 1194 }, level = 1, group = "IncreasedMaceDamageForJewel", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, tradeHash = 1181419800, },
	["JewelMaceStun"] = { type = "Suffix", affix = "of Thumping", "(15-25)% increased Stun Buildup with Maces", statOrder = { 7183 }, level = 1, group = "MaceStun", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack" }, tradeHash = 872504239, },
	["JewelManaFlaskRecovery"] = { type = "Suffix", affix = "of Quenching", "(5-15)% increased Mana Recovery from Flasks", statOrder = { 1739 }, level = 1, group = "FlaskManaRecovery", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "mana" }, tradeHash = 2222186378, },
	["JewelManaFlaskChargeGen"] = { type = "Suffix", affix = "of Fountains", "(10-20)% increased Mana Flask Charges gained", statOrder = { 7212 }, level = 1, group = "ManaFlaskChargePercentGeneration", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 3590792340, },
	["JewelManaLeech"] = { type = "Suffix", affix = "of Thirsting", "(5-15)% increased amount of Mana Leeched", statOrder = { 1841 }, level = 1, group = "ManaLeechAmount", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "mana" }, tradeHash = 2839066308, },
	["JewelManaonKill"] = { type = "Suffix", affix = "of Osmosis", "Recover (1-2)% of maximum Mana on Kill", statOrder = { 1457 }, level = 1, group = "ManaGainedOnKillPercentage", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "mana" }, tradeHash = 1604736568, },
	["JewelManaRegeneration"] = { type = "Suffix", affix = "of Energy", "(5-15)% increased Mana Regeneration Rate", statOrder = { 971 }, level = 1, group = "ManaRegeneration", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "mana" }, tradeHash = 789117908, },
	["JewelMarkCastSpeed"] = { type = "Suffix", affix = "of Targeting", "Mark Skills have (5-15)% increased Cast Speed", statOrder = { 1889 }, level = 1, group = "MarkCastSpeed", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "speed", "curse" }, tradeHash = 4189061307, },
	["JewelMarkDuration"] = { type = "Suffix", affix = "of Tracking", "Mark Skills have (18-32)% increased Skill Effect Duration", statOrder = { 7977 }, level = 1, group = "MarkDuration", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2594634307, },
	["JewelMarkEffect"] = { type = "Prefix", affix = "Marking", "(4-8)% increased Effect of your Mark Skills", statOrder = { 2290 }, level = 1, group = "MarkEffect", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "caster", "curse" }, tradeHash = 712554801, },
	["JewelMaximumColdResistance"] = { type = "Suffix", affix = "of the Kraken", "+1% to Maximum Cold Resistance", statOrder = { 952 }, level = 1, group = "MaximumColdResist", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental", "cold", "resistance" }, tradeHash = 3676141501, },
	["JewelMaximumFireResistance"] = { type = "Suffix", affix = "of the Phoenix", "+1% to Maximum Fire Resistance", statOrder = { 951 }, level = 1, group = "MaximumFireResist", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental", "fire", "resistance" }, tradeHash = 4095671657, },
	["JewelMaximumLightningResistance"] = { type = "Suffix", affix = "of the Leviathan", "+1% to Maximum Lightning Resistance", statOrder = { 953 }, level = 1, group = "MaximumLightningResistance", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental", "lightning", "resistance" }, tradeHash = 1011760251, },
	["JewelMaximumRage"] = { type = "Prefix", affix = "Angry", "+1 to Maximum Rage", statOrder = { 8640 }, level = 1, group = "MaximumRage", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1181501418, },
	["JewelMeleeDamage"] = { type = "Prefix", affix = "Clashing", "(5-15)% increased Melee Damage", statOrder = { 1132 }, level = 1, group = "MeleeDamage", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, tradeHash = 1002362373, },
	["JewelMinionAccuracy"] = { type = "Prefix", affix = "Training", "(10-20)% increased Minion Accuracy Rating", statOrder = { 8126 }, level = 1, group = "MinionAccuracyRatingForJewel", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "minion" }, tradeHash = 1718147982, },
	["JewelMinionArea"] = { type = "Prefix", affix = "Companion", "Minions have (5-10)% increased Area of Effect", statOrder = { 2683 }, level = 1, group = "MinionAreaOfEffect", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "minion" }, tradeHash = 3811191316, },
	["JewelMinionAttackandCastSpeed"] = { type = "Suffix", affix = "of Orchestration", "Minions have (2-4)% increased Attack and Cast Speed", statOrder = { 8131 }, level = 1, group = "MinionAttackSpeedAndCastSpeed", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "caster", "speed", "minion" }, tradeHash = 3091578504, },
	["JewelMinionChaosResistance"] = { type = "Suffix", affix = "of Righteousness", "Minions have +(7-13)% to Chaos Resistance", statOrder = { 2586 }, level = 1, group = "MinionChaosResistance", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "chaos", "resistance", "minion" }, tradeHash = 3837707023, },
	["JewelMinionCriticalChance"] = { type = "Suffix", affix = "of Marshalling", "Minions have (10-20)% increased Critical Hit Chance", statOrder = { 8149 }, level = 1, group = "MinionCriticalStrikeChanceIncrease", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "minion", "critical" }, tradeHash = 491450213, },
	["JewelMinionCriticalMultiplier"] = { type = "Suffix", affix = "of Gripping", "Minions have (15-25)% increased Critical Damage Bonus", statOrder = { 8151 }, level = 1, group = "MinionCriticalStrikeMultiplier", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "minion", "critical" }, tradeHash = 1854213750, },
	["JewelMinionDamage"] = { type = "Prefix", affix = "Authoritative", "Minions deal (5-15)% increased Damage", statOrder = { 1663 }, level = 1, group = "MinionDamage", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "minion" }, tradeHash = 1589917703, },
	["JewelMinionLife"] = { type = "Prefix", affix = "Fortuitous", "Minions have (5-15)% increased maximum Life", statOrder = { 961 }, level = 1, group = "MinionLife", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life", "minion" }, tradeHash = 770672621, },
	["JewelMinionPhysicalDamageReduction"] = { type = "Suffix", affix = "of Confidence", "Minions have (6-16)% additional Physical Damage Reduction", statOrder = { 1974 }, level = 1, group = "MinionPhysicalDamageReduction", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "physical", "minion" }, tradeHash = 3119612865, },
	["JewelMinionResistances"] = { type = "Suffix", affix = "of Acclimatisation", "Minions have +(5-10)% to all Elemental Resistances", statOrder = { 2585 }, level = 1, group = "MinionElementalResistance", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental", "resistance", "minion" }, tradeHash = 1423639565, },
	["JewelMinionReviveSpeed"] = { type = "Suffix", affix = "of Revival", "Minions Revive (5-15)% faster", statOrder = { 8197 }, level = 1, group = "MinionReviveSpeed", weightKey = { "default" }, weightVal = { 0 }, modTags = {  }, tradeHash = 2639966148, },
	["JewelMovementSpeed"] = { type = "Suffix", affix = "of Speed", "(1-2)% increased Movement Speed", statOrder = { 829 }, level = 1, group = "MovementVelocity", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "speed" }, tradeHash = 2250533757, },
	["JewelOfferingDuration"] = { type = "Suffix", affix = "of Offering", "Offering Skills have (15-25)% increased Duration", statOrder = { 8411 }, level = 1, group = "OfferingDuration", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2957407601, },
	["JewelOfferingLife"] = { type = "Prefix", affix = "Sacrificial", "Offerings have (15-25)% increased Maximum Life", statOrder = { 8412 }, level = 1, group = "OfferingLife", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 3787460122, },
	["JewelPhysicalDamage"] = { type = "Prefix", affix = "Sharpened", "(5-15)% increased Global Physical Damage", statOrder = { 1130 }, level = 1, group = "PhysicalDamagePercent", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "physical_damage", "damage", "physical" }, tradeHash = 1310194496, },
	["JewelPiercingProjectiles"] = { type = "Suffix", affix = "of Piercing", "(10-20)% chance to Pierce an Enemy", statOrder = { 997 }, level = 1, group = "ChanceToPierce", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2321178454, },
	["JewelPinBuildup"] = { type = "Suffix", affix = "of Pinning", "(10-20)% increased Pin Buildup", statOrder = { 6540 }, level = 1, group = "PinBuildup", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 3473929743, },
	["JewelPoisonChance"] = { type = "Suffix", affix = "of Poisoning", "(5-10)% chance to Poison on Hit", statOrder = { 2823 }, level = 1, group = "BaseChanceToPoison", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 795138349, },
	["JewelPoisonDamage"] = { type = "Prefix", affix = "Venomous", "(5-15)% increased Magnitude of Poison you inflict", statOrder = { 8536 }, level = 1, group = "PoisonEffect", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "ailment" }, tradeHash = 2487305362, },
	["JewelPoisonDuration"] = { type = "Suffix", affix = "of Infection", "(5-10)% increased Poison Duration", statOrder = { 2820 }, level = 1, group = "PoisonDuration", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "poison", "chaos", "ailment" }, tradeHash = 2011656677, },
	["JewelProjectileDamage"] = { type = "Prefix", affix = "Archer's", "(5-15)% increased Projectile Damage", statOrder = { 1682 }, level = 1, group = "ProjectileDamage", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, tradeHash = 1839076647, },
	["JewelProjectileSpeed"] = { type = "Prefix", affix = "Soaring", "(4-8)% increased Projectile Speed", statOrder = { 878 }, level = 1, group = "ProjectileSpeed", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "speed" }, tradeHash = 3759663284, },
	["JewelQuarterstaffDamage"] = { type = "Prefix", affix = "Monk's", "(6-16)% increased Damage with Quarterstaves", statOrder = { 1183 }, level = 1, group = "IncreasedStaffDamageForJewel", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, tradeHash = 4045894391, },
	["JewelQuarterstaffFreezeBuildup"] = { type = "Suffix", affix = "of Glaciers", "(10-20)% increased Freeze Buildup with Quarterstaves", statOrder = { 8628 }, level = 1, group = "QuarterstaffFreezeBuildup", weightKey = { "dexjewel", "intjewel", "default" }, weightVal = { 1, 1, 0 }, modTags = {  }, tradeHash = 1697447343, },
	["JewelQuarterstaffSpeed"] = { type = "Suffix", affix = "of Sequencing", "(2-4)% increased Attack Speed with Quarterstaves", statOrder = { 1264 }, level = 1, group = "StaffAttackSpeedForJewel", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "speed" }, tradeHash = 3283482523, },
	["JewelQuiverEffect"] = { type = "Prefix", affix = "Fletching", "(4-6)% increased bonuses gained from Equipped Quiver", statOrder = { 8636 }, level = 1, group = "QuiverModifierEffect", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1200678966, },
	["JewelRageonHit"] = { type = "Suffix", affix = "of Raging", "Gain 1 Rage on Melee Hit", statOrder = { 6240 }, level = 1, group = "RageOnHit", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2709367754, },
	["JewelRagewhenHit"] = { type = "Suffix", affix = "of Retribution", "Gain (1-3) Rage when Hit by an Enemy", statOrder = { 6242 }, level = 1, group = "GainRageWhenHit", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 3292710273, },
	["JewelShieldDefences"] = { type = "Prefix", affix = "Shielding", "(18-32)% increased Defences from Equipped Shield", statOrder = { 8825 }, level = 1, group = "ShieldArmourIncrease", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "defences" }, tradeHash = 145497481, },
	["JewelShockChance"] = { type = "Suffix", affix = "of Shocking", "(10-20)% increased chance to Shock", statOrder = { 988 }, level = 1, group = "ShockChanceIncrease", weightKey = { "dexjewel", "intjewel", "default" }, weightVal = { 1, 1, 0 }, modTags = {  }, tradeHash = 293638271, },
	["JewelShockDuration"] = { type = "Suffix", affix = "of Paralyzing", "(15-25)% increased Shock Duration", statOrder = { 1554 }, level = 1, group = "ShockDuration", weightKey = { "dexjewel", "intjewel", "default" }, weightVal = { 1, 1, 0 }, modTags = { "elemental", "lightning", "ailment" }, tradeHash = 3668351662, },
	["JewelShockEffect"] = { type = "Prefix", affix = "Jolting", "(10-15)% increased Magnitude of Shock you inflict", statOrder = { 8831 }, level = 1, group = "ShockEffect", weightKey = { "dexjewel", "intjewel", "default" }, weightVal = { 1, 1, 0 }, modTags = { "elemental", "lightning", "ailment" }, tradeHash = 2527686725, },
	["JewelSlowEffectOnSelf"] = { type = "Suffix", affix = "of Hastening", "(5-10)% reduced Slowing Potency of Debuffs on You", statOrder = { 4548 }, level = 1, group = "SlowPotency", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 924253255, },
	["JewelSpearAttackSpeed"] = { type = "Suffix", affix = "of Spearing", "(2-4)% increased Attack Speed with Spears", statOrder = { 1271 }, level = 1, group = "SpearAttackSpeed", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "speed" }, tradeHash = 1165163804, },
	["JewelSpearCriticalDamage"] = { type = "Suffix", affix = "of Hunting", "(10-20)% increased Critical Damage Bonus with Spears", statOrder = { 1336 }, level = 1, group = "SpearCriticalDamage", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "critical" }, tradeHash = 2456523742, },
	["JewelSpearDamage"] = { type = "Prefix", affix = "Spearheaded", "(6-16)% increased Damage with Spears", statOrder = { 1212 }, level = 1, group = "SpearDamage", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, tradeHash = 2696027455, },
	["JewelSpellCriticalChance"] = { type = "Suffix", affix = "of Annihilating", "(5-15)% increased Critical Hit Chance for Spells", statOrder = { 933 }, level = 1, group = "SpellCriticalStrikeChance", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "caster", "critical" }, tradeHash = 737908626, },
	["JewelSpellDamage"] = { type = "Prefix", affix = "Mystic", "(5-15)% increased Spell Damage", statOrder = { 855 }, level = 1, group = "WeaponSpellDamage", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "caster_damage", "damage", "caster" }, tradeHash = 2974417149, },
	["JewelStunBuildup"] = { type = "Suffix", affix = "of Stunning", "(10-20)% increased Stun Buildup", statOrder = { 979 }, level = 1, group = "StunDamageIncrease", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 239367161, },
	["JewelStunThreshold"] = { type = "Suffix", affix = "of Withstanding", "(6-16)% increased Stun Threshold", statOrder = { 2914 }, level = 1, group = "IncreasedStunThreshold", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 680068163, },
	["JewelStunThresholdfromEnergyShield"] = { type = "Suffix", affix = "of Barriers", "Gain additional Stun Threshold equal to (5-15)% of maximum Energy Shield", statOrder = { 9099 }, level = 1, group = "StunThresholdfromEnergyShield", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 416040624, },
	["JewelAilmentThresholdfromEnergyShield"] = { type = "Suffix", affix = "of Inuring", "Gain additional Ailment Threshold equal to (5-15)% of maximum Energy Shield", statOrder = { 4161 }, level = 1, group = "AilmentThresholdfromEnergyShield", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "ailment" }, tradeHash = 3398301358, },
	["JewelStunThresholdIfNotStunnedRecently"] = { type = "Suffix", affix = "of Stoutness", "(15-25)% increased Stun Threshold if you haven't been Stunned Recently", statOrder = { 9101 }, level = 1, group = "IncreasedStunThresholdIfNoRecentStun", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1405298142, },
	["JewelBleedingEffect"] = { type = "Prefix", affix = "Haemorrhaging", "(5-15)% increased Magnitude of Bleeding you inflict", statOrder = { 4598 }, level = 1, group = "BleedDotMultiplier", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "physical_damage", "bleed", "damage", "physical", "attack", "ailment" }, tradeHash = 3166958180, },
	["JewelSwordDamage"] = { type = "Prefix", affix = "Vicious", "(6-16)% increased Damage with Swords", statOrder = { 1204 }, level = 1, group = "IncreasedSwordDamageForJewel", weightKey = { "default" }, weightVal = { 0 }, modTags = { "damage", "attack" }, tradeHash = 83050999, },
	["JewelSwordSpeed"] = { type = "Suffix", affix = "of Fencing", "(2-4)% increased Attack Speed with Swords", statOrder = { 1269 }, level = 1, group = "SwordAttackSpeedForJewel", weightKey = { "default" }, weightVal = { 0 }, modTags = { "attack", "speed" }, tradeHash = 3293699237, },
	["JewelThorns"] = { type = "Prefix", affix = "Retaliating", "(10-20)% increased Thorns damage", statOrder = { 9211 }, level = 1, group = "ThornsDamageIncrease", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, tradeHash = 1315743832, },
	["JewelTotemDamage"] = { type = "Prefix", affix = "Shaman's", "(10-18)% increased Totem Damage", statOrder = { 1097 }, level = 1, group = "TotemDamageForJewel", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, tradeHash = 3851254963, },
	["JewelTotemLife"] = { type = "Prefix", affix = "Carved", "(10-20)% increased Totem Life", statOrder = { 1473 }, level = 1, group = "IncreasedTotemLife", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life" }, tradeHash = 686254215, },
	["JewelTotemPlacementSpeed"] = { type = "Suffix", affix = "of Ancestry", "(10-20)% increased Totem Placement speed", statOrder = { 2272 }, level = 1, group = "SummonTotemCastSpeed", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "speed" }, tradeHash = 3374165039, },
	["JewelTrapDamage"] = { type = "Prefix", affix = "Trapping", "(6-16)% increased Trap Damage", statOrder = { 856 }, level = 1, group = "TrapDamage", weightKey = { "default" }, weightVal = { 0 }, modTags = { "damage" }, tradeHash = 2941585404, },
	["JewelTrapThrowSpeed"] = { type = "Suffix", affix = "of Preparation", "(4-8)% increased Trap Throwing Speed", statOrder = { 1615 }, level = 1, group = "TrapThrowSpeed", weightKey = { "default" }, weightVal = { 0 }, modTags = { "speed" }, tradeHash = 118398748, },
	["JewelTriggeredSpellDamage"] = { type = "Prefix", affix = "Triggered", "Triggered Spells deal (10-18)% increased Spell Damage", statOrder = { 9273 }, level = 1, group = "DamageWithTriggeredSpells", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "caster_damage", "damage", "caster" }, tradeHash = 3067892458, },
	["JewelUnarmedDamage"] = { type = "Prefix", affix = "Punching", "(6-16)% increased Damage with Unarmed Attacks", statOrder = { 3195 }, level = 1, group = "UnarmedDamage", weightKey = { "default" }, weightVal = { 0 }, modTags = { "damage", "attack" }, tradeHash = 1870736574, },
	["JewelWarcryBuffEffect"] = { type = "Prefix", affix = "of Warcries", "(5-15)% increased Warcry Buff Effect", statOrder = { 9424 }, level = 1, group = "WarcryEffect", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 3037553757, },
	["JewelWarcryCooldown"] = { type = "Suffix", affix = "of Rallying", "(5-15)% increased Warcry Cooldown Recovery Rate", statOrder = { 2967 }, level = 1, group = "WarcryCooldownSpeed", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 4159248054, },
	["JewelWarcryDamage"] = { type = "Prefix", affix = "Yelling", "(10-20)% increased Damage with Warcries", statOrder = { 9427 }, level = 1, group = "WarcryDamage", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, tradeHash = 1594812856, },
	["JewelWarcrySpeed"] = { type = "Suffix", affix = "of Lungs", "(10-20)% increased Warcry Speed", statOrder = { 2920 }, level = 1, group = "WarcrySpeed", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "speed" }, tradeHash = 1316278494, },
	["JewelWeaponSwapSpeed"] = { type = "Suffix", affix = "of Swapping", "(15-25)% increased Weapon Swap Speed", statOrder = { 9436 }, level = 1, group = "WeaponSwapSpeed", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "speed" }, tradeHash = 3233599707, },
	["JewelWitheredEffect"] = { type = "Prefix", affix = "Withering", "(5-10)% increased Effect of Withered", statOrder = { 9454 }, level = 1, group = "WitheredEffect", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = { "chaos" }, tradeHash = 2545584555, },
	["JewelUnarmedAttackSpeed"] = { type = "Suffix", affix = "of Jabbing", "(2-4)% increased Unarmed Attack Speed", statOrder = { 9328 }, level = 1, group = "UnarmedAttackSpeed", weightKey = { "default" }, weightVal = { 0 }, modTags = { "attack", "speed" }, tradeHash = 662579422, },
	["JewelProjectileDamageIfMeleeHitRecently"] = { type = "Prefix", affix = "Retreating", "(10-20)% increased Projectile Damage if you've dealt a Melee Hit in the past eight seconds", statOrder = { 8584 }, level = 1, group = "ProjectileDamageIfMeleeHitRecently", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, tradeHash = 3596695232, },
	["JewelMeleeDamageIfProjectileHitRecently"] = { type = "Prefix", affix = "Engaging", "(10-20)% increased Melee Damage if you've dealt a Projectile Attack Hit in the past eight seconds", statOrder = { 8046 }, level = 1, group = "MeleeDamageIfProjectileHitRecently", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, tradeHash = 3028809864, },
	["JewelParryDamage"] = { type = "Prefix", affix = "Parrying", "(15-25)% increased Parry Damage", statOrder = { 8433 }, level = 1, group = "ParryDamage", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1569159338, },
	["JewelParriedDebuffDuration"] = { type = "Suffix", affix = "of Unsettling", "(10-15)% increased Parried Debuff Duration", statOrder = { 8439 }, level = 1, group = "ParriedDebuffDuration", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "block" }, tradeHash = 3401186585, },
	["JewelStunThresholdDuringParry"] = { type = "Suffix", affix = "of Biding", "(15-25)% increased Stun Threshold while Parrying", statOrder = { 8440 }, level = 1, group = "StunThresholdDuringParry", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1911237468, },
	["JewelVolatilityOnKillChance"] = { type = "Suffix", affix = "of Volatility", "(2-3)% chance to gain Volatility on Kill", statOrder = { 9403 }, level = 1, group = "VolatilityOnKillChance", weightKey = { "intjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 3749502527, },
	["JewelCompanionDamage"] = { type = "Prefix", affix = "Kinship", "Companions deal (10-20)% increased Damage", statOrder = { 5252 }, level = 1, group = "CompanionDamage", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "minion" }, tradeHash = 234296660, },
	["JewelCompanionLife"] = { type = "Prefix", affix = "Kindred", "Companions have (10-20)% increased maximum Life", statOrder = { 5254 }, level = 1, group = "CompanionLife", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life", "minion" }, tradeHash = 1805182458, },
	["JewelHazardDamage"] = { type = "Prefix", affix = "Hazardous", "(10-20)% increased Hazard Damage", statOrder = { 6336 }, level = 1, group = "HazardDamage", weightKey = { "dexjewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, tradeHash = 1697951953, },
	["JewelIncisionChance"] = { type = "Prefix", affix = "Incise", "(15-25)% chance for Attack Hits to apply Incision", statOrder = { 5099 }, level = 1, group = "IncisionChance", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = { "physical_damage", "bleed", "damage", "physical", "ailment" }, tradeHash = 300723956, },
	["JewelBannerValourGained"] = { type = "Suffix", affix = "of Valour", "(15-20)% increased Valour gained", statOrder = { 4469 }, level = 1, group = "BannerValourGained", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1050359418, },
	["JewelBannerArea"] = { type = "Prefix", affix = "Rallying", "Banner Skills have (10-20)% increased Area of Effect", statOrder = { 4464 }, level = 1, group = "BannerArea", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 429143663, },
	["JewelBannerDuration"] = { type = "Suffix", affix = "of Inspiring", "Banner Skills have (15-25)% increased Duration", statOrder = { 4466 }, level = 1, group = "BannerDuration", weightKey = { "strjewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2720982137, },
	["JewelPresenceRadius"] = { type = "Prefix", affix = "Iconic", "(15-25)% increased Presence Area of Effect", statOrder = { 998 }, level = 1, group = "PresenceRadius", weightKey = { "strjewel", "intjewel", "default" }, weightVal = { 1, 1, 0 }, modTags = {  }, tradeHash = 101878827, },
	["JewelRadiusMediumSize"] = { type = "Prefix", affix = "Greater", "Upgrades Radius to Medium", statOrder = { 7009 }, level = 1, group = "JewelRadiusLargerRadius", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1817879664, },
	["JewelRadiusLargeSize"] = { type = "Prefix", affix = "Grand", "Upgrades Radius to Large", statOrder = { 7009 }, level = 1, group = "JewelRadiusLargerRadius", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1817879664, },
	["JewelRadiusSmallNodeEffect"] = { type = "Suffix", affix = "of Potency", "(15-25)% increased Effect of Small Passive Skills in Radius", statOrder = { 7032 }, level = 1, group = "JewelRadiusSmallNodeEffect", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1060572482, },
	["JewelRadiusNotableEffect"] = { type = "Suffix", affix = "of Influence", "(15-25)% increased Effect of Small Passive Skills in Radius", statOrder = { 7032 }, level = 1, group = "JewelRadiusSmallNodeEffect", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1060572482, },
	["JewelRadiusNotableEffectNew"] = { type = "Suffix", affix = "of Supremacy", "(15-25)% increased Effect of Notable Passive Skills in Radius", statOrder = { 7028 }, level = 1, group = "JewelRadiusNotableEffect", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 4234573345, },
	["JewelRadiusAccuracy"] = { type = "Prefix", affix = "Accurate", "(1-2)% increased Accuracy Rating", statOrder = { 1276 }, level = 1, group = "IncreasedAccuracyPercent", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack" }, nodeType = 1, tradeHash = 624954515, },
	["JewelRadiusAilmentChance"] = { type = "Suffix", affix = "of Ailing", "(3-7)% increased chance to inflict Ailments", statOrder = { 4155 }, level = 1, group = "AilmentChance", weightKey = { "dex_radius_jewel", "int_radius_jewel", "default" }, weightVal = { 1, 1, 0 }, modTags = { "ailment" }, nodeType = 2, tradeHash = 1772247089, },
	["JewelRadiusAilmentEffect"] = { type = "Prefix", affix = "Acrimonious", "(3-7)% increased Magnitude of Ailments you inflict", statOrder = { 4156 }, level = 1, group = "AilmentEffect", weightKey = { "dex_radius_jewel", "int_radius_jewel", "default" }, weightVal = { 1, 1, 0 }, modTags = { "damage", "ailment" }, nodeType = 2, tradeHash = 1303248024, },
	["JewelRadiusAilmentThreshold"] = { type = "Suffix", affix = "of Enduring", "(2-3)% increased Elemental Ailment Threshold", statOrder = { 4162 }, level = 1, group = "IncreasedAilmentThreshold", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 3544800472, },
	["JewelRadiusAreaofEffect"] = { type = "Prefix", affix = "Blasting", "(2-3)% increased Area of Effect", statOrder = { 1571 }, level = 1, group = "AreaOfEffect", weightKey = { "str_radius_jewel", "int_radius_jewel", "default" }, weightVal = { 1, 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 280731498, },
	["JewelRadiusArmour"] = { type = "Prefix", affix = "Armoured", "(2-3)% increased Armour", statOrder = { 867 }, level = 1, group = "GlobalPhysicalDamageReductionRatingPercent", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "armour", "defences" }, nodeType = 1, tradeHash = 2866361420, },
	["JewelRadiusArmourBreak"] = { type = "Prefix", affix = "Shattering", "Break (1-2)% increased Armour", statOrder = { 4275 }, level = 1, group = "ArmourBreak", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 1776411443, },
	["JewelRadiusArmourBreakDuration"] = { type = "Suffix", affix = "Resonating", "(5-10)% increased Armour Break Duration", statOrder = { 4278 }, level = 1, group = "ArmourBreakDuration", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 2215233444, },
	["JewelRadiusAttackCriticalChance"] = { type = "Suffix", affix = "of Deadliness", "(3-7)% increased Critical Hit Chance for Attacks", statOrder = { 932 }, level = 1, group = "AttackCriticalStrikeChance", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "critical" }, nodeType = 2, tradeHash = 2194114101, },
	["JewelRadiusAttackCriticalDamage"] = { type = "Suffix", affix = "of Demolishing", "(5-10)% increased Critical Damage Bonus for Attack Damage", statOrder = { 936 }, level = 1, group = "AttackCriticalStrikeMultiplier", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "critical" }, nodeType = 2, tradeHash = 3714003708, },
	["JewelRadiusAttackDamage"] = { type = "Prefix", affix = "Combat", "(1-2)% increased Attack Damage", statOrder = { 1101 }, level = 1, group = "AttackDamage", weightKey = { "str_radius_jewel", "dex_radius_jewel", "default" }, weightVal = { 1, 1, 0 }, modTags = { "damage", "attack" }, nodeType = 1, tradeHash = 2843214518, },
	["JewelRadiusAttackSpeed"] = { type = "Suffix", affix = "of Alacrity", "(1-2)% increased Attack Speed", statOrder = { 939 }, level = 1, group = "IncreasedAttackSpeed", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "speed" }, nodeType = 2, tradeHash = 681332047, },
	["JewelRadiusAuraEffect"] = { type = "Prefix", affix = "Commanding", "Aura Skills have (1-3)% increased Magnitudes", statOrder = { 2498 }, level = 1, group = "AuraEffectForJewel", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "aura" }, nodeType = 2, tradeHash = 315791320, },
	["JewelRadiusAxeDamage"] = { type = "Prefix", affix = "Sinister", "(2-3)% increased Damage with Axes", statOrder = { 1178 }, level = 1, group = "IncreasedAxeDamageForJewel", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, nodeType = 1, tradeHash = 3314142259, },
	["JewelRadiusAxeSpeed"] = { type = "Suffix", affix = "of Cleaving", "(1-2)% increased Attack Speed with Axes", statOrder = { 1263 }, level = 1, group = "AxeAttackSpeedForJewel", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "speed" }, nodeType = 2, tradeHash = 3550868361, },
	["JewelRadiusBleedingChance"] = { type = "Prefix", affix = "Bleeding", "1% chance to inflict Bleeding on Hit", statOrder = { 4498 }, level = 1, group = "BaseChanceToBleed", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 2174054121, },
	["JewelRadiusBleedingDuration"] = { type = "Suffix", affix = "of Haemophilia", "(3-7)% increased Bleeding Duration", statOrder = { 4491 }, level = 1, group = "BleedDuration", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "bleed", "physical", "attack", "ailment" }, nodeType = 2, tradeHash = 1459321413, },
	["JewelRadiusBlindEffect"] = { type = "Prefix", affix = "Stifling", "(3-5)% increased Blind Effect", statOrder = { 4715 }, level = 1, group = "BlindEffect", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 1585769763, },
	["JewelRadiusBlindonHit"] = { type = "Suffix", affix = "of Blinding", "1% chance to Blind Enemies on Hit with Attacks", statOrder = { 4428 }, level = 1, group = "AttacksBlindOnHitChance", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack" }, nodeType = 1, tradeHash = 318953428, },
	["JewelRadiusBlock"] = { type = "Prefix", affix = "Protecting", "(1-3)% increased Block chance", statOrder = { 1074 }, level = 1, group = "IncreasedBlockChance", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "block" }, nodeType = 2, tradeHash = 4147897060, },
	["JewelRadiusDamageVsRareOrUnique"] = { type = "Prefix", affix = "Slaying", "(2-3)% increased Damage with Hits against Rare and Unique Enemies", statOrder = { 2855 }, level = 1, group = "DamageVsRareOrUnique", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, nodeType = 1, tradeHash = 1852872083, },
	["JewelRadiusBowAccuracyRating"] = { type = "Prefix", affix = "Precise", "(1-2)% increased Accuracy Rating with Bows", statOrder = { 1285 }, level = 1, group = "BowIncreasedAccuracyRating", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack" }, nodeType = 1, tradeHash = 169946467, },
	["JewelRadiusBowDamage"] = { type = "Prefix", affix = "Perforating", "(2-3)% increased Damage with Bows", statOrder = { 1198 }, level = 1, group = "IncreasedBowDamageForJewel", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, nodeType = 1, tradeHash = 4188894176, },
	["JewelRadiusBowSpeed"] = { type = "Suffix", affix = "of Nocking", "(1-2)% increased Attack Speed with Bows", statOrder = { 1268 }, level = 1, group = "BowAttackSpeedForJewel", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "speed" }, nodeType = 2, tradeHash = 3759735052, },
	["JewelRadiusCastSpeed"] = { type = "Suffix", affix = "of Enchanting", "(1-2)% increased Cast Speed", statOrder = { 940 }, level = 1, group = "IncreasedCastSpeed", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "caster", "speed" }, nodeType = 2, tradeHash = 2891184298, },
	["JewelRadiusChainFromTerrain"] = { type = "Suffix", affix = "of Chaining", "Projectiles have (1-2)% chance to Chain an additional time from terrain", statOrder = { 8581 }, level = 1, group = "ChainFromTerrain", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 4081947835, },
	["JewelRadiusCharmDuration"] = { type = "Suffix", affix = "of the Woodland", "(1-2)% increased Charm Effect Duration", statOrder = { 881 }, level = 1, group = "CharmDuration", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 1389754388, },
	["JewelRadiusCharmChargesGained"] = { type = "Suffix", affix = "of the Thicker", "(3-7)% increased Charm Charges gained", statOrder = { 5146 }, level = 1, group = "CharmChargesGained", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 3585532255, },
	["JewelRadiusCharmDamageWhileUsing"] = { type = "Prefix", affix = "Verdant", "(2-3)% increased Damage while you have an active Charm", statOrder = { 5511 }, level = 1, group = "CharmDamageWhileUsing", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 627767961, },
	["JewelRadiusChaosDamage"] = { type = "Prefix", affix = "Chaotic", "(1-2)% increased Chaos Damage", statOrder = { 860 }, level = 1, group = "IncreasedChaosDamage", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "chaos_damage", "damage", "chaos" }, nodeType = 1, tradeHash = 736967255, },
	["JewelRadiusChillDuration"] = { type = "Suffix", affix = "of Frost", "(6-12)% increased Chill Duration on Enemies", statOrder = { 1553 }, level = 1, group = "IncreasedChillDuration", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental", "cold", "ailment" }, nodeType = 2, tradeHash = 3485067555, },
	["JewelRadiusColdDamage"] = { type = "Prefix", affix = "Chilling", "(1-2)% increased Cold Damage", statOrder = { 858 }, level = 1, group = "ColdDamagePercentage", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental_damage", "damage", "elemental", "cold" }, nodeType = 1, tradeHash = 3291658075, },
	["JewelRadiusColdPenetration"] = { type = "Prefix", affix = "Numbing", "Damage Penetrates (1-2)% Cold Resistance", statOrder = { 2644 }, level = 1, group = "ColdResistancePenetration", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental_damage", "damage", "elemental", "cold" }, nodeType = 1, tradeHash = 3417711605, },
	["JewelRadiusCooldownSpeed"] = { type = "Suffix", affix = "of Chronomancy", "(1-3)% increased Cooldown Recovery Rate", statOrder = { 4504 }, level = 1, group = "GlobalCooldownRecovery", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 1004011302, },
	["JewelRadiusCorpses"] = { type = "Prefix", affix = "Necromantic", "(2-3)% increased Damage if you have Consumed a Corpse Recently", statOrder = { 3850 }, level = 1, group = "DamageIfConsumedCorpse", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, nodeType = 1, tradeHash = 2118708619, },
	["JewelRadiusCriticalAilmentEffect"] = { type = "Prefix", affix = "Rancorous", "(5-10)% increased Magnitude of Damaging Ailments you inflict with Critical Hits", statOrder = { 5327 }, level = 1, group = "CriticalAilmentEffect", weightKey = { "dex_radius_jewel", "int_radius_jewel", "default" }, weightVal = { 1, 1, 0 }, modTags = { "damage", "critical", "ailment" }, nodeType = 2, tradeHash = 440490623, },
	["JewelRadiusCriticalChance"] = { type = "Suffix", affix = "of Annihilation", "(3-7)% increased Critical Hit Chance", statOrder = { 931 }, level = 1, group = "CriticalStrikeChance", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "critical" }, nodeType = 2, tradeHash = 587431675, },
	["JewelRadiusCriticalDamage"] = { type = "Suffix", affix = "of Potency", "(5-10)% increased Critical Damage Bonus", statOrder = { 935 }, level = 1, group = "CriticalStrikeMultiplier", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "critical" }, nodeType = 2, tradeHash = 3556824919, },
	["JewelRadiusSpellCriticalDamage"] = { type = "Suffix", affix = "of Unmaking", "(5-10)% increased Critical Spell Damage Bonus", statOrder = { 937 }, level = 1, group = "SpellCritMultiplierForJewel", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "caster_damage", "damage", "caster", "critical" }, nodeType = 2, tradeHash = 274716455, },
	["JewelRadiusCrossbowDamage"] = { type = "Prefix", affix = "Bolting", "(2-3)% increased Damage with Crossbows", statOrder = { 3897 }, level = 1, group = "CrossbowDamage", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, nodeType = 1, tradeHash = 427684353, },
	["JewelRadiusCrossbowReloadSpeed"] = { type = "Suffix", affix = "of Reloading", "(5-7)% increased Crossbow Reload Speed", statOrder = { 8749 }, level = 1, group = "CrossbowReloadSpeed", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "speed" }, nodeType = 2, tradeHash = 3192728503, },
	["JewelRadiusCrossbowSpeed"] = { type = "Suffix", affix = "of Rapidity", "(1-2)% increased Attack Speed with Crossbows", statOrder = { 3901 }, level = 1, group = "CrossbowSpeed", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "speed" }, nodeType = 2, tradeHash = 1135928777, },
	["JewelRadiusCurseArea"] = { type = "Prefix", affix = "Expanding", "(6-12)% increased Area of Effect of Curses", statOrder = { 1893 }, level = 1, group = "CurseAreaOfEffect", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "caster", "curse" }, nodeType = 2, tradeHash = 153777645, },
	["JewelRadiusCurseDuration"] = { type = "Suffix", affix = "of Continuation", "(2-4)% increased Curse Duration", statOrder = { 1480 }, level = 1, group = "BaseCurseDuration", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "curse" }, nodeType = 1, tradeHash = 3824372849, },
	["JewelRadiusCurseEffect"] = { type = "Prefix", affix = "Hexing", "1% increased Curse Magnitudes", statOrder = { 2288 }, level = 1, group = "CurseEffectivenessForJewel", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "caster", "curse" }, nodeType = 2, tradeHash = 2353576063, },
	["JewelRadiusDaggerCriticalChance"] = { type = "Suffix", affix = "of Backstabbing", "(3-7)% increased Critical Hit Chance with Daggers", statOrder = { 1307 }, level = 1, group = "CritChanceWithDaggerForJewel", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "critical" }, nodeType = 2, tradeHash = 4018186542, },
	["JewelRadiusDaggerDamage"] = { type = "Prefix", affix = "Lethal", "(2-3)% increased Damage with Daggers", statOrder = { 1190 }, level = 1, group = "IncreasedDaggerDamageForJewel", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, nodeType = 1, tradeHash = 3586984690, },
	["JewelRadiusDaggerSpeed"] = { type = "Suffix", affix = "of Slicing", "(1-2)% increased Attack Speed with Daggers", statOrder = { 1266 }, level = 1, group = "DaggerAttackSpeedForJewel", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "speed" }, nodeType = 2, tradeHash = 2538566497, },
	["JewelRadiusDamagefromMana"] = { type = "Suffix", affix = "of Mind", "1% of Damage is taken from Mana before Life", statOrder = { 2384 }, level = 1, group = "DamageRemovedFromManaBeforeLife", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life", "mana" }, nodeType = 2, tradeHash = 458438597, },
	["JewelRadiusDamagevsArmourBrokenEnemies"] = { type = "Prefix", affix = "Exploiting", "(2-4)% increased Damage against Enemies with Fully Broken Armour", statOrder = { 5445 }, level = 1, group = "DamagevsArmourBrokenEnemies", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, nodeType = 1, tradeHash = 2301718443, },
	["JewelRadiusDamagingAilmentDuration"] = { type = "Suffix", affix = "of Suffusion", "(3-5)% increased Duration of Damaging Ailments on Enemies", statOrder = { 5550 }, level = 1, group = "DamagingAilmentDuration", weightKey = { "dex_radius_jewel", "int_radius_jewel", "default" }, weightVal = { 1, 1, 0 }, modTags = { "ailment" }, nodeType = 2, tradeHash = 1829102168, },
	["JewelRadiusDazeBuildup"] = { type = "Suffix", affix = "of Dazing", "(2-3)% increased Daze Buildup", statOrder = { 5560 }, level = 1, group = "DazeBuildup", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 1949833742, },
	["JewelRadiusDebuffExpiry"] = { type = "Suffix", affix = "of Diminishing", "Debuffs on you expire (3-5)% faster", statOrder = { 5583 }, level = 1, group = "DebuffTimePassed", weightKey = { "dex_radius_jewel", "int_radius_jewel", "default" }, weightVal = { 1, 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 1238227257, },
	["JewelRadiusElementalAilmentDuration"] = { type = "Suffix", affix = "of Suffering", "(3-5)% increased Duration of Ignite, Shock and Chill on Enemies", statOrder = { 6598 }, level = 1, group = "ElementalAilmentDuration", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental", "ailment" }, nodeType = 2, tradeHash = 1062710370, },
	["JewelRadiusElementalDamage"] = { type = "Prefix", affix = "Prismatic", "(1-2)% increased Elemental Damage", statOrder = { 1668 }, level = 1, group = "ElementalDamagePercent", weightKey = { "str_radius_jewel", "dex_radius_jewel", "int_radius_jewel", "default" }, weightVal = { 1, 1, 1, 0 }, modTags = { "elemental_damage", "damage", "elemental" }, nodeType = 1, tradeHash = 3141070085, },
	["JewelRadiusEmpoweredAttackDamage"] = { type = "Prefix", affix = "Empowering", "Empowered Attacks deal (2-3)% increased Damage", statOrder = { 5778 }, level = 1, group = "ExertedAttackDamage", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, nodeType = 1, tradeHash = 1569101201, },
	["JewelRadiusEnergy"] = { type = "Suffix", affix = "of Generation", "Meta Skills gain (2-4)% increased Energy", statOrder = { 5847 }, level = 1, group = "EnergyGeneration", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 4236566306, },
	["JewelRadiusEnergyShield"] = { type = "Prefix", affix = "Shimmering", "(2-3)% increased maximum Energy Shield", statOrder = { 871 }, level = 1, group = "GlobalEnergyShieldPercent", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "energy_shield", "defences" }, nodeType = 1, tradeHash = 2482852589, },
	["JewelRadiusEnergyShieldDelay"] = { type = "Prefix", affix = "Serene", "(5-7)% faster start of Energy Shield Recharge", statOrder = { 1379 }, level = 1, group = "EnergyShieldDelay", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "energy_shield", "defences" }, nodeType = 2, tradeHash = 1782086450, },
	["JewelRadiusEnergyShieldRecharge"] = { type = "Prefix", affix = "Fevered", "(2-3)% increased Energy Shield Recharge Rate", statOrder = { 962 }, level = 1, group = "EnergyShieldRegeneration", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "energy_shield", "defences" }, nodeType = 1, tradeHash = 2339757871, },
	["JewelRadiusEvasion"] = { type = "Prefix", affix = "Evasive", "(2-3)% increased Evasion Rating", statOrder = { 869 }, level = 1, group = "GlobalEvasionRatingPercent", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "evasion", "defences" }, nodeType = 1, tradeHash = 2106365538, },
	["JewelRadiusFasterAilments"] = { type = "Suffix", affix = "of Decrepifying", "Damaging Ailments deal damage (2-3)% faster", statOrder = { 5552 }, level = 1, group = "FasterAilmentDamageForJewel", weightKey = { "dex_radius_jewel", "int_radius_jewel", "default" }, weightVal = { 1, 1, 0 }, modTags = { "ailment" }, nodeType = 2, tradeHash = 538241406, },
	["JewelRadiusFireDamage"] = { type = "Prefix", affix = "Flaming", "(1-2)% increased Fire Damage", statOrder = { 857 }, level = 1, group = "FireDamagePercentage", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental_damage", "damage", "elemental", "fire" }, nodeType = 1, tradeHash = 3962278098, },
	["JewelRadiusFirePenetration"] = { type = "Prefix", affix = "Searing", "Damage Penetrates (1-2)% Fire Resistance", statOrder = { 2643 }, level = 1, group = "FireResistancePenetration", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental_damage", "damage", "elemental", "fire" }, nodeType = 1, tradeHash = 2653955271, },
	["JewelRadiusFlailCriticalChance"] = { type = "Suffix", affix = "of Thrashing", "(3-7)% increased Critical Hit Chance with Flails", statOrder = { 3891 }, level = 1, group = "FlailCriticalChance", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "critical" }, nodeType = 2, tradeHash = 1484710594, },
	["JewelRadiusFlailDamage"] = { type = "Prefix", affix = "Flailing", "(1-2)% increased Damage with Flails", statOrder = { 3886 }, level = 1, group = "FlailDamage", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, nodeType = 1, tradeHash = 1731242173, },
	["JewelRadiusFlaskChargesGained"] = { type = "Suffix", affix = "of Gathering", "(3-5)% increased Flask Charges gained", statOrder = { 6050 }, level = 1, group = "IncreasedFlaskChargesGained", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, nodeType = 2, tradeHash = 1836676211, },
	["JewelRadiusFlaskDuration"] = { type = "Suffix", affix = "of Prolonging", "(1-2)% increased Flask Effect Duration", statOrder = { 882 }, level = 1, group = "FlaskDuration", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, nodeType = 1, tradeHash = 3741323227, },
	["JewelRadiusFocusEnergyShield"] = { type = "Prefix", affix = "Focusing", "(15-25)% increased Energy Shield from Equipped Focus", statOrder = { 5857 }, level = 1, group = "FocusEnergyShield", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "energy_shield", "defences" }, nodeType = 2, tradeHash = 3174700878, },
	["JewelRadiusForkingProjectiles"] = { type = "Suffix", affix = "of Forking", "Projectiles have (5-7)% chance for an additional Projectile when Forking", statOrder = { 5064 }, level = 1, group = "ForkingProjectiles", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 3003542304, },
	["JewelRadiusFreezeAmount"] = { type = "Suffix", affix = "of Freezing", "(5-10)% increased Freeze Buildup", statOrder = { 986 }, level = 1, group = "FreezeDamageIncrease", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 473429811, },
	["JewelRadiusFreezeThreshold"] = { type = "Suffix", affix = "of Snowbreathing", "(2-4)% increased Freeze Threshold", statOrder = { 2915 }, level = 1, group = "FreezeThreshold", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, nodeType = 1, tradeHash = 3780644166, },
	["JewelRadiusHeraldDamage"] = { type = "Prefix", affix = "Heralding", "Herald Skills deal (2-4)% increased Damage", statOrder = { 5516 }, level = 1, group = "HeraldDamage", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 21071013, },
	["JewelRadiusIgniteChance"] = { type = "Suffix", affix = "of Ignition", "(2-3)% increased chance to Ignite", statOrder = { 984 }, level = 1, group = "IgniteChanceIncrease", weightKey = { "str_radius_jewel", "int_radius_jewel", "default" }, weightVal = { 1, 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 2968503605, },
	["JewelRadiusIgniteEffect"] = { type = "Prefix", affix = "Burning", "(3-7)% increased Magnitude of Ignite you inflict", statOrder = { 6596 }, level = 1, group = "IgniteEffect", weightKey = { "str_radius_jewel", "int_radius_jewel", "default" }, weightVal = { 1, 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 3791899485, },
	["JewelRadiusIncreasedDuration"] = { type = "Suffix", affix = "of Lengthening", "(3-5)% increased Skill Effect Duration", statOrder = { 1586 }, level = 1, group = "SkillEffectDuration", weightKey = { "str_radius_jewel", "int_radius_jewel", "default" }, weightVal = { 1, 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 3377888098, },
	["JewelRadiusKnockback"] = { type = "Suffix", affix = "of Fending", "(3-7)% increased Knockback Distance", statOrder = { 1688 }, level = 1, group = "KnockbackDistance", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 565784293, },
	["JewelRadiusLifeCost"] = { type = "Suffix", affix = "of Sacrifice", "(2-3)% of Skill Mana Costs Converted to Life Costs", statOrder = { 4545 }, level = 1, group = "LifeCost", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life" }, nodeType = 2, tradeHash = 2480498143, },
	["JewelRadiusLifeFlaskRecovery"] = { type = "Suffix", affix = "of Recovery", "(2-3)% increased Life Recovery from Flasks", statOrder = { 1738 }, level = 1, group = "GlobalFlaskLifeRecovery", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "life" }, nodeType = 1, tradeHash = 821241191, },
	["JewelRadiusLifeFlaskChargeGen"] = { type = "Suffix", affix = "of Pathfinding", "(5-10)% increased Life Flask Charges gained", statOrder = { 6734 }, level = 1, group = "LifeFlaskChargePercentGeneration", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 4009879772, },
	["JewelRadiusLifeLeech"] = { type = "Suffix", affix = "of Frenzy", "(2-3)% increased amount of Life Leeched", statOrder = { 1839 }, level = 1, group = "LifeLeechAmount", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 2112395885, },
	["JewelRadiusLifeonKill"] = { type = "Suffix", affix = "of Success", "Recover 1% of maximum Life on Kill", statOrder = { 1451 }, level = 1, group = "MaximumLifeOnKillPercent", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life" }, nodeType = 2, tradeHash = 2023107756, },
	["JewelRadiusLifeRecoup"] = { type = "Suffix", affix = "of Infusion", "1% of Damage taken Recouped as Life", statOrder = { 965 }, level = 1, group = "LifeRecoupForJewel", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life" }, nodeType = 2, tradeHash = 1444556985, },
	["JewelRadiusLifeRegeneration"] = { type = "Suffix", affix = "of Regeneration", "(3-5)% increased Life Regeneration rate", statOrder = { 964 }, level = 1, group = "LifeRegenerationRate", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life" }, nodeType = 2, tradeHash = 44972811, },
	["JewelRadiusLightningDamage"] = { type = "Prefix", affix = "Humming", "(1-2)% increased Lightning Damage", statOrder = { 859 }, level = 1, group = "LightningDamagePercentage", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental_damage", "damage", "elemental", "lightning" }, nodeType = 1, tradeHash = 2231156303, },
	["JewelRadiusLightningPenetration"] = { type = "Prefix", affix = "Surging", "Damage Penetrates (1-2)% Lightning Resistance", statOrder = { 2645 }, level = 1, group = "LightningResistancePenetration", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental_damage", "damage", "elemental", "lightning" }, nodeType = 1, tradeHash = 818778753, },
	["JewelRadiusMaceDamage"] = { type = "Prefix", affix = "Beating", "(1-2)% increased Damage with Maces", statOrder = { 1194 }, level = 1, group = "IncreasedMaceDamageForJewel", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, nodeType = 1, tradeHash = 1181419800, },
	["JewelRadiusMaceStun"] = { type = "Suffix", affix = "of Thumping", "(6-12)% increased Stun Buildup with Maces", statOrder = { 7183 }, level = 1, group = "MaceStun", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack" }, nodeType = 2, tradeHash = 872504239, },
	["JewelRadiusManaFlaskRecovery"] = { type = "Suffix", affix = "of Quenching", "(1-2)% increased Mana Recovery from Flasks", statOrder = { 1739 }, level = 1, group = "FlaskManaRecovery", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "mana" }, nodeType = 1, tradeHash = 2222186378, },
	["JewelRadiusManaFlaskChargeGen"] = { type = "Suffix", affix = "of Fountains", "(5-10)% increased Mana Flask Charges gained", statOrder = { 7212 }, level = 1, group = "ManaFlaskChargePercentGeneration", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 3590792340, },
	["JewelRadiusManaLeech"] = { type = "Suffix", affix = "of Thirsting", "(1-2)% increased amount of Mana Leeched", statOrder = { 1841 }, level = 1, group = "ManaLeechAmount", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "mana" }, nodeType = 1, tradeHash = 2839066308, },
	["JewelRadiusManaonKill"] = { type = "Suffix", affix = "of Osmosis", "Recover 1% of maximum Mana on Kill", statOrder = { 1457 }, level = 1, group = "ManaGainedOnKillPercentage", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "mana" }, nodeType = 2, tradeHash = 1604736568, },
	["JewelRadiusManaRegeneration"] = { type = "Suffix", affix = "of Energy", "(1-2)% increased Mana Regeneration Rate", statOrder = { 971 }, level = 1, group = "ManaRegeneration", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "mana" }, nodeType = 1, tradeHash = 789117908, },
	["JewelRadiusMarkCastSpeed"] = { type = "Suffix", affix = "of Targeting", "Mark Skills have (2-3)% increased Cast Speed", statOrder = { 1889 }, level = 1, group = "MarkCastSpeed", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "speed", "curse" }, nodeType = 1, tradeHash = 4189061307, },
	["JewelRadiusMarkDuration"] = { type = "Suffix", affix = "of Tracking", "Mark Skills have (3-4)% increased Skill Effect Duration", statOrder = { 7977 }, level = 1, group = "MarkDuration", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 2594634307, },
	["JewelRadiusMarkEffect"] = { type = "Prefix", affix = "Marking", "(2-3)% increased Effect of your Mark Skills", statOrder = { 2290 }, level = 1, group = "MarkEffect", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "caster", "curse" }, nodeType = 2, tradeHash = 712554801, },
	["JewelRadiusMaximumRage"] = { type = "Prefix", affix = "Angry", "+1 to Maximum Rage", statOrder = { 8640 }, level = 1, group = "MaximumRage", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 1181501418, },
	["JewelRadiusMeleeDamage"] = { type = "Prefix", affix = "Clashing", "(1-2)% increased Melee Damage", statOrder = { 1132 }, level = 1, group = "MeleeDamage", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, nodeType = 1, tradeHash = 1002362373, },
	["JewelRadiusMinionAccuracy"] = { type = "Prefix", affix = "Training", "(2-3)% increased Minion Accuracy Rating", statOrder = { 8126 }, level = 1, group = "MinionAccuracyRatingForJewel", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "minion" }, nodeType = 1, tradeHash = 1718147982, },
	["JewelRadiusMinionArea"] = { type = "Prefix", affix = "Companion", "Minions have (3-7)% increased Area of Effect", statOrder = { 2683 }, level = 1, group = "MinionAreaOfEffect", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "minion" }, nodeType = 2, tradeHash = 3811191316, },
	["JewelRadiusMinionAttackandCastSpeed"] = { type = "Suffix", affix = "of Orchestration", "Minions have (1-2)% increased Attack and Cast Speed", statOrder = { 8131 }, level = 1, group = "MinionAttackSpeedAndCastSpeed", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "caster", "speed", "minion" }, nodeType = 2, tradeHash = 3091578504, },
	["JewelRadiusMinionChaosResistance"] = { type = "Suffix", affix = "of Righteousness", "Minions have +(1-2)% to Chaos Resistance", statOrder = { 2586 }, level = 1, group = "MinionChaosResistance", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "chaos", "resistance", "minion" }, nodeType = 1, tradeHash = 3837707023, },
	["JewelRadiusMinionCriticalChance"] = { type = "Suffix", affix = "of Marshalling", "Minions have (5-10)% increased Critical Hit Chance", statOrder = { 8149 }, level = 1, group = "MinionCriticalStrikeChanceIncrease", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "minion", "critical" }, nodeType = 2, tradeHash = 491450213, },
	["JewelRadiusMinionCriticalMultiplier"] = { type = "Suffix", affix = "of Gripping", "Minions have (6-12)% increased Critical Damage Bonus", statOrder = { 8151 }, level = 1, group = "MinionCriticalStrikeMultiplier", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "minion", "critical" }, nodeType = 2, tradeHash = 1854213750, },
	["JewelRadiusMinionDamage"] = { type = "Prefix", affix = "Authoritative", "Minions deal (1-2)% increased Damage", statOrder = { 1663 }, level = 1, group = "MinionDamage", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "minion" }, nodeType = 1, tradeHash = 1589917703, },
	["JewelRadiusMinionLife"] = { type = "Prefix", affix = "Fortuitous", "Minions have (1-2)% increased maximum Life", statOrder = { 961 }, level = 1, group = "MinionLife", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life", "minion" }, nodeType = 1, tradeHash = 770672621, },
	["JewelRadiusMinionPhysicalDamageReduction"] = { type = "Suffix", affix = "of Confidence", "Minions have (1-2)% additional Physical Damage Reduction", statOrder = { 1974 }, level = 1, group = "MinionPhysicalDamageReduction", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "physical", "minion" }, nodeType = 1, tradeHash = 3119612865, },
	["JewelRadiusMinionResistances"] = { type = "Suffix", affix = "of Acclimatisation", "Minions have +(1-2)% to all Elemental Resistances", statOrder = { 2585 }, level = 1, group = "MinionElementalResistance", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "elemental", "resistance", "minion" }, nodeType = 1, tradeHash = 1423639565, },
	["JewelRadiusMinionReviveSpeed"] = { type = "Suffix", affix = "of Revival", "Minions Revive (3-7)% faster", statOrder = { 8197 }, level = 1, group = "MinionReviveSpeed", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 2639966148, },
	["JewelRadiusMovementSpeed"] = { type = "Suffix", affix = "of Speed", "1% increased Movement Speed", statOrder = { 829 }, level = 1, group = "MovementVelocity", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "speed" }, nodeType = 2, tradeHash = 2250533757, },
	["JewelRadiusOfferingDuration"] = { type = "Suffix", affix = "of Offering", "Offering Skills have (6-12)% increased Duration", statOrder = { 8411 }, level = 1, group = "OfferingDuration", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 2957407601, },
	["JewelRadiusOfferingLife"] = { type = "Prefix", affix = "Sacrificial", "Offerings have (2-3)% increased Maximum Life", statOrder = { 8412 }, level = 1, group = "OfferingLife", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 3787460122, },
	["JewelRadiusPhysicalDamage"] = { type = "Prefix", affix = "Sharpened", "(1-2)% increased Global Physical Damage", statOrder = { 1130 }, level = 1, group = "PhysicalDamagePercent", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "physical_damage", "damage", "physical" }, nodeType = 1, tradeHash = 1310194496, },
	["JewelRadiusPiercingProjectiles"] = { type = "Suffix", affix = "of Piercing", "(5-10)% chance to Pierce an Enemy", statOrder = { 997 }, level = 1, group = "ChanceToPierce", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 2321178454, },
	["JewelRadiusPinBuildup"] = { type = "Suffix", affix = "of Pinning", "(5-10)% increased Pin Buildup", statOrder = { 6540 }, level = 1, group = "PinBuildup", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 3473929743, },
	["JewelRadiusPoisonChance"] = { type = "Suffix", affix = "of Poisoning", "1% chance to Poison on Hit", statOrder = { 2823 }, level = 1, group = "BaseChanceToPoison", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 795138349, },
	["JewelRadiusPoisonDamage"] = { type = "Prefix", affix = "Venomous", "(3-7)% increased Magnitude of Poison you inflict", statOrder = { 8536 }, level = 1, group = "PoisonEffect", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "ailment" }, nodeType = 2, tradeHash = 2487305362, },
	["JewelRadiusPoisonDuration"] = { type = "Suffix", affix = "of Infection", "(3-7)% increased Poison Duration", statOrder = { 2820 }, level = 1, group = "PoisonDuration", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "poison", "chaos", "ailment" }, nodeType = 2, tradeHash = 2011656677, },
	["JewelRadiusProjectileDamage"] = { type = "Prefix", affix = "Archer's", "(1-2)% increased Projectile Damage", statOrder = { 1682 }, level = 1, group = "ProjectileDamage", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, nodeType = 1, tradeHash = 1839076647, },
	["JewelRadiusProjectileSpeed"] = { type = "Prefix", affix = "Soaring", "(2-3)% increased Projectile Speed", statOrder = { 878 }, level = 1, group = "ProjectileSpeed", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "speed" }, nodeType = 2, tradeHash = 3759663284, },
	["JewelRadiusQuarterstaffDamage"] = { type = "Prefix", affix = "Monk's", "(1-2)% increased Damage with Quarterstaves", statOrder = { 1183 }, level = 1, group = "IncreasedStaffDamageForJewel", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, nodeType = 1, tradeHash = 4045894391, },
	["JewelRadiusQuarterstaffFreezeBuildup"] = { type = "Suffix", affix = "of Glaciers", "(5-10)% increased Freeze Buildup with Quarterstaves", statOrder = { 8628 }, level = 1, group = "QuarterstaffFreezeBuildup", weightKey = { "dex_radius_jewel", "int_radius_jewel", "default" }, weightVal = { 1, 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 1697447343, },
	["JewelRadiusQuarterstaffSpeed"] = { type = "Suffix", affix = "of Sequencing", "(1-2)% increased Attack Speed with Quarterstaves", statOrder = { 1264 }, level = 1, group = "StaffAttackSpeedForJewel", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "speed" }, nodeType = 2, tradeHash = 3283482523, },
	["JewelRadiusQuiverEffect"] = { type = "Prefix", affix = "Fletching", "(2-3)% increased bonuses gained from Equipped Quiver", statOrder = { 8636 }, level = 1, group = "QuiverModifierEffect", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 1200678966, },
	["JewelRadiusRageonHit"] = { type = "Suffix", affix = "of Raging", "Gain 1 Rage on Melee Hit", statOrder = { 6240 }, level = 1, group = "RageOnHit", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 2709367754, },
	["JewelRadiusRagewhenHit"] = { type = "Suffix", affix = "of Retribution", "Gain (1-2) Rage when Hit by an Enemy", statOrder = { 6242 }, level = 1, group = "GainRageWhenHit", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 3292710273, },
	["JewelRadiusShieldDefences"] = { type = "Prefix", affix = "Shielding", "(8-15)% increased Defences from Equipped Shield", statOrder = { 8825 }, level = 1, group = "ShieldArmourIncrease", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "defences" }, nodeType = 2, tradeHash = 145497481, },
	["JewelRadiusShockChance"] = { type = "Suffix", affix = "of Shocking", "(2-3)% increased chance to Shock", statOrder = { 988 }, level = 1, group = "ShockChanceIncrease", weightKey = { "dex_radius_jewel", "int_radius_jewel", "default" }, weightVal = { 1, 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 293638271, },
	["JewelRadiusShockDuration"] = { type = "Suffix", affix = "of Paralyzing", "(2-3)% increased Shock Duration", statOrder = { 1554 }, level = 1, group = "ShockDuration", weightKey = { "dex_radius_jewel", "int_radius_jewel", "default" }, weightVal = { 1, 1, 0 }, modTags = { "elemental", "lightning", "ailment" }, nodeType = 1, tradeHash = 3668351662, },
	["JewelRadiusShockEffect"] = { type = "Prefix", affix = "Jolting", "(5-7)% increased Magnitude of Shock you inflict", statOrder = { 8831 }, level = 1, group = "ShockEffect", weightKey = { "dex_radius_jewel", "int_radius_jewel", "default" }, weightVal = { 1, 1, 0 }, modTags = { "elemental", "lightning", "ailment" }, nodeType = 2, tradeHash = 2527686725, },
	["JewelRadiusSlowEffectOnSelf"] = { type = "Suffix", affix = "of Hastening", "(2-5)% reduced Slowing Potency of Debuffs on You", statOrder = { 4548 }, level = 1, group = "SlowPotency", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 924253255, },
	["JewelRadiusSpearAttackSpeed"] = { type = "Suffix", affix = "of Spearing", "(1-2)% increased Attack Speed with Spears", statOrder = { 1271 }, level = 1, group = "SpearAttackSpeed", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "speed" }, nodeType = 2, tradeHash = 1165163804, },
	["JewelRadiusSpearCriticalDamage"] = { type = "Suffix", affix = "of Hunting", "(5-10)% increased Critical Damage Bonus with Spears", statOrder = { 1336 }, level = 1, group = "SpearCriticalDamage", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "critical" }, nodeType = 2, tradeHash = 2456523742, },
	["JewelRadiusSpearDamage"] = { type = "Prefix", affix = "Spearheaded", "(1-2)% increased Damage with Spears", statOrder = { 1212 }, level = 1, group = "SpearDamage", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, nodeType = 1, tradeHash = 2696027455, },
	["JewelRadiusSpellCriticalChance"] = { type = "Suffix", affix = "of Annihilating", "(3-7)% increased Critical Hit Chance for Spells", statOrder = { 933 }, level = 1, group = "SpellCriticalStrikeChance", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "caster", "critical" }, nodeType = 2, tradeHash = 737908626, },
	["JewelRadiusSpellDamage"] = { type = "Prefix", affix = "Mystic", "(1-2)% increased Spell Damage", statOrder = { 855 }, level = 1, group = "WeaponSpellDamage", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "caster_damage", "damage", "caster" }, nodeType = 1, tradeHash = 2974417149, },
	["JewelRadiusStunBuildup"] = { type = "Suffix", affix = "of Stunning", "(5-10)% increased Stun Buildup", statOrder = { 979 }, level = 1, group = "StunDamageIncrease", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 239367161, },
	["JewelRadiusStunThreshold"] = { type = "Suffix", affix = "of Withstanding", "(1-2)% increased Stun Threshold", statOrder = { 2914 }, level = 1, group = "IncreasedStunThreshold", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 680068163, },
	["JewelRadiusStunThresholdfromEnergyShield"] = { type = "Suffix", affix = "of Barriers", "Gain additional Stun Threshold equal to (1-2)% of maximum Energy Shield", statOrder = { 9099 }, level = 1, group = "StunThresholdfromEnergyShield", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 416040624, },
	["JewelRadiusAilmentThresholdfromEnergyShield"] = { type = "Suffix", affix = "of Inuring", "Gain additional Ailment Threshold equal to (1-2)% of maximum Energy Shield", statOrder = { 4161 }, level = 1, group = "AilmentThresholdfromEnergyShield", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "ailment" }, nodeType = 1, tradeHash = 3398301358, },
	["JewelRadiusStunThresholdIfNotStunnedRecently"] = { type = "Suffix", affix = "of Stoutness", "(2-3)% increased Stun Threshold if you haven't been Stunned Recently", statOrder = { 9101 }, level = 1, group = "IncreasedStunThresholdIfNoRecentStun", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 1405298142, },
	["JewelRadiusBleedingEffect"] = { type = "Prefix", affix = "Haemorrhaging", "(3-7)% increased Magnitude of Bleeding you inflict", statOrder = { 4598 }, level = 1, group = "BleedDotMultiplier", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "physical_damage", "bleed", "damage", "physical", "attack", "ailment" }, nodeType = 2, tradeHash = 3166958180, },
	["JewelRadiusSwordDamage"] = { type = "Prefix", affix = "Vicious", "(1-2)% increased Damage with Swords", statOrder = { 1204 }, level = 1, group = "IncreasedSwordDamageForJewel", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, nodeType = 1, tradeHash = 83050999, },
	["JewelRadiusSwordSpeed"] = { type = "Suffix", affix = "of Fencing", "(1-2)% increased Attack Speed with Swords", statOrder = { 1269 }, level = 1, group = "SwordAttackSpeedForJewel", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "speed" }, nodeType = 2, tradeHash = 3293699237, },
	["JewelRadiusThorns"] = { type = "Prefix", affix = "Retaliating", "(2-3)% increased Thorns damage", statOrder = { 9211 }, level = 1, group = "ThornsDamageIncrease", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, nodeType = 1, tradeHash = 1315743832, },
	["JewelRadiusTotemDamage"] = { type = "Prefix", affix = "Shaman's", "(2-3)% increased Totem Damage", statOrder = { 1097 }, level = 1, group = "TotemDamageForJewel", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, nodeType = 1, tradeHash = 3851254963, },
	["JewelRadiusTotemLife"] = { type = "Prefix", affix = "Carved", "(2-3)% increased Totem Life", statOrder = { 1473 }, level = 1, group = "IncreasedTotemLife", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life" }, nodeType = 1, tradeHash = 686254215, },
	["JewelRadiusTotemPlacementSpeed"] = { type = "Suffix", affix = "of Ancestry", "(2-3)% increased Totem Placement speed", statOrder = { 2272 }, level = 1, group = "SummonTotemCastSpeed", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "speed" }, nodeType = 1, tradeHash = 3374165039, },
	["JewelRadiusTrapDamage"] = { type = "Prefix", affix = "Trapping", "(1-2)% increased Trap Damage", statOrder = { 856 }, level = 1, group = "TrapDamage", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, nodeType = 1, tradeHash = 2941585404, },
	["JewelRadiusTrapThrowSpeed"] = { type = "Suffix", affix = "of Preparation", "(2-4)% increased Trap Throwing Speed", statOrder = { 1615 }, level = 1, group = "TrapThrowSpeed", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "speed" }, nodeType = 2, tradeHash = 118398748, },
	["JewelRadiusTriggeredSpellDamage"] = { type = "Prefix", affix = "Triggered", "Triggered Spells deal (2-3)% increased Spell Damage", statOrder = { 9273 }, level = 1, group = "DamageWithTriggeredSpells", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "caster_damage", "damage", "caster" }, nodeType = 1, tradeHash = 3067892458, },
	["JewelRadiusUnarmedDamage"] = { type = "Prefix", affix = "Punching", "(1-2)% increased Damage with Unarmed Attacks", statOrder = { 3195 }, level = 1, group = "UnarmedDamage", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, nodeType = 1, tradeHash = 1870736574, },
	["JewelRadiusWarcryBuffEffect"] = { type = "Prefix", affix = "of Warcries", "(3-7)% increased Warcry Buff Effect", statOrder = { 9424 }, level = 1, group = "WarcryEffect", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 3037553757, },
	["JewelRadiusWarcryCooldown"] = { type = "Suffix", affix = "of Rallying", "(3-7)% increased Warcry Cooldown Recovery Rate", statOrder = { 2967 }, level = 1, group = "WarcryCooldownSpeed", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 4159248054, },
	["JewelRadiusWarcryDamage"] = { type = "Prefix", affix = "Yelling", "(2-3)% increased Damage with Warcries", statOrder = { 9427 }, level = 1, group = "WarcryDamage", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, nodeType = 1, tradeHash = 1594812856, },
	["JewelRadiusWarcrySpeed"] = { type = "Suffix", affix = "of Lungs", "(2-3)% increased Warcry Speed", statOrder = { 2920 }, level = 1, group = "WarcrySpeed", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "speed" }, nodeType = 1, tradeHash = 1316278494, },
	["JewelRadiusWeaponSwapSpeed"] = { type = "Suffix", affix = "of Swapping", "(2-4)% increased Weapon Swap Speed", statOrder = { 9436 }, level = 1, group = "WeaponSwapSpeed", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "speed" }, nodeType = 1, tradeHash = 3233599707, },
	["JewelRadiusWitheredEffect"] = { type = "Prefix", affix = "Withering", "(3-5)% increased Effect of Withered", statOrder = { 9454 }, level = 1, group = "WitheredEffect", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "chaos" }, nodeType = 2, tradeHash = 2545584555, },
	["JewelRadiusUnarmedAttackSpeed"] = { type = "Suffix", affix = "of Jabbing", "(1-2)% increased Unarmed Attack Speed", statOrder = { 9328 }, level = 1, group = "UnarmedAttackSpeed", weightKey = { "radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "attack", "speed" }, nodeType = 2, tradeHash = 662579422, },
	["JewelRadiusProjectileDamageIfMeleeHitRecently"] = { type = "Prefix", affix = "Retreating", "(2-3)% increased Projectile Damage if you've dealt a Melee Hit in the past eight seconds", statOrder = { 8584 }, level = 1, group = "ProjectileDamageIfMeleeHitRecently", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, nodeType = 1, tradeHash = 3596695232, },
	["JewelRadiusMeleeDamageIfProjectileHitRecently"] = { type = "Prefix", affix = "Engaging", "(2-3)% increased Melee Damage if you've dealt a Projectile Attack Hit in the past eight seconds", statOrder = { 8046 }, level = 1, group = "MeleeDamageIfProjectileHitRecently", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "attack" }, nodeType = 1, tradeHash = 3028809864, },
	["JewelRadiusParryDamage"] = { type = "Prefix", affix = "Parrying", "(2-3)% increased Parry Damage", statOrder = { 8433 }, level = 1, group = "ParryDamage", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 1569159338, },
	["JewelRadiusParriedDebuffDuration"] = { type = "Suffix", affix = "of Unsettling", "(5-10)% increased Parried Debuff Duration", statOrder = { 8439 }, level = 1, group = "ParriedDebuffDuration", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "block" }, nodeType = 2, tradeHash = 3401186585, },
	["JewelRadiusStunThresholdDuringParry"] = { type = "Suffix", affix = "of Biding", "(8-12)% increased Stun Threshold while Parrying", statOrder = { 8440 }, level = 1, group = "StunThresholdDuringParry", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 1911237468, },
	["JewelRadiusVolatilityOnKillChance"] = { type = "Suffix", affix = "of Volatility", "1% chance to gain Volatility on Kill", statOrder = { 9403 }, level = 1, group = "VolatilityOnKillChance", weightKey = { "int_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 3749502527, },
	["JewelRadiusCompanionDamage"] = { type = "Prefix", affix = "Kinship", "Companions deal (2-3)% increased Damage", statOrder = { 5252 }, level = 1, group = "CompanionDamage", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage", "minion" }, nodeType = 1, tradeHash = 234296660, },
	["JewelRadiusCompanionLife"] = { type = "Prefix", affix = "Kindred", "Companions have (2-3)% increased maximum Life", statOrder = { 5254 }, level = 1, group = "CompanionLife", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life", "minion" }, nodeType = 1, tradeHash = 1805182458, },
	["JewelRadiusHazardDamage"] = { type = "Prefix", affix = "Hazardous", "(2-3)% increased Hazard Damage", statOrder = { 6336 }, level = 1, group = "HazardDamage", weightKey = { "dex_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "damage" }, nodeType = 1, tradeHash = 1697951953, },
	["JewelRadiusIncisionChance"] = { type = "Prefix", affix = "Incise", "(3-5)% chance for Attack Hits to apply Incision", statOrder = { 5099 }, level = 1, group = "IncisionChance", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = { "physical_damage", "bleed", "damage", "physical", "ailment" }, nodeType = 1, tradeHash = 300723956, },
	["JewelRadiusBannerValourGained"] = { type = "Suffix", affix = "of Valour", "(8-12)% increased Valour gained", statOrder = { 4469 }, level = 1, group = "BannerValourGained", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 1050359418, },
	["JewelRadiusBannerArea"] = { type = "Prefix", affix = "Rallying", "Banner Skills have (2-3)% increased Area of Effect", statOrder = { 4464 }, level = 1, group = "BannerArea", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 429143663, },
	["JewelRadiusBannerDuration"] = { type = "Suffix", affix = "of Inspiring", "Banner Skills have (3-4)% increased Duration", statOrder = { 4466 }, level = 1, group = "BannerDuration", weightKey = { "str_radius_jewel", "default" }, weightVal = { 1, 0 }, modTags = {  }, nodeType = 1, tradeHash = 2720982137, },
	["JewelRadiusPresenceRadius"] = { type = "Prefix", affix = "Iconic", "(8-12)% increased Presence Area of Effect", statOrder = { 998 }, level = 1, group = "PresenceRadius", weightKey = { "str_radius_jewel", "int_radius_jewel", "default" }, weightVal = { 1, 1, 0 }, modTags = {  }, nodeType = 2, tradeHash = 101878827, },
}