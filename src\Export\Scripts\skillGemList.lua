--
-- Export all playable skill gems from game data
--
local out = io.open("../Export/Skills/SkillGems.txt", "w")
out:write('-- This file is automatically generated, do not edit!\n')
out:write('-- Gem data (c) Grinding Gear Games\n\n')

local export = false

local types = { "Strength", "Dexterity", "Intelligence", "Other" }

local function grantedEffectString(grantedEffect) 
	local s =  "#skill "..grantedEffect.Id.."\n"
	for _, statSet in ipairs(tableConcat({grantedEffect.GrantedEffectStatSets}, grantedEffect.AdditionalStatSets)) do
		if not (statSet.LabelType and statSet.LabelType.Id == "Hidden") then
			s = s.."#set "..statSet.Id.."\n#flags\n#mods\n"
		end
	end
	return s
end
for i, _ in ipairs(types) do
	local active = {}
	local support = {}
	local activeExport = {}
	local supportExport = {}
	local colour
	for skillGem in dat("SkillGems"):Rows() do
		for _, gemEffect in ipairs(skillGem.GemEffects) do
			if skillGem.Str >= 50 then
				colour = "Strength"
			elseif skillGem.Int >= 50 then
				colour = "Intelligence"
			elseif skillGem.Dex >= 50 then
				colour = "Dexterity"
			else
				colour = "Other"
			end
			if skillGem.IsSupport and skillGem.GemColour == i and not gemEffect.Id:match("Unknown") and not gemEffect.Id:match("Playtest") and not skillGem.BaseItemType.Name:match("DNT")
			and dat("SupportGems"):GetRow("SkillGem", dat("SkillGems"):GetRow("BaseItemType", dat("BaseItemTypes"):GetRow("Id", skillGem.BaseItemType.Id))) then
				local temp = skillGem.BaseItemType.Name..string.rep(" ", 30 - string.len(skillGem.BaseItemType.Name)).."\t\t----\t\t"..gemEffect.GrantedEffect.Id
				local temp1 = skillGem.BaseItemType.Name..grantedEffectString(gemEffect.GrantedEffect)
				if gemEffect.AdditionalGrantedEffects then
					for _, additionalGrantedEffect in ipairs(gemEffect.AdditionalGrantedEffects) do
						temp = temp.."\t"..additionalGrantedEffect.Id
						temp1 = temp1..grantedEffectString(additionalGrantedEffect)
					end
				end
				table.insert(support, temp)
				table.insert(supportExport, temp1)
			elseif not skillGem.IsSupport and types[i] == colour and not gemEffect.Id:match("Unknown") and not gemEffect.Id:match("Playtest") and not gemEffect.GrantedEffect.ActiveSkill.DisplayName:match("DNT") and not skillGem.BaseItemType.Name:match("DNT") 
			and (gemEffect.Id:match("UniqueBreach") or dat("SkillGemSupports"):GetRow("ActiveGem", dat("SkillGems"):GetRow("BaseItemType", dat("BaseItemTypes"):GetRow("Id", skillGem.BaseItemType.Id)))) then
				local temp = gemEffect.GrantedEffect.ActiveSkill.DisplayName..string.rep(" ", 30 - string.len(gemEffect.GrantedEffect.ActiveSkill.DisplayName)).."\t\t----\t\t"..gemEffect.GrantedEffect.Id
				local temp1 = gemEffect.GrantedEffect.ActiveSkill.DisplayName..grantedEffectString(gemEffect.GrantedEffect)
				if gemEffect.AdditionalGrantedEffects then
					for _, additionalGrantedEffect in ipairs(gemEffect.AdditionalGrantedEffects) do
						temp = temp.."\t"..additionalGrantedEffect.Id
						temp1 = temp1.."\n"..grantedEffectString(additionalGrantedEffect)
					end
				end
				table.insert(active, temp)
				table.insert(activeExport, temp1)
			end
		end
	end
	table.sort(active)
	table.sort(support)
	table.sort(supportExport)
	table.sort(activeExport)
	
	for i, row in ipairs(supportExport) do
		-- Remove text before "#skill" only if it is at the start of the string
		supportExport[i] = string.gsub(row, "^(.-)#skill", "#skill")
	end
	for i, row in ipairs(activeExport) do
		-- Remove text before "#skill" only if it is at the start of the string
		activeExport[i] = string.gsub(row, "^(.-)#skill", "#skill")
	end
	
	out:write("\t\t\t\t\t\t--------- Active "..types[i].." ---------\n")
	if export == false then
		out:write(table.concat(active, "\n"))
	else
		out:write(table.concat(activeExport, "\n"))
	end
	out:write('\n\n')
	out:write("\t\t\t\t\t\t--------- Support "..types[i].." ---------\n")
	if export == false then
		out:write(table.concat(support, "\n"))
	else
		out:write(table.concat(supportExport, "\n"))
	end
	out:write('\n\n')
end

out:close()

print("Skill gems exported.")
