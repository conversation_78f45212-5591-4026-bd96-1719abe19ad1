-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="number_of_crossbow_bolts"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Requiem lasts for {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Requiem lasts for {0} seconds"
			}
		},
		stats={
			[1]="rapidshot_requiem_active_duration_ms"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Requires {0} anguish to use"
			}
		},
		stats={
			[1]="rapidshot_requiem_base_buff_count_requirement"
		}
	},
	[4]={
		[1]={
			[1]={
				[1]={
					k="per_minute_to_per_second_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Accumulates {0} anguish per second"
			}
		},
		stats={
			[1]="rapidshot_requiem_number_of_stacks_per_minute"
		}
	},
	["number_of_crossbow_bolts"]=1,
	parent="skill_stat_descriptions",
	["rapidshot_requiem_active_duration_ms"]=2,
	["rapidshot_requiem_base_buff_count_requirement"]=3,
	["rapidshot_requiem_number_of_stacks_per_minute"]=4
}