-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Curse radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Curse radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Curse makes Non-Unique targets deal {0}% more Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				[2]={
					k="canonical_line",
					v=true
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Curse makes Non-Unique targets deal {0}% less Damage"
			}
		},
		stats={
			[1]="base_skill_buff_damage_+%_final_to_apply"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Curse makes Unique targets deal {0}% more Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				[2]={
					k="canonical_line",
					v=true
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Curse makes Unique targets deal {0}% less Damage"
			}
		},
		stats={
			[1]="base_skill_buff_damage_+%_final_vs_unique_to_apply"
		}
	},
	[5]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Curse duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Curse duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[6]={
		[1]={
		},
		stats={
			[1]="skill_curse_damage_+%_final_magnitude_to_apply"
		}
	},
	[7]={
		[1]={
		},
		stats={
			[1]="skill_curse_damage_+%_final_vs_unique_magnitude_to_apply"
		}
	},
	[8]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	["active_skill_area_of_effect_radius"]=1,
	["active_skill_base_area_of_effect_radius"]=2,
	["base_skill_buff_damage_+%_final_to_apply"]=3,
	["base_skill_buff_damage_+%_final_vs_unique_to_apply"]=4,
	["base_skill_effect_duration"]=5,
	parent="skill_stat_descriptions",
	["skill_curse_damage_+%_final_magnitude_to_apply"]=6,
	["skill_curse_damage_+%_final_vs_unique_magnitude_to_apply"]=7,
	["skill_effect_duration"]=8
}