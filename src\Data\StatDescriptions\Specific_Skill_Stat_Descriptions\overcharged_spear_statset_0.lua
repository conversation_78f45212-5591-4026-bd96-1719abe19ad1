-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="base_projectiles_cannot_chain"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Limit {0} Spear thrown per Attack"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Limit {0} Spears thrown per Attack"
			}
		},
		stats={
			[1]="base_number_of_thrown_remote_spears_allowed"
		}
	},
	[3]={
		[1]={
		},
		stats={
			[1]="number_of_thrown_remote_spears_allowed"
		}
	},
	["base_number_of_thrown_remote_spears_allowed"]=2,
	["base_projectiles_cannot_chain"]=1,
	["number_of_thrown_remote_spears_allowed"]=3,
	parent="skill_stat_descriptions"
}