-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="base_projectiles_cannot_chain"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Limit {0} lodged Spear"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Limit {0} lodged Spears"
			}
		},
		stats={
			[1]="base_number_of_remote_spear_mines_allowed"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Limit {0} Spear thrown per Attack"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Limit {0} Spears thrown per Attack"
			}
		},
		stats={
			[1]="base_number_of_thrown_remote_spears_allowed"
		}
	},
	[4]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Lodged spear duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Lodged spear duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[5]={
		[1]={
		},
		stats={
			[1]="number_of_remote_spear_mines_allowed"
		}
	},
	[6]={
		[1]={
		},
		stats={
			[1]="number_of_thrown_remote_spears_allowed"
		}
	},
	[7]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	["base_number_of_remote_spear_mines_allowed"]=2,
	["base_number_of_thrown_remote_spears_allowed"]=3,
	["base_projectiles_cannot_chain"]=1,
	["base_skill_effect_duration"]=4,
	["number_of_remote_spear_mines_allowed"]=5,
	["number_of_thrown_remote_spears_allowed"]=6,
	parent="skill_stat_descriptions",
	["skill_effect_duration"]=7
}