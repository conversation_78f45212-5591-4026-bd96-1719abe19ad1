-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	["FlaskChargesAddedIncreasePercent1"] = { type = "Suffix", affix = "of the Constant", "(23-30)% increased Charges gained", statOrder = { 1001 }, level = 1, group = "FlaskIncreasedChargesAdded", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3196823591, },
	["FlaskChargesAddedIncreasePercent2_"] = { type = "Suffix", affix = "of the Continuous", "(31-38)% increased Charges gained", statOrder = { 1001 }, level = 13, group = "FlaskIncreasedChargesAdded", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3196823591, },
	["FlaskChargesAddedIncreasePercent3_"] = { type = "Suffix", affix = "of the Endless", "(39-46)% increased Charges gained", statOrder = { 1001 }, level = 33, group = "FlaskIncreasedChargesAdded", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3196823591, },
	["FlaskChargesAddedIncreasePercent4_"] = { type = "Suffix", affix = "of the Bottomless", "(47-54)% increased Charges gained", statOrder = { 1001 }, level = 48, group = "FlaskIncreasedChargesAdded", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3196823591, },
	["FlaskChargesAddedIncreasePercent5__"] = { type = "Suffix", affix = "of the Perpetual", "(55-62)% increased Charges gained", statOrder = { 1001 }, level = 63, group = "FlaskIncreasedChargesAdded", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3196823591, },
	["FlaskChargesAddedIncreasePercent6"] = { type = "Suffix", affix = "of the Eternal", "(63-70)% increased Charges gained", statOrder = { 1001 }, level = 82, group = "FlaskIncreasedChargesAdded", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3196823591, },
	["FlaskExtraCharges1"] = { type = "Suffix", affix = "of the Wide", "(23-30)% increased Charges", statOrder = { 1004 }, level = 1, group = "FlaskIncreasedMaxCharges", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 1366840608, },
	["FlaskExtraCharges2__"] = { type = "Suffix", affix = "of the Copious", "(31-38)% increased Charges", statOrder = { 1004 }, level = 12, group = "FlaskIncreasedMaxCharges", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 1366840608, },
	["FlaskExtraCharges3_"] = { type = "Suffix", affix = "of the Plentiful", "(39-46)% increased Charges", statOrder = { 1004 }, level = 32, group = "FlaskIncreasedMaxCharges", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 1366840608, },
	["FlaskExtraCharges4__"] = { type = "Suffix", affix = "of the Bountiful", "(47-54)% increased Charges", statOrder = { 1004 }, level = 47, group = "FlaskIncreasedMaxCharges", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 1366840608, },
	["FlaskExtraCharges5"] = { type = "Suffix", affix = "of the Abundant", "(55-62)% increased Charges", statOrder = { 1004 }, level = 62, group = "FlaskIncreasedMaxCharges", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 1366840608, },
	["FlaskExtraCharges6"] = { type = "Suffix", affix = "of the Ample", "(63-70)% increased Charges", statOrder = { 1004 }, level = 81, group = "FlaskIncreasedMaxCharges", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 1366840608, },
	["FlaskChargesUsed1"] = { type = "Suffix", affix = "of the Apprentice", "(15-17)% reduced Charges per use", statOrder = { 1002 }, level = 1, group = "FlaskChargesUsed", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3139816101, },
	["FlaskChargesUsed2"] = { type = "Suffix", affix = "of the Practitioner", "(18-20)% reduced Charges per use", statOrder = { 1002 }, level = 14, group = "FlaskChargesUsed", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3139816101, },
	["FlaskChargesUsed3__"] = { type = "Suffix", affix = "of the Mixologist", "(21-23)% reduced Charges per use", statOrder = { 1002 }, level = 34, group = "FlaskChargesUsed", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3139816101, },
	["FlaskChargesUsed4__"] = { type = "Suffix", affix = "of the Distiller", "(24-26)% reduced Charges per use", statOrder = { 1002 }, level = 49, group = "FlaskChargesUsed", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3139816101, },
	["FlaskChargesUsed5"] = { type = "Suffix", affix = "of the Brewer", "(27-29)% reduced Charges per use", statOrder = { 1002 }, level = 64, group = "FlaskChargesUsed", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3139816101, },
	["FlaskChargesUsed6"] = { type = "Suffix", affix = "of the Chemist", "(30-32)% reduced Charges per use", statOrder = { 1002 }, level = 83, group = "FlaskChargesUsed", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3139816101, },
	["FlaskChanceRechargeOnKill1"] = { type = "Suffix", affix = "of the Medic", "(21-25)% Chance to gain a Charge when you Kill an Enemy", statOrder = { 1000 }, level = 8, group = "FlaskChanceRechargeOnKill", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 828533480, },
	["FlaskChanceRechargeOnKill2"] = { type = "Suffix", affix = "of the Doctor", "(26-30)% Chance to gain a Charge when you Kill an Enemy", statOrder = { 1000 }, level = 26, group = "FlaskChanceRechargeOnKill", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 828533480, },
	["FlaskChanceRechargeOnKill3"] = { type = "Suffix", affix = "of the Surgeon", "(31-35)% Chance to gain a Charge when you Kill an Enemy", statOrder = { 1000 }, level = 45, group = "FlaskChanceRechargeOnKill", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 828533480, },
	["FlaskFillChargesPerMinute1"] = { type = "Suffix", affix = "of the Foliage", "Gains 0.15 Charges per Second", statOrder = { 6985 }, level = 8, group = "FlaskGainChargePerMinute", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1873752457, },
	["FlaskFillChargesPerMinute2"] = { type = "Suffix", affix = "of the Verdant", "Gains 0.2 Charges per Second", statOrder = { 6985 }, level = 26, group = "FlaskGainChargePerMinute", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1873752457, },
	["FlaskFillChargesPerMinute3"] = { type = "Suffix", affix = "of the Sylvan", "Gains 0.25 Charges per Second", statOrder = { 6985 }, level = 45, group = "FlaskGainChargePerMinute", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1873752457, },
	["CharmIncreasedDuration1"] = { type = "Prefix", affix = "Investigator's", "(16-20)% increased Duration", statOrder = { 903 }, level = 1, group = "CharmIncreasedDuration", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2541588185, },
	["CharmIncreasedDuration2"] = { type = "Prefix", affix = "Analyst's", "(21-25)% increased Duration", statOrder = { 903 }, level = 20, group = "CharmIncreasedDuration", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2541588185, },
	["CharmIncreasedDuration3"] = { type = "Prefix", affix = "Examiner's", "(26-30)% increased Duration", statOrder = { 903 }, level = 42, group = "CharmIncreasedDuration", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2541588185, },
	["CharmIncreasedDuration4"] = { type = "Prefix", affix = "Clinician's", "(31-35)% increased Duration", statOrder = { 903 }, level = 61, group = "CharmIncreasedDuration", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2541588185, },
	["CharmIncreasedDuration5"] = { type = "Prefix", affix = "Experimenter's", "(36-40)% increased Duration", statOrder = { 903 }, level = 78, group = "CharmIncreasedDuration", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2541588185, },
	["CharmGainLifeOnUse1"] = { type = "Prefix", affix = "Herbal", "Recover (8-12) Life when Used", statOrder = { 901 }, level = 1, group = "CharmGainLifeOnUse", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life" }, tradeHash = **********, },
	["CharmGainLifeOnUse2"] = { type = "Prefix", affix = "Floral", "Recover (35-52) Life when Used", statOrder = { 901 }, level = 14, group = "CharmGainLifeOnUse", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life" }, tradeHash = **********, },
	["CharmGainLifeOnUse3"] = { type = "Prefix", affix = "Blooming", "Recover (63-92) Life when Used", statOrder = { 901 }, level = 26, group = "CharmGainLifeOnUse", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life" }, tradeHash = **********, },
	["CharmGainLifeOnUse4"] = { type = "Prefix", affix = "Sprouting", "Recover (96-130) Life when Used", statOrder = { 901 }, level = 36, group = "CharmGainLifeOnUse", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life" }, tradeHash = **********, },
	["CharmGainLifeOnUse5"] = { type = "Prefix", affix = "Petaled", "Recover (134-180) Life when Used", statOrder = { 901 }, level = 47, group = "CharmGainLifeOnUse", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life" }, tradeHash = **********, },
	["CharmGainLifeOnUse6"] = { type = "Prefix", affix = "Botanic", "Recover (185-230) Life when Used", statOrder = { 901 }, level = 58, group = "CharmGainLifeOnUse", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life" }, tradeHash = **********, },
	["CharmGainLifeOnUse7"] = { type = "Prefix", affix = "Natural", "Recover (235-280) Life when Used", statOrder = { 901 }, level = 67, group = "CharmGainLifeOnUse", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life" }, tradeHash = **********, },
	["CharmGainLifeOnUse8"] = { type = "Prefix", affix = "Evergreen", "Recover (285-350) Life when Used", statOrder = { 901 }, level = 76, group = "CharmGainLifeOnUse", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "life" }, tradeHash = **********, },
	["CharmGainManaOnUse1"] = { type = "Prefix", affix = "Drizzling", "Recover (16-24) Mana when Used", statOrder = { 902 }, level = 1, group = "CharmGainManaOnUse", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "mana" }, tradeHash = 1120862500, },
	["CharmGainManaOnUse2"] = { type = "Prefix", affix = "Soaked", "Recover (33-50) Mana when Used", statOrder = { 902 }, level = 14, group = "CharmGainManaOnUse", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "mana" }, tradeHash = 1120862500, },
	["CharmGainManaOnUse3"] = { type = "Prefix", affix = "Mistbound", "Recover (55-75) Mana when Used", statOrder = { 902 }, level = 26, group = "CharmGainManaOnUse", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "mana" }, tradeHash = 1120862500, },
	["CharmGainManaOnUse4"] = { type = "Prefix", affix = "Tidebound", "Recover (80-110) Mana when Used", statOrder = { 902 }, level = 36, group = "CharmGainManaOnUse", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "mana" }, tradeHash = 1120862500, },
	["CharmGainManaOnUse5"] = { type = "Prefix", affix = "Aqueous", "Recover (115-145) Mana when Used", statOrder = { 902 }, level = 47, group = "CharmGainManaOnUse", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "mana" }, tradeHash = 1120862500, },
	["CharmGainManaOnUse6"] = { type = "Prefix", affix = "Flooded", "Recover (150-180) Mana when Used", statOrder = { 902 }, level = 58, group = "CharmGainManaOnUse", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "mana" }, tradeHash = 1120862500, },
	["CharmGainManaOnUse7"] = { type = "Prefix", affix = "Oceanic", "Recover (185-225) Mana when Used", statOrder = { 902 }, level = 67, group = "CharmGainManaOnUse", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "mana" }, tradeHash = 1120862500, },
	["CharmGainManaOnUse8"] = { type = "Prefix", affix = "Raindancer's", "Recover (230-300) Mana when Used", statOrder = { 902 }, level = 76, group = "CharmGainManaOnUse", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = { "resource", "mana" }, tradeHash = 1120862500, },
	["CharmGuardWhileActive1"] = { type = "Prefix", affix = "Sunny", "Gain (44-66) Guard during Effect", statOrder = { 900 }, level = 10, group = "CharmGuardWhileActive", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2676834156, },
	["CharmGuardWhileActive2"] = { type = "Prefix", affix = "Dawnlit", "Gain (85-128) Guard during Effect", statOrder = { 900 }, level = 21, group = "CharmGuardWhileActive", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2676834156, },
	["CharmGuardWhileActive3"] = { type = "Prefix", affix = "Bright", "Gain (148-200) Guard during Effect", statOrder = { 900 }, level = 36, group = "CharmGuardWhileActive", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2676834156, },
	["CharmGuardWhileActive4"] = { type = "Prefix", affix = "Vibrant", "Gain (205-260) Guard during Effect", statOrder = { 900 }, level = 48, group = "CharmGuardWhileActive", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2676834156, },
	["CharmGuardWhileActive5"] = { type = "Prefix", affix = "Lustrous", "Gain (265-350) Guard during Effect", statOrder = { 900 }, level = 60, group = "CharmGuardWhileActive", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2676834156, },
	["CharmGuardWhileActive6"] = { type = "Prefix", affix = "Sunburst", "Gain (355-500) Guard during Effect", statOrder = { 900 }, level = 75, group = "CharmGuardWhileActive", weightKey = { "utility_flask", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 2676834156, },
}