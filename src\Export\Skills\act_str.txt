-- Path of Building
--
-- Active Strength skill gems
-- Skill data (c) Grinding Gear Games
--
local skills, mod, flag, skill = ...

#skill AncestralWarriorTotemPlayer
#set AncestralWarriorTotemPlayer
#flags totem attack
#mods
#skillEnd

#skill SupportAncestralWarriorTotemPlayer
#set SupportAncestralWarriorTotemPlayer
#flags
#mods
#skillEnd

#skill ArmourBreakerPlayer
#set ArmourBreakerPlayer
#flags attack area melee
#mods
#skillEnd

#skill ArtilleryBallistaPlayer
#set ArtilleryBallistaPlayer
#flags totem duration
#mods
#skillEnd

#skill ArtilleryBallistaProjectilePlayer
#set ArtilleryBallistaProjectilePlayer
#flags attack area projectile totem
#mods
#set ArtilleryBallistaProjectileExplodePlayer
#flags attack area projectile totem
#mods
#skillEnd

#skill AttritionPlayer
#set AttritionPlayer
#flags
statMap = {
	["skill_attrition_presence_max_seconds"] = {
		mod("Multiplier:AttritionMaxDamage", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff"}),
	},
	["skill_attrition_culling_strike_at_x_or_more_stacks"] = {
		mod("Multiplier:AttritionCullSeconds", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff"}),
	},
	["skill_attrition_hit_damage_+%_final_vs_rare_or_unique_enemy_per_second_ever_in_presence_up_to_max"] = {
		{mod("Damage", "MORE", nil, 0, KeywordFlag.Hit, { type = "GlobalEffect", effectType = "Buff"}, { type = "Multiplier", var = "EnemyPresenceSeconds", actor = "enemy", limitVar = "AttritionMaxDamage", div = 2, limitTotal = true }, { type = "ActorCondition", actor = "enemy", var = "RareOrUnique" })},
		{mod("CullPercent", "MAX", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff"}, { type = "MultiplierThreshold", var = "EnemyPresenceSeconds", actor = "enemy", thresholdVar = "AttritionCullSeconds"}, { type = "ActorCondition", actor = "enemy", var = "RareOrUnique" }),
		value = 10,}
		},
},
#mods
#skillEnd

#skill BerserkPlayer
#set BerserkPlayer
#flags
statMap = {
	["life_loss_%_per_minute_per_rage_while_not_losing_rage"] = {
		mod("LifeDegen", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff" }, { type = "Multiplier", var = "RageEffect" }),
		div = 60
	},
	["life_leech_from_physical_attack_damage_permyriad_per_rage"] = {
		mod("PhysicalDamageLifeLeech", "BASE", nil, ModFlag.Attack, 0, { type = "GlobalEffect", effectType = "Buff" }, { type = "Multiplier", var = "RageEffect" }),
		div = 100,
	},
	["rage_effect_+%"] = {
		mod( "RageEffect", "INC", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff" } ),
	}
},
#mods
#skillEnd

#skill BoneshatterPlayer
#set BoneshatterPlayer
#flags attack melee
#mods
#set BoneshatterShockwavePlayer
#flags attack melee area
#mods
#skillEnd

#from item
#skill MetaCastOnBlockPlayer
#set MetaCastOnBlockPlayer
#flags
#mods
#skillEnd

#from item
#skill SupportMetaCastOnBlockPlayer
#set SupportMetaCastOnBlockPlayer
#flags
#mods
#skillEnd

#from item
#skill MetaCastOnMeleeKillPlayer
#set MetaCastOnMeleeKillPlayer
#flags
#mods
#skillEnd

#from item
#skill SupportMetaCastOnMeleeKillPlayer
#set SupportMetaCastOnMeleeKillPlayer
#flags
#mods
#skillEnd

#from item
#skill MetaCastOnMeleeStunPlayer
#set MetaCastOnMeleeStunPlayer
#flags
#mods
#skillEnd

#from item
#skill SupportMetaCastOnMeleeStunPlayer
#set SupportMetaCastOnMeleeStunPlayer
#flags
#mods
#skillEnd

#skill ClusterGrenadePlayer
#set ClusterGrenadePlayer
#flags attack area projectile duration
#mods
#skillEnd

#skill ClusterGrenadeMiniPlayer
#set ClusterGrenadeMiniPlayer
#flags attack area projectile
#mods
#skillEnd

#from item
#skill StaffConsecratePlayer
#set StaffConsecratePlayer
#flags spell
#mods
#skillEnd

#skill DefianceBannerReservationPlayer
#skillEnd

#skill DefianceBannerPlayer
#set DefianceBannerPlayer
#flags duration
statMap = {
	["base_skill_buff_armour_evasion_+%_final_to_apply"] = {
		mod("Armour", "MORE", nil, 0, 0, { type = "Condition", var = "BannerPlanted" }, { type = "GlobalEffect", effectType = "Aura"}),
		mod("Evasion", "MORE", nil, 0, 0, { type = "Condition", var = "BannerPlanted" }, { type = "GlobalEffect", effectType = "Aura"}),
	},
	["base_skill_buff_movement_speed_+%_to_apply"] = {
		mod("MovementSpeed", "INC", nil, 0, 0, { type = "Condition", var = "BannerPlanted" }, { type = "GlobalEffect", effectType = "Aura"}),
	},
},
#mods
#skillEnd

#skill DreadBannerReservationPlayer
#skillEnd

#skill DreadBannerPlayer
#set DreadBannerPlayer
#flags duration
statMap = {
	["base_skill_buff_flask_charge_per_min_to_apply"] = {
		mod("FlaskChargesGenerated", "BASE", nil, 0, 0, { type = "Condition", var = "BannerPlanted" }, { type = "GlobalEffect", effectType = "Aura"}),
		div = 60,
	},
	["base_skill_buff_stun_and_ailment_threshold_+%_final_to_apply"] = {
		mod("AilmentThreshold", "MORE", nil, 0, 0, { type = "Condition", var = "BannerPlanted" }, { type = "GlobalEffect", effectType = "Aura"}),
		mod("StunThreshold", "MORE", nil, 0, 0, { type = "Condition", var = "BannerPlanted" }, { type = "GlobalEffect", effectType = "Aura"}),
	},
},
#mods
#skillEnd

#skill EarthquakePlayer
#set EarthquakePlayer
#flags attack melee area duration
statMap = {
	["active_skill_base_area_of_effect_radius"] = {
		skill("radius", nil),
	},
	["skill_jagged_ground_base_duration_ms"] = {
		skill("duration", nil),
		div = 1000,
	},
},
#mods
#set EarthquakeAftershockPlayer
#flags attack melee area
statMap = {
	["jagged_ground_effect_+%"] = {
		mod("Damage", "MORE", nil, 0, 0),
	},
	["active_skill_base_tertiary_area_of_effect_radius"] = {
		skill("radius", nil),
	},
},
#baseMod skill("showAverage", true)
#mods
#skillEnd

#skill EarthshatterPlayer
#set EarthshatterPlayer
#flags attack area melee
#mods
#set EarthshatterSpikePlayer
#flags attack area melee duration
#mods
#skillEnd

#skill EmergencyReloadPlayer
#set EmergencyReloadPlayer
#flags duration
#mods
#skillEnd

#skill ExplosiveGrenadePlayer
#set ExplosiveGrenadePlayer
#flags attack area projectile
#mods
#skillEnd

#from item
#skill ExsanguinatePlayer
#set ExsanguinatePlayer
#flags spell duration chaining
#mods
#set ExsanguinateDotPlayer
#flags spell duration chaining
#mods
#skillEnd

#skill FlashGrenadePlayer
#set FlashGrenadePlayer
#flags attack area projectile
#mods
#skillEnd

#skill ToxicGrenadePlayer
#set ToxicGrenadePlayer
#flags attack area projectile duration
#mods
#set ToxicGrenadeCloudPlayer
#flags attack area projectile duration
#mods
#set ToxicGrenadeCloudExplosionPlayer
#flags attack area projectile
#mods
#skillEnd

#skill HammerOfTheGodsPlayer
#set HammerOfTheGodsPlayer
#flags attack area melee duration
#mods
#skillEnd

#skill HeraldOfAshPlayer
#set HeraldOfAshPlayer
#flags
statMap = {
	["herald_of_ash_overkill_threshold_%"] = {
		mod("HeraldOfAshBuff", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Herald of Ash" }),
	},
},
#mods
#set HeraldOfAshOnKillPlayer
#flags
#mods
#skillEnd

#skill HeraldOfBloodPlayer
#set HeraldOfBloodPlayer
#flags
#mods
#set HeraldOfBloodExplosionPlayer
#flags
#mods
#skillEnd

#skill InfernalCryPlayer
#set InfernalCryPlayer
#flags warcry area duration
#mods
#skillEnd

#skill InfernalCryCorpseExplosionPlayer
#set InfernalCryCorpseExplosionPlayer
#flags area hit
#mods
#skillEnd

#skill LeapSlamPlayer
#set LeapSlamPlayer
#flags attack area melee
#mods
#skillEnd

#skill MetaCastLightningSpellOnHitPlayer
#set MetaCastLightningSpellOnHitPlayer
#flags spell
#mods
#set SupportMetaCastLightningSpellOnHitPlayer
#flags
#mods
#skillEnd

#skill ArmourPiercingBoltsAmmoPlayer
#set ArmourPiercingBoltsAmmoPlayer
#flags
#mods
#skillEnd

#skill ArmourPiercingBoltsPlayer
#set ArmourPiercingBoltsPlayer
#flags attack projectile
#mods
#skillEnd

#skill ExplosiveShotAmmoPlayer
#set ExplosiveShotAmmoPlayer
#flags
#mods
#skillEnd

#skill ExplosiveShotPlayer
#set ExplosiveShotPlayer
#flags attack projectile
#mods
#set ExplosiveShotExplosionPlayer
#flags attack projectile area 
#mods
#skillEnd

#skill FragmentationRoundsAmmoPlayer
#set FragmentationRoundsAmmoPlayer
#flags
#mods
#skillEnd

#skill FragmentationRoundsPlayer
#set FragmentationRoundsPlayer
#flags attack projectile
#mods
#set FragmentationRoundsFreezeShatterPlayer
#flags attack projectile area
#mods
#skillEnd

#skill GalvanicShardsAmmoPlayer
#set GalvanicShardsAmmoPlayer
#flags
#mods
#skillEnd

#skill GalvanicShardsPlayer
#set GalvanicShardsPlayer
#flags attack projectile
#mods
#set GalvanicShardsBeamPlayer
#flags attack projectile
#mods
#skillEnd

#skill GlacialBoltAmmoPlayer
#set GlacialBoltAmmoPlayer
#flags
#mods
#skillEnd

#skill GlacialBoltPlayer
#set GlacialBoltPlayer
#flags attack projectile
#mods
#set GlacialBoltWallPlayer
#flags attack area duration projectile
#mods
#skillEnd

#skill HailstormRoundsAmmoPlayer
#set HailstormRoundsAmmoPlayer
#flags
#mods
#skillEnd

#skill HailstormRoundsPlayer
#set HailstormRoundsPlayer
#flags attack projectile area
#mods
#skillEnd

#skill HighVelocityRoundsAmmoPlayer
#set HighVelocityRoundsAmmoPlayer
#flags
#mods
#skillEnd

#skill HighVelocityRoundsPlayer
#set HighVelocityRoundsPlayer
#flags attack projectile
#mods
#set HighVelocityRoundsArmourBrokenPlayer
#flags attack projectile
#mods
#skillEnd

#skill IceShardsAmmoPlayer
#set IceShardsAmmoPlayer
#flags
#mods
#skillEnd

#skill IceShardsPlayer
#set IceShardsPlayer
#flags attack projectile
#mods
#set IceShardsShardPlayer
#flags attack projectile area
#baseMod flag("CanCreateHazards")
#mods
#skillEnd

#skill IncendiaryShotAmmoPlayer
#set IncendiaryShotAmmoPlayer
#flags
#mods
#skillEnd

#skill IncendiaryShotPlayer
#set IncendiaryShotPlayer
#flags attack projectile
#mods
#skillEnd

#skill PermafrostBoltsAmmoPlayer
#set PermafrostBoltsAmmoPlayer
#flags
#mods
#skillEnd

#skill PermafrostBoltsPlayer
#set PermafrostBoltsPlayer
#flags attack projectile
#mods
#skillEnd

#skill PlasmaBlastAmmoPlayer
#set PlasmaBlastAmmoPlayer
#flags
#mods
#skillEnd

#skill PlasmaBlastPlayer
#set PlasmaBlastPlayer
#flags attack projectile channelRelease
#mods
#set PlasmaBlastExplosionPlayer
#flags attack projectile area channelRelease
#mods
#skillEnd

#skill RapidShotAmmoPlayer
#set RapidShotAmmoPlayer
#flags
#mods
#skillEnd

#skill RapidShotPlayer
#set RapidShotPlayer
#flags attack projectile
#mods
#skillEnd

#skill ShockburstRoundsAmmoPlayer
#set ShockburstRoundsAmmoPlayer
#flags
#mods
#skillEnd

#skill ShockburstRoundsPlayer
#set ShockburstRoundsPlayer
#flags attack projectile
#mods
#set ShockburstRoundsExplosionPlayer
#flags attack projectile area
#mods
#skillEnd

#skill SiegeCascadeAmmoPlayer
#set SiegeCascadeAmmoPlayer
#flags
#mods
#skillEnd

#skill SiegeCascadePlayer
#set SiegeCascadePlayer
#flags attack projectile area
statMap = {
	["siege_cascade_damage_+%_final_vs_immobilised_enemies"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "ActorCondition", actor = "enemy", var = "Immobilised" }),
	},
},
#mods
#set SiegeCascadeExplodePlayer
#flags attack projectile area
statMap = {
	["siege_cascade_damage_+%_final_vs_immobilised_enemies"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "ActorCondition", actor = "enemy", var = "Immobilised" }),
	},
},
#mods
#skillEnd

#skill StormblastBoltsAmmoPlayer
#set StormblastBoltsAmmoPlayer
#flags
#mods
#skillEnd

#skill StormblastBoltsPlayer
#set StormblastBoltsPlayer
#flags attack projectile area
#mods
#set StormblastBoltsExplosionPlayer
#flags attack projectile area
#mods
#skillEnd

#skill MagmaBarrierPlayer
#set MagmaBarrierPlayer
#flags
#mods
#skillEnd

#skill MagmaSprayPlayer
#set MagmaSprayPlayer
#flags attack area melee shieldAttack
#mods
#skillEnd

#skill MoltenBlastPlayer
#set MoltenBlastPlayer
#flags attack area projectile
#mods
#set MoltenBlastSecondaryPlayer
#flags attack projectile
#mods
#skillEnd

#skill OilGrenadePlayer
#set OilGrenadePlayer
#flags attack area projectile duration
#mods
#skillEnd

#skill OverwhelmingPresencePlayer
#set OverwhelmingPresencePlayer
#flags
#mods
#skillEnd

#skill PerfectStrikePlayer
preDamageFunc = function(activeSkill, output)
	activeSkill.skillData.hitTimeMultiplier = activeSkill.skillData.channelPercentOfAttackTime
end,
#set PerfectStrikePlayer
#flags attack area melee duration channelRelease
#mods
#set PerfectStrikeShockwavePlayer
#flags attack area melee duration channelRelease
#baseMod mod("Condition:PerfectTiming", "FLAG", true)
#mods
#skillEnd

#from item
#skill ReapPlayer
#set ReapPlayer
#flags spell area
#mods
#skillEnd

#skill ResonatingShieldPlayer
#set ResonatingShieldPlayer
#flags attack area melee shieldAttack
#mods
#skillEnd

#skill RipwireBallistaPlayer
#set RipwireBallistaPlayer
#flags totem
#mods
#skillEnd

#skill RipwireBallistaProjectilePlayer
#set RipwireBallistaProjectilePlayer
#flags attack projectile totem
#mods
#skillEnd

#skill RollingSlamPlayer
#set RollingSlamPlayer
#flags attack area melee
#mods
#set RollingSlamFirstSlamPlayer
#flags attack area melee
#mods
#set RollingSlamSecondSlamPlayer
#flags attack area melee
#mods
#skillEnd

#skill ScavengedPlatingPlayer
#set ScavengedPlatingPlayer
#flags duration
statMap = {
	["scavenged_plating_armour_+%_final_per_stack"] = {
		mod("Armour", "MORE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Scavenged Plating" }, { type = "Multiplier", var = "ScavengedPlatingStacks", limitVar = "ScavengedPlatingStacksLimit" }),
	},
	["scavenged_plating_maximum_stacks_display"] = {
		mod("Multiplier:ScavengedPlatingStacksLimit", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Scavenged Plating"}),
	},
},
#mods
#skillEnd

#skill SeismicCryPlayer
#set SeismicCryPlayer
#flags warcry hit area
#mods
#skillEnd

#skill ShardScavengerPlayer
#set ShardScavengerPlayer
#flags
#mods
#skillEnd

#skill ShieldChargePlayer
#set ShieldChargePlayer
#flags attack area melee shieldAttack
#mods
#set ShieldChargeFinalConePlayer
#flags attack area melee shieldAttack
#mods
#skillEnd

#skill ShieldWallPlayer
#set ShieldWallPlayer
#flags attack shieldAttack melee
#mods
#skillEnd

#skill ShockwaveTotemPlayer
#set ShockwaveTotemPlayer
#flags totem
#mods
#skillEnd

#skill ShockwaveTotemQuakePlayer
#set ShockwaveTotemQuakePlayer
#flags totem attack area melee
#mods
#set ShockwaveTotemJaggedQuakePlayer
#flags totem attack area
#mods
#skillEnd

#skill StampedePlayer
#set StampedePlayer
#flags attack area melee
#mods
#set StampedeSlamPlayer
#flags attack area melee
#mods
#set StampedeEruptionPlayer
#flags attack area melee
#mods
#skillEnd

#skill SunderPlayer
#set SunderPlayer
#flags attack area melee
#mods
#set SunderShockwavePlayer
#flags attack area
#mods
#skillEnd

#skill SuperchargedSlamPlayer
#set SuperchargedSlamPlayer
#flags attack area melee
#mods
#set SuperchargedSlamAftershockPlayer
#flags attack area melee
#mods
#skillEnd

#skill TimeOfNeedPlayer
#set TimeOfNeedPlayer
#flags duration
#mods
#skillEnd

#skill VolcanicFissurePlayer
#set VolcanicFissurePlayer
#flags attack area melee duration
#mods
#skillEnd

#skill ShockGrenadePlayer
#set ShockGrenadePlayer
#flags attack area projectile
#mods
#skillEnd

#skill WarBannerReservationPlayer
#skillEnd

#skill WarBannerPlayer
#set WarBannerPlayer
#flags duration
statMap = {
	["base_skill_buff_attack_damage_+%_final_to_apply"] = {
		mod("Damage", "MORE", nil,  ModFlag.Attack, 0, { type = "Condition", var = "BannerPlanted" }, { type = "GlobalEffect", effectType = "Aura"}),
	},
	["base_skill_buff_banner_accuracy_+%_to_apply"] = {
		mod("Accuracy", "INC", nil, 0, 0, { type = "Condition", var = "BannerPlanted" }, { type = "GlobalEffect", effectType = "Aura"}),
	},
},
#mods
#skillEnd