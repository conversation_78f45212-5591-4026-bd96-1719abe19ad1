-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more attack speed while Dual Wielding"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less attack speed while Dual Wielding"
			}
		},
		stats={
			[1]="active_skill_attack_speed_+%_final_while_dual_wielding"
		}
	},
	["active_skill_attack_speed_+%_final_while_dual_wielding"]=1,
	parent="skill_stat_descriptions"
}