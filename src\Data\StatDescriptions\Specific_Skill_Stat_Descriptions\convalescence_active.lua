-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d} second to Buff duration"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d} seconds to Buff duration"
			},
			[3]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Buff duration is {0} second"
			},
			[4]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Buff duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration",
			[2]="quality_display_base_skill_effect_duration_is_gem"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Cooldown does not recover during Buff effect"
			}
		},
		stats={
			[1]="display_this_skill_cooldown_does_not_recover_during_buff"
		}
	},
	[3]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	["base_skill_effect_duration"]=1,
	["display_this_skill_cooldown_does_not_recover_during_buff"]=2,
	parent="skill_stat_descriptions",
	["quality_display_base_skill_effect_duration_is_gem"]=1,
	["skill_effect_duration"]=3
}