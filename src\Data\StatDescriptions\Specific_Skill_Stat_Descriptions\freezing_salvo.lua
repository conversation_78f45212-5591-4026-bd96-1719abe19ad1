-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Impact radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Impact radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Accumulates up to {0} additional missiles"
			}
		},
		stats={
			[1]="freezing_salvo_maximum_number_of_seals"
		}
	},
	[4]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="Delay between missile accumulation is {0} seconds shorter"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1,
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Accumulates a missile every {0} seconds"
			}
		},
		stats={
			[1]="freezing_salvo_seals_gain_base_interval_ms",
			[2]="quality_display_freezing_salvo_is_gem"
		}
	},
	[5]={
		[1]={
		},
		stats={
			[1]="freezing_salvo_seals_gain_interval_ms"
		}
	},
	["active_skill_area_of_effect_radius"]=1,
	["active_skill_base_area_of_effect_radius"]=2,
	["freezing_salvo_maximum_number_of_seals"]=3,
	["freezing_salvo_seals_gain_base_interval_ms"]=4,
	["freezing_salvo_seals_gain_interval_ms"]=5,
	parent="skill_stat_descriptions",
	["quality_display_freezing_salvo_is_gem"]=4
}