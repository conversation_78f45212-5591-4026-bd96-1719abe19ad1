-- These are manually input, as they are unique mods they are hard to grab from trade
-- The ones for keystones could easily be generated from the names, but the devotion ones cannot

return {
	[1] = {
		keystone = {
			[1] = "explicit.pseudo_timeless_jewel_doryani",
			[2] = "explicit.pseudo_timeless_jewel_xibaqua",
			[3] = "explicit.pseudo_timeless_jewel_ahuana"
		}
	},
	[2] = {
		keystone = {
			[1] = "explicit.pseudo_timeless_jewel_kaom",
			[2] = "explicit.pseudo_timeless_jewel_rakiata",
			[3] = "explicit.pseudo_timeless_jewel_akoya"
		}
	},
	[3] = {
		keystone = {
			[1] = "explicit.pseudo_timeless_jewel_asenath",
			[2] = "explicit.pseudo_timeless_jewel_nasima",
			[3] = "explicit.pseudo_timeless_jewel_balbala"
		}
	},
	[4] = {
		keystone = {
			[1] = "explicit.pseudo_timeless_jewel_avarius",  
			[2] = "explicit.pseudo_timeless_jewel_dominus",
			[3] = "explicit.pseudo_timeless_jewel_maxarius"
		},
		devotion = {
			[1] = "explicit.stat_2566390555",
			[2] = "explicit.stat_2697019412",
			[3] = "explicit.stat_970844066",
			[4] = "explicit.stat_1724614884",
			[5] = "explicit.stat_3103189267",
			[6] = "explicit.stat_1910205563",
			[7] = "explicit.stat_1810368194",
			[8] = "explicit.stat_730530528",
			[9] = "explicit.stat_4235333770",
			[10] = "explicit.stat_3808469650",
			[11] = "explicit.stat_2830135449",
			[12] = "explicit.stat_2042813020",
			[13] = "explicit.stat_3293275880",
			[14] = "explicit.stat_2585926696",
			[15] = "explicit.stat_2803981661"
		}
	},
	[5] = {
		keystone = {
			[1] = "explicit.pseudo_timeless_jewel_cadiro",
			[2] = "explicit.pseudo_timeless_jewel_victario",
			[3] = "explicit.pseudo_timeless_jewel_caspiro"
		}
	},
}