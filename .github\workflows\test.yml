name: Run Tests
on:
  push:
    branches: [ dev ]
  pull_request:
    branches: [ dev ]
  workflow_dispatch:
jobs:
  run_tests:
    runs-on: ubuntu-latest
    container: ghcr.io/pathofbuildingcommunity/pathofbuilding-tests:latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Run tests
        run: busted --lua=luajit
      - name: Report coverage
        run: cd src; luacov-coveralls --repo-token=${{ secrets.github_token }} -e TestData -e Data -e runtime
