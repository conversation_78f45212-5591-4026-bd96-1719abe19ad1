-- Path of Building
--
-- Module: Calc Sections
-- List of sections for the Calcs tab
--

-- Commonly used modifier lists
local physicalHitTaken = {
	"DamageTaken", "PhysicalDamageTaken", "CurseEffectOnSelf", "Armour", "IgnoreArmour"
}
local lightningHitTaken = {
	"DamageTaken", "LightningDamageTaken", "ElementalDamageTaken", "LightningResist", "ElementalResist", "CurseEffectOnSelf"
}
local coldHitTaken = {
	"DamageTaken", "ColdDamageTaken", "ElementalDamageTaken", "ColdResist", "ElementalResist", "CurseEffectOnSelf"
}
local fireHitTaken = {
	"DamageTaken", "FireDamageTaken", "ElementalDamageTaken", "FireResist", "ElementalResist", "CurseEffectOnSelf"
}
local chaosHitTaken = {
	"DamageTaken", "ChaosDamageTaken", "ChaosResist", "CurseEffectOnSelf"
}

local function fillConvert(damageType)
	local convert = {}
	for _, type in ipairs({ "Physical", "Lightning", "Cold", "Fire", "Chaos" }) do
		table.insert(convert, "Skill"..damageType.."DamageConvertTo"..type)
		table.insert(convert, "SkillDamageGainAs"..type)
		table.insert(convert, "Skill"..damageType.."DamageGainAs"..type)
		table.insert(convert, damageType.."DamageConvertTo"..type)
		table.insert(convert, "DamageGainAs"..type)
		table.insert(convert, damageType.."DamageGainAs"..type)

		if type ~= "Chaos" and type ~= "Physical" then
			table.insert(convert, "ElementalDamageConvertTo"..type)
			table.insert(convert, "ElementalDamageGainAs"..type)
		end
	end
	return convert
end

local physicalConvert = fillConvert("Physical")
local lightningConvert = fillConvert("Lightning")
local coldConvert = fillConvert("Cold")
local fireConvert = fillConvert("Fire")
local chaosConvert = fillConvert("Chaos")

-- format {width, id, group, color, subsection:{default hidden, label, data:{}}}
return {
{ 3, "HitDamage", 1, colorCodes.OFFENCE, {{ defaultCollapsed = false, label = "Skill Hit Damage", data = {
	extra = "{output:DisplayDamage}",
	flag = "hit",
	colWidth = 95,
	{
		{ format = "All Types:", },
		{ format = "Physical:" },
		{ format = colorCodes.LIGHTNING.."Lightning:" },
		{ format = colorCodes.COLD.."Cold:" },
		{ format = colorCodes.FIRE.."Fire:" },
		{ format = colorCodes.CHAOS.."Chaos:" },
	},
	{ label = "Added Min",
		{ },
		{ format = "{0:mod:1,2}", 
			{ label = "Player modifiers", modName = "PhysicalMin", modType = "BASE", cfg = "skill" },
			{ label = "Enemy modifiers", modName = "SelfPhysicalMin", modType = "BASE", enemy = true, cfg = "skill" },
		},
		{ format = "{0:mod:1,2}", 
			{ label = "Player modifiers", modName = "LightningMin", modType = "BASE", cfg = "skill" },
			{ label = "Enemy modifiers", modName = "SelfLightningMin", modType = "BASE", enemy = true, cfg = "skill" },
		},
		{ format = "{0:mod:1,2}", 
			{ label = "Player modifiers", modName = "ColdMin", modType = "BASE", cfg = "skill" },
			{ label = "Enemy modifiers", modName = "SelfColdMin", modType = "BASE", enemy = true, cfg = "skill" },
		},
		{ format = "{0:mod:1,2}", 
			{ label = "Player modifiers", modName = "FireMin", modType = "BASE", cfg = "skill" },
			{ label = "Enemy modifiers", modName = "SelfFireMin", modType = "BASE", enemy = true, cfg = "skill" },
		},
		{ format = "{0:mod:1,2}", 
			{ label = "Player modifiers", modName = "ChaosMin", modType = "BASE", cfg = "skill" },
			{ label = "Enemy modifiers", modName = "SelfChaosMin", modType = "BASE", enemy = true, cfg = "skill" },
		},
	},
	{ label = "Added Max",
		{ },
		{ format = "{0:mod:1,2}", 
			{ label = "Player modifiers", modName = "PhysicalMax", modType = "BASE", cfg = "skill" },
			{ label = "Enemy modifiers", modName = "SelfPhysicalMax", modType = "BASE", enemy = true, cfg = "skill" },
		},
		{ format = "{0:mod:1,2}", 
			{ label = "Player modifiers", modName = "LightningMax", modType = "BASE", cfg = "skill" },
			{ label = "Enemy modifiers", modName = "SelfLightningMax", modType = "BASE", enemy = true, cfg = "skill" },
		},
		{ format = "{0:mod:1,2}", 
			{ label = "Player modifiers", modName = "ColdMax", modType = "BASE", cfg = "skill" },
			{ label = "Enemy modifiers", modName = "SelfColdMax", modType = "BASE", enemy = true, cfg = "skill" },
		},
		{ format = "{0:mod:1,2}", 
			{ label = "Player modifiers", modName = "FireMax", modType = "BASE", cfg = "skill" },
			{ label = "Enemy modifiers", modName = "SelfFireMax", modType = "BASE", enemy = true, cfg = "skill" },
		},
		{ format = "{0:mod:1,2}", 
			{ label = "Player modifiers", modName = "ChaosMax", modType = "BASE", cfg = "skill" },
			{ label = "Enemy modifiers", modName = "SelfChaosMax", modType = "BASE", enemy = true, cfg = "skill" },
		},
	},
	-- Skill Hit Damage
	{ label = "Total Increased", notFlag = "attack",
		{ format = "{0:mod:1}%", { modName = "Damage", modType = "INC", cfg = "skill" }, },
		{ format = "{0:mod:1}%", { modName = "PhysicalDamage", modType = "INC", cfg = "skill" }, },
		{ format = "{0:mod:1}%", { modName = { "LightningDamage", "ElementalDamage" }, modType = "INC", cfg = "skill" }, },
		{ format = "{0:mod:1}%", { modName = { "ColdDamage", "ElementalDamage" }, modType = "INC", cfg = "skill" }, },
		{ format = "{0:mod:1}%", { modName = { "FireDamage", "ElementalDamage" }, modType = "INC", cfg = "skill" }, },
		{ format = "{0:mod:1}%", { modName = "ChaosDamage", modType = "INC", cfg = "skill" }, },
	},
	{ label = "Total More", notFlag = "attack",
		{ format = "{0:mod:1}%", { modName = "Damage", modType = "MORE", cfg = "skill" }, },
		{ format = "{0:mod:1}%", { modName = "PhysicalDamage", modType = "MORE", cfg = "skill" }, },
		{ format = "{0:mod:1}%", { modName = { "LightningDamage", "ElementalDamage" }, modType = "MORE", cfg = "skill" }, },
		{ format = "{0:mod:1}%", { modName = { "ColdDamage", "ElementalDamage" }, modType = "MORE", cfg = "skill" }, },
		{ format = "{0:mod:1}%", { modName = { "FireDamage", "ElementalDamage" }, modType = "MORE", cfg = "skill" }, },
		{ format = "{0:mod:1}%", { modName = "ChaosDamage", modType = "MORE", cfg = "skill" }, },
	},
	{ label = "Effective DPS Mod", notFlag = "attack", flag = "effective",
		{ },
		{ format = "x {3:output:PhysicalEffMult}",
			{ breakdown = "PhysicalEffMult" },
			{ label = "Enemy modifiers", modName = physicalHitTaken, enemy = true, cfg = "skill" }, 
		},
		{ format = "x {3:output:LightningEffMult}",
			{ breakdown = "LightningEffMult" },
			{ label = "Player modifiers", modName = { "LightningPenetration", "ElementalPenetration", "IgnoreLightningResistance", "IgnoreNonNegativeEleRes", "IgnoreElementalResistances" }, cfg = "skill" },
			{ label = "Enemy modifiers", modName = lightningHitTaken, enemy = true, cfg = "skill" },
		},
		{ format = "x {3:output:ColdEffMult}",
			{ breakdown = "ColdEffMult" },
			{ label = "Player modifiers", modName = { "ColdPenetration", "ElementalPenetration", "IgnoreColdResistance", "IgnoreNonNegativeEleRes", "IgnoreElementalResistances" }, cfg = "skill" },
			{ label = "Enemy modifiers", modName = coldHitTaken, enemy = true, cfg = "skill" },
		},
		{ format = "x {3:output:FireEffMult}",
			{ breakdown = "FireEffMult" },
			{ label = "Player modifiers", modName = { "FirePenetration", "ElementalPenetration", "IgnoreFireResistance", "IgnoreNonNegativeEleRes", "IgnoreElementalResistances" }, cfg = "skill" },
			{ label = "Enemy modifiers", modName = fireHitTaken, enemy = true, cfg = "skill" },
		},
		{ format = "x {3:output:ChaosEffMult}",
			{ breakdown = "ChaosEffMult" },
			{ label = "Player modifiers", modName = {"ChaosPenetration", "IgnoreChaosResistance"}, cfg = "skill" },
			{ label = "Enemy modifiers", modName = chaosHitTaken, enemy = true, cfg = "skill" },
		},
	},
	{ label = "Skill Hit Damage", textSize = 12, notFlag = "attack",
		{ format = "{0:output:TotalMin} to {0:output:TotalMax}", },
		{ format = "{0:output:PhysicalMin} to {0:output:PhysicalMax}", 
			{ breakdown = "Physical" }, 
			{ label = "Conversions", modType = "BASE", cfg = "skill", modName = physicalConvert }, 
		},
		{ format = "{0:output:LightningMin} to {0:output:LightningMax}", 
			{ breakdown = "Lightning" }, 
			{ label = "Conversions", modType = "BASE", cfg = "skill", modName = lightningConvert }, 
		},
		{ format = "{0:output:ColdMin} to {0:output:ColdMax}", 
			{ breakdown = "Cold" }, 
			{ label = "Conversions", modType = "BASE", cfg = "skill", modName = coldConvert }, 
		},
		{ format = "{0:output:FireMin} to {0:output:FireMax}", 
			{ breakdown = "Fire" }, 
			{ label = "Conversions", modType = "BASE", cfg = "skill", modName = fireConvert }, 
		},
		{ format = "{0:output:ChaosMin} to {0:output:ChaosMax}", 
			{ breakdown = "Chaos" }, 
			{ label = "Conversions", modType = "BASE", cfg = "skill", modName = chaosConvert }, 
		},
	},
	{ label = "Skill Average Hit", notFlag = "attack", { format = "{1:output:AverageHit}", { breakdown = "AverageHit" }, }, },
	{ label = "Skill PvP Average Hit", flag = "notAttackPvP", { format = "{1:output:PvpAverageHit}", { breakdown = "PvpAverageHit" }, 
		{ label = "Tvalue Override (ms)", modName = "MultiplierPvpTvalueOverride" }, 
		{ label = "PvP Multiplier", cfg = "skill", modName = "PvpDamageMultiplier" }, 
	}, },
	-- Main Hand Hit Damage
	{ label = "MH Total Increased", bgCol = colorCodes.MAINHANDBG, flag = "weapon1Attack",
		{ format = "{0:mod:1}%", { modName = "Damage", modType = "INC", cfg = "weapon1" }, },
		{ format = "{0:mod:1}%", { modName = "PhysicalDamage", modType = "INC", cfg = "weapon1" }, },
		{ format = "{0:mod:1}%", { modName = { "LightningDamage", "ElementalDamage" }, modType = "INC", cfg = "weapon1" }, },
		{ format = "{0:mod:1}%", { modName = { "ColdDamage", "ElementalDamage" }, modType = "INC", cfg = "weapon1" }, },
		{ format = "{0:mod:1}%", { modName = { "FireDamage", "ElementalDamage" }, modType = "INC", cfg = "weapon1" }, },
		{ format = "{0:mod:1}%", { modName = "ChaosDamage", modType = "INC", cfg = "weapon1" }, },
	},
	{ label = "MH Total More", bgCol = colorCodes.MAINHANDBG, flag = "weapon1Attack",
		{ format = "{0:mod:1}%", { modName = "Damage", modType = "MORE", cfg = "weapon1" }, },
		{ format = "{0:mod:1}%", { modName = "PhysicalDamage", modType = "MORE", cfg = "weapon1" }, },
		{ format = "{0:mod:1}%", { modName = { "LightningDamage", "ElementalDamage" }, modType = "MORE", cfg = "weapon1" }, },
		{ format = "{0:mod:1}%", { modName = { "ColdDamage", "ElementalDamage" }, modType = "MORE", cfg = "weapon1" }, },
		{ format = "{0:mod:1}%", { modName = { "FireDamage", "ElementalDamage" }, modType = "MORE", cfg = "weapon1" }, },
		{ format = "{0:mod:1}%", { modName = "ChaosDamage", modType = "MORE", cfg = "weapon1" }, },
	},
	{ label = "MH Eff. DPS Mod", bgCol = colorCodes.MAINHANDBG, flagList = {"weapon1Attack","effective"},
		{ },
		{ format = "x {3:output:MainHand.PhysicalEffMult}",
			{ breakdown = "MainHand.PhysicalEffMult" },
			{ label = "Enemy modifiers", modName = physicalHitTaken, enemy = true, cfg = "weapon1" }, 
		},
		{ format = "x {3:output:MainHand.LightningEffMult}",
			{ breakdown = "MainHand.LightningEffMult" },
			{ label = "Player modifiers", modName = { "LightningPenetration", "ElementalPenetration" }, cfg = "weapon1" },
			{ label = "Enemy modifiers", modName = lightningHitTaken, enemy = true, cfg = "weapon1" },
		},
		{ format = "x {3:output:MainHand.ColdEffMult}",
			{ breakdown = "MainHand.ColdEffMult" },
			{ label = "Player modifiers", modName = { "ColdPenetration", "ElementalPenetration" }, cfg = "weapon1" },
			{ label = "Enemy modifiers", modName = coldHitTaken, enemy = true, cfg = "weapon1" },
		},
		{ format = "x {3:output:MainHand.FireEffMult}",
			{ breakdown = "MainHand.FireEffMult" },
			{ label = "Player modifiers", modName = { "FirePenetration", "ElementalPenetration" }, cfg = "weapon1" },
			{ label = "Enemy modifiers", modName = fireHitTaken, enemy = true, cfg = "weapon1" },
		},
		{ format = "x {3:output:MainHand.ChaosEffMult}",
			{ breakdown = "MainHand.ChaosEffMult" },
			{ label = "Player modifiers", modName = "ChaosPenetration", cfg = "weapon1" },
			{ label = "Enemy modifiers", modName = chaosHitTaken, enemy = true, cfg = "weapon1" },
		},
	},
	{ label = "MH Hit Damage", bgCol = colorCodes.MAINHANDBG, textSize = 12, flag = "weapon1Attack",
		{ format = "{0:output:MainHand.TotalMin} to {0:output:MainHand.TotalMax}", },
		{ format = "{0:output:MainHand.PhysicalMin} to {0:output:MainHand.PhysicalMax}", 
			{ breakdown = "MainHand.Physical" }, 
			{ label = "Conversions", modType = "BASE", cfg = "weapon1", modName = physicalConvert },
		},
		{ format = "{0:output:MainHand.LightningMin} to {0:output:MainHand.LightningMax}", 
			{ breakdown = "MainHand.Lightning" }, 
			{ label = "Conversions", modType = "BASE", cfg = "weapon1", modName = lightningConvert }, 
		},
		{ format = "{0:output:MainHand.ColdMin} to {0:output:MainHand.ColdMax}", 
			{ breakdown = "MainHand.Cold" }, 
			{ label = "Conversions", modType = "BASE", cfg = "weapon1", modName = coldConvert }, 
		},
		{ format = "{0:output:MainHand.FireMin} to {0:output:MainHand.FireMax}", 
			{ breakdown = "MainHand.Fire" }, 
			{ label = "Conversions", modType = "BASE", cfg = "weapon1", modName = fireConvert }, 
		},
		{ format = "{0:output:MainHand.ChaosMin} to {0:output:MainHand.ChaosMax}", 
			{ breakdown = "MainHand.Chaos" }, 
			{ label = "Conversions", modType = "BASE", cfg = "weapon1", modName = chaosConvert },
		},
	},
	{ label = "MH Average Hit", bgCol = colorCodes.MAINHANDBG, flag = "weapon1Attack", { format = "{1:output:MainHand.AverageHit}", { breakdown = "MainHand.AverageHit" }, }, },
	{ label = "MH PvP Average Hit", bgCol = colorCodes.MAINHANDBG, flag = "weapon1AttackPvP", { format = "{1:output:MainHand.PvpAverageHit}", { breakdown = "MainHand.PvpAverageHit" }, 
		{ label = "Tvalue Override (ms)", modName = "MultiplierPvpTvalueOverride" }, 
		{ label = "PvP Multiplier", cfg = "skill", modName = "PvpDamageMultiplier" }, 
	}, }, 
	-- Off Hand Hit Damage
	{ label = "OH Total Increased", bgCol = colorCodes.OFFHANDBG, flag = "weapon2Attack",
		{ format = "{0:mod:1}%", { modName = "Damage", modType = "INC", cfg = "weapon2" }, },
		{ format = "{0:mod:1}%", { modName = "PhysicalDamage", modType = "INC", cfg = "weapon2" }, },
		{ format = "{0:mod:1}%", { modName = { "LightningDamage", "ElementalDamage" }, modType = "INC", cfg = "weapon2" }, },
		{ format = "{0:mod:1}%", { modName = { "ColdDamage", "ElementalDamage" }, modType = "INC", cfg = "weapon2" }, },
		{ format = "{0:mod:1}%", { modName = { "FireDamage", "ElementalDamage" }, modType = "INC", cfg = "weapon2" }, },
		{ format = "{0:mod:1}%", { modName = "ChaosDamage", modType = "INC", cfg = "weapon2" }, },
	},
	{ label = "OH Total More", bgCol = colorCodes.OFFHANDBG, flag = "weapon2Attack",
		{ format = "{0:mod:1}%", { modName = "Damage", modType = "MORE", cfg = "weapon2" }, },
		{ format = "{0:mod:1}%", { modName = "PhysicalDamage", modType = "MORE", cfg = "weapon2" }, },
		{ format = "{0:mod:1}%", { modName = { "LightningDamage", "ElementalDamage" }, modType = "MORE", cfg = "weapon2" }, },
		{ format = "{0:mod:1}%", { modName = { "ColdDamage", "ElementalDamage" }, modType = "MORE", cfg = "weapon2" }, },
		{ format = "{0:mod:1}%", { modName = { "FireDamage", "ElementalDamage" }, modType = "MORE", cfg = "weapon2" }, },
		{ format = "{0:mod:1}%", { modName = "ChaosDamage", modType = "MORE", cfg = "weapon2" }, },
	},
	{ label = "OH Eff. DPS Mod", bgCol = colorCodes.OFFHANDBG, flagList = {"weapon2Attack","effective"},
		{ },
		{ format = "x {3:output:OffHand.PhysicalEffMult}",
			{ breakdown = "OffHand.PhysicalEffMult" },
			{ label = "Enemy modifiers", modName = physicalHitTaken, enemy = true, cfg = "weapon2" }, 
		},
		{ format = "x {3:output:OffHand.LightningEffMult}",
			{ breakdown = "OffHand.LightningEffMult" },
			{ label = "Player modifiers", modName = { "LightningPenetration", "ElementalPenetration" }, cfg = "weapon2" },
			{ label = "Enemy modifiers", modName = lightningHitTaken, enemy = true, cfg = "weapon2" },
		},
		{ format = "x {3:output:OffHand.ColdEffMult}",
			{ breakdown = "OffHand.ColdEffMult" },
			{ label = "Player modifiers", modName = { "ColdPenetration", "ElementalPenetration" }, cfg = "weapon2" },
			{ label = "Enemy modifiers", modName = coldHitTaken, enemy = true, cfg = "weapon2" },
		},
		{ format = "x {3:output:OffHand.FireEffMult}",
			{ breakdown = "OffHand.FireEffMult" },
			{ label = "Player modifiers", modName = { "FirePenetration", "ElementalPenetration" }, cfg = "weapon2" },
			{ label = "Enemy modifiers", modName = fireHitTaken, enemy = true, cfg = "weapon2" },
		},
		{ format = "x {3:output:OffHand.ChaosEffMult}",
			{ breakdown = "OffHand.ChaosEffMult" },
			{ label = "Player modifiers", modName = "ChaosPenetration", cfg = "weapon2" },
			{ label = "Enemy modifiers", modName = chaosHitTaken, enemy = true, cfg = "weapon2" },
		},
	},
	{ label = "OH Hit Damage", bgCol = colorCodes.OFFHANDBG, textSize = 12, flag = "weapon2Attack",
		{ format = "{0:output:OffHand.TotalMin} to {0:output:OffHand.TotalMax}", },
		{ format = "{0:output:OffHand.PhysicalMin} to {0:output:OffHand.PhysicalMax}", 
			{ breakdown = "OffHand.Physical" }, 
			{ label = "Conversions", modType = "BASE", cfg = "weapon2", modName = physicalConvert },
		},
		{ format = "{0:output:OffHand.LightningMin} to {0:output:OffHand.LightningMax}", 
			{ breakdown = "OffHand.Lightning" }, 
			{ label = "Conversions", modType = "BASE", cfg = "weapon2", modName = lightningConvert }, 
		},
		{ format = "{0:output:OffHand.ColdMin} to {0:output:OffHand.ColdMax}", 
			{ breakdown = "OffHand.Cold" }, 
			{ label = "Conversions", modType = "BASE", cfg = "weapon2", modName = coldConvert }, 
		},
		{ format = "{0:output:OffHand.FireMin} to {0:output:OffHand.FireMax}", 
			{ breakdown = "OffHand.Fire" }, 
			{ label = "Conversions", modType = "BASE", cfg = "weapon2", modName = fireConvert }, 
		},
		{ format = "{0:output:OffHand.ChaosMin} to {0:output:OffHand.ChaosMax}", 
			{ breakdown = "OffHand.Chaos" },
			{ label = "Conversions", modType = "BASE", cfg = "weapon2", modName = chaosConvert }, 
		},
	},
	{ label = "OH Average Hit", bgCol = colorCodes.OFFHANDBG, flag = "weapon2Attack", { format = "{1:output:OffHand.AverageHit}", { breakdown = "OffHand.AverageHit" }, }, },
	{ label = "OH PvP Average Hit", bgCol = colorCodes.OFFHANDBG, flag = "weapon2AttackPvP", { format = "{1:output:OffHand.PvpAverageHit}", { breakdown = "OffHand.PvpAverageHit" }, 
		{ label = "Tvalue Override (ms)", modName = "MultiplierPvpTvalueOverride" }, 
		{ label = "PvP Multiplier", cfg = "skill", modName = "PvpDamageMultiplier" }, 
	}, },
	{ label = "Average Damage", flag = "attack", { format = "{1:output:AverageDamage}", 
		{ breakdown = "MainHand.AverageDamage" },
		{ breakdown = "OffHand.AverageDamage" },
		{ breakdown = "AverageDamage" },
	}, },
	{ label = "Chance to Hit", haveOutput = "enemyHasSpellBlock", { format = "{0:output:HitChance}%",
		{ breakdown = "HitChance" }, 
		{ label = "Enemy Block", modName = { "BlockChance" }, enemy = true },
		{ label = "Block Chance Reduction", cfg = "skill", modName = { "reduceEnemyBlock" } },
	}, },
	{ label = "Average Damage", haveOutput = "enemyHasSpellBlock", { format = "{1:output:AverageDamage}", 
		{ breakdown = "AverageDamage" },
	}, },
	{ label = "Chance to Explode", haveOutput = "ExplodeChance", { format = "{0:output:ExplodeChance}%" }, },
	{ label = "Average Damage", haveOutput = "ExplodeChance", { format = "{1:output:AverageDamage}",
		{ breakdown = "AverageDamage" },
	}, },
	{ label = "PvP Average Dmg", flag = "attackPvP", { format = "{1:output:PvpAverageDamage}", 
		{ breakdown = "MainHand.PvpAverageDamage" },
		{ breakdown = "OffHand.PvpAverageDamage" },
		{ breakdown = "PvpAverageDamage" },
		{ label = "Tvalue Override (ms)", modName = "MultiplierPvpTvalueOverride" }, 
		{ label = "PvP Multiplier", cfg = "skill", modName = "PvpDamageMultiplier" }, 
	}, },
	{ label = "Skill DPS", flag = "notAverage", notFlag = "triggered", { format = "{1:output:TotalDPS}", { breakdown = "TotalDPS" }, { label = "DPS Multiplier", modName = "DPS" }, }, },
	{ label = "Skill PvP DPS", flag = "notAveragePvP", { format = "{1:output:PvpTotalDPS}", { breakdown = "PvpTotalDPS" }, 
		{ label = "Tvalue Override (ms)", modName = "MultiplierPvpTvalueOverride" }, 
		{ label = "PvP Multiplier", cfg = "skill", modName = "PvpDamageMultiplier" },  
		{ label = "DPS Multiplier", modName = "DPS" }, 
	}, },
	{ label = "Skill DPS", flag = "triggered", { format = "{1:output:TotalDPS}", { breakdown = "TotalDPS" }, { label = "DPS Multiplier", modName = "DPS" }, }, },
} }
} },
{ 3, "Warcries", 1, colorCodes.OFFENCE, {{ defaultCollapsed = false, label = "Exerting Warcries", data = {
	extra = "{2:output:TheoreticalOffensiveWarcryEffect} Avg Combined Impact  |  {2:output:TheoreticalMaxOffensiveWarcryEffect} Max Combined Impact",
	colWidth = 114,
	{
		haveOutput = "CreateWarcryOffensiveCalcSection",
		{ format = "Effective Impact" },
		{ format = "Avg Dmg Effect" },
		{ format = "Uptime" },
		{ format = "Number of Exerts" },
		{ format = "Max 1-Hit Impact"},
	},
	{ label = "Seismic Cry", haveOutput = "SeismicUpTimeRatio", 
		{ format = "{2:output:SeismicHitEffect}", { breakdown = "SeismicHitEffect"}, },
		{ format = "{2:output:SeismicAvgDmg}", { breakdown = "SeismicAvgDmg"}, },
		{ format = "{0:output:SeismicUpTimeRatio}%", { breakdown = "SeismicUpTimeRatio" }, },
		{ format = "{0:output:SeismicExertsCount}" },
		{ format = "{2:output:SeismicMaxHitEffect}" },
	},
	{ label = "Intimidating Cry", haveOutput = "IntimidatingUpTimeRatio", 
		{ format = "{2:output:IntimidatingHitEffect}", { breakdown = "IntimidatingHitEffect"}, },
		{ format = "{2:output:IntimidatingAvgDmg}", { breakdown = "IntimidatingAvgDmg"}, },
		{ format = "{0:output:IntimidatingUpTimeRatio}%", { breakdown = "IntimidatingUpTimeRatio" }, },
		{ format = "{0:output:IntimidatingExertsCount}" },
		{ format = "{2:output:IntimidatingMaxHitEffect}" },
	},
	{ label = "Rallying Cry", haveOutput = "RallyingUpTimeRatio", 
		{ format = "{2:output:RallyingHitEffect}", { breakdown = "RallyingHitEffect"}, },
		{ format = "{2:output:RallyingAvgDmg}", { breakdown = "RallyingAvgDmg"}, },
		{ format = "{0:output:RallyingUpTimeRatio}%", { breakdown = "RallyingUpTimeRatio" }, },
		{ format = "{0:output:RallyingExertsCount}" },
		{ format = "{2:output:RallyingMaxHitEffect}" },
	},
	{ label = "Infernal Cry", haveOutput = "InfernalUpTimeRatio", 
		{ format = "" },
		{ format = "" },
		{ format = "{0:output:InfernalUpTimeRatio}%", { breakdown = "InfernalUpTimeRatio" }, },
		{ format = "{0:output:InfernalExertsCount}" },
		{ format = "" },
	},
	{ label = "Battlemage's Cry", haveOutput = "BattlemageUpTimeRatio", 
		{ format = "" },
		{ format = "" },
		{ format = "{0:output:BattlemageUpTimeRatio}%", { breakdown = "BattlemageUpTimeRatio" }, },
		{ format = "{0:output:BattleCryExertsCount}" },
		{ format = "" },
	},
	{ label = "Ancestral Cry", haveOutput = "AncestralUpTimeRatio", 
		{ format = "" },
		{ format = "" },
		{ format = "{0:output:AncestralUpTimeRatio}%", { breakdown = "AncestralUpTimeRatio" }, },
		{ format = "{0:output:AncestralExertsCount}" },
		{ format = "" },
	},
	{ label = "Exerted Attacks", bgCol = colorCodes.MAINHANDBG, haveOutput = "ExertedAttackUptimeRatio",
		{ format = "{2:output:ExertedAttackHitEffect}", { breakdown = "ExertedAttackHitEffect"}, },
		{ format = "{2:output:ExertedAttackAvgDmg}", { modName = "ExertIncrease", cfg = "skill" }, { modName = "ExertAttackIncrease", cfg = "skill" }, },
		{ format = "{0:output:ExertedAttackUptimeRatio}%", { breakdown = "ExertedAttackUptimeRatio" }, },
		{ format = "" },
		{ format = "{2:output:ExertedAttackMaxHitEffect}", { modName = "ExertIncrease", cfg = "skill" }, { modName = "ExertAttackIncrease", cfg = "skill" }, },
	},
	{ label = "Fist of War", bgCol = colorCodes.MAINHANDBG, haveOutput = "FistOfWarUptimeRatio",
		{ format = "{2:output:AvgFistOfWarDamageEffect}", { breakdown = "AvgFistOfWarDamageEffect"}, },
		{ format = "{2:output:AvgFistOfWarDamage}", { modName = "FistOfWarDamageMultiplier", cfg = "skill"}, },
		{ format = "{0:output:FistOfWarUptimeRatio}%", { breakdown = "FistOfWarUptimeRatio" },  },
		{ format = "" },
		{ format = "{2:output:MaxFistOfWarDamageEffect}" },
	},
} }
} },
{ 3, "Dot", 1, colorCodes.OFFENCE, {{ defaultCollapsed = false, label = "Skill Damage over Time", data = {
	extra = "{1:output:TotalDotCalcSection} total DoT",
	flag = "dot",
	colWidth = 95,
	{ { format = "All Types:", }, { format = "Physical:" }, { format = colorCodes.LIGHTNING.."Lightning:" }, { format = colorCodes.COLD.."Cold:" }, { format = colorCodes.FIRE.."Fire:" }, { format = colorCodes.CHAOS.."Chaos:" }, },
	{ label = "Total Increased",
		{ format = "{0:mod:1}%", { modName = "Damage", modType = "INC", cfg = "dot" }, },
		{ format = "{0:mod:1}%", { modName = "PhysicalDamage", modType = "INC", cfg = "dotPhysical" }, },
		{ format = "{0:mod:1}%", { modName = { "LightningDamage", "ElementalDamage" }, modType = "INC", cfg = "dotLightning" }, },
		{ format = "{0:mod:1}%", { modName = { "ColdDamage", "ElementalDamage" }, modType = "INC", cfg = "dotCold" }, },
		{ format = "{0:mod:1}%", { modName = { "FireDamage", "ElementalDamage" }, modType = "INC", cfg = "dotFire" }, },
		{ format = "{0:mod:1}%", { modName = "ChaosDamage", modType = "INC", cfg = "dotChaos" }, },
	},
	{ label = "Total More",
		{ format = "{0:mod:1}%", { modName = "Damage", modType = "MORE", cfg = "dot" }, },
		{ format = "{0:mod:1}%", { modName = "PhysicalDamage", modType = "MORE", cfg = "dotPhysical" }, },
		{ format = "{0:mod:1}%", { modName = { "LightningDamage", "ElementalDamage" }, modType = "MORE", cfg = "dotLightning" }, },
		{ format = "{0:mod:1}%", { modName = { "ColdDamage", "ElementalDamage" }, modType = "MORE", cfg = "dotCold" }, },
		{ format = "{0:mod:1}%", { modName = { "FireDamage", "ElementalDamage" }, modType = "MORE", cfg = "dotFire" }, },
		{ format = "{0:mod:1}%", { modName = "ChaosDamage", modType = "MORE", cfg = "dotChaos" }, },
	},
	{ label = "Total Multiplier",
		{ format = "{0:mod:1}%", { modName = "DotMultiplier", modType = "BASE", cfg = "dot" }, },
		{ format = "{0:mod:1}%", { modName = "PhysicalDotMultiplier", modType = "BASE", cfg = "dotPhysical" }, },
		{ format = "{0:mod:1}%", { modName = "LightningDotMultiplier", modType = "BASE", cfg = "dotLightning" }, },
		{ format = "{0:mod:1}%", { modName = "ColdDotMultiplier", modType = "BASE", cfg = "dotCold" }, },
		{ format = "{0:mod:1}%", { modName = "FireDotMultiplier", modType = "BASE", cfg = "dotFire" }, },
		{ format = "{0:mod:1}%", { modName = "ChaosDotMultiplier", modType = "BASE", cfg = "dotChaos" }, },
	},
	{ label = "Effective DPS Mod", flag = "effective",
		{ },
		{ format = "x {3:output:PhysicalDotEffMult}",
			{ breakdown = "PhysicalDotEffMult" },
			{ label = "Enemy modifiers", modName = { "DamageTaken", "DamageTakenOverTime", "PhysicalDamageTaken", "PhysicalDamageTakenOverTime", "PhysicalDamageReduction" }, enemy = true }, 
		},
		{ format = "x {3:output:LightningDotEffMult}",
			{ breakdown = "LightningDotEffMult" },
			{ label = "Enemy modifiers", modName = { "DamageTaken", "DamageTakenOverTime", "LightningDamageTaken", "LightningDamageTakenOverTime", "ElementalDamageTaken", "LightningResist", "ElementalResist" }, enemy = true },
		},
		{ format = "x {3:output:ColdDotEffMult}",
			{ breakdown = "ColdDotEffMult" },
			{ label = "Enemy modifiers", modName = { "DamageTaken", "DamageTakenOverTime", "ColdDamageTaken", "ColdDamageTakenOverTime", "ElementalDamageTaken", "ColdResist", "ElementalResist" }, enemy = true },
		},
		{ format = "x {3:output:FireDotEffMult}",
			{ breakdown = "FireDotEffMult" },
			{ label = "Enemy modifiers", modName = { "DamageTaken", "DamageTakenOverTime", "FireDamageTaken", "FireDamageTakenOverTime", "ElementalDamageTaken", "FireResist", "ElementalResist" }, enemy = true },
		},
		{ format = "x {3:output:ChaosDotEffMult}",
			{ breakdown = "ChaosDotEffMult" },
			{ label = "Enemy modifiers", modName = { "DamageTaken", "DamageTakenOverTime", "ChaosDamageTaken", "ChaosDamageTakenOverTime", "ChaosResist" }, enemy = true },
		},
	},
	{ label = "Damage over Time",
		{ format = "{1:output:TotalDotInstance}", },
		{ format = "{1:output:PhysicalDot}", { breakdown = "PhysicalDot" }, },
		{ format = "{1:output:LightningDot}", { breakdown = "LightningDot" }, },
		{ format = "{1:output:ColdDot}", { breakdown = "ColdDot" }, },
		{ format = "{1:output:FireDot}", { breakdown = "FireDot" }, },
		{ format = "{1:output:ChaosDot}", { breakdown = "ChaosDot" }, },
	},
	{ label = "Skill DoT DPS", { format = "{1:output:TotalDotCalcSection}", { breakdown = "TotalDot" }, }, },
} }
} },
{ 1, "Speed", 1, colorCodes.OFFENCE, {{ defaultCollapsed = false, label = "Attack/Cast Rate", data = {
	extra = "{2:output:Speed}/s",
	{ label = "MH Inc. Att. Speed", bgCol = colorCodes.MAINHANDBG, flag = "weapon1Attack", notFlag = "triggered", { format = "{0:mod:1}%", { modName = "Speed", modType = "INC", cfg = "weapon1", }, }, },
	{ label = "MH More Att. Speed", bgCol = colorCodes.MAINHANDBG, flag = "weapon1Attack", notFlag = "triggered", { format = "{0:mod:1}%", { modName = "Speed", modType = "MORE", cfg = "weapon1", }, }, },
	{ label = "MH Att. per second", bgCol = colorCodes.MAINHANDBG, flag = "weapon1Attack", notFlag = "triggered", { format = "{2:output:MainHand.Speed}", { breakdown = "MainHand.Speed" }, }, },
	{ label = "OH Inc. Att. Speed", bgCol = colorCodes.OFFHANDBG, flag = "weapon2Attack", notFlag = "triggered", { format = "{0:mod:1}%", { modName = "Speed", modType = "INC", cfg = "weapon2", }, }, },
	{ label = "OH More Att. Speed", bgCol = colorCodes.OFFHANDBG, flag = "weapon2Attack", notFlag = "triggered", { format = "{0:mod:1}%", { modName = "Speed", modType = "MORE", cfg = "weapon2", }, }, },
	{ label = "OH Att. per second", bgCol = colorCodes.OFFHANDBG, flag = "weapon2Attack", notFlag = "triggered", { format = "{2:output:OffHand.Speed}", { breakdown = "OffHand.Speed" }, }, },
	{ label = "Firing Rate", haveOutput = "FiringRate", notFlag = "triggered", { format = "{2:output:FiringRate}", { breakdown = "FiringRate" }, }, },
	{ label = "Total Firing Time", haveOutput = "TotalFiringTime", notFlag = "triggered", { format = "{2:output:TotalFiringTime}s", { breakdown = "TotalFiringTime" }, }, },
	{ label = "Inc. Reload Speed", haveOutput = "ReloadTime", notFlag = "triggered", { format = "{0:mod:1}%", { modName = "ReloadSpeed", modType = "INC", cfg = "skill"}, }, },
	{ label = "More Reload Speed", haveOutput = "ReloadTime", notFlag = "triggered", { format = "{0:mod:1}%", { modName = "ReloadSpeed", modType = "MORE", cfg = "skill"}, }, },
	{ label = "Reload Time", haveOutput = "ReloadTime", notFlag = "triggered", { format = "{2:output:ReloadTime}s", { breakdown = "ReloadTime" }, }, },
	{ label = "Attacks per second", flag = "bothWeaponAttack", notFlag = "triggered", { format = "{2:output:Speed}", { breakdown = "Speed" }, }, },
	{ label = "Attack time", flag = "attack", notFlag = "triggered", { format = "{2:output:Time}s", { breakdown = "MainHand.Time" }, }, },
	{ label = "Inc. Cast Speed", flag = "spell", notFlag = "triggered", { format = "{0:mod:1}%", { modName = "Speed", modType = "INC", cfg = "skill", }, }, },
	{ label = "More Cast Speed", flag = "spell", notFlag = "triggered", { format = "{0:mod:1}%", { modName = "Speed", modType = "MORE", cfg = "skill", }, }, },
	{ label = "Casts per second", flag = "spell", notFlag = "triggered", { format = "{2:output:Speed}", { breakdown = "Speed" }, }, },
	{ label = "Cast Time", flag = "addsCastTime", { format = "{2:output:addsCastTime}", { breakdown = "AddedCastTime" }, }, },
	{ label = "Trigger Rate Cap", flag = "triggered", notFlagList = {"focused", "hasOverride"}, { format = "{2:output:TriggerRateCap}", { breakdown = "TriggerRateCap" }, { modName = "CooldownRecovery", modType = "INC", cfg = "skill", }, }, },
	{ label = "Trigger Rate Cap", flagList = {"triggered", "hasOverride"}, notFlag = "focused", { format = "{2:output:TriggerRateCap}", { breakdown = "TriggerRateCap" }, { modName = "CooldownRecovery", modType = "OVERRIDE", cfg = "skill", }, }, },
	{ label = "Trigger Rate Cap", flagList = {"triggered", "focused"}, { format = "{2:output:TriggerRateCap}", { breakdown = "TriggerRateCap" }, { modName = "FocusCooldownRecovery", modType = "INC", cfg = "skill", }, }, },
	{ label = "Eff. Source Rate", flag = "triggered", notFlag = "focused", notFlag = "globalTrigger", { format = "{2:output:EffectiveSourceRate}", { breakdown = "EffectiveSourceRate" } }, },
	{ label = "Skill Trigger Rate", flag = "triggered", notFlag = "focused", { format = "{2:output:SkillTriggerRate}", { breakdown = "SkillTriggerRate" }, { breakdown = "SimData" }, }, },
	{ label = "Skill Trigger Rate", flagList = {"triggered", "focused"}, { format = "{2:output:SkillTriggerRate}", { breakdown = "SkillTriggerRate" }, { breakdown = "SimData" }, { modName = "FocusCooldownRecovery", modType = "INC", cfg = "skill", }, }, },
	{ label = "Cast time", flag = "spell", notFlag = "triggered", { format = "{2:output:Time}s", }, },
	{ label = "CWDT Threshold", haveOutput = "CWDTThreshold", flag = "triggered", { format = "{2:output:CWDTThreshold}", { breakdown = "CWDTThreshold" }, }, },
	{ label = "Channel time", flag = "channelRelease", haveOutput = "HitTime", { format = "{2:output:HitTime}s", { breakdown = "HitTime" } }, },
	{ label = "Hit Rate", haveOutput = "HitSpeed", { format = "{2:output:HitSpeed}", { breakdown = "HitSpeed" } }, },
} }
} },
{ 1, "Crit", 1, colorCodes.OFFENCE, {{ defaultCollapsed = false, label = "Crits", data = {
	extra = "{2:output:CritChance}% x{2:output:CritMultiplier}",
	flag = "hit",
	-- Skill
	{ label = "Inc. Crit Chance", notFlag = "attack", { format = "{0:mod:1,2}%", 
		{ label = "Player modifiers", modName = "CritChance", modType = "INC", cfg = "skill" }, 
		{ label = "Enemy modifiers", modName = "SelfCritChance", modType = "INC", enemy = true }, 
	}, },
	{ label = "Crit Chance", notFlag = "attack", { format = "{2:output:CritChance}%", 
		{ breakdown = "CritChance" }, 
		{ label = "Player modifiers", modName = {"CritChance", "SpellSkillsCannotDealCriticalStrikesExceptOnFinalRepeat", "SpellSkillsAlwaysDealCriticalStrikesOnFinalRepeat"}, cfg = "skill" }, 
		{ label = "Enemy modifiers", modName = "SelfCritChance", enemy = true }, 
	}, },
	{ label = "Crit Multiplier", notFlag = "attack", { format = "x {2:output:CritMultiplier}", 
		{ breakdown = "CritMultiplier" }, 
		{ label = "Player modifiers", modName = "CritMultiplier", cfg = "skill" }, 
		{ label = "Enemy modifiers", modName = "SelfCritMultiplier", enemy = true }, 
	}, },
	{ label = "Crit Effect Mod", notFlag = "attack", { format = "x {3:output:CritEffect}", { breakdown = "CritEffect" }, }, },
	-- Main Hand
	{ label = "MH Inc. Crit Chance", bgCol = colorCodes.MAINHANDBG, flag = "weapon1Attack", { format = "{0:mod:1,2}%", 
		{ label = "Player modifiers", modName = "CritChance", modType = "INC", cfg = "weapon1" }, 
		{ label = "Enemy modifiers", modName = "SelfCritChance", modType = "INC", enemy = true }, 
	}, },
	{ label = "MH Crit Chance", bgCol = colorCodes.MAINHANDBG, flag = "weapon1Attack", { format = "{2:output:MainHand.CritChance}%", 
		{ breakdown = "MainHand.CritChance" }, 
		{ label = "Player modifiers", modName = { "CritChance", "WeaponBaseCritChance", "MainHandCritIsEqualToParent", "MainHandCritIsEqualToPartyMember", "AttackCritIsEqualToParentMainHand" }, cfg = "weapon1" }, 
		{ label = "Enemy modifiers", modName = "SelfCritChance", enemy = true }, 
	}, },
	{ label = "MH Crit Forks", bgCol = colorCodes.MAINHANDBG, haveOutput = "MainHand.CritForks", flag = "weapon1Attack", { format = "{2:output:MainHand.CritForks}%", 
		{ breakdown = "MainHand.CritForks" }, 
		{ label = "Player modifiers", modName = "ForkCrit", cfg = "weapon1" },
	}, },
	{ label = "MH Crit Multiplier", bgCol = colorCodes.MAINHANDBG, flag = "weapon1Attack", { format = "x {2:output:MainHand.CritMultiplier}", 
		{ breakdown = "MainHand.CritMultiplier" }, 
		{ label = "Player modifiers", modName = "CritMultiplier", cfg = "weapon1" }, 
		{ label = "Enemy modifiers", modName = "SelfCritMultiplier", enemy = true },
	}, },
	{ label = "MH Crit Effect Mod", bgCol = colorCodes.MAINHANDBG, flag = "weapon1Attack", { format = "x {3:output:MainHand.CritEffect}", { breakdown = "MainHand.CritEffect" }, }, },
	-- Off Hand
	{ label = "OH Inc. Crit Chance", bgCol = colorCodes.OFFHANDBG, flag = "weapon2Attack", { format = "{0:mod:1,2}%", 
		{ label = "Player modifiers", modName = "CritChance", modType = "INC", cfg = "weapon2" }, 
		{ label = "Enemy modifiers", modName = "SelfCritChance", modType = "INC", enemy = true }, 
	}, },
	{ label = "OH Crit Chance", bgCol = colorCodes.OFFHANDBG, flag = "weapon2Attack", { format = "{2:output:OffHand.CritChance}%", 
		{ breakdown = "OffHand.CritChance" }, 
		{ label = "Player modifiers", modName = { "CritChance", "WeaponBaseCritChance", "AttackCritIsEqualToParentMainHand" }, cfg = "weapon2" },
		{ label = "Enemy modifiers", modName = "SelfCritChance", enemy = true }, 
	}, },
	{ label = "OH Crit Forks", bgCol = colorCodes.OFFHANDBG, haveOutput = "OffHand.CritForks", flag = "weapon2Attack", { format = "{2:output:OffHand.CritForks}%", 
		{ breakdown = "OffHand.CritForks" }, 
		{ label = "Player modifiers", modName = "ForkCrit", cfg = "weapon2" },
	}, },
	{ label = "OH Crit Multiplier", bgCol = colorCodes.OFFHANDBG, flag = "weapon2Attack", { format = "x {2:output:OffHand.CritMultiplier}", 
		{ breakdown = "OffHand.CritMultiplier" }, 
		{ label = "Player modifiers", modName = "CritMultiplier", cfg = "weapon2" }, 
		{ label = "Enemy modifiers", modName = "SelfCritMultiplier", enemy = true },
	}, },
	{ label = "OH Crit Effect Mod", bgCol = colorCodes.OFFHANDBG, flag = "weapon2Attack", { format = "x {3:output:OffHand.CritEffect}", { breakdown = "OffHand.CritEffect" }, }, },
} }
} },
{ 1, "Impale", 1, colorCodes.OFFENCE, {{ defaultCollapsed = false, label = "Impale", data = {
    flag = "impale",
	extra = "{0:output:ImpaleChance}%",
	{ label = "Max Impale Stacks", { format = "{1:output:ImpaleStacksMax}", { modName = { "ImpaleStacksMax", "ImpaleAdditionalDurationChance" } } }, },
	{ label = "Stacks on Enemy", { format = "{1:output:ImpaleStacks}" }},
	{ label = "Impale Chance", bgCol = colorCodes.MAINHANDBG, flag = "spell", haveOutput = "ImpaleChance", { format = "{0:output:ImpaleChance}%",
		{ flag = "impale", modName = "ImpaleChance", modType = "BASE", cfg = "skill" },
	}, },
	{ label = "Crit Impale Chance", bgCol = colorCodes.MAINHANDBG, flag = "spell", haveOutput = "ImpaleChanceOnCrit", { format = "{0:output:ImpaleChanceOnCrit}%",
		{ flag = "impale", modName = "ImpaleChanceOnCrit", modType = "BASE", cfg = "skill" },
	}, },
	{ label = "Stored Damage",  bgCol = colorCodes.MAINHANDBG, flag = "spell", haveOutput = "ImpaleStoredDamage", { format = "{1:output:ImpaleStoredDamage}%",
		{ breakdown = "ImpaleStoredDamage" },
		{ flag = "spell", modName = "ImpaleEffect", cfg = "skill" },
	}, },
	{ label = "Damage Mod.",  bgCol = colorCodes.MAINHANDBG, flag = "spell", haveOutput = "ImpaleModifier", { format = "{3:output:ImpaleModifier}",
		{ breakdown = "ImpaleModifier" },
	} },
	{ label = "MH Impale Chance", bgCol = colorCodes.MAINHANDBG, flag = "weapon1Attack", haveOutput = "MainHand.ImpaleChance", { format = "{0:output:MainHand.ImpaleChance}%",
		{ flag = "weapon1Attack", modName = "ImpaleChance", modType = "BASE", cfg = "weapon1" },
	}, },
	{ label = "MH Stored Damage",  bgCol = colorCodes.MAINHANDBG, flag = "weapon1Attack", haveOutput = "MainHand.ImpaleStoredDamage", { format = "{1:output:MainHand.ImpaleStoredDamage}%",
		{ breakdown = "MainHand.ImpaleStoredDamage" },
		{ flag = "weapon1Attack", modName = "ImpaleEffect", cfg = "weapon1" },
	}, },
	{ label = "MH DMG Mod.",  bgCol = colorCodes.MAINHANDBG, flag = "weapon1Attack", haveOutput = "MainHand.ImpaleModifier", { format = "{3:output:MainHand.ImpaleModifier}",
		{ breakdown = "MainHand.ImpaleModifier" },
	} },
	{ label = "OH Impale Chance", bgCol = colorCodes.OFFHANDBG, flag = "weapon2Attack", haveOutput = "OffHand.ImpaleChance", { format = "{0:output:OffHand.ImpaleChance}%",
		{ flag = "weapon2Attack", modName = "ImpaleChance", modType = "BASE", cfg = "weapon2" },
	}, },
	{ label = "OH Stored Damage",  bgCol = colorCodes.OFFHANDBG, flag = "weapon2Attack", haveOutput = "OffHand.ImpaleStoredDamage", { format = "{1:output:OffHand.ImpaleStoredDamage}%",
		{ breakdown = "OffHand.ImpaleStoredDamage" },
		{ flag = "weapon2Attack", modName = "ImpaleEffect", cfg = "weapon2" },
	}, },
	{ label = "OH DMG Mod.", bgCol = colorCodes.OFFHANDBG, flag = "weapon2Attack", haveOutput = "OffHand.ImpaleModifier", { format = "{3:output:OffHand.ImpaleModifier}", modType = "MORE",
		{ breakdown = "OffHand.ImpaleModifier" },
	}, },
	{ label = "Impale DPS", flag = "impale", flag = "notAverage", { format = "{1:output:ImpaleDPS}", { breakdown = "ImpaleDPS" }, }, },
	{ label = "Impale Damage", flag = "impale", flag = "showAverage", { format = "{1:output:ImpaleDPS}", { breakdown = "ImpaleDPS" }, }, },
} }
} },
{ 1, "SkillTypeStats", 1, colorCodes.OFFENCE, {{ defaultCollapsed = false, label = "Skill type-specific Stats", data = {
	{ label = "Gem Level", haveOutput = "GemHasLevel", { format = "{0:output:GemLevel}", { breakdown = "GemLevel" }, { modName = { "GemLevel" }, cfg = "skill" },{ modName = { "GemSupportLevel" }, cfg = "skill" }, { modName = { "GemItemLevel" }, cfg = "skill" }, }, },
	{ label = "Spirit Cost", color = colorCodes.SPIRIT, haveOutput = "SpiritHasCost", { format = "{0:output:SpiritCost}", { breakdown = "SpiritCost" }, { modName = { "SpiritCost", "Cost", "SpiritCostNoMult" }, cfg = "skill" }, }, },
	{ label = "Spirit % Cost", color = colorCodes.SPIRIT, haveOutput = "SpiritPercentHasCost", { format = "{0:output:SpiritPercentCost}", { breakdown = "SpiritPercentCost" }, { modName = { "SpiritCost", "Cost", "SpiritCostNoMult" }, cfg = "skill" }, }, },
	{ label = "Mana Cost", color = colorCodes.MANA, haveOutput = "ManaHasCost", { format = "{0:output:ManaCost}", { breakdown = "ManaCost" }, { modName = { "ManaCost", "Cost", "ManaCostNoMult" }, cfg = "skill" }, }, },
	{ label = "Mana % Cost", color = colorCodes.MANA, haveOutput = "ManaPercentHasCost", { format = "{0:output:ManaPercentCost}", { breakdown = "ManaPercentCost" }, { modName = { "ManaCost", "Cost", "ManaCostNoMult" }, cfg = "skill" }, }, },
	{ label = "Mana per second", color = colorCodes.MANA, haveOutput = "ManaPerSecondHasCost", { format = "{2:output:ManaPerSecondCost}", { breakdown = "ManaPerSecondCost" }, { modName = { "ManaCost", "Cost", "ManaCostNoMult" }, cfg = "skill" }, }, },
	{ label = "Mana % per second", color = colorCodes.MANA, haveOutput = "ManaPercentPerSecondHasCost", { format = "{2:output:ManaPercentPerSecondCost}", { breakdown = "ManaPercentPerSecondCost" }, { modName = { "ManaCost", "Cost", "ManaCostNoMult" }, cfg = "skill" }, }, },
	{ label = "Life Cost", color = colorCodes.LIFE, haveOutput = "LifeHasCost", { format = "{0:output:LifeCost}", { breakdown = "LifeCost" }, { modName = { "LifeCost", "Cost", "LifeCostNoMult" }, cfg = "skill" }, }, },
	{ label = "Life % Cost", color = colorCodes.LIFE, haveOutput = "LifePercentHasCost", { format = "{0:output:LifePercentCost}", { breakdown = "LifePercentCost" }, { modName = { "LifeCost", "Cost", "LifeCostNoMult" }, cfg = "skill" }, }, },
	{ label = "Life per second", color = colorCodes.LIFE, haveOutput = "LifePerSecondHasCost", { format = "{2:output:LifePerSecondCost}", { breakdown = "LifePerSecondCost" }, { modName = { "LifeCost", "Cost", "LifeCostNoMult" }, cfg = "skill" }, }, },
	{ label = "Life % per second", color = colorCodes.LIFE, haveOutput = "LifePercentPerSecondHasCost", { format = "{2:output:LifePercentPerSecondCost}", { breakdown = "LifePercentPerSecondCost" }, { modName = { "LifeCost", "Cost", "LifeCostNoMult" }, cfg = "skill" }, }, },
	{ label = "ES Cost", color = colorCodes.ES, haveOutput = "ESHasCost", { format = "{0:output:ESCost}", { breakdown = "ESCost" }, { modName = { "ESCost", "Cost", "ESCostNoMult" }, cfg = "skill" }, }, },
	{ label = "ES per second", color = colorCodes.ES, haveOutput = "ESPerSecondHasCost", { format = "{2:output:ESPerSecondCost}", { breakdown = "ESPerSecondCost" }, { modName = { "ESCost", "Cost", "ESCostNoMult" }, cfg = "skill" }, }, },
	{ label = "ES % per second", color = colorCodes.ES, haveOutput = "ESPercentPerSecondHasCost", { format = "{2:output:ESPercentPerSecondCost}", { breakdown = "ESPercentPerSecondCost" }, { modName = { "ESCost", "Cost", "ESCostNoMult" }, cfg = "skill" }, }, },
	{ label = "Rage Cost", color = colorCodes.RAGE, haveOutput = "RageHasCost", { format = "{0:output:RageCost}", { breakdown = "RageCost" }, { modName = { "RageCost", "Cost", "RageNoMult" }, cfg = "skill" }, }, },
	{ label = "Rage per second", color = colorCodes.RAGE, haveOutput = "RagePerSecondHasCost", { format = "{2:output:RagePerSecondCost}", { breakdown = "RagePerSecondCost" }, { modName = { "RageCost", "Cost", "RageNoMult" }, cfg = "skill" }, }, },
	{ label = "Armour Break / hit", haveOutput = "ArmourBreakPerHit", { format = "{0:output:ArmourBreakPerHit}", { modName = "ArmourBreakPerHit", modType = "BASE"} }, },
	{ label = "Soul Cost", color = colorCodes.RAGE, haveOutput = "SoulHasCost", { format = "{0:output:SoulCost}", { breakdown = "SoulCost" }, { modName = { "SoulCost" }, cfg = "skill" }, }, },
	{ label = "Active Minion Limit", haveOutput = "ActiveMinionLimit", { format = "{0:output:ActiveMinionLimit}" } },
	{ label = "Minion Revival Time", haveOutput = "MinionRevivalTime", { format = "{2:output:MinionRevivalTime}", { breakdown = "MinionRevivalTime" }, { modName = { "MinionRevivalTime" } } },},
	{ label = "Quantity Multiplier", haveOutput = "QuantityMultiplier", { format = "{0:output:QuantityMultiplier}",
	    { breakdown = "QuantityMultiplier" },
	    { modName = { "QuantityMultiplier" }, cfg = "skill" },
	}, },
	{ label = "Skill Cooldown", haveOutput = "Cooldown", { format = "{3:output:EffectiveCooldown}s",
		{ breakdown = "Cooldown" }, 
		{ breakdown = "EffectiveCooldown" }, 
		{ modName = {"CooldownRecovery", "CooldownChanceNotConsume"}, cfg = "skill" },
	}, },
	{ label = "Stored Uses", haveOutput = "StoredUses", { format = "{output:StoredUses}",
	{ breakdown = "StoredUses" },
	{ modName = "AdditionalCooldownUses", cfg = "skill" },
}, },
	{ label = "Duration Mod", flag = "duration", { format = "x {4:output:DurationMod}",
		{ breakdown = "DurationMod" }, 
		{ breakdown = "SecondaryDurationMod" },
		{ breakdown = "TertiaryDurationMod" },
		{ modName = { "Duration", "PrimaryDuration", "SecondaryDuration", "TertiaryDuration", "SkillAndDamagingAilmentDuration" }, cfg = "skill" },
	}, },
	{ label = "Skill Duration", flag = "duration", haveOutput = "Duration", { format = "{3:output:Duration}s", { breakdown = "Duration" }, }, },
	{ label = "Secondary Duration", flag = "duration", haveOutput = "DurationSecondary", { format = "{3:output:DurationSecondary}s", { breakdown = "DurationSecondary" }, }, },
	{ label = "Tertiary Duration", flag = "duration", haveOutput = "DurationTertiary", { format = "{3:output:DurationTertiary}s", { breakdown = "DurationTertiary" }, }, },
	{ label = "Perfect Timing", haveOutput = "PerfectTiming", { format = "{3:output:PerfectTiming}s", { breakdown = "PerfectTiming" }, { modName = "PerfectTiming", cfg = "skill" }}, },
	{ label = "Aura Duration", haveOutput = "AuraDuration", { format = "{3:output:AuraDuration}s", { breakdown = "AuraDuration" }, }, },
	{ label = "Reserve Duration", haveOutput = "ReserveDuration", { format = "{3:output:ReserveDuration}s", { breakdown = "ReserveDuration" }, }, },
	{ label = "Soul Gain Prevent.", haveOutput = "SoulGainPreventionDuration", { format = "{3:output:SoulGainPreventionDuration}s", { breakdown = "SoulGainPreventionDuration" }, }, },
	{ label = "Uptime", haveOutput = "DurationUptime", { format = "{2:output:DurationUptime}%", { breakdown = "DurationUptime" }, }, },
	{ label = "Secondary Uptime", haveOutput = "DurationSecondaryUptime", { format = "{2:output:DurationSecondaryUptime}%", { breakdown = "DurationSecondaryUptime" }, }, },
	{ label = "Tertiary Uptime", haveOutput = "DurationTertiaryUptime", { format = "{2:output:DurationTertiaryUptime}%", { breakdown = "DurationTertiaryUptime" }, }, },
	{ label = "Aura Uptime", haveOutput = "AuraDurationUptime", { format = "{2:output:AuraDurationUptime}%", { breakdown = "AuraDurationUptime" }, }, },
	{ label = "Reserve Uptime", haveOutput = "ReserveDurationUptime", { format = "{2:output:ReserveDurationUptime}%", { breakdown = "ReserveDurationUptime" }, }, },
	{ label = "Sustainable Trauma", haveOutput = "SustainableTrauma", { format = "{0:output:SustainableTrauma}", { breakdown = "SustainableTrauma" }, { modName = { "ExtraTrauma", "RepeatCount", "Duration", "PrimaryDuration", "SecondaryDuration", "SkillAndDamagingAilmentDuration"}, cfg = "skill" }, }, },
	{ label = "Repeat Count", haveOutput = "RepeatCount", { format = "{output:Repeats}", { modName = { "RepeatCount" }, cfg = "skill" }, }, },
	{ label = "Projectile Count", flag = "projectile", { format = "{output:ProjectileCount}", { modName = { "NoAdditionalProjectiles" , "ProjectileCount" }, cfg = "skill" }, }, },
	{ label = "2 Add. Proj. Chance", haveOutput = "TwoAdditionalProjectiles", { format = "{output:TwoAdditionalProjectiles}%", { modName = { "TwoAdditionalProjectilesChance", "NoAdditionalProjectiles" }, cfg = "skill" }, }, },
	{ label = "Pierce Count", haveOutput = "PierceCount", { format = "{output:PierceCountString}", { modName = { "CannotPierce", "PierceCount", "PierceAllTargets" }, cfg = "skill" }, }, },
	{ label = "Fork Count", haveOutput = "ForkCountMax", { format = "{output:ForkCountString}", { modName = { "CannotFork", "ForkCountMax" }, cfg = "skill" }, }, },
	{ label = "Max Chain Count", haveOutput = "ChainMax", { format = "{output:ChainMaxString}", { modName = { "CannotChain", "ChainCountMax", "NoAdditionalChains" }, cfg = "skill" }, }, },
	{ label = "Terrain Chain", haveOutput = "TerrainChain", { format = "{output:TerrainChain}%", { modName = { "TerrainChainChance", "NoAdditionalChains" }, cfg = "skill" }, }, },
	{ label = "Split Count", haveOutput = "SplitCountString", { format = "{output:SplitCountString}", 
	{ label = "Player modifiers", modName = { "CannotSplit", "SplitCount", "AdditionalProjectilesAddSplitsInstead", "AdditionalChainsAddSplitsInstead" }, cfg = "skill" },
	{ label = "Enemy modifiers", modName = { "SelfSplitCount" }, enemy = true, cfg = "skill" }, 
	}, },
	{ label = "Proj. Speed Mod", flag = "projectile", { format = "x {2:output:ProjectileSpeedMod}",
	{ breakdown = "ProjectileSpeedMod" },
	{ modName = "ProjectileSpeed", cfg = "skill" },
	}, },
	{ label = "Bolt Count", haveOutput = "ReloadTime", { format = "{0:output:BoltCount}", { modName = { "CrossbowBoltCount", modType ="BASE" }, cfg = "skill" }, }, },
	{ label = "No Ammo Consume", haveOutput = "ChanceToNotConsumeAmmo", { format = "{0:output:ChanceToNotConsumeAmmo}%", { modName = { "ChanceToNotConsumeAmmo", modType ="BASE" }, cfg = "skill" }, }, },
	{ label = "Eff. Bolt Count", haveOutput = "ChanceToNotConsumeAmmo", { format = "{2:output:EffectiveBoltCount}", { breakdown = "EffectiveBoltCount" }, }, },
	{ label = "Self hit Damage", haveOutput = "SelfHitDamage", { format = "{0:output:SelfHitDamage}", { breakdown = "SelfHitDamage" } } },
	{ label = "Bounces Count", flag = "bounce", { format = "{output:BounceCount}", { modName = { "BounceCount", "ProjectileCount" }, cfg = "skill" }, }, },
	{ label = "Aura Effect Mod", haveOutput = "AuraEffectMod", { format = "x {2:output:AuraEffectMod}",
		{ breakdown = "AuraEffectMod" },
		{ modName = { "AuraEffect", "SkillAuraEffectOnSelf" }, cfg = "skill" },
	}, },
	{ label = "Mana Reserve Mod", haveOutput = "ManaReservedMod", { format = "x {2:output:ManaReservedMod}",
		{ breakdown = "ManaReservedMod" },
		{ modName = { "ManaReserved", "Reserved", "ReservationMultiplier", "ManaReservationEfficiency", "ReservationEfficiency" }, cfg = "skill"}
	}, },
	{ label = "Life Reserve Mod", haveOutput = "LifeReservedMod", { format = "x {2:output:LifeReservedMod}",
		{ breakdown = "LifeReservedMod" },
		{ modName = { "LifeReserved", "Reserved", "ReservationMultiplier", "LifeReservationEfficiency", "ReservationEfficiency"  }, cfg = "skill"}
	}, },
	{ label = "Spirit Reserve Mod", haveOutput = "SpiritReservedMod", { format = "x {2:output:SpiritReservedMod}",
		{ breakdown = "SpiritReservedMod" },
		{ modName = { "SpiritReserved", "Reserved", "ReservationMultiplier", "SpiritReservationEfficiency", "ReservationEfficiency", "ExtraSpirit"  }, cfg = "skill"}
	}, },
	{ label = "Curse Effect Mod", haveOutput = "CurseEffectMod", { format = "x {2:output:CurseEffectMod}",
		{ breakdown = "CurseEffectMod" },
		{ modName = "CurseEffect", cfg = "skill" },
	}, },
	{ label = "Curse Delay", haveOutput = "CurseDelayBase", { format = "{3:output:CurseDelay}s", 
		{ breakdown = "CurseDelay" },
		{ modName = { "CurseDelay" }, cfg = "skill" },
		{ modName = { "CurseActivation" }, cfg = "skill" },
	} },
	{ label = "Curse Limit", haveOutput = "CurseEffectMod", { format = "{0:output:EnemyCurseLimit}",
		{ breakdown = "EnemyCurseLimit" },
		{ modName = { "CurseLimitIsMaximumPowerCharges", "EnemyCurseLimit" } },
	}, },
	{ label = "Ice Crystal Life", haveOutput = "IceCrystalLife", { format = "{0:output:IceCrystalLife}",
		{ breakdown = "IceCrystalLife" },
		{ modName = { "IceCrystalLife" }, cfg = "skill" }
	},},
	{ label = "Mark Effect Mod", haveOutput = "MarkEffectMod", { format = "x {2:output:MarkEffectMod}",
		{ breakdown = "MarkEffectMod" },
		{ modName = "MarkEffect", cfg = "skill" },
	}, },
	{ label = "Mark Limit", haveOutput = "MarkEffectMod", { format = "{0:output:EnemyMarkLimit}",
		{ breakdown = "EnemyMarkLimit" },
		{ modName = { "EnemyMarkLimit" } },
	}, },
	{ label = "Warcry Effect Mod", haveOutput = "WarcryEffectMod", { format = "x {2:output:WarcryEffectMod}",
		{ breakdown = "WarcryEffectMod" },
		{ modName = { "WarcryEffect", "BuffEffect" }, cfg = "skill" },
	}, },
	{ label = "Link Effect Mod", haveOutput = "LinkEffectMod", { format = "x {2:output:LinkEffectMod}",
		{ breakdown = "LinkEffectMod" },
		{ modName = { "LinkEffect", "BuffEffect" }, cfg = "skill" },
	}, },
	{ label = "Area of Effect Mod", haveOutput = "AreaOfEffectMod", { format = "x {2:output:AreaOfEffectMod}", 
		{ breakdown = "AreaOfEffectMod" }, 
		{ modName = "AreaOfEffect", cfg = "skill" },
	}, },
	{ label = "Radius", haveOutput = "AreaOfEffectRadius", { format = "{1:output:AreaOfEffectRadiusMetres}m", { breakdown = "AreaOfEffectRadius" }, }, },
	{ label = "Secondary Radius", haveOutput = "AreaOfEffectRadiusSecondary", { format = "{1:output:AreaOfEffectRadiusSecondaryMetres}m",
		{ breakdown = "AreaOfEffectRadiusSecondary" },
		{ label = "Area of Effect modifiers", modName = "AreaOfEffectSecondary", cfg = "skill" },
	}, },
	{ label = "Tertiary Radius", haveOutput = "AreaOfEffectRadiusTertiary", { format = "{1:output:AreaOfEffectRadiusTertiaryMetres}m",
		{ breakdown = "AreaOfEffectRadiusTertiary" },
		{ label = "Area of Effect modifiers", modName = "AreaOfEffectTertiary", cfg = "skill" },
	}, },
	{ label = "Weapon Range", haveOutput = "WeaponRange", { format = "{1:output:WeaponRangeMetre}m", { breakdown = "WeaponRange" }, }, },
	{ label = "Strike Targets", haveOutput = "StrikeTargets", { format = "{1:output:StrikeTargets}",
		{ breakdown = "StrikeTargets" }, 
		{ modName = "AdditionalStrikeTarget", cfg = "skill" }
	}, },
	{ label = "Attachment Range", flag = "brand", { format = "{1:output:BrandAttachmentRangeMetre}m",
		{ breakdown = "BrandAttachmentRange" },
		{ modName = "BrandAttachmentRange", cfg = "skill"},
	}, },
	{ label = "Max Sust. Fuses", haveOutput = "MaxExplosiveArrowFuseCalculated", { format = "{0:output:MaxExplosiveArrowFuseCalculated} Fuses", { breakdown = "MaxExplosiveArrowFuseCalculated" }, }, },
	{ label = "Explosions /s", haveOutput = "MaxExplosiveArrowFuseCalculated", { format = "{2:output:HitSpeed}", { breakdown = "ExplosionsPerSecond" }, }, },
	{ label = "Overlap Chance", haveOutput = "OverlapChance", { format = "{2:output:OverlapChance}%", { breakdown = "OverlapChance" }, }, },
	{ label = "Trap Cooldown", haveOutput = "TrapCooldown", { format = "{3:output:TrapCooldown}s",
		{ breakdown = "TrapCooldown" },
		{ modName = "CooldownRecovery", cfg = "skill" },
	}, },
	{ label = "Wave Pulse Rate", haveOutput = "WavePulseRate", { format = "{2:output:WavePulseRate}/s", { breakdown = "WavePulseRate" }, { modName = { "TrapThrowingSpeed", "SeismicPulseFrequency" }, cfg = "skill" }, }, },
	{ label = "Pulses Per Trap", haveOutput = "PulsesPerTrap", { format = "{0:output:PulsesPerTrap}", { breakdown = "PulsesPerTrap" }, }, },
	{ label = "Small Explosions", haveOutput = "SmallExplosionsPerTrap", { format = "{0:output:SmallExplosionsPerTrap}", 
		{ label = "Small Explosions", modName = "SmallExplosions", cfg = "skill" },
	}, },
	{ label = "Normal Hits/Cast", haveOutput = "NormalHitsPerCast", { format = "{3:output:NormalHitsPerCast}", { breakdown = "NormalHitsPerCast" }, }, },
	{ label = "Super Hits/Cast", haveOutput = "SuperchargedHitsPerCast", { format = "{3:output:SuperchargedHitsPerCast}", { breakdown = "SuperchargedHitsPerCast" }, }, },
	{ label = "DPS Multiplier", haveOutput = "SkillDPSMultiplier", { format = "{3:output:SkillDPSMultiplier}", { breakdown = "SkillDPSMultiplier" }, }, },
	-- Traps
	{ label = "Avg. Active Traps", haveOutput = "AverageActiveTraps", { format = "{2:output:AverageActiveTraps}", { breakdown = "AverageActiveTraps" }, }, },
	{ label = "Active Trap Limit", flag = "trap", { format = "{0:output:ActiveTrapLimit}", { modName = "ActiveTrapLimit", cfg = "skill" }, }, },
	{ label = "Trap Throw Rate", flag = "trap", { format = "{2:output:TrapThrowingSpeed}",
		{ breakdown = "TrapThrowingSpeed" },
		{ modName = "TrapThrowingSpeed", cfg = "skill" },
	}, },
	{ label = "Trap Throw Time", flag = "trap", { format = "{2:output:TrapThrowingTime}s", { breakdown = "TrapThrowingTime" }, }, },
	{ label = "Avg. Traps per Throw", flag = "trap", { format = "{2:output:TrapThrowCount}", { modName = "TrapThrowCount", cfg = "skill"}, }, },
	{ label = "Trap Trigg. Radius", flag = "trap", { format = "{1:output:TrapTriggerRadiusMetre}m",
		{ breakdown = "TrapTriggerRadius" },
		{ label = "Area of Effect modifiers", modName = "TrapTriggerAreaOfEffect", cfg = "skill" },
	}, },
	-- Seal (Unleash)
	{ label = "Seal Gain Rate", haveOutput = "SealMax", { format = "{2:output:SealCooldown}s", 
		{ breakdown = "SealGainTime" },
		{ modName = "SealGainFrequency", cfg = "skill" }, 
	}, },
	-- Mines
	{ label = "Active Mine Limit", flag = "mine", { format = "{0:output:ActiveMineLimit}", { modName = "ActiveMineLimit", cfg = "skill" }, }, },
	{ label = "Mine Throw Rate", flag = "mine", { format = "{2:output:MineLayingSpeed}", 
		{ breakdown = "MineLayingTime" },
		{ modName = "MineLayingSpeed", cfg = "skill" }, 
	}, },
	{ label = "Mine Throw Time", flag = "mine", { format = "{2:output:MineLayingTime}s", { breakdown = "MineThrowingTime" }, }, },
	{ label = "Avg. Mines per Throw", flag = "mine", { format = "{2:output:MineThrowCount}", { modName = "MineThrowCount", cfg = "skill"}, }, },
	{ label = "Mine Deton. Radius", flag = "mine", { format = "{1:output:MineDetonationRadiusMetre}m",
		{ breakdown = "MineDetonationRadius" },
		{ label = "Area of Effect modifiers", modName = "MineDetonationAreaOfEffect", cfg = "skill" },
	}, },
	{ label = "Mine Aura Radius", haveOutput = "MineAuraRadius", { format = "{1:output:MineAuraRadiusMetre}m", { breakdown = "MineAuraRadius" }, }, },
	{ label = "Totem Place Time", flag = "totem", notFlag = "triggered", { format = "{2:output:TotemPlacementTime}s", 
		{ breakdown = "TotemPlacementTime" },
		{ modName = "TotemPlacementSpeed", cfg = "skill" }, 
	}, },
	{ label = "Active Totem Limit", flag = "totem", notFlag = "triggered", { format = "{0:output:ActiveTotemLimit}", 
		{ breakdown = "ActiveTotemLimit" },
		{ modName = { "ActiveTotemLimit", "ActiveBallistaLimit" }, cfg = "skill" }, 
	}, },
	{ label = "Totem Dur. Mod", flagList = {"duration", "totem"}, { format = "x {4:output:TotemDurationMod}",
		{ breakdown = "TotemDurationMod" }, 
		{ modName = { "Duration", "PrimaryDuration", "TotemDuration" }, cfg = "skill" }, 
	}, },
	{ label = "Totem Duration", flagList = {"duration", "totem"}, { format = "{3:output:TotemDuration}s",
		{ breakdown = "TotemDuration" },
	}, },
	{ label = "Totem Life Mod", flag = "totem", notFlag = "triggered", { format = "x {2:output:TotemLifeMod}", 
		{ breakdown = "TotemLifeMod" },
		{ modName = "TotemLife", cfg = "skill" },
	}, },
	{ label = "Totem Life", flag = "totem", notFlag = "triggered", { format = "{0:output:TotemLife}", { breakdown = "TotemLife" }, }, },
	{ label = "Totem ES", haveOutput = "TotemEnergyShield", { format = "{0:output:TotemEnergyShield}", 
		{ breakdown = "TotemEnergyShield" },
		{ modName = "TotemEnergyShield", cfg = "skill" },
	}, },
	{ label = "Totem Block Chance", haveOutput = "TotemBlockChance", { format = "{0:output:TotemBlockChance}%", 
		{ breakdown = "TotemBlockChance" },
		{ modName = "TotemBlockChance", cfg = "skill" },
	}, },
	{ label = "Totem Armour", haveOutput = "TotemArmour", { format = "{0:output:TotemArmour}", 
		{ breakdown = "TotemArmour" },
		{ modName = "TotemArmour", cfg = "skill" },
	}, },
	{ label = "Active Brand Limit", flag = "brand", { format = "{0:output:ActiveBrandLimit}", { modName = "ActiveBrandLimit", cfg = "skill" }, }, },
	{ label = "Totem Fire Res", flag = "totem", notFlag = "triggered",{ format = "{0:output:TotemFireResist}% (+{0:output:TotemFireResistOverCap}%)",
		{ breakdown = "TotemFireResist" }, 
		{ modName = { "TotemFireResistMax", "TotemElementalResistMax", "TotemFireResist", "TotemElementalResist" }, },
	}, },
	{ label = "Totem Cold Res", flag = "totem", notFlag = "triggered", { format = "{0:output:TotemColdResist}% (+{0:output:TotemColdResistOverCap}%)", 
		{ breakdown = "TotemColdResist" },
		{ modName = { "TotemColdResistMax", "TotemElementalResistMax", "TotemColdResist", "TotemElementalResist" }, },
	}, },
	{ label = "Totem Light. Res", flag = "totem", notFlag = "triggered", { format = "{0:output:TotemLightningResist}% (+{0:output:TotemLightningResistOverCap}%)",
		{ breakdown = "TotemLightningResist" },
		{ modName = { "TotemLightningResistMax", "TotemElementalResistMax", "TotemLightningResist", "TotemElementalResist" }, },
	}, },
	{ label = "Totem Chaos Res", flag = "totem", notFlag = "triggered", { format = "{0:output:TotemChaosResist}% (+{0:output:TotemChaosResistOverCap}%)",
		{ breakdown = "TotemChaosResist" },
		{ modName = { "TotemChaosResistMax", "TotemChaosResist" }, }, 
	}, },
	{ label = "Corpse Level", haveOutput = "CorpseLevel", { format = "{0:output:CorpseLevel}",
		{ breakdown = "CorpseLevel" },
		{ modName = "CorpseLevel", cfg = "skill" },
	}, },
	{ label = "Corpse Life", haveOutput = "CorpseLife", { format = "{0:output:CorpseLife}",
		{ breakdown = "CorpseLife" },
		{ modName = "CorpseLife", cfg = "skill" },
	}, },
	{ label = "Burst Damage", haveOutput = "ShowBurst", { format = "{1:output:AverageBurstDamage}", { breakdown = "AverageBurstDamage" }, }, },
} }
} },
{ 1, "HitChance", 1, colorCodes.OFFENCE, {{ defaultCollapsed = false, label = "Accuracy", data = {
	extra = "{0:output:HitChance}%",
	flag = "attack",
	{ label = "MH Accuracy", bgCol = colorCodes.MAINHANDBG, flag = "weapon1Attack", { format = "{0:output:MainHand.Accuracy}", 
		{ breakdown = "MainHand.Accuracy" }, 
		{ modName = "Accuracy", cfg = "weapon1" }, 
	}, },
	{ label = "MH Chance to Hit", bgCol = colorCodes.MAINHANDBG, flag = "weapon1Attack", { format = "{0:output:MainHand.AccuracyHitChance}%",
		{ breakdown = "MainHand.AccuracyHitChance" }, 
		{ label = "Enemy Evasion modifiers", modName = { "Evasion", "CannotEvade" }, enemy = true },
		{ label = "Player modifiers", modName = { "HitChance", "CannotBeEvaded", "IgnoreBlindHitChance" } },
	}, },
	{ label = "MH Chance to Hit", haveOutput = "MainHand.enemyBlockChance", bgCol = colorCodes.MAINHANDBG, flag = "weapon1Attack", { format = "{0:output:MainHand.HitChance}%",
		{ breakdown = "MainHand.HitChance" }, 
		{ label = "Enemy Evasion modifiers", modName = { "Evasion", "CannotEvade" }, enemy = true },
		{ label = "Enemy Block", modName = { "BlockChance" }, enemy = true },
		{ label = "Block Chance Reduction", cfg = "skill", modName = { "reduceEnemyBlock" } },
		{ label = "Player modifiers", modName = { "HitChance", "CannotBeEvaded", "IgnoreBlindHitChance" } },
	}, },
	{ label = "OH Accuracy", bgCol = colorCodes.OFFHANDBG, flag = "weapon2Attack", { format = "{0:output:OffHand.Accuracy}",
		{ breakdown = "OffHand.Accuracy" }, 
		{ modName = "Accuracy", cfg = "weapon2" },
	}, },
	{ label = "OH Chance to Hit", bgCol = colorCodes.OFFHANDBG, flag = "weapon2Attack", { format = "{0:output:OffHand.AccuracyHitChance}%",
		{ breakdown = "OffHand.AccuracyHitChance" },
		{ label = "Enemy Evasion modifiers", modName = { "Evasion", "CannotEvade" }, enemy = true },
		{ label = "Player modifiers", modName = { "HitChance", "CannotBeEvaded", "IgnoreBlindHitChance" } },
	}, },
	{ label = "OH Chance to Hit", haveOutput = "OffHand.enemyBlockChance", bgCol = colorCodes.OFFHANDBG, flag = "weapon2Attack", { format = "{0:output:OffHand.HitChance}%",
		{ breakdown = "OffHand.HitChance" },
		{ label = "Enemy Evasion modifiers", modName = { "Evasion", "CannotEvade" }, enemy = true },
		{ label = "Enemy Block", modName = { "BlockChance" }, enemy = true },
		{ label = "Block Chance Reduction", cfg = "skill", modName = { "reduceEnemyBlock" } },
		{ label = "Player modifiers", modName = { "HitChance", "CannotBeEvaded", "IgnoreBlindHitChance" } },
	}, },
	{ label = "Effect of Blind", haveOutput = "BlindEffectMod", { format = "{0:output:BlindEffectMod}%", { breakdown = "BlindEffectMod" }, { modName = { "BlindEffect", "BuffEffectOnSelf" }, }, } },
} }
} },
{ 1, "Bleed", 1, colorCodes.OFFENCE, {{ defaultCollapsed = false, label = "Bleed", data = {
	extra = "{0:output:BleedChance}% {1:output:BleedDPS} {2:output:BleedDuration}s",
	flag = "bleed",
	{ label = "Max Bleed Stacks", { format = "{0:output:BleedStacksMax}", { modName = "BleedStacksMax" } }, },
	{ label = "Stack Potential", { format = "{2:output:BleedStackPotentialPercent}%", { breakdown = "BleedStackPotential" } }},
	{ label = "Average Bleed Roll", { format = "{2:output:BleedRollAverage}%", { breakdown = "BleedRollAverage" } }},
	{ label = "Chance to Bleed", { format = "{0:output:BleedChance}%", 
		{ breakdown = "MainHand.BleedChance" },
		{ breakdown = "OffHand.BleedChance" },
		{ breakdown = "BleedChance" },
		{ label = "Main Hand", flag = "weapon1Attack", modName = "BleedChance", cfg = "weapon1" },
		{ label = "Off Hand", flag = "weapon2Attack", modName = "BleedChance", cfg = "weapon2" },
		{ label = "Enemy modifiers", modName = "SelfBleedChance", modType = "BASE", enemy = true },
		{ label = "Ailment modifiers", modName = "AilmentChance", cfg = "skill" },
	}, },
	{ label = "Magnitude Effect", { format = "x {2:output:BleedMagnitudeEffect}", { modName = "AilmentMagnitude", cfg = "bleed" }, }, },
	{ label = "Source Physical", textSize = 12, notFlag = "attack", haveOutput = "BleedPhysicalMax", { format = "{0:output:BleedPhysicalMin} to {0:output:BleedPhysicalMax}", { breakdown = "BleedPhysical" }, }, },
	{ label = "MH Source Physical", bgCol = colorCodes.MAINHANDBG, textSize = 12, flag = "weapon1Attack", haveOutput = "MainHand.BleedPhysicalMax", { format = "{0:output:MainHand.BleedPhysicalMin} to {0:output:MainHand.BleedPhysicalMax}", { breakdown = "MainHand.BleedPhysical" }, }, },
	{ label = "OH Source Physical", bgCol = colorCodes.OFFHANDBG, textSize = 12, flag = "weapon2Attack", haveOutput = "OffHand.BleedPhysicalMax", { format = "{0:output:OffHand.BleedPhysicalMin} to {0:output:OffHand.BleedPhysicalMax}", { breakdown = "OffHand.BleedPhysical" }, }, },
	{ label = "Source Lightning", textSize = 12, notFlag = "attack", haveOutput = "BleedLightningMax", { format = "{0:output:BleedLightningMin} to {0:output:BleedLightningMax}", { breakdown = "BleedLightning" }, }, },
	{ label = "MH Source Lightning", bgCol = colorCodes.MAINHANDBG, textSize = 12, flag = "weapon1Attack", haveOutput = "MainHand.BleedLightningMax", { format = "{0:output:MainHand.BleedLightningMin} to {0:output:MainHand.BleedLightningMax}", { breakdown = "MainHand.BleedLightning" }, }, },
	{ label = "OH Source Lightning", bgCol = colorCodes.OFFHANDBG, textSize = 12, flag = "weapon2Attack", haveOutput = "OffHand.BleedLightningMax", { format = "{0:output:OffHand.BleedLightningMin} to {0:output:OffHand.BleedLightningMax}", { breakdown = "OffHand.BleedLightning" }, }, },
	{ label = "Source Cold", textSize = 12, notFlag = "attack", haveOutput = "BleedColdMax", { format = "{0:output:BleedColdMin} to {0:output:BleedColdMax}", { breakdown = "BleedCold" }, }, },
	{ label = "MH Source Cold", bgCol = colorCodes.MAINHANDBG, textSize = 12, flag = "weapon1Attack", haveOutput = "MainHand.BleedColdMax", { format = "{0:output:MainHand.BleedColdMin} to {0:output:MainHand.BleedColdMax}", { breakdown = "MainHand.BleedCold" }, }, },
	{ label = "OH Source Cold", bgCol = colorCodes.OFFHANDBG, textSize = 12, flag = "weapon2Attack", haveOutput = "OffHand.BleedColdMax", { format = "{0:output:OffHand.BleedColdMin} to {0:output:OffHand.BleedColdMax}", { breakdown = "OffHand.BleedCold" }, }, },
	{ label = "Source Fire", textSize = 12, notFlag = "attack", haveOutput = "BleedFireMax", { format = "{0:output:BleedFireMin} to {0:output:BleedFireMax}", { breakdown = "BleedFire" }, }, },
	{ label = "MH Source Fire", bgCol = colorCodes.MAINHANDBG, textSize = 12, flag = "weapon1Attack", haveOutput = "MainHand.BleedFireMax", { format = "{0:output:MainHand.BleedFireMin} to {0:output:MainHand.BleedFireMax}", { breakdown = "MainHand.BleedFire" }, }, },
	{ label = "OH Source Fire", bgCol = colorCodes.OFFHANDBG, textSize = 12, flag = "weapon2Attack", haveOutput = "OffHand.BleedFireMax", { format = "{0:output:OffHand.BleedFireMin} to {0:output:OffHand.BleedFireMax}", { breakdown = "OffHand.BleedFire" }, }, },
	{ label = "Source Chaos", textSize = 12, notFlag = "attack", haveOutput = "BleedChaosMax", { format = "{0:output:BleedChaosMin} to {0:output:BleedChaosMax}", { breakdown = "BleedChaos" }, }, },
	{ label = "MH Source Chaos", bgCol = colorCodes.MAINHANDBG, textSize = 12, flag = "weapon1Attack", haveOutput = "MainHand.BleedChaosMax", { format = "{0:output:MainHand.BleedChaosMin} to {0:output:MainHand.BleedChaosMax}", { breakdown = "MainHand.BleedChaos" }, }, },
	{ label = "OH Source Chaos", bgCol = colorCodes.OFFHANDBG, textSize = 12, flag = "weapon2Attack", haveOutput = "OffHand.BleedChaosMax", { format = "{0:output:OffHand.BleedChaosMin} to {0:output:OffHand.BleedChaosMax}", { breakdown = "OffHand.BleedChaos" }, }, },
	{ label = "Effective DPS Mod", flag = "effective", { format = "x {3:output:BleedEffMult}", { breakdown = "BleedEffMult" }, { label = "Enemy modifiers", modName = { "DamageTaken", "DamageTakenOverTime", "PhysicalDamageTaken", "PhysicalDamageTakenOverTime", "PhysicalDamageReduction" }, enemy = true, cfg = "bleed" }, }, },
	{ label = "Bleed DPS", { format = "{1:output:BleedDPS}",
		{ breakdown = "BleedDPS" },
		{ breakdown = "MainHand.BleedDPS" },
		{ breakdown = "OffHand.BleedDPS" },
	}, },
	{ label = "Bleed Duration", { format = "{2:output:BleedDuration}s", 
		{ breakdown = "BleedDuration" }, 
		{ label = "Player modifiers", modName = { "EnemyBleedDuration", "EnemyAilmentDuration", "SkillAndDamagingAilmentDuration", "BleedFaster" }, cfg = "bleed" }, 
		{ label = "Enemy modifiers", modName = { "SelfBleedDuration", "SelfAilmentDuration", "SelfBleedFaster", "BleedExpireRate" }, enemy = true },
	}, },
	{ label = "Dmg. of all Bleeds", { format = "{1:output:BleedDamage}",
		{ breakdown = "MainHand.BleedDamage" },
		{ breakdown = "OffHand.BleedDamage" },
		{ breakdown = "BleedDamage" },
	}, },
} }
} },
{ 1, "Poison", 1, colorCodes.OFFENCE, {{ defaultCollapsed = false, label = "Poison", data = {
	extra = "{0:output:PoisonChance}% {1:output:PoisonDPS} {2:output:PoisonDuration}s",
	flag = "poison",
	{ label = "Max Poison Stacks", { format = "{1:output:PoisonStacksMax}", 
		{ modName = "PoisonStacks" },
		{ label = "Main Hand", flag = "weapon1Attack", modName = "PoisonStacks", cfg = "weapon1" },
		{ label = "Off Hand", flag = "weapon2Attack", modName = "PoisonStacks", cfg = "weapon2" },
	}, },
	{ label = "Stack Potential", { format = "{2:output:PoisonStackPotentialPercent}%", { breakdown = "PoisonStackPotential" } }},
	{ label = "Average Poison Roll", { format = "{2:output:PoisonRollAverage}%", { breakdown = "PoisonRollAverage" } }},
	{ label = "Chance to Poison", { format = "{0:output:PoisonChance}%", 
		{ breakdown = "MainHand.PoisonChance" }, 
		{ breakdown = "OffHand.PoisonChance" }, 
		{ breakdown = "PoisonChance" }, 
		{ notFlag = "attack", modName = "PoisonChance", modType = "BASE", cfg = "skill" }, 
		{ label = "Main Hand", flag = "weapon1Attack", modName = "PoisonChance", cfg = "weapon1" },
		{ label = "Off Hand", flag = "weapon2Attack", modName = "PoisonChance", cfg = "weapon2" },
		{ label = "Ailment modifiers", modName = "AilmentChance", cfg = "skill" },
	}, },
	{ label = "Magnitude Effect", { format = "x {2:output:PoisonMagnitudeEffect}", { modName = "AilmentMagnitude", cfg = "poison" }, }, },
	{ label = "Source Physical", textSize = 12, notFlag = "attack", haveOutput = "PoisonPhysicalMax", { format = "{0:output:PoisonPhysicalMin} to {0:output:PoisonPhysicalMax}", { breakdown = "PoisonPhysical" }, }, },
	{ label = "MH Source Physical", bgCol = colorCodes.MAINHANDBG, textSize = 12, flag = "weapon1Attack", haveOutput = "MainHand.PoisonPhysicalMax", { format = "{0:output:MainHand.PoisonPhysicalMin} to {0:output:MainHand.PoisonPhysicalMax}", { breakdown = "MainHand.PoisonPhysical" }, }, },
	{ label = "OH Source Physical", bgCol = colorCodes.OFFHANDBG, textSize = 12, flag = "weapon2Attack", haveOutput = "OffHand.PoisonPhysicalMax", { format = "{0:output:OffHand.PoisonPhysicalMin} to {0:output:OffHand.PoisonPhysicalMax}", { breakdown = "OffHand.PoisonPhysical" }, }, },
	{ label = "Source Lightning", textSize = 12, notFlag = "attack", haveOutput = "PoisonLightningMax", { format = "{0:output:PoisonLightningMin} to {0:output:PoisonLightningMax}", { breakdown = "PoisonLightning" }, }, },
	{ label = "MH Source Lightning", bgCol = colorCodes.MAINHANDBG, textSize = 12, flag = "weapon1Attack", haveOutput = "MainHand.PoisonLightningMax", { format = "{0:output:MainHand.PoisonLightningMin} to {0:output:MainHand.PoisonLightningMax}", { breakdown = "MainHand.PoisonLightning" }, }, },
	{ label = "OH Source Lightning", bgCol = colorCodes.OFFHANDBG, textSize = 12, flag = "weapon2Attack", haveOutput = "OffHand.PoisonLightningMax", { format = "{0:output:OffHand.PoisonLightningMin} to {0:output:OffHand.PoisonLightningMax}", { breakdown = "OffHand.PoisonLightning" }, }, },
	{ label = "Source Cold", textSize = 12, notFlag = "attack", haveOutput = "PoisonColdMax", { format = "{0:output:PoisonColdMin} to {0:output:PoisonColdMax}", { breakdown = "PoisonCold" }, }, },
	{ label = "MH Source Cold", bgCol = colorCodes.MAINHANDBG, textSize = 12, flag = "weapon1Attack", haveOutput = "MainHand.PoisonColdMax", { format = "{0:output:MainHand.PoisonColdMin} to {0:output:MainHand.PoisonColdMax}", { breakdown = "MainHand.PoisonCold" }, }, },
	{ label = "OH Source Cold", bgCol = colorCodes.OFFHANDBG, textSize = 12, flag = "weapon2Attack", haveOutput = "OffHand.PoisonColdMax", { format = "{0:output:OffHand.PoisonColdMin} to {0:output:OffHand.PoisonColdMax}", { breakdown = "OffHand.PoisonCold" }, }, },
	{ label = "Source Fire", textSize = 12, notFlag = "attack", haveOutput = "PoisonFireMax", { format = "{0:output:PoisonFireMin} to {0:output:PoisonFireMax}", { breakdown = "PoisonFire" }, }, },
	{ label = "MH Source Fire", bgCol = colorCodes.MAINHANDBG, textSize = 12, flag = "weapon1Attack", haveOutput = "MainHand.PoisonFireMax", { format = "{0:output:MainHand.PoisonFireMin} to {0:output:MainHand.PoisonFireMax}", { breakdown = "MainHand.PoisonFire" }, }, },
	{ label = "OH Source Fire", bgCol = colorCodes.OFFHANDBG, textSize = 12, flag = "weapon2Attack", haveOutput = "OffHand.PoisonFireMax", { format = "{0:output:OffHand.PoisonFireMin} to {0:output:OffHand.PoisonFireMax}", { breakdown = "OffHand.PoisonFire" }, }, },
	{ label = "Source Chaos", textSize = 12, notFlag = "attack", haveOutput = "PoisonChaosMax", { format = "{0:output:PoisonChaosMin} to {0:output:PoisonChaosMax}", { breakdown = "PoisonChaos" }, }, },
	{ label = "MH Source Chaos", bgCol = colorCodes.MAINHANDBG, textSize = 12, flag = "weapon1Attack", haveOutput = "MainHand.PoisonChaosMax", { format = "{0:output:MainHand.PoisonChaosMin} to {0:output:MainHand.PoisonChaosMax}", { breakdown = "MainHand.PoisonChaos" }, }, },
	{ label = "OH Source Chaos", bgCol = colorCodes.OFFHANDBG, textSize = 12, flag = "weapon2Attack", haveOutput = "OffHand.PoisonChaosMax", { format = "{0:output:OffHand.PoisonChaosMin} to {0:output:OffHand.PoisonChaosMax}", { breakdown = "OffHand.PoisonChaos" }, }, },
	{ label = "Effective DPS Mod", flag = "effective", { format = "x {3:output:PoisonEffMult}", 
		{ breakdown = "PoisonEffMult" }, 
		{ label = "Enemy modifiers", modName = { "ChaosResist", "DamageTaken", "DamageTakenOverTime", "ChaosDamageTaken", "ChaosDamageTakenOverTime" }, enemy = true },
	}, },
	{ label = "Poison DPS", { format = "{1:output:PoisonDPS}", 
		{ breakdown = "PoisonDPS" }, 
		{ breakdown = "MainHand.PoisonDPS" },
		{ breakdown = "OffHand.PoisonDPS" },
	}, },
	{ label = "Caustic Ground", haveOutput = "CausticGroundFromPoison", { format = "{0:output:CausticGroundDPS}", { breakdown = "CausticGroundDPS" } } },
	{ label = "Poison Duration", { format = "{2:output:PoisonDuration}s",
		{ breakdown = "PoisonDuration" },
		{ label = "Player modifiers", notSkillData = "poisonDurationIsSkillDuration", modName = { "EnemyPoisonDuration", "EnemyAilmentDuration", "SkillAndDamagingAilmentDuration", "PoisonFaster" }, cfg = "poison" },
		{ label = "Player modifiers", skillData = "poisonDurationIsSkillDuration", modName = { "EnemyPoisonDuration", "EnemyAilmentDuration", "SkillAndDamagingAilmentDuration", "PoisonFaster", "Duration" }, cfg = "poison" },
		{ label = "Enemy modifiers", modName = { "SelfPoisonDuration", "SelfAilmentDuration", "SelfPoisonFaster" }, enemy = true },
	}, },
	{ label = "Dmg. of all Poisons", { format = "{1:output:PoisonDamage}", 
		{ breakdown = "MainHand.PoisonDamage" }, 
		{ breakdown = "OffHand.PoisonDamage" }, 
		{ breakdown = "PoisonDamage" }, 
	}, },
} }
} },
{ 1, "Ignite", 1, colorCodes.OFFENCE, {{ defaultCollapsed = false, label = "Ignite", data = {	
	extra = "{0:output:IgniteChancePerHit}% {1:output:IgniteDPS} {2:output:IgniteDuration}s",
	flag = "ignite",
	{ label = "Enemy Ail. Thresh.", { format = "{0:output:EnemyAilmentThreshold}", { modName = "EnemyAilmentThreshold" }, }, },
	{ label = "Max Ignite Stacks", { format = "{1:output:IgniteStacksMax}", { modName = "IgniteStacks" }, }, },
	{ label = "Stack Potential", { format = "{2:output:IgniteStackPotentialPercent}%", { breakdown = "IgniteStackPotential" } }},
	{ label = "Average Ignite Roll", { format = "{2:output:IgniteRollAverage}%", { breakdown = "IgniteRollAverage" } }},
	{ label = "Chance to Ignite", { format = "{0:output:IgniteChancePerHit}%", 
		{ breakdown = "MainHand.IgniteChance" }, 
		{ breakdown = "OffHand.IgniteChance" },
		{ breakdown = "IgniteChance" },
		{ label = "Player modifiers", modName = "EnemyIgniteChance", cfg = "skill" },
		{ label = "Enemy modifiers", modName = "SelfIgniteChance", enemy = true },
		{ label = "Ailment modifiers", modName = "AilmentChance", cfg = "skill" },
	}, },
	{ label = "Magnitude Effect", { format = "x {2:output:IgniteMagnitudeEffect}", { modName = "AilmentMagnitude", cfg = "ignite" }, }, },
	{ label = "Source Physical", textSize = 12, notFlag = "attack", haveOutput = "IgnitePhysicalMax", { format = "{0:output:IgnitePhysicalMin} to {0:output:IgnitePhysicalMax}", { breakdown = "IgnitePhysical" }, }, },
	{ label = "MH Source Physical", bgCol = colorCodes.MAINHANDBG, textSize = 12, flag = "weapon1Attack", haveOutput = "MainHand.IgnitePhysicalMax", { format = "{0:output:MainHand.IgnitePhysicalMin} to {0:output:MainHand.IgnitePhysicalMax}", { breakdown = "MainHand.IgnitePhysical" }, }, },
	{ label = "OH Source Physical", bgCol = colorCodes.OFFHANDBG, textSize = 12, flag = "weapon2Attack", haveOutput = "OffHand.IgnitePhysicalMax", { format = "{0:output:OffHand.IgnitePhysicalMin} to {0:output:OffHand.IgnitePhysicalMax}", { breakdown = "OffHand.IgnitePhysical" }, }, },
	{ label = "Source Lightning", textSize = 12, notFlag = "attack", haveOutput = "IgniteLightningMax", { format = "{0:output:IgniteLightningMin} to {0:output:IgniteLightningMax}", { breakdown = "IgniteLightning" }, }, },
	{ label = "MH Source Lightning", bgCol = colorCodes.MAINHANDBG, textSize = 12, flag = "weapon1Attack", haveOutput = "MainHand.IgniteLightningMax", { format = "{0:output:MainHand.IgniteLightningMin} to {0:output:MainHand.IgniteLightningMax}", { breakdown = "MainHand.IgniteLightning" }, }, },
	{ label = "OH Source Lightning", bgCol = colorCodes.OFFHANDBG, textSize = 12, flag = "weapon2Attack", haveOutput = "OffHand.IgniteLightningMax", { format = "{0:output:OffHand.IgniteLightningMin} to {0:output:OffHand.IgniteLightningMax}", { breakdown = "OffHand.IgniteLightning" }, }, },
	{ label = "Source Cold", textSize = 12, notFlag = "attack", haveOutput = "IgniteColdMax", { format = "{0:output:IgniteColdMin} to {0:output:IgniteColdMax}", { breakdown = "IgniteCold" }, }, },
	{ label = "MH Source Cold", bgCol = colorCodes.MAINHANDBG, textSize = 12, flag = "weapon1Attack", haveOutput = "MainHand.IgniteColdMax", { format = "{0:output:MainHand.IgniteColdMin} to {0:output:MainHand.IgniteColdMax}", { breakdown = "MainHand.IgniteCold" }, }, },
	{ label = "OH Source Cold", bgCol = colorCodes.OFFHANDBG, textSize = 12, flag = "weapon2Attack", haveOutput = "OffHand.IgniteColdMax", { format = "{0:output:OffHand.IgniteColdMin} to {0:output:OffHand.IgniteColdMax}", { breakdown = "OffHand.IgniteCold" }, }, },
	{ label = "Source Fire", textSize = 12, notFlag = "attack", haveOutput = "IgniteFireMax", { format = "{0:output:IgniteFireMin} to {0:output:IgniteFireMax}", { breakdown = "IgniteFire" }, }, },
	{ label = "MH Source Fire", bgCol = colorCodes.MAINHANDBG, textSize = 12, flag = "weapon1Attack", haveOutput = "MainHand.IgniteFireMax", { format = "{0:output:MainHand.IgniteFireMin} to {0:output:MainHand.IgniteFireMax}", { breakdown = "MainHand.IgniteFire" }, }, },
	{ label = "OH Source Fire", bgCol = colorCodes.OFFHANDBG, textSize = 12, flag = "weapon2Attack", haveOutput = "OffHand.IgniteFireMax", { format = "{0:output:OffHand.IgniteFireMin} to {0:output:OffHand.IgniteFireMax}", { breakdown = "OffHand.IgniteFire" }, }, },
	{ label = "Source Chaos", textSize = 12, notFlag = "attack", haveOutput = "IgniteChaosMax", { format = "{0:output:IgniteChaosMin} to {0:output:IgniteChaosMax}", { breakdown = "IgniteChaos" }, }, },
	{ label = "MH Source Chaos", bgCol = colorCodes.MAINHANDBG, textSize = 12, flag = "weapon1Attack", haveOutput = "MainHand.IgniteChaosMax", { format = "{0:output:MainHand.IgniteChaosMin} to {0:output:MainHand.IgniteChaosMax}", { breakdown = "MainHand.IgniteChaos" }, }, },
	{ label = "OH Source Chaos", bgCol = colorCodes.OFFHANDBG, textSize = 12, flag = "weapon2Attack", haveOutput = "OffHand.IgniteChaosMax", { format = "{0:output:OffHand.IgniteChaosMin} to {0:output:OffHand.IgniteChaosMax}", { breakdown = "OffHand.IgniteChaos" }, }, },
	{ label = "Effective DPS Mod", flag = "effective", notFlag = "igniteToChaos", { format = "x {3:output:IgniteEffMult}", 
		{ breakdown = "IgniteEffMult" }, 
		{ label = "Enemy modifiers", modName = { "FireResist", "ElementalResist", "DamageTaken", "DamageTakenOverTime", "FireDamageTaken", "FireDamageTakenOverTime", "ElementalDamageTaken" }, enemy = true },
	}, },
	{ label = "Effective DPS Mod", flagList = { "effective", "igniteToChaos" }, { format = "x {3:output:IgniteEffMult}", 
		{ breakdown = "IgniteEffMult" }, 
		{ label = "Enemy modifiers", modName = { "ChaosResist", "DamageTaken", "DamageTakenOverTime", "ChaosDamageTaken", "ChaosDamageTakenOverTime" }, enemy = true },
	}, },
	{ label = "Ignite DPS", { format = "{1:output:IgniteDPS}", 
		{ breakdown = "IgniteDPS" }, 
		{ breakdown = "MainHand.IgniteDPS" },
		{ breakdown = "OffHand.IgniteDPS" },
	}, },
	{ label = "Burning Ground", haveOutput = "BurningGroundFromIgnite", { format = "{0:output:BurningGroundDPS}", { breakdown = "BurningGroundDPS" } } },
	{ label = "Ignite Duration", { format = "{2:output:IgniteDuration}s", 
		{ breakdown = "IgniteDuration" },
		{ label = "Player modifiers", modName = { "EnemyIgniteDuration", "EnemyAilmentDuration", "EnemyElementalAilmentDuration", "SkillAndDamagingAilmentDuration", "IgniteFaster", "IgniteSlower" }, cfg = "skill" },
		{ label = "Enemy modifiers", modName = {"SelfIgniteDuration", "SelfAilmentDuration", "SelfElementalAilmentDuration", "SelfIgniteFaster"}, enemy = true },
	}, },
	{ label = "Dmg. of all Ignites", { format = "{1:output:IgniteDamage}",
		{ breakdown = "MainHand.IgniteDamage" },
		{ breakdown = "OffHand.IgniteDamage" },
		{ breakdown = "IgniteDamage" },
	}, },
} }
} },
{ 1, "Decay", 1, colorCodes.OFFENCE, {{ defaultCollapsed = false, label = "Decay", data = {
	extra = "{1:output:DecayDPS} {2:output:DecayDuration}s",
	flag = "decay",
	{ label = "Total Increased", { format = "{0:mod:1}%", { modName = { "Damage", "ChaosDamage" }, modType = "INC", cfg = "decay" }, }, },
	{ label = "Total More", { format = "{0:mod:1}%", { modName = { "Damage", "ChaosDamage" }, modType = "MORE", cfg = "decay" }, }, },
	{ label = "Effective DPS Mod", flag = "effective", { format = "x {3:output:DecayEffMult}", 
		{ breakdown = "DecayEffMult" }, 
		{ label = "Enemy modifiers", modName = { "ChaosResist", "DamageTaken", "DamageTakenOverTime", "ChaosDamageTaken", "ChaosDamageTakenOverTime" }, enemy = true },
	}, },
	{ label = "Decay DPS", { format = "{1:output:DecayDPS}", 
		{ breakdown = "DecayDPS" }, 
	}, },
	{ label = "Decay Duration", { format = "{2:output:DecayDuration}s", 
		{ breakdown = "DecayDuration" },
	}, },
} }
} },
{ 1, "LeechGain", 1, colorCodes.OFFENCE, {{ defaultCollapsed = false, label = "Leech & Gain on Hit", data = {
	{ label = "Life Leech Cap", flag = "leechLife", { format = "{1:output:MaxLifeLeechRate}", 
		{ breakdown = "MaxLifeLeechRate" },
		{ modName = "MaxLifeLeechRate" },
	}, },
	{ label = "Life Leech Rate", flag = "leechLife", notFlag = "showAverage", { format = "{1:output:LifeLeechRate}", 
		{ breakdown = "LifeLeech" }, 
		{ label = "Player modifiers", notFlagList = { "totem", "attack" }, modName = { "DamageLeech", "DamageLifeLeech", "PhysicalDamageLifeLeech", "LightningDamageLifeLeech", "ColdDamageLifeLeech", "FireDamageLifeLeech", "ChaosDamageLifeLeech", "ElementalDamageLifeLeech" }, modType = "BASE", cfg = "skill" }, 
		{ label = "Main Hand", notFlag = "totem", flag = "weapon1Attack", modName = { "DamageLeech", "DamageLifeLeech", "PhysicalDamageLifeLeech", "LightningDamageLifeLeech", "ColdDamageLifeLeech", "FireDamageLifeLeech", "ChaosDamageLifeLeech", "ElementalDamageLifeLeech" }, modType = "BASE", cfg = "weapon1" }, 
		{ label = "Off Hand", notFlag = "totem", flag = "weapon2Attack", modName = { "DamageLeech", "DamageLifeLeech", "PhysicalDamageLifeLeech", "LightningDamageLifeLeech", "ColdDamageLifeLeech", "FireDamageLifeLeech", "ChaosDamageLifeLeech", "ElementalDamageLifeLeech" }, modType = "BASE", cfg = "weapon2" }, 
		{ label = "Totem modifiers", flag = "totem", modName = { "DamageLifeLeechToPlayer" }, modType = "BASE", cfg = "skill" },
		{ label = "Enemy modifiers", modName = { "SelfDamageLifeLeech" }, modType = "BASE", enemy = true },
	}, },
	{ label = "Life Leech per Hit", flagList = { "leechLife", "showAverage" }, { format = "{1:output:LifeLeechPerHit}", 
		{ breakdown = "LifeLeech" },
		{ label = "Player modifiers", notFlagList = { "totem", "attack" }, modName = { "DamageLeech", "DamageLifeLeech", "PhysicalDamageLifeLeech", "LightningDamageLifeLeech", "ColdDamageLifeLeech", "FireDamageLifeLeech", "ChaosDamageLifeLeech", "ElementalDamageLifeLeech" }, modType = "BASE", cfg = "skill" }, 
		{ label = "Main Hand", notFlag = "totem", flag = "weapon1Attack", modName = { "DamageLeech", "DamageLifeLeech", "PhysicalDamageLifeLeech", "LightningDamageLifeLeech", "ColdDamageLifeLeech", "FireDamageLifeLeech", "ChaosDamageLifeLeech", "ElementalDamageLifeLeech" }, modType = "BASE", cfg = "weapon1" }, 
		{ label = "Off Hand", notFlag = "totem", flag = "weapon2Attack", modName = { "DamageLeech", "DamageLifeLeech", "PhysicalDamageLifeLeech", "LightningDamageLifeLeech", "ColdDamageLifeLeech", "FireDamageLifeLeech", "ChaosDamageLifeLeech", "ElementalDamageLifeLeech" }, modType = "BASE", cfg = "weapon2" }, 
		{ label = "Totem modifiers", flag = "totem", modName = { "DamageLifeLeechToPlayer" }, modType = "BASE", cfg = "skill" },
		{ label = "Enemy modifiers", modName = { "SelfDamageLifeLeech" }, modType = "BASE", enemy = true },
	}, },
	{ label = "Life Gain Rate", notFlag = "showAverage", haveOutput = "LifeOnHitRate", { format = "{1:output:LifeOnHitRate}", 
		{ label = "Player modifiers", notFlag = "attack", modName = "LifeOnHit", modType = "BASE", cfg = "skill" }, 
		{ label = "Main Hand", flag = "weapon1Attack", modName = "LifeOnHit", modType = "BASE", cfg = "weapon1" }, 
		{ label = "Off Hand", flag = "weapon2Attack", modName = "LifeOnHit", modType = "BASE", cfg = "weapon2" }, 
		{ label = "Enemy modifiers", modName = { "SelfLifeOnHit" }, modType = "BASE", cfg = "skill", enemy = true },
	}, },
	{ label = "Life Gain per Hit", flag = "showAverage", haveOutput = "LifeOnHit", { format = "{1:output:LifeOnHit}", 
		{ label = "Player modifiers", notFlag = "attack", modName = "LifeOnHit", modType = "BASE", cfg = "skill" }, 
		{ label = "Main Hand", flag = "weapon1Attack", modName = "LifeOnHit", modType = "BASE", cfg = "weapon1" }, 
		{ label = "Off Hand", flag = "weapon2Attack", modName = "LifeOnHit", modType = "BASE", cfg = "weapon2" }, 
		{ label = "Enemy modifiers", modName = { "SelfLifeOnHit" }, modType = "BASE", cfg = "skill", enemy = true },
	}, },
	{ label = "Life Gain on Kill", haveOutput = "LifeOnKill", { format = "{1:output:LifeOnKill}", 
		{modName = "LifeOnKill"}, 
	}, },
	{ label = "ES Leech Cap", flag = "leechES", { format = "{1:output:MaxEnergyShieldLeechRate}", 
		{ breakdown = "MaxEnergyShieldLeechRate" },
		{ modName = "MaxEnergyShieldLeechRate" },
	}, },
	{ label = "ES Leech Rate", flag = "leechES", notFlag = "showAverage", { format = "{1:output:EnergyShieldLeechRate}", 
		{ breakdown = "EnergyShieldLeech" },
		{ label = "Player modifiers", notFlagList = { "totem", "attack" }, modName = { "DamageEnergyShieldLeech", "PhysicalDamageEnergyShieldLeech", "LightningDamageEnergyShieldLeech", "ColdDamageEnergyShieldLeech", "FireDamageEnergyShieldLeech", "ChaosDamageEnergyShieldLeech", "ElementalDamageEnergyShieldLeech" }, modType = "BASE", cfg = "skill" }, 
		{ label = "Main Hand", notFlag = "totem", flag = "weapon1Attack", modName = { "DamageEnergyShieldLeech", "PhysicalDamageEnergyShieldLeech", "LightningDamageEnergyShieldLeech", "ColdDamageEnergyShieldLeech", "FireDamageEnergyShieldLeech", "ChaosDamageEnergyShieldLeech", "ElementalDamageEnergyShieldLeech" }, modType = "BASE", cfg = "weapon1" }, 
		{ label = "Off Hand", notFlag = "totem", flag = "weapon2Attack", modName = { "DamageEnergyShieldLeech", "PhysicalDamageEnergyShieldLeech", "LightningDamageEnergyShieldLeech", "ColdDamageEnergyShieldLeech", "FireDamageEnergyShieldLeech", "ChaosDamageEnergyShieldLeech", "ElementalDamageEnergyShieldLeech" }, modType = "BASE", cfg = "weapon2" }, 
		{ label = "Totem modifiers", flag = "totem", modName = { "DamageEnergyShieldLeechToPlayer" }, modType = "BASE", cfg = "skill" },
		{ label = "Enemy modifiers", modName = { "SelfDamageEnergyShieldLeech" }, modType = "BASE", enemy = true },
	}, },
	{ label = "ES Leech per Hit", flagList = { "leechES", "showAverage" }, { format = "{1:output:EnergyShieldLeechPerHit}",
		{ breakdown = "EnergyShieldLeech" },
		{ label = "Player modifiers", notFlagList = { "totem", "attack" }, modName = { "DamageEnergyShieldLeech", "PhysicalDamageEnergyShieldLeech", "LightningDamageEnergyShieldLeech", "ColdDamageEnergyShieldLeech", "FireDamageEnergyShieldLeech", "ChaosDamageEnergyShieldLeech", "ElementalDamageEnergyShieldLeech" }, modType = "BASE", cfg = "skill" }, 
		{ label = "Main Hand", notFlag = "totem", flag = "weapon1Attack", modName = { "DamageEnergyShieldLeech", "PhysicalDamageEnergyShieldLeech", "LightningDamageEnergyShieldLeech", "ColdDamageEnergyShieldLeech", "FireDamageEnergyShieldLeech", "ChaosDamageEnergyShieldLeech", "ElementalDamageEnergyShieldLeech" }, modType = "BASE", cfg = "weapon1" }, 
		{ label = "Off Hand", notFlag = "totem", flag = "weapon2Attack", modName = { "DamageEnergyShieldLeech", "PhysicalDamageEnergyShieldLeech", "LightningDamageEnergyShieldLeech", "ColdDamageEnergyShieldLeech", "FireDamageEnergyShieldLeech", "ChaosDamageEnergyShieldLeech", "ElementalDamageEnergyShieldLeech" }, modType = "BASE", cfg = "weapon2" }, 
		{ label = "Totem modifiers", flag = "totem", modName = { "DamageEnergyShieldLeechToPlayer" }, modType = "BASE", cfg = "skill" },
		{ label = "Enemy modifiers", modName = { "SelfDamageEnergyShieldLeech" }, modType = "BASE", enemy = true },
	}, },
	{ label = "ES Gain Rate", notFlag = "showAverage", haveOutput = "EnergyShieldOnHitRate", { format = "{1:output:EnergyShieldOnHitRate}", 
		{ label = "Player modifiers", notFlag = "attack", modName = "EnergyShieldOnHit", modType = "BASE", cfg = "skill" }, 
		{ label = "Main Hand", flag = "weapon1Attack", modName = "EnergyShieldOnHit", modType = "BASE", cfg = "weapon1" }, 
		{ label = "Off Hand", flag = "weapon2Attack", modName = "EnergyShieldOnHit", modType = "BASE", cfg = "weapon2" }, 
		{ label = "Enemy modifiers", modName = { "SelfEnergyShieldOnHit" }, modType = "BASE", enemy = true },
	}, },
	{ label = "ES Gain per Hit", flag = "showAverage", haveOutput = "EnergyShieldOnHit", { format = "{1:output:EnergyShieldOnHit}", 
		{ label = "Player modifiers", notFlag = "attack", modName = "EnergyShieldOnHit", modType = "BASE", cfg = "skill" }, 
		{ label = "Main Hand", flag = "weapon1Attack", modName = "EnergyShieldOnHit", modType = "BASE", cfg = "weapon1" }, 
		{ label = "Off Hand", flag = "weapon2Attack", modName = "EnergyShieldOnHit", modType = "BASE", cfg = "weapon2" }, 
		{ label = "Enemy modifiers", modName = { "SelfEnergyShieldOnHit" }, modType = "BASE", cfg = "skill", enemy = true },
	}, },
	{ label = "ES Gain on Kill", haveOutput = "EnergyShieldOnKill", { format = "{1:output:EnergyShieldOnKill}", 
		{modName = "EnergyShieldOnKill"}, 
	}, },
	{ label = "Mana Leech Cap", flag = "leechMana", { format = "{1:output:MaxManaLeechRate}", 
		{ breakdown = "MaxManaLeechRate" },
		{ modName = "MaxManaLeechRate" },
	}, },
	{ label = "Mana Leech Rate", flag = "leechMana", notFlag = "showAverage", { format = "{1:output:ManaLeechRate}", 
		{ breakdown = "ManaLeech" }, 
		{ label = "Player modifiers", notFlag = "attack", modName = { "DamageLeech", "DamageManaLeech", "PhysicalDamageManaLeech", "LightningDamageManaLeech", "ColdDamageManaLeech", "FireDamageManaLeech", "ChaosDamageManaLeech", "ElementalDamageManaLeech" }, modType = "BASE", cfg = "skill" }, 
		{ label = "Main Hand", flag = "weapon1Attack", modName = { "DamageLeech", "DamageManaLeech", "PhysicalDamageManaLeech", "LightningDamageManaLeech", "ColdDamageManaLeech", "FireDamageManaLeech", "ChaosDamageManaLeech", "ElementalDamageManaLeech" }, modType = "BASE", cfg = "weapon1" }, 
		{ label = "Off Hand", flag = "weapon2Attack", modName = { "DamageLeech", "DamageManaLeech", "PhysicalDamageManaLeech", "LightningDamageManaLeech", "ColdDamageManaLeech", "FireDamageManaLeech", "ChaosDamageManaLeech", "ElementalDamageManaLeech" }, modType = "BASE", cfg = "weapon2" }, 
		{ label = "Enemy modifiers", modName = { "SelfDamageManaLeech" }, modType = "BASE", cfg = "skill", enemy = true },
	}, },
	{ label = "Mana Leech per Hit", flagList = { "leechMana", "showAverage" }, { format = "{1:output:ManaLeechPerHit}", 
		{ breakdown = "ManaLeech" }, 
		{ label = "Player modifiers", notFlag = "attack", modName = { "DamageLeech", "DamageManaLeech", "PhysicalDamageManaLeech", "LightningDamageManaLeech", "ColdDamageManaLeech", "FireDamageManaLeech", "ChaosDamageManaLeech", "ElementalDamageManaLeech" }, modType = "BASE", cfg = "skill" }, 
		{ label = "Main Hand", flag = "weapon1Attack", modName = { "DamageLeech", "DamageManaLeech", "PhysicalDamageManaLeech", "LightningDamageManaLeech", "ColdDamageManaLeech", "FireDamageManaLeech", "ChaosDamageManaLeech", "ElementalDamageManaLeech" }, modType = "BASE", cfg = "weapon1" }, 
		{ label = "Off Hand", flag = "weapon2Attack", modName = { "DamageLeech", "DamageManaLeech", "PhysicalDamageManaLeech", "LightningDamageManaLeech", "ColdDamageManaLeech", "FireDamageManaLeech", "ChaosDamageManaLeech", "ElementalDamageManaLeech" }, modType = "BASE", cfg = "weapon2" }, 
		{ label = "Enemy modifiers", modName = { "SelfDamageManaLeech" }, modType = "BASE", enemy = true },
	}, },
	{ label = "Mana Gain Rate", notFlag = "showAverage", haveOutput = "ManaOnHitRate", { format = "{1:output:ManaOnHitRate}", 
		{ label = "Player modifiers", notFlag = "attack", modName = "ManaOnHit", modType = "BASE", cfg = "skill" }, 
		{ label = "Main Hand", flag = "weapon1Attack", modName = "ManaOnHit", modType = "BASE", cfg = "weapon1" }, 
		{ label = "Off Hand", flag = "weapon2Attack", modName = "ManaOnHit", modType = "BASE", cfg = "weapon2" }, 
		{ label = "Enemy modifiers", modName = { "SelfManaOnHit" }, modType = "BASE", cfg = "skill", enemy = true },
	}, },
	{ label = "Mana Gain per Hit", flag = "showAverage", haveOutput = "ManaOnHit", { format = "{1:output:ManaOnHit}",
		{ label = "Player modifiers", notFlag = "attack", modName = "ManaOnHit", modType = "BASE", cfg = "skill" }, 
		{ label = "Main Hand", flag = "weapon1Attack", modName = "ManaOnHit", modType = "BASE", cfg = "weapon1" }, 
		{ label = "Off Hand", flag = "weapon2Attack", modName = "ManaOnHit", modType = "BASE", cfg = "weapon2" }, 
		{ label = "Enemy modifiers", modName = { "SelfManaOnHit" }, modType = "BASE", cfg = "skill", enemy = true },
	}, },
	{ label = "Mana Gain on Kill", haveOutput = "ManaOnKill", { format = "{1:output:ManaOnKill}", 
		{modName = "ManaOnKill"}, 
	}, },
} }
} },
{ 1, "EleAilments", 1, colorCodes.OFFENCE, {{ defaultCollapsed = false, label = "Non-Damaging Ailments", data = {
	{ label = "Enemy Ail. Thresh.", { format = "{0:output:EnemyAilmentThreshold}", { modName = "EnemyAilmentThreshold" }, }, },
	{ label = "Scorch Effect Mod", bgCol = colorCodes.SCORCHBG, flag = "scorch", { format = "x {2:output:ScorchEffectMod}",
		{ breakdown = "ScorchEffectMod" },
		{ breakdown = "MainHand.ScorchDPS" },
		{ breakdown = "OffHand.ScorchDPS" },
		{ breakdown = "ScorchDPS" },
		{ label = "Player modifiers", modName = { "EnemyScorchEffect", "ScorchAsThoughDealing" }, cfg = "skill" },
		{ label = "Enemy modifiers", modName = "SelfScorchEffect", enemy = true },
	}, },
	{ label = "Chance to Scorch", bgCol = colorCodes.SCORCHBG, flag = "scorch", { format = "{0:output:ScorchChance}%",
		{ breakdown = "MainHand.ScorchChance" },
		{ breakdown = "OffHand.ScorchChance" },
		{ breakdown = "ScorchChance" },
		{ label = "Player modifiers", modName = "EnemyScorchChance", cfg = "skill" },
		{ label = "Enemy modifiers", modName = "SelfScorchChance", enemy = true },
	}, },
	{ label = "Scorch Duration", bgCol = colorCodes.SCORCHBG, flag = "scorch", { format = "{2:output:ScorchDuration}s",
		{ breakdown = "MainHand.ScorchDuration" },
		{ breakdown = "OffHand.ScorchDuration" },	
		{ breakdown = "ScorchDuration" },
		{ label = "Player modifiers", modName = { "EnemyScorchDuration", "EnemyAilmentDuration", "EnemyElementalAilmentDuration" }, cfg = "skill" },
		{ label = "Enemy modifiers", modName = { "SelfScorchDuration", "SelfAilmentDuration", "SelfElementalAilmentDuration", "BuffExpireFaster" }, enemy = true },
	}, },
	{ label = "Maximum Scorch", bgCol = colorCodes.SCORCHBG, flag = "scorch", { format = "{0:output:MaximumScorch}%",
		{ modName = "ScorchMax" },
	}, },
	{ label = "Current Scorch", bgCol = colorCodes.SCORCHBG, haveOutput = "CurrentScorch", { format = "{0:output:CurrentScorch}%",
		{ label = "Configured Scorch", modName = "ScorchVal", enemy = true, modType = "BASE" },
		{ label = "Guaranteed Scorches", modName = "ScorchOverride", modType = "BASE" },
		{ label = "Total Scorch", modName = "ElementalResistByScorch", enemy = true, cfg = "skill" },
	}, },
	{ label = "Effect of Chill", bgCol = colorCodes.CHILLBG, flag = "chill", haveOutput = "ChillSourceEffect", { format = "{0:output:ChillSourceEffect}%", { breakdown = "DotChill" }, }, },
	{ label = "Chill Effect Mod", bgCol = colorCodes.CHILLBG, flag = "chill", { format = "x {2:output:ChillEffectMod}",
		{ breakdown = "ChillEffectMod" },
		{ breakdown = "MainHand.ChillDPS" },
		{ breakdown = "OffHand.ChillDPS" },
		{ breakdown = "ChillDPS" },
		{ label = "Player modifiers", modName = { "EnemyChillMagnitude", "AilmentMagnitude", "ChillAsThoughDealing" }, cfg = "skill" },
		{ label = "Enemy modifiers", modName = "SelfChillEffect", enemy = true },
	}, },
	{ label = "Chill Duration", bgCol = colorCodes.CHILLBG, flag = "chill", { format = "{2:output:ChillDuration}s",
		{ breakdown = "MainHand.ChillDuration" },
		{ breakdown = "OffHand.ChillDuration" },
		{ breakdown = "ChillDuration" },
		{ label = "Player modifiers", modName = { "EnemyChillDuration", "EnemyAilmentDuration", "EnemyElementalAilmentDuration" }, cfg = "skill" },
		{ label = "Enemy modifiers", modName = { "SelfChillDuration", "SelfAilmentDuration", "SelfElementalAilmentDuration", "BuffExpireFaster" }, enemy = true },
	}, },
	{ label = "Maximum Chill", bgCol = colorCodes.CHILLBG, flag = "chill", { format = "{0:output:MaximumChill}%",
		{ modName = "ChillMax" },
	}, },
	{ label = "Current Chill", bgCol = colorCodes.CHILLBG, haveOutput = "CurrentChill", { format = "{0:output:CurrentChill}%",
		{ label = "Configured Chill", modName = "ChillVal", enemy = true, modType = "BASE" },
		{ label = "Guaranteed Chills", modName = "ChillOverride", modType = "BASE" },
	}, },
	{ label = "Chance to Freeze", bgCol = colorCodes.FREEZEBG, flag = "freeze", { format = "{0:output:FreezeChance}%",
		{ breakdown = "MainHand.FreezeChance" },
		{ breakdown = "OffHand.FreezeChance" },
		{ breakdown = "FreezeChance" },
		{ label = "Player modifiers", modName = "EnemyFreezeChance", cfg = "skill" },
		{ label = "Enemy modifiers", modName = "SelfFreezeChance", enemy = true },
	}, },
	{ label = "Freeze Duration", bgCol = colorCodes.FREEZEBG, flag = "freeze", { format = "x {2:output:FreezeDurationMod}",
		{ breakdown = "FreezeDurationMod" },
		{ breakdown = "MainHand.FreezeDPS" },
		{ breakdown = "OffHand.FreezeDPS" },
		{ breakdown = "FreezeDPS" },
		{ label = "Player modifiers", modName = { "EnemyFreezeDuration", "EnemyAilmentDuration", "EnemyElementalAilmentDuration", "FreezeAsThoughDealing" }, cfg = "skill" },
		{ label = "Enemy modifiers", modName = { "SelfFreezeDuration", "SelfAilmentDuration", "SelfElementalAilmentDuration", "BuffExpireFaster", "HoarfrostFreezeDuration" }, enemy = true },
	}, },
	{ label = "Brittle Effect Mod", bgCol = colorCodes.BRITTLEBG, flag = "brittle", { format = "x {2:output:BrittleEffectMod}",
		{ breakdown = "BrittleEffectMod" },
		{ breakdown = "MainHand.BrittleDPS" },
		{ breakdown = "OffHand.BrittleDPS" },
		{ breakdown = "BrittleDPS" },
		{ label = "Player modifiers", modName = { "EnemyBrittleEffect", "BrittleAsThoughDealing" }, cfg = "skill" },
		{ label = "Enemy modifiers", modName = "SelfBrittleEffect", enemy = true },
	}, },
	{ label = "Chance to Brittle", bgCol = colorCodes.BRITTLEBG, flag = "brittle", { format = "{0:output:BrittleChance}%",
		{ breakdown = "MainHand.BrittleChance" },
		{ breakdown = "OffHand.BrittleChance" },
		{ breakdown = "BrittleChance" },
		{ label = "Player modifiers", modName = "EnemyBrittleChance", cfg = "skill" },
		{ label = "Enemy modifiers", modName = "SelfBrittleChance", enemy = true },
	}, },
	{ label = "Brittle Duration", bgCol = colorCodes.BRITTLEBG, flag = "brittle", { format = "{2:output:BrittleDuration}s",
		{ breakdown = "MainHand.BrittleDuration" },
		{ breakdown = "OffHand.BrittleDuration" },
		{ breakdown = "BrittleDuration" },
		{ label = "Player modifiers", modName = { "EnemyBrittleDuration", "EnemyAilmentDuration", "EnemyElementalAilmentDuration" }, cfg = "skill" },
		{ label = "Enemy modifiers", modName = { "SelfBrittleDuration", "SelfAilmentDuration", "SelfElementalAilmentDuration", "BuffExpireFaster" }, enemy = true },
	}, },
	{ label = "Maximum Brittle", bgCol = colorCodes.BRITTLEBG, flag = "brittle", { format = "{0:output:MaximumBrittle}%",
		{ modName = "BrittleMax" },
	}, },
	{ label = "Current Brittle", bgCol = colorCodes.BRITTLEBG, haveOutput = "CurrentBrittle", { format = "{0:output:CurrentBrittle}%",
		{ label = "Configured Brittle", modName = "BrittleVal", enemy = true, modType = "BASE" },
		{ label = "Guaranteed Brittles", modName = "BrittleOverride", modType = "BASE" },
	}, },
	{ label = "Shock Effect Mod", bgCol = colorCodes.SHOCKBG, flag = "shock", { format = "x {2:output:ShockEffectMod}",
		{ breakdown = "ShockEffectMod" },
		{ breakdown = "MainHand.ShockDPS" },
		{ breakdown = "OffHand.ShockDPS" },
		{ breakdown = "ShockDPS" },
		{ label = "Player modifiers", notFlag = "attack", modName = { "EnemyShockMagnitude", "AilmentMagnitude", "ShockAsThoughDealing" }, cfg = "skill" },
		{ label = "Main hand modifiers", flag = "weapon1Attack", modName = { "EnemyShockMagnitude", "AilmentMagnitude", "ShockAsThoughDealing" }, cfg = "weapon1" },
		{ label = "Off hand modifiers", flag = "weapon2Attack", modName = { "EnemyShockMagnitude", "AilmentMagnitude", "ShockAsThoughDealing" }, cfg = "weapon2" },
		{ label = "Enemy modifiers", modName = "SelfShockMagnitude", enemy = true },
	}, },
	{ label = "Chance to Shock", bgCol = colorCodes.SHOCKBG, flag = "shock", { format = "{0:output:ShockChance}%",
		{ breakdown = "MainHand.ShockChance" },
		{ breakdown = "OffHand.ShockChance" },
		{ breakdown = "ShockChance" },
		{ label = "Player modifiers", modName = "EnemyShockChance", cfg = "skill" },
		{ label = "Enemy modifiers", modName = "SelfShockChance", enemy = true },
	}, },
	{ label = "Shock Duration", bgCol = colorCodes.SHOCKBG, flag = "shock", { format = "{2:output:ShockDuration}s",
		{ breakdown = "MainHand.ShockDuration" },
		{ breakdown = "OffHand.ShockDuration" },
		{ breakdown = "ShockDuration" },
		{ label = "Player modifiers", modName = { "EnemyShockDuration", "EnemyAilmentDuration", "EnemyElementalAilmentDuration" }, cfg = "skill" },
		{ label = "Enemy modifiers", modName = { "SelfShockDuration", "SelfAilmentDuration", "SelfElementalAilmentDuration", "BuffExpireFaster" }, enemy = true },
	}, },
	{ label = "Maximum Shock", bgCol = colorCodes.SHOCKBG, flag = "shock", { format = "{0:output:MaximumShock}%",
		{ modName = "ShockMax" },
	}, },
	{ label = "Shock Stacks", bgCol = colorCodes.SHOCKBG, haveOutput = "ShockStackCount", { format = "{0:output:ShockStackCount}",
	}, },
	{ label = "Current Shock", bgCol = colorCodes.SHOCKBG, haveOutput = "CurrentShock", { format = "{0:output:CurrentShock}%",
		{ label = "Configured Shock", modName = "ShockVal", enemy = true, modType = "BASE" },
		{ label = "Guaranteed Shocks", modName = "ShockOverride", modType = "BASE" },
		{ label = "Total Shock", modName = "DamageTakenByShock", enemy = true, cfg = "skill" },
	}, },
	{ label = "Sap Effect Mod", bgCol = colorCodes.SAPBG, flag = "sap", { format = "x {2:output:SapEffectMod}",
		{ breakdown = "SapEffectMod" },
		{ breakdown = "MainHand.SapDPS" },
		{ breakdown = "OffHand.SapDPS" },
		{ breakdown = "SapDPS" },
		{ label = "Player modifiers", modName = { "EnemySapEffect", "SapAsThoughDealing" }, cfg = "skill" },
		{ label = "Enemy modifiers", modName = "SelfSapEffect", enemy = true },
	}, },
	{ label = "Chance to Sap", bgCol = colorCodes.SAPBG, flag = "sap", { format = "{0:output:SapChance}%",
		{ breakdown = "MainHand.SapChance" },
		{ breakdown = "OffHand.SapChance" },
		{ breakdown = "SapChance" },
		{ label = "Player modifiers", modName = "EnemySapChance", cfg = "skill" },
		{ label = "Enemy modifiers", modName = "SelfSapChance", enemy = true },
	}, },
	{ label = "Sap Duration", bgCol = colorCodes.SAPBG, flag = "sap", { format = "{2:output:SapDuration}s",
		{ breakdown = "MainHand.SapDuration" },
		{ breakdown = "OffHand.SapDuration" },
		{ breakdown = "SapDuration" },
		{ label = "Player modifiers", modName = { "EnemySapDuration", "EnemyAilmentDuration", "EnemyElementalAilmentDuration" }, cfg = "skill" },
		{ label = "Enemy modifiers", modName = { "SelfSapDuration", "SelfAilmentDuration", "SelfElementalAilmentDuration", "BuffExpireFaster" }, enemy = true },
	}, },
	{ label = "Maximum Sap", bgCol = colorCodes.SAPBG, flag = "sap", { format = "{0:output:MaximumSap}%",
		{ modName = "SapMax" },
	}, },
	{ label = "Current Sap", bgCol = colorCodes.SAPBG, haveOutput = "CurrentSap", { format = "{0:output:CurrentSap}%",
		{ label = "Configured Sap", modName = "SapVal", enemy = true, modType = "BASE" },
		{ label = "Guaranteed Saps", modName = "SapOverride", modType = "BASE" },
	}, },
} }
} },
{ 1, "MiscEffects", 1, colorCodes.OFFENCE, {{ defaultCollapsed = false, label = "Other Effects", data = {
	{ label = "Stun Threshold", flag = "hit", notFlag = "attack", { format = "x {2:output:EnemyStunThresholdMod}", { modName = "EnemyStunThreshold", cfg = "skill" }, }, },
	{ label = "Stun Duration", flag = "hit", notFlag = "attack", { format = "{2:output:EnemyStunDuration}s", 
		{ breakdown = "EnemyStunDuration" }, 
		{ label = "Player modifiers", modName = { "EnemyStunDuration", "EnemyStunDurationOnCrit", "DoubleEnemyStunDurationChance" }, cfg = "skill" },
		{ label = "Enemy modifiers", modName = { "StunRecovery", "SelfDoubleStunDurationChance" }, enemy = true },
	}, },
	{ label = "MH Stun Threshold", bgCol = colorCodes.MAINHANDBG, flagList = {"hit","weapon1Attack"}, { format = "x {2:output:MainHand.EnemyStunThresholdMod}", { modName = "EnemyStunThreshold", cfg = "weapon1" }, }, },
	{ label = "MH Stun Duration", bgCol = colorCodes.MAINHANDBG, flagList = {"hit","weapon1Attack"}, { format = "{2:output:MainHand.EnemyStunDuration}s", 
		{ breakdown = "MainHand.EnemyStunDuration" }, 
		{ label = "Player modifiers", modName = { "EnemyStunDuration", "EnemyStunDurationOnCrit", "DoubleEnemyStunDurationChance" }, cfg = "weapon1" },
		{ label = "Enemy modifiers", modName = { "StunRecovery", "SelfDoubleStunDurationChance" }, enemy = true },
	}, },
	{ label = "OH Stun Threshold", bgCol = colorCodes.OFFHANDBG, flagList = {"hit","weapon2Attack"}, { format = "x {2:output:OffHand.EnemyStunThresholdMod}", { modName = "EnemyStunThreshold", cfg = "weapon2" }, }, },
	{ label = "OH Stun Duration", bgCol = colorCodes.OFFHANDBG, flagList = {"hit","weapon2Attack"}, { format = "{2:output:OffHand.EnemyStunDuration}s", 
		{ breakdown = "OffHand.EnemyStunDuration" }, 
		{ label = "Player modifiers", modName = { "EnemyStunDuration", "EnemyStunDurationOnCrit", "DoubleEnemyStunDurationChance" }, cfg = "weapon2" },
		{ label = "Enemy modifiers", modName = { "StunRecovery", "SelfDoubleStunDurationChance" }, enemy = true },
	}, },
	{ label = "Knockback Chance", haveOutput = "KnockbackChance", { format = "{0:output:KnockbackChance}%", 
		{ label = "Player modifiers", modName = "EnemyKnockbackChance", cfg = "skill" }, 
		{ label = "Enemy modifiers", modName = "SelfKnockbackChance", enemy = true },
	}, },
	{ label = "Knockback Dist.", haveOutput = "KnockbackChance", { format = "{0:output:KnockbackDistance}", 
		{ breakdown = "KnockbackDistance" },
		{ modName = "EnemyKnockbackDistance", cfg = "skill" }, 
	}, },
	{ label = "MH K.B. Chance", bgCol = colorCodes.MAINHANDBG, haveOutput = "MainHand.KnockbackChance", { format = "{0:output:MainHand.KnockbackChance}%", 
		{ label = "Player modifiers", modName = "EnemyKnockbackChance", cfg = "weapon1" },
		{ label = "Enemy modifiers", modName = "SelfKnockbackChance", enemy = true },
	}, },
	{ label = "MH K.B. Dist.", bgCol = colorCodes.MAINHANDBG, haveOutput = "MainHand.KnockbackChance", { format = "{0:output:MainHand.KnockbackDistance}",
		{ breakdown = "MainHand.KnockbackDistance" },
		{ modName = "EnemyKnockbackDistance", cfg = "weapon1" },
	}, },
	{ label = "OH K.B. Chance", bgCol = colorCodes.OFFHANDBG, haveOutput = "OffHand.KnockbackChance", { format = "{0:output:OffHand.KnockbackChance}%", 
		{ label = "Player modifiers", modName = "EnemyKnockbackChance", cfg = "weapon2" },
		{ label = "Enemy modifiers", modName = "SelfKnockbackChance", enemy = true },
	}, },
	{ label = "OH K.B. Dist.", bgCol = colorCodes.OFFHANDBG, haveOutput = "OffHand.KnockbackChance", { format = "{0:output:OffHand.KnockbackDistance}",
		{ breakdown = "OffHand.KnockbackDistance" },
		{ modName = "EnemyKnockbackDistance", cfg = "weapon2" },
	}, },
	{ label = "Presence Mod", haveOutput = "PresenceMod", { format = "{2:output:PresenceMod}", { breakdown = "PresenceMod" }, { modName = "PresenceRadius", cfg = "skill" }} , },
	{ label = "Presence Radius", haveOutput = "PresenceRadius", { format = "{1:output:PresenceRadiusMetres}m", { breakdown = "PresenceRadius" }, { modName = "PresenceArea", cfg = "skill"} }, },
	{ label = "Chance to Blind", { format = "{0:mod:1}%", { modName = "BlindChance", modType = "BASE", cfg = "skill" }, }, },
	{ label = "Chance to Rearm", haveOutput = "HazardRearmChance", { format = "{2:mod:1}%", { modName = "HazardRearmChance", modType = "BASE", cfg = "skill"}, }, },
	{ label = "Inc. Quiver Effect", { format = "{0:mod:1}%", { modName="EffectOfBonusesFromQuiver", modType= "INC", cfg = "skill" }, }, },
	{ label = "Inc. Item Quantity", { format = "{0:mod:1}%", { modName = "LootQuantity", modType = "INC", cfg = "skill" }, }, },
	{ label = "IIQ for Normal Mobs", haveOutput = "LootQuantityNormalEnemies", { format = "{0:output:LootQuantityNormalEnemies}%", { modName = { "LootQuantityNormalEnemies", "LootQuantity" } }, }, },
	{ label = "Inc. Item Rarity", { format = "{0:mod:1}%", { modName = "LootRarity", modType = "INC", cfg = "skill" }, }, },
	{ label = "IIR for Magic Mobs", haveOutput = "LootRarityMagicEnemies", { format = "{0:output:LootRarityMagicEnemies}%", { modName = { "LootRarityMagicEnemies", "LootRarity" } }, }, },
	{ label = "Culling Strike", haveOutput = "CullPercent", { format = "{0:output:CullPercent}%", { modName = { "CullPercent", "CriticalCullPercent" }, cfg = "skill" } } },
	{ label = "Enemy Life Recovery", haveOutput = "EnemyLifeRegen", { format =  "{0:output:EnemyLifeRegen}%", { modName = "LifeRegen", modType = "INC", enemy = true }, }, },
	{ label = "Enemy Mana Recovery", haveOutput = "EnemyManaRegen", { format =  "{0:output:EnemyManaRegen}%", { modName = "ManaRegen", modType = "INC", enemy = true }, }, },
	{ label = "Enemy ES Recovery", haveOutput = "EnemyEnergyShieldRegen", { format =  "{0:output:EnemyEnergyShieldRegen}%", { modName = "EnergyShieldRegen", modType = "INC", enemy = true }, }, },
} }
} },
-- attributes/resists
{ 1, "Attributes", 2, colorCodes.NORMAL, {{ defaultCollapsed = false, label = "Attributes", data = {
	extra = colorCodes.STRENGTH.."{0:output:Str}^7, "..colorCodes.DEXTERITY.."{0:output:Dex}^7, "..colorCodes.INTELLIGENCE.."{0:output:Int}^7",
	{ label = "Strength", { format = "{0:output:Str}", { breakdown = "Str" }, { modName = "Str" }, }, },
	{ label = "Dexterity", { format = "{0:output:Dex}", { breakdown = "Dex" }, { modName = "Dex" }, }, },
	{ label = "Intelligence", { format = "{0:output:Int}", { breakdown = "Int" }, { modName = "Int" }, }, },
	{ label = "Gem Req Mod", { format = "{2:output:GlobalGemAttributeRequirements}", { modName = { "GlobalGemAttributeRequirements", "GlobalAttributeRequirements"} }, }, },
	{ label = "Item Req Mod", { format = "{2:output:GlobalItemAttributeRequirements}", { modName = { "GlobalItemAttributeRequirements", "GlobalAttributeRequirements"} }, }, },
	{ label = "Weapon Req Mod", { format = "{2:output:GlobalWeaponAttributeRequirements}", { modName = { "GlobalWeaponAttributeRequirements", "GlobalItemAttributeRequirements", "GlobalAttributeRequirements"} }, }, },
	{ notFlag = "minionSkill", label = "Str. Required", { format = "{output:ReqStrString}", { breakdown = "ReqStr" }, }, },
	{ notFlag = "minionSkill", label = "Dex. Required", { format = "{output:ReqDexString}", { breakdown = "ReqDex" }, }, },
	{ notFlag = "minionSkill", label = "Int. Required", { format = "{output:ReqIntString}", { breakdown = "ReqInt" }, }, },
} }
} },
-- primary defenses
{ 1, "Life", 2, colorCodes.LIFE, {{ defaultCollapsed = false, label = "Life", data = {
	extra = "{0:output:LifeUnreserved}/{0:output:Life}",
	{ label = "Base from Gear", { format = "{0:mod:1}", { modName = "Life", modType = "BASE", modSource = "Item" }, }, },
	{ label = "Inc. from Tree", { format = "{0:mod:1}%", { modName = "Life", modType = "INC", modSource = "Tree" }, }, },
	{ label = "Total Base", { format = "{0:mod:1}", { modName = "Life", modType = "BASE" }, }, },
	{ label = "Total Increased", { format = "{0:mod:1}%", { modName = "Life", modType = "INC", }, }, },
	{ label = "Total More", { format = "{0:mod:1}%", { modName = "Life", modType = "MORE", }, }, },
	{ label = "Total", { format = "{0:output:Life}", { breakdown = "Life" }, }, },
	{ label = "Reserved", { format = "{0:output:LifeReserved} ({0:output:LifeReservedPercent}%)", { breakdown = "LifeReserved" }, }, },
	{ label = "Unreserved", { format = "{0:output:LifeUnreserved} ({0:output:LifeUnreservedPercent}%)" }, },
	{ label = "Total Recoverable", haveOutput = "CappingLife", { format = "{0:output:LifeRecoverable}", { breakdown = "LifeUnreserved" }, }, },
	{ label = "Recharge Rate", haveOutput = "EnergyShieldRechargeAppliesToLife", { format = "{1:output:LifeRecharge}", 
		{ breakdown = "LifeRecharge" },
		{ modName = { "EnergyShieldRecharge", "LifeRecoveryRate", "EnergyShieldRechargeAppliesToLife" }, },
	}, },
	{ label = "Recharge Delay", haveOutput = "EnergyShieldRechargeAppliesToLife", { format = "{2:output:EnergyShieldRechargeDelay}s", 
		{ breakdown = "EnergyShieldRechargeDelay" },
		{ modName = "EnergyShieldRechargeFaster" },
	}, },
	{ label = "Recovery", { format = "{1:output:LifeRegenRecovery} ({1:output:LifeRegenPercent}%)",
		{ breakdown = "LifeRegenRecovery" }, 
		{ label = "Sources", modName = { "LifeRegen", "LifeRegenPercent", "LifeDegen", "LifeDegenPercent", "LifeRecovery" }, modType = "BASE" },
		{ label = "Increased Life Recovery", modName = { "LifeRegen", "LifeRecoveryRate" }, modType = "INC" },
		{ label = "More Life Recovery", modName = { "LifeRegen", "LifeRecoveryRate" }, modType = "MORE" },
	}, },
	{ label = "Recoup", haveOutput = "LifeRecoup", { format = "{1:output:LifeRecoup}%", { breakdown = "LifeRecoup" }, 
		{ label = "Sources", modName = "LifeRecoup" },
		{ label = "Recovery modifiers", modName = "LifeRecoveryRate" },
		{ label = "FasterRecoup", modName = { "3SecondRecoup", "3SecondLifeRecoup" } },
	}, },
	{ label = "Phys Recoup", haveOutput = "PhysicalLifeRecoup", { format = "{1:output:PhysicalLifeRecoup}%", { breakdown = "PhysicalLifeRecoup" }, 
		{ label = "Sources", modName = "PhysicalLifeRecoup" },
		{ label = "Recovery modifiers", modName = "LifeRecoveryRate" },
		{ label = "FasterRecoup", modName = { "3SecondRecoup", "3SecondLifeRecoup" } },
	}, },
	{ label = "Light Recoup", haveOutput = "LightningLifeRecoup", { format = "{1:output:LightningLifeRecoup}%", { breakdown = "LightningLifeRecoup" }, 
		{ label = "Sources", modName = "LightningLifeRecoup" },
		{ label = "Recovery modifiers", modName = "LifeRecoveryRate" },
		{ label = "FasterRecoup", modName = { "3SecondRecoup", "3SecondLifeRecoup" } },
	}, },
	{ label = "Cold Recoup", haveOutput = "ColdLifeRecoup", { format = "{1:output:ColdLifeRecoup}%", { breakdown = "ColdLifeRecoup" }, 
		{ label = "Sources", modName = "ColdLifeRecoup" },
		{ label = "Recovery modifiers", modName = "LifeRecoveryRate" },
		{ label = "FasterRecoup", modName = { "3SecondRecoup", "3SecondLifeRecoup" } },
	}, },
	{ label = "Fire Recoup", haveOutput = "FireLifeRecoup", { format = "{1:output:FireLifeRecoup}%", { breakdown = "FireLifeRecoup" }, 
		{ label = "Sources", modName = "FireLifeRecoup" },
		{ label = "Recovery modifiers", modName = "LifeRecoveryRate" },
		{ label = "FasterRecoup", modName = { "3SecondRecoup", "3SecondLifeRecoup" } },
	}, },
	{ label = "Chaos Recoup", haveOutput = "ChaosLifeRecoup", { format = "{1:output:ChaosLifeRecoup}%", { breakdown = "ChaosLifeRecoup" }, 
		{ label = "Sources", modName = "ChaosLifeRecoup" },
		{ label = "Recovery modifiers", modName = "LifeRecoveryRate" },
		{ label = "FasterRecoup", modName = { "3SecondRecoup", "3SecondLifeRecoup" } },
	}, },
	{ label = "Dmg. Mit. Regen", haveOutput = "PhysicalDamageMitigatedLifePseudoRecoup", { format = "{1:output:PhysicalDamageMitigatedLifePseudoRecoup}%", { breakdown = "PhysicalDamageMitigatedLifePseudoRecoup" }, 
		{ label = "Sources", modName = "PhysicalDamageMitigatedLifePseudoRecoup" },
		{ label = "Increased Life Regeneration Rate", modName = { "LifeRegen" }, modType = "INC" },
		{ label = "More Life Regeneration Rate", modName = { "LifeRegen" }, modType = "MORE" },
		{ label = "Recovery modifiers", modName = "LifeRecoveryRate" },
	}, },
} }
} },
{ 1, "Mana", 2, colorCodes.MANA, {{ defaultCollapsed = false, label = "Mana", data = {
	extra = "{0:output:ManaUnreserved}/{0:output:Mana}",
	notFlag = "minionSkill",
	{ label = "Base from Gear", { format = "{0:mod:1}", { modName = "Mana", modType = "BASE", modSource = "Item" }, }, },
	{ label = "Inc. from Tree", { format = "{0:mod:1}%", { modName = "Mana", modType = "INC", modSource = "Tree" }, }, },
	{ label = "Total Base", { format = "{0:mod:1}", { modName = "Mana", modType = "BASE" }, }, },
	{ label = "Total Increased", { format = "{0:mod:1}%", { modName = "Mana", modType = "INC" }, }, },
	{ label = "Total", { format = "{0:output:Mana}", { breakdown = "Mana" }, }, },
	{ label = "Reserved", { format = "{0:output:ManaReserved} ({0:output:ManaReservedPercent}%)", { breakdown = "ManaReserved" }, }, },
	{ label = "Unreserved", { format = "{0:output:ManaUnreserved} ({0:output:ManaUnreservedPercent}%)" }, },
	{ label = "Recovery", { format = "{1:output:ManaRegenRecovery} ({1:output:ManaRegenPercent}%)",
		{ breakdown = "ManaRegenRecovery" }, 
		{ label = "Sources", modName = { "ManaRegen", "ManaRegenPercent", "ManaDegen", "ManaDegenPercent", "ManaRecovery" }, modType = "BASE" },
		{ label = "Increased Mana Recovery", modName = { "ManaRegen", "ManaRecoveryRate" }, modType = "INC" },
		{ label = "More Mana Recovery", modName = { "ManaRegen", "ManaRecoveryRate" }, modType = "MORE" },
	}, },
	{ label = "Recoup", haveOutput = "ManaRecoup", { format = "{1:output:ManaRecoup}%", { breakdown = "ManaRecoup" }, 
		{ label = "Sources", modName = "ManaRecoup" },
		{ label = "Recovery modifiers", modName = "ManaRecoveryRate" },
		{ label = "FasterRecoup", modName = "3SecondRecoup" },
	}, },
} }
} },
{ 1, "Spirit", 2, colorCodes.SPIRIT, {{ defaultCollapsed = false, label = "Spirit", data = {
	extra = "{0:output:SpiritUnreserved}/{0:output:Spirit}",
	notFlag = "minionSkill",
	{ label = "Base from Gear", { format = "{0:mod:1}", { modName = "Spirit", modType = "BASE", modSource = "Item" }, }, },
	{ label = "Inc. from Tree", { format = "{0:mod:1}%", { modName = "Spirit", modType = "INC", modSource = "Tree" }, }, },
	{ label = "Total Base", { format = "{0:mod:1}", { modName = "Spirit", modType = "BASE" }, }, },
	{ label = "Total Increased", { format = "{0:mod:1}%", { modName = "Spirit", modType = "INC" }, }, },
	{ label = "Override", haveOutput = "SpiritHasOverride", { format = "{0:mod:1}", { modName = "Spirit", modType = "OVERRIDE" }, }, },
	{ label = "Total", { format = "{0:output:Spirit}", { breakdown = "Spirit" }, }, },
	{ label = "Reserved", { format = "{0:output:SpiritReserved} ({0:output:SpiritReservedPercent}%)", { breakdown = "SpiritReserved" }, }, },
	{ label = "Unreserved", { format = "{0:output:SpiritUnreserved} ({0:output:SpiritUnreservedPercent}%)" }, },
} }
} },
{ 1, "EnergyShield", 2, colorCodes.ES, {{ defaultCollapsed = false, label = "Energy Shield", data = {
	extra = "{0:output:EnergyShield}",
	{ label = "Base from Armours", { format = "{0:output:Gear:EnergyShield}", { breakdown = "EnergyShield", gearOnly = true }, }, },
	{ label = "Global Base", { format = "{0:mod:1}", { modName = { "EnergyShield", "EnergyShieldTotal" }, modType = "BASE" }, }, },
	{ label = "Inc. from Tree", { format = "{0:mod:1}%", { modName = "EnergyShield", modType = "INC", modSource = "Tree" }, }, },
	{ label = "Total Increased", { format = "{0:mod:1}%", { modName = { "EnergyShield", "Defences" }, modType = "INC" }, }, },
	{ label = "Total More", { format = "{0:mod:1}%", { modName = { "EnergyShield", "Defences" }, modType = "MORE" }, }, },
	{ label = "Total", { format = "{0:output:EnergyShield}", { breakdown = "EnergyShield" }, }, },
	{ label = "Total Recoverable", haveOutput = "CappingES", { format = "{0:output:EnergyShieldRecoveryCap}", { breakdown = "EnergyShield" }, }, },
	{ label = "Recharge Rate", haveOutput = "EnergyShieldRechargeAppliesToEnergyShield", { format = "{1:output:EnergyShieldRecharge}", 
		{ breakdown = "EnergyShieldRecharge" },
		{ modName = { "EnergyShieldRecharge", "EnergyShieldRecoveryRate", "NoEnergyShieldRecharge", "CannotGainEnergyShield" }, },
	}, },
	{ label = "Recharge Delay", haveOutput = "EnergyShieldRechargeAppliesToEnergyShield", { format = "{2:output:EnergyShieldRechargeDelay}s", 
		{ breakdown = "EnergyShieldRechargeDelay" },
		{ modName = "EnergyShieldRechargeFaster" },
	}, },
	{ label = "Recovery", { format = "{1:output:EnergyShieldRegenRecovery} ({1:output:EnergyShieldRegenPercent}%)",
		{ breakdown = "EnergyShieldRegenRecovery" }, 
		{ label = "Sources", modName = { "EnergyShieldRegen", "EnergyShieldRegenPercent", "EnergyShieldDegen", "EnergyShieldDegenPercent", "EnergyShieldRecovery" }, modType = "BASE" },
		{ label = "Increased Energy Shield Recovery", modName = { "EnergyShieldRegen", "EnergyShieldRecoveryRate" }, modType = "INC" },
		{ label = "More Energy Shield Recovery", modName = { "EnergyShieldRegen", "EnergyShieldRecoveryRate" }, modType = "MORE" },
	}, },
	{ label = "Recoup", haveOutput = "EnergyShieldRecoup", { format = "{1:output:EnergyShieldRecoup}%", { breakdown = "EnergyShieldRecoup" }, 
		{ label = "Sources", modName = "EnergyShieldRecoup" },
		{ label = "Recovery modifiers", modName = "EnergyShieldRecoveryRate" },
		{ label = "FasterRecoup", modName = "3SecondRecoup" },
	}, },
	{ label = "Ele Recoup", haveOutput = "ElementalEnergyShieldRecoup", { format = "{1:output:ElementalEnergyShieldRecoup}%", { breakdown = "ElementalEnergyShieldRecoup" }, 
		{ label = "Sources", modName = "ElementalEnergyShieldRecoup" },
		{ label = "Recovery modifiers", modName = "EnergyShieldRecoveryRate" },
		{ label = "FasterRecoup", modName = "3SecondRecoup" },
	}, },
	{ label = "Dmg. Mit. Regen", haveOutput = "PhysicalDamageMitigatedEnergyShieldPseudoRecoup", { format = "{1:output:PhysicalDamageMitigatedEnergyShieldPseudoRecoup}%", { breakdown = "PhysicalDamageMitigatedEnergyShieldPseudoRecoup" }, 
		{ label = "Sources", modName = "PhysicalDamageMitigatedEnergyShieldPseudoRecoup" },
		{ label = "Increased Energy Shield Regeneration Rate", modName = { "EnergyShieldRegen" }, modType = "INC" },
		{ label = "More Energy Shield Regeneration Rate", modName = { "EnergyShieldRegen" }, modType = "MORE" },
		{ label = "Recovery modifiers", modName = "EnergyShieldRecoveryRate" },
	}, },
} }
} },
-- secondary defenses
{ 1, "Resist", 3, colorCodes.DEFENCE, {{ defaultCollapsed = false, label = "Resists", data = {
	extra = colorCodes.FIRE.."{0:output:FireResist}+{0:output:FireResistOverCap}^7/"..colorCodes.COLD.."{0:output:ColdResist}+{0:output:ColdResistOverCap}^7/"..colorCodes.LIGHTNING.."{0:output:LightningResist}+{0:output:LightningResistOverCap}",
	{ label = "Fire Resist", { format = "{0:output:FireResist}% (+{0:output:FireResistOverCap}%)",
		{ breakdown = "FireResist" },
		{ modName = { "FireResistMax", "ElementalResistMax", "FireResist", "ElementalResist" }, },
	}, },
	{ label = "Cold Resist", { format = "{0:output:ColdResist}% (+{0:output:ColdResistOverCap}%)",
		{ breakdown = "ColdResist" },
		{ modName = { "ColdResistMax", "ElementalResistMax", "ColdResist", "ElementalResist" }, },
	}, },
	{ label = "Lightning Resist", { format = "{0:output:LightningResist}% (+{0:output:LightningResistOverCap}%)",
		{ breakdown = "LightningResist" },
		{ modName = { "LightningResistMax", "ElementalResistMax", "LightningResist", "ElementalResist" }, },
	}, },
	{ label = "Chaos Resist", { format = "{0:output:ChaosResist}% (+{0:output:ChaosResistOverCap}%)",
		{ breakdown = "ChaosResist" },
		{ modName = { "ChaosResistMax", "ChaosResist" }, },
	}, },
} }
} },
{ 1, "Armour", 3, colorCodes.ARMOUR, {{ defaultCollapsed = false, label = "Armour", data = {
	extra = "{0:output:Armour}",
	{ label = "Base from Armours", { format = "{0:output:Gear:Armour}", { breakdown = "Armour", gearOnly = true }, }, },
	{ label = "Global Base", { format = "{0:mod:1}", { modName = { "Armour", "ArmourAndEvasion", "ArmourTotal" }, modType = "BASE" }, }, },
	{ label = "Inc. from Tree", { format = "{0:mod:1}%", { modName = { "Armour", "ArmourAndEvasion" }, modType = "INC", modSource = "Tree", }, }, },
	{ label = "Total Increased", { format = "{0:mod:1}%", { modName = { "Armour", "ArmourAndEvasion", "Defences" }, modType = "INC" }, }, },
	{ label = "Total More", { format = "{0:mod:1}%", { modName = { "Armour", "ArmourAndEvasion", "Defences" }, modType = "MORE" }, }, },
	{ label = "Total", { format = "{0:output:Armour}", { breakdown = "Armour" }, }, },
	{ label = "Armour Defense", haveOutput = "RawArmourDefense", { format = "{0:output:RawArmourDefense}%", { modName = "ArmourDefense" }, }, },
	{ label = "Phys. Dmg. Reduct", { format = "{0:output:PhysicalDamageReduction}%",
		{ breakdown = "PhysicalDamageReduction" },
		{ modName = { "PhysicalDamageReduction", "PhysicalDamageReductionWhenHit", "ArmourDoesNotApplyToPhysicalDamageTaken", "DamageReductionMax", "PhysicalDamageReductionMax" } },
	}, },
	{ label = "Fire Dmg. Reduct", haveOutput = "FireDamageReduction", { format = "{0:output:FireDamageReduction}%",
		{ breakdown = "FireDamageReduction" },
		{ modName = { "FireDamageReduction", "ElementalDamageReduction", "ArmourAppliesToFireDamageTaken", "SelfIgnoreFireResistance", "DamageReductionMax" } },
	}, },
	{ label = "Cold Dmg. Reduct", haveOutput = "ColdDamageReduction", { format = "{0:output:ColdDamageReduction}%",
		{ breakdown = "ColdDamageReduction" },
		{ modName = { "ColdDamageReduction", "ElementalDamageReduction", "ArmourAppliesToColdDamageTaken", "SelfIgnoreColdResistance", "DamageReductionMax" } },
	}, },
	{ label = "Light. Dmg. Reduct", haveOutput = "LightningDamageReduction", { format = "{0:output:LightningDamageReduction}%",
		{ breakdown = "LightningDamageReduction" },
		{ modName = { "LightningDamageReduction", "ElementalDamageReduction", "ArmourAppliesToLightningDamageTaken", "SelfIgnoreLightningResistance", "DamageReductionMax" } },
	}, },
	{ label = "Chaos Dmg. Reduct", haveOutput = "ChaosDamageReduction", { format = "{0:output:ChaosDamageReduction}%",
		{ breakdown = "ChaosDamageReduction" },
		{ modName = { "ChaosDamageReduction", "ArmourAppliesToChaosDamageTaken", "SelfIgnoreChaosResistance", "DamageReductionMax" } },
	}, },
} }
} },
{ 1, "Evasion", 3, colorCodes.EVASION, {{ defaultCollapsed = false, label = "Evasion", data = {
	extra = "{0:output:Evasion}",
	{ label = "Base from Armours", { format = "{0:output:Gear:Evasion}", { breakdown = "Evasion", gearOnly = true }, }, },
	{ label = "Global Base", { format = "{0:mod:1}", { modName = { "Evasion", "ArmourAndEvasion", "EvasionTotal" }, modType = "BASE" }, }, },
	{ label = "Inc. from Tree", { format = "{0:mod:1}%", { modName = { "Evasion", "ArmourAndEvasion" }, modType = "INC", modSource = "Tree" }, }, },
	{ label = "Total Increased", { format = "{0:mod:1}%", { modName = { "Evasion", "ArmourAndEvasion", "Defences" }, modType = "INC" }, }, },
	{ label = "Total More", { format = "{0:mod:1}%", { modName = { "Evasion", "ArmourAndEvasion", "Defences" }, modType = "MORE" }, }, },
	{ label = "Total", { format = "{0:output:Evasion}", { breakdown = "Evasion" }, }, },
	{ label = "Evade Chance", haveOutput = "noSplitEvade", { format = "{0:output:EvadeChance}%", 
	  { breakdown = "EvadeChance" },
    { label = "Player modifiers", modName = { "CannotEvade", "EvadeChance", "MeleeEvadeChance", "ProjectileEvadeChance" } },
	  { label = "Enemy modifiers", modName = { "Accuracy", "HitChance" }, enemy = true },
	}, },
	 { label = "Melee Evade Ch.", haveOutput = "splitEvade", { format = "{0:output:MeleeEvadeChance}%",
	    { breakdown = "MeleeEvadeChance" },
	    { label = "Player modifiers", modName = { "CannotEvade", "EvadeChance", "MeleeEvadeChance" } },
	    { label = "Enemy modifiers", modName = { "Accuracy", "HitChance" }, enemy = true },
	}, },
	{ label = "Proj. Evade Ch.", haveOutput = "splitEvade", { format = "{0:output:ProjectileEvadeChance}%",
	    { breakdown = "ProjectileEvadeChance" },
	    { label = "Player modifiers", modName = { "CannotEvade", "EvadeChance", "ProjectileEvadeChance" } },
	    { label = "Enemy modifiers", modName = { "Accuracy", "HitChance" }, enemy = true },
	}, },
	{ label = "Effect of Blind", haveOutput = "BlindEffectMod", { format = "{0:output:BlindEffectMod}%", { breakdown = "BlindEffectMod" }, { modName = { "BlindEffect", "BuffEffectOnSelf" }, }, } },
} }
} },
{ 1, "DamageAvoidance", 3, colorCodes.DEFENCE, { { defaultCollapsed = false, label = "Damage Avoidance", data = {
	{ label = "Avoid All Dmg Ch.", haveOutput = "AvoidAllDamageFromHitsChance", { format = "{0:output:AvoidAllDamageFromHitsChance}%", { modName = "AvoidAllDamageFromHitsChance" }, }, },
	{ label = "Avoid Physical Ch.", haveOutput = "AvoidPhysicalDamageChance", { format = "{0:output:AvoidPhysicalDamageChance}%", { modName = "AvoidPhysicalDamageChance" }, }, },
	{ label = "Avoid Lightning Ch.", haveOutput = "AvoidLightningDamageChance", { format = "{0:output:AvoidLightningDamageChance}%", { modName = "AvoidLightningDamageChance" }, }, },
	{ label = "Avoid Cold Chance", haveOutput = "AvoidColdDamageChance", { format = "{0:output:AvoidColdDamageChance}%", { modName = "AvoidColdDamageChance" }, }, },
	{ label = "Avoid Fire Chance", haveOutput = "AvoidFireDamageChance", { format = "{0:output:AvoidFireDamageChance}%", { modName = "AvoidFireDamageChance" }, }, },
	{ label = "Avoid Chaos Chance", haveOutput = "AvoidChaosDamageChance", { format = "{0:output:AvoidChaosDamageChance}%", { modName = "AvoidChaosDamageChance" }, }, },
	{ label = "Avoid Proj Ch.", haveOutput = "AvoidProjectilesChance", { format = "{0:output:AvoidProjectilesChance}%", { modName = "AvoidProjectilesChance" }, }, },
} }, { defaultCollapsed = false, label = "Block", data = {
	extra = "{0:output:EffectiveBlockChance}%",
	{ label = "Block Chance", { format = "{0:output:BlockChance}% (+{0:output:BlockChanceOverCap}%)",
		{ breakdown = "BlockChance" },
		{ modName = { "BlockChance", "ReplaceShieldBlock" } }, 
	}, },
	{ label = "Taken From Block", haveOutput = "ShowBlockEffect", { format = "{0:output:DamageTakenOnBlock}%", 
		{ breakdown = "BlockEffect" }, 
		{ modName = { "BlockEffect" }, },
	}, },
	{ label = "Life on Block", haveOutput = "LifeOnBlock", { format = "{0:output:LifeOnBlock}", { modName = "LifeOnBlock" }, }, },
	{ label = "Mana on Block", haveOutput = "ManaOnBlock", { format = "{0:output:ManaOnBlock}", { modName = "ManaOnBlock" }, }, },
	{ label = "ES on Block", haveOutput = "EnergyShieldOnBlock", { format = "{0:output:EnergyShieldOnBlock}", { modName = "EnergyShieldOnBlock" }, }, },
	{ label = "ES on Spell Block", haveOutput = "EnergyShieldOnSpellBlock", { format = "{0:output:EnergyShieldOnSpellBlock}", { modName = "EnergyShieldOnSpellBlock" }, }, },
} }, { defaultCollapsed = false, label = "Dodge", data = {
	extra = "{0:output:AttackDodgeChance}%/{0:output:SpellDodgeChance}%",
	{ label = "Dodge Chance", { format = "{0:output:AttackDodgeChance}% (+{0:output:AttackDodgeChanceOverCap}%)",
		{ breakdown = "AttackDodgeChance" },
		{ modName = "AttackDodgeChance" },
	}, },
	{ label = "Spell Ddg. Chance", { format = "{0:output:SpellDodgeChance}% (+{0:output:SpellDodgeChanceOverCap}%)",
		{ breakdown = "SpellDodgeChance" },
		{ modName = { "SpellDodgeChanceMax", "SpellDodgeChance" }, },
	}, },
} },
{ defaultCollapsed = false, label = "Spell Suppression", data = {
	extra = "{0:output:SpellSuppressionChance}%",
	{ label = "Suppression Ch.", { format = "{0:output:SpellSuppressionChance}% (+{0:output:SpellSuppressionChanceOverCap}%)", { modName = "SpellSuppressionChance" }, }, },
	{ label = "Suppression Effect", { format = "{0:output:SpellSuppressionEffect}%", { modName = "SpellSuppressionEffect" }, }, },
	{ label = "Life on Suppression", haveOutput = "LifeOnSuppress", { format = "{0:output:LifeOnSuppress}", { modName = "LifeOnSuppress" }, }, },
	{ label = "ES on Suppression", haveOutput = "EnergyShieldOnSuppress", { format = "{0:output:EnergyShieldOnSuppress}", { modName = "EnergyShieldOnSuppress" }, }, },
} },
} },
-- misc resources
{ 1, "Flasks", 3, colorCodes.ENCHANTED, {{ defaultCollapsed = false, label = "Flasks", data = {
	extra = "+{0:output:FlaskEffect}%, {2:output:FlaskChargeGen}/s",
	{ label = "Inc. Effect", { format = "{0:mod:1}%", { modName = "FlaskEffect", modType = "INC", actor = "player"}, }, },
	{ label = "Inc. Duration", { format = "{0:mod:1}%", { modName = "FlaskDuration", modType = "INC" }, }, },
	{ label = "Inc. Charges Gain", { format = "{0:mod:1}%", { modName = "FlaskChargesGained", modType = "INC" }, }, },
	{ label = "Inc. Charges Used", { format = "{0:mod:1}%", { modName = "FlaskChargesUsed", modType = "INC" }, }, },
	{ label = "% to not Consume", { format = "{0:mod:1}%", { modName = "FlaskChanceNotConsumeCharges", modType = "BASE" }, }, },
	{ label = "Charge on crit %", { format = "{0:mod:1}%", { modName = "FlaskChargeOnCritChance", modType = "BASE" }}, },
	{ label = "Charges/s", { format = "{2:mod:1}", { modName = "FlaskChargesGenerated", modType = "BASE" }}, },
} }, { defaultCollapsed = true, label = "Utility Flasks", color = colorCodes.MAGIC, data = {
	extra = "{2:output:UtilityFlaskChargeGen}/s",
	{ label = "Inc. Magic Effect", { format = "{0:mod:1,2}%",
		{ label = "Magic Utility Flask Effect", modName = "MagicUtilityFlaskEffect", modType = "INC", actor = "player" },
		{ label = "Generic Flask Effect", modName = "FlaskEffect", modType = "INC", actor = "player"}}, },
	{ label = "Charges/s", { format = "{2:mod:1,2}",
		{ label = "Utility Flask Charges/s", modName = "UtilityFlaskChargesGenerated", modType = "BASE"},
		{ label = "Generic Flask Charges/s", modName = "FlaskChargesGenerated", modType = "BASE" }}, },
} }, { defaultCollapsed = true, label = "Life Flasks", color = colorCodes.LIFE, data = {
	extra = "{2:output:LifeFlaskChargeGen}/s",
	{ label = "Inc. Recovery", { format = "{0:mod:1}%", { modName = "FlaskLifeRecovery", modType = "INC"}}, },
	{ label = "More Recovery", { format = "{0:mod:1}%", { modName = "FlaskLifeRecovery", modType = "MORE"}}, },
	{ label = "Inc. Recovery Rate", { format = "{0:mod:1,2}%",
		{ label = "Life Flask Recovery Rate", modName = "FlaskLifeRecoveryRate", modType = "INC"},
		{ label = "Generic Flask Recovery Rate", modName = "FlaskRecoveryRate", modType = "INC" }}, },
	{ label = "Inc. Charges Gain", { format = "{0:mod:1}%", { modName = { "FlaskChargesGained", "LifeFlaskChargesGained" }, modType = "INC" }, }, },
	{ label = "Inc. Charges Used", { format = "{0:mod:1}%", { modName = { "FlaskChargesUsed", "LifeFlaskChargesUsed" }, modType = "INC" }, }, },
	{ label = "Charges/s", { format = "{2:mod:1,2}",
		{ label = "Life Flask Charges/s", modName = "LifeFlaskChargesGenerated", modType = "BASE"},
		{ label = "Generic Flask Charges/s", modName = "FlaskChargesGenerated", modType = "BASE" }}, },
} }, { defaultCollapsed = true, label = "Mana Flasks", color = colorCodes.MANA, data = {
	extra = "{2:output:ManaFlaskChargeGen}/s",
	{ label = "Inc. Recovery", { format = "{0:mod:1}%", { modName = "FlaskManaRecovery", modType = "INC"}}, },
	{ label = "More Recovery", { format = "{0:mod:1}%", { modName = "FlaskManaRecovery", modType = "MORE"}}, },
	{ label = "Inc. Recovery Rate", { format = "{0:mod:1,2}%",
		{ label = "Mana Flask Recovery Rate", modName = "FlaskManaRecoveryRate", modType = "INC"},
		{ label = "Generic Flask Recovery Rate", modName = "FlaskRecoveryRate", modType = "INC" }}, },
	{ label = "Inc. Charges Gain", { format = "{0:mod:1}%", { modName = { "FlaskChargesGained", "ManaFlaskChargesGained" }, modType = "INC" }, }, },
	{ label = "Inc. Charges Used", { format = "{0:mod:1}%", { modName = { "FlaskChargesUsed", "ManaFlaskChargesUsed" }, modType = "INC" }, }, },
	{ label = "Charges/s", { format = "{2:mod:1,2}",
		{ label = "Mana Flask Charges/s", modName = "ManaFlaskChargesGenerated", modType = "BASE"},
		{ label = "Generic Flask Charges/s", modName = "FlaskChargesGenerated", modType = "BASE" }}, },
} }, { defaultCollapsed = true, label = "Charms", data = {
	extra = "+{0:output:CharmEffect}%, {0:output:CharmLimit}",
	{ label = "Charm Limit", { format = "{0:mod:1}", { modName = "CharmLimit", modType = "BASE"}, }, },
	{ label = "Inc. Effect", { format = "{0:mod:1}%", { modName = "CharmEffect", modType = "INC", actor = "player"}, }, },
	{ label = "Inc. Duration", { format = "{0:mod:1}%", { modName = "CharmDuration", modType = "INC" }, }, },
	{ label = "Inc. Charges Gain", { format = "{0:mod:1}%", { modName = "CharmChargesGained", modType = "INC" }, }, },
	{ label = "Inc. Charges Used", { format = "{0:mod:1}%", { modName = "CharmChargesUsed", modType = "INC" }, }, },
	{ label = "Charges/s", { format = "{2:mod:1}", { modName = "CharmChargesGenerated", modType = "BASE" }, }, },
} },
} },
{ 1, "Rage", 3, colorCodes.RAGE, {{ defaultCollapsed = true, label = "Rage", data = {
	extra = "{0:output:Rage} ({1:output:RageEffect})",
	{ label = "Total", { format = "{0:output:Rage}", }, },
	{ label = "Rage Effect", { format = "{1:output:RageEffect}", { modName = "RageEffect" }, }, },
	{ label = "Maximum Rage", { format = "{0:output:MaximumRage}", { modName = "MaximumRage" }, }, },
	{ label = "Recovery", { format = "{1:output:RageRegenRecovery} ({1:output:RageRegenPercent}%)",
		{ breakdown = "RageRegenRecovery" },
		{ label = "Sources", modName = { "RageRegen", "RageRegenPercent", "RageDegen", "RageDegenPercent", "RageRecovery" }, modType = "BASE" },
		{ label = "Increased Rage Regeneration Rate", modName = { "RageRegen" }, modType = "INC" },
		{ label = "More Rage Regeneration Rate", modName = { "RageRegen" }, modType = "MORE" },
		{ label = "Recovery modifiers", modName = "RageRecoveryRate" },
	}, },
	{ label = "Rage Loss Delay", { format = "{1:output:InherentRageLossDelay}s", { modName = "InherentRageLossDelay" }, }, },
	{ label = "Inherent Rage Loss", { format = "{1:output:InherentRageLoss} /s", { modName = "InherentRageLoss" }, { modName = { "InherentRageLossIsPrevented" } }, }, },
} }
} },
{ 1, "Charges", 3, colorCodes.NORMAL, {{ defaultCollapsed = true, label = "Charges", data = {
	extra = colorCodes.RAGE.."{0:output:EnduranceCharges}^7, "..colorCodes.EVASION.."{0:output:FrenzyCharges}^7, "..colorCodes.MANA.."{0:output:PowerCharges}",} },
	{ defaultCollapsed = true, label = "Endurance", haveOutput="UseEnduranceCharges", data = {
	extra = colorCodes.RAGE.."{0:output:EnduranceCharges} ^7/ "..colorCodes.RAGE.."{0:output:EnduranceChargesMax}",
		{ label = "Max", { format = "{0:output:EnduranceChargesMax}", { modName = { "EnduranceChargesMax", "PartyMemberMaximumEnduranceChargesEqualToYours" } } }, },
		{ label = "Current", { format = "{0:output:EnduranceCharges}", { modName = { "EnduranceChargesMin", "HaveMaximumEnduranceCharges" } } }, },
		{ label = "Spendable", { format = "{0:output:RemovableEnduranceCharges}", }, },
		{ label = "Duration", { format = "{0:output:EnduranceChargesDuration}s", { modName = { "EnduranceChargesDuration", "ChargeDuration" } } }, },
	} },
	{ defaultCollapsed = true, label = "Frenzy", haveOutput="UseFrenzyCharges", data = {
	extra = colorCodes.EVASION.."{0:output:FrenzyCharges} ^7/ "..colorCodes.EVASION.."{0:output:FrenzyChargesMax}",
		{ label = "Max", { format = "{0:output:FrenzyChargesMax}", { modName = { "FrenzyChargesMax" } } }, },
		{ label = "Current", { format = "{0:output:FrenzyCharges}", { modName = { "FrenzyChargesMin", "HaveMaximumFrenzyCharges" } } }, },
		{ label = "Spendable", { format = "{0:output:RemovableFrenzyCharges}", }, },
		{ label = "Duration", { format = "{0:output:FrenzyChargesDuration}s", { modName = { "FrenzyChargesDuration", "ChargeDuration" } } }, },
	} },
	{ defaultCollapsed = true, label = "Power", haveOutput="UsePowerCharges", data = {
	extra = colorCodes.MANA.."{0:output:PowerCharges} ^7/ "..colorCodes.MANA.."{0:output:PowerChargesMax}",
		{ label = "Max", { format = "{0:output:PowerChargesMax}", { modName = { "PowerChargesMax" } } }, },
		{ label = "Current", { format = "{0:output:PowerCharges}", { modName = { "PowerChargesMin", "HaveMaximumPowerCharges" } } }, },
		{ label = "Spendable", { format = "{0:output:RemovablePowerCharges}", }, },
		{ label = "Duration", { format = "{0:output:PowerChargesDuration}s", { modName = { "PowerChargesDuration", "ChargeDuration" } } }, },
	} },
} },
-- misc defense
{ 1, "MiscDefences", 3, colorCodes.DEFENCE, {{ defaultCollapsed = false, label = "Other Defences", data = {
	{ label = "Movement Speed", { format = "x {3:output:EffectiveMovementSpeedMod}", { breakdown = "EffectiveMovementSpeedMod" }, { modName = { "MovementSpeed", "MovementSpeedEqualHighestLinkedPlayers" } }, }, },
	{ label = "Effect of Elusive", haveOutput = "ElusiveEffectMod", { format = "{0:output:ElusiveEffectMod}%", { breakdown = "ElusiveEffectMod" }, { modName = { "ElusiveEffect", "BuffEffectOnSelf", "NightbladeSupportedElusiveEffect" }, }, } },
	{ label = "Light Radius Mod", { format = "x {2:output:LightRadiusMod}", { breakdown = "LightRadiusMod" }, { modName = "LightRadius" }, }, },
	{ label = "Curse Effect on You", { format = "{1:output:CurseEffectOnSelf}%", { modName = "CurseEffectOnSelf" }, }, },
	{ label = "Exposure Effect", { format = "{1:output:ExposureEffectOnSelf}%", { modName = "ExposureEffectOnSelf" }, }, },
	{ label = "Wither Effect", { format = "{1:output:WitherEffectOnSelf}%", { modName = "WitherEffectOnSelf" }, }, },
	{ label = "Debuff Dur. Mult.", haveOutput = "showDebuffExpirationModifier", { format = "{1:output:DebuffExpirationModifier}%", { modName = "SelfDebuffExpirationRate" }, }, },
	{ label = "Weap. Swap Speed", { format = "{0:output:WeaponSwapSpeed} ms", { breakdown = "WeaponSwapSpeedMod" }, { modName = { "WeaponSwapSpeed" } }, }, },
} }, { defaultCollapsed = false, label = "Stun Duration", data = {
	{ label = "Stun Avoid Chance", haveOutput = "StunAvoidChance", { format = "{0:output:StunAvoidChance}%",  { breakdown = "StunAvoidChance" }, { modName = "AvoidStun" }, }, },
	{ label = "Stun Threshold", { format = "{0:output:StunThreshold}", { breakdown = "StunThreshold" }, { modName = { "StunThreshold", "StunThresholdManaPercent", "StunThresholdEnergyShieldPercent" } }, }, },
	{ label = "Stun Chance", { format = "{0:output:SelfStunChance}%", { breakdown = "SelfStunChance" }, }, },
	{ label = "Interrupt Avoid Ch.", haveOutput = "InterruptStunAvoidChance", { format = "{0:output:InterruptStunAvoidChance}%", { modName = "AvoidInterruptStun" }, }, },
	{ label = "Stun Duration", { format = "{2:output:StunDuration}s", 
		{ breakdown = "StunDuration" },
		{ modName = { "StunDuration", "StunRecovery" } },
	}, },
	{ label = "Block Stun Duration", { format = "{2:output:BlockDuration}s", 
		{ breakdown = "BlockDuration" },
		{ modName = { "StunDuration", "StunRecovery", "BlockRecovery" }, }, 
	}, },
} }, { defaultCollapsed = true, label = "Other Avoidance", data = {
	{ label = "Blind Avoid Ch.", haveOutput = "BlindAvoidChance", { format = "{0:output:BlindAvoidChance}%", { modName = { "AvoidBlind", "BlindImmune" } }, }, },
	{ label = "Shock Avoid Ch.", haveOutput = "ShockAvoidChance", { format = "{0:output:ShockAvoidChance}%", { modName = { "AvoidShock", "AvoidElementalAilments", "AvoidAilments", "ShockImmune", "ElementalAilmentImmune" } }, }, },
	{ label = "Freeze Avoid Ch.", haveOutput = "FreezeAvoidChance", { format = "{0:output:FreezeAvoidChance}%", { modName = { "AvoidFreeze", "AvoidElementalAilments", "AvoidAilments", "AvoidShockAppliesToElementalAilments", "FreezeImmune", "ElementalAilmentImmune" } }, }, },
	{ label = "Chill Avoid Ch.", haveOutput = "ChillAvoidChance", { format = "{0:output:ChillAvoidChance}%", { modName = { "AvoidChill", "AvoidElementalAilments", "AvoidAilments", "AvoidShockAppliesToElementalAilments", "ChillImmune", "ElementalAilmentImmune" } }, }, },
	{ label = "Ignite Avoid Ch.", haveOutput = "IgniteAvoidChance", { format = "{0:output:IgniteAvoidChance}%", { modName = { "AvoidIgnite", "AvoidElementalAilments", "AvoidAilments", "AvoidShockAppliesToElementalAilments", "IgniteImmune", "ElementalAilmentImmune" } }, }, },
	{ label = "Sap Avoid Ch.", haveOutput = "SapAvoidChance", { format = "{0:output:SapAvoidChance}%", { modName = { "AvoidSap", "AvoidElementalAilments", "AvoidAilments", "AvoidShockAppliesToElementalAilments", "SapImmune", "ElementalAilmentImmune" } }, }, },
	{ label = "Brittle Avoid Ch.", haveOutput = "BrittleAvoidChance", { format = "{0:output:BrittleAvoidChance}%", { modName = { "AvoidBrittle", "AvoidElementalAilments", "AvoidAilments", "AvoidShockAppliesToElementalAilments", "BrittleImmune", "ElementalAilmentImmune" } }, }, },
	{ label = "Scorch Avoid Ch.", haveOutput = "ScorchAvoidChance", { format = "{0:output:ScorchAvoidChance}%", { modName = { "AvoidScorch", "AvoidElementalAilments", "AvoidAilments", "AvoidShockAppliesToElementalAilments", "ScorchImmune", "ElementalAilmentImmune" } }, }, },
	{ label = "Bleed Avoid Ch.", haveOutput = "BleedAvoidChance", { format = "{0:output:BleedAvoidChance}%", { modName = { "AvoidBleed", "AvoidAilments", "BleedImmune" } }, }, },
	{ label = "Poison Avoid Ch.", haveOutput = "PoisonAvoidChance", { format = "{0:output:PoisonAvoidChance}%", { modName = { "AvoidPoison", "AvoidAilments", "PoisonImmune" } }, }, },
	{ label = "Impale Avoid Ch.", haveOutput = "ImpaleAvoidChance", { format = "{0:output:ImpaleAvoidChance}%", { modName = { "AvoidImpale", "ImpaleImmune" } }, }, },
	{ label = "Curse Avoid Ch.", haveOutput = "CurseAvoidChance", { format = "{0:output:CurseAvoidChance}%", { modName = { "AvoidCurse", "CurseImmune" } }, }, },
	{ label = "Silence Avoid Ch.", haveOutput = "SilenceAvoidChance", { format = "{0:output:SilenceAvoidChance}%", { modName = { "SilenceImmune", "AvoidCurse", "CurseImmune" } }, }, },
	{ label = "Crit Reduction", haveOutput = "CritExtraDamageReduction", { format = "{0:output:CritExtraDamageReduction}%", { modName = "ReduceCritExtraDamage" }, }, },
	{ label = "Corr Blood Immune", haveOutput = "CorruptedBloodImmunity", { format = "True", { modName = "CorruptedBloodImmune" }, }, },
	{ label = "Maim Immune", haveOutput = "MaimImmunity", { format = "True", { modName = "MaimImmune" }, }, },
	{ label = "Hinder Immune", haveOutput = "HinderImmunity", { format = "True", { modName = "HinderImmune" }, }, },
	{ label = "Knockback Immune", haveOutput = "KnockbackImmunity", { format = "True", { modName = "KnockbackImmune" }, }, },
	{ label = "Blind Duration", haveOutput = "SelfBlindDuration", { format = "{0:output:SelfBlindDuration}%", { modName = "SelfBlindDuration" }, }, },
} }, { defaultCollapsed = true, label = "Other Ailment Defences", data = {
	{ label = "Ailment Threshold", { format = "{2:output:AilmentThreshold}", { modName = { "AilmentThreshold" }, }, }, },
	{ label = "Freeze Duration", { format = "{1:output:SelfFreezeDuration}%", { modName = { "SelfFreezeDuration", "SelfDebuffExpirationRate", "SelfFreezeDebuffExpirationRate", "SelfAilmentDuration", "SelfElementalAilmentDuration", "SelfIgniteDurationToElementalAilments" }, }, }, },
	{ label = "Chill Duration", { format = "{1:output:SelfChillDuration}%", { modName = { "SelfChillDuration", "SelfDebuffExpirationRate", "SelfChillDebuffExpirationRate", "SelfAilmentDuration", "SelfElementalAilmentDuration", "SelfIgniteDurationToElementalAilments" }, }, }, },
	{ label = "Shock Duration", { format = "{1:output:SelfShockDuration}%", { modName = { "SelfShockDuration", "SelfDebuffExpirationRate", "SelfShockDebuffExpirationRate", "SelfAilmentDuration", "SelfElementalAilmentDuration", "SelfIgniteDurationToElementalAilments" }, }, }, },
	{ label = "Ignite Duration", { format = "{1:output:SelfIgniteDuration}%", { modName = { "SelfIgniteDuration", "SelfDebuffExpirationRate", "SelfIgniteDebuffExpirationRate", "SelfAilmentDuration", "SelfElementalAilmentDuration" }, }, }, },
	{ label = "Sap Duration", { format = "{1:output:SelfSapDuration}%", { modName = { "SelfSapDuration", "SelfDebuffExpirationRate", "SelfSapDebuffExpirationRate", "SelfAilmentDuration", "SelfElementalAilmentDuration", "SelfIgniteDurationToElementalAilments" }, }, }, },
	{ label = "Brittle Duration", { format = "{1:output:SelfBrittleDuration}%", { modName = { "SelfBrittleDuration", "SelfDebuffExpirationRate", "SelfBrittleDebuffExpirationRate", "SelfAilmentDuration", "SelfElementalAilmentDuration", "SelfIgniteDurationToElementalAilments" }, }, }, },
	{ label = "Scorch Duration", { format = "{1:output:SelfScorchDuration}%", { modName = { "SelfScorchDuration", "SelfDebuffExpirationRate", "SelfScorchDebuffExpirationRate", "SelfAilmentDuration", "SelfElementalAilmentDuration", "SelfIgniteDurationToElementalAilments" }, }, }, },
	{ label = "Bleed Duration", { format = "{1:output:SelfBleedDuration}%", { modName = { "SelfBleedDuration", "SelfDebuffExpirationRate", "SelfBleedDebuffExpirationRate", "SelfAilmentDuration" }, }, }, },
	{ label = "Poison Duration", { format = "{1:output:SelfPoisonDuration}%", { modName = { "SelfPoisonDuration", "SelfDebuffExpirationRate", "SelfPoisonDebuffExpirationRate", "SelfAilmentDuration" }, }, }, },
	{ label = "Freeze Effect", { format = "{1:output:SelfFreezeEffect}%", { modName = "SelfFreezeEffect" }, }, },
	{ label = "Chill Effect", { format = "{1:output:SelfChillEffect}%", { modName = "SelfChillEffect" }, }, },
	{ label = "Shock Effect", { format = "{1:output:SelfShockEffect}%", { modName = "SelfShockEffect" }, }, },
	{ label = "Ignite Effect", { format = "{1:output:SelfIgniteEffect}%", { modName = "SelfIgniteEffect" }, }, },
	{ label = "Sap Effect", { format = "{1:output:SelfSapEffect}%", { modName = "SelfSapEffect" }, }, },
	{ label = "Brittle Effect", { format = "{1:output:SelfBrittleEffect}%", { modName = "SelfBrittleEffect" }, }, },
	{ label = "Scorch Effect", { format = "{1:output:SelfScorchEffect}%", { modName = "SelfScorchEffect" }, }, },
	{ label = "Bleed Effect", { format = "{1:output:SelfBleedEffect}%", { modName = "SelfBleedEffect" }, }, },
	{ label = "Poison Effect", { format = "{1:output:SelfPoisonEffect}%", { modName = "SelfPoisonEffect" }, }, },
} },
} },
-- damage taken
{ 3, "DamageTaken", 1, colorCodes.DEFENCE, {{ defaultCollapsed = false, label = "Damage Taken", data = {
	colWidth = 95,
	{
		{ format = "Total:" },
		{ format = "Physical:" },
		{ format = colorCodes.LIGHTNING.."Lightning:" },
		{ format = colorCodes.COLD.."Cold:" },
		{ format = colorCodes.FIRE.."Fire:" },
		{ format = colorCodes.CHAOS.."Chaos:" },
	},
	{ label = "Enemy Damage",
		{ format = "{2:output:totalEnemyDamage}", 
			{ breakdown = "totalEnemyDamage" }, 
			 { label = "Enemy modifiers", modName = {"Damage", "CritChance", "CritMultiplier"}, enemy = true },
			 { label = "Enemy Conversion modifiers", modName = {
				"PhysicalDamageGainAsLightning", "PhysicalDamageSkillConvertToLightning", "PhysicalDamageConvertToLightning",
				"PhysicalDamageGainAsCold", "PhysicalDamageSkillConvertToCold", "PhysicalDamageConvertToCold",
				"PhysicalDamageGainAsFire", "PhysicalDamageSkillConvertToFire", "PhysicalDamageConvertToFire",
				"PhysicalDamageGainAsChaos", "PhysicalDamageSkillConvertToChaos", "PhysicalDamageConvertToChaos"
			}, enemy = true },
		},
		{ format = "{2:output:PhysicalEnemyDamage}", 
			{ breakdown = "PhysicalEnemyDamage" }, 
			 { label = "Enemy modifiers", modName = {"Damage", "PhysicalDamage", "CritChance", "CritMultiplier"}, enemy = true },
			 { label = "Enemy Conversion modifiers", modName = {
				"PhysicalDamageGainAsLightning", "PhysicalDamageSkillConvertToLightning", "PhysicalDamageConvertToLightning",
				"PhysicalDamageGainAsCold", "PhysicalDamageSkillConvertToCold", "PhysicalDamageConvertToCold",
				"PhysicalDamageGainAsFire", "PhysicalDamageSkillConvertToFire", "PhysicalDamageConvertToFire",
				"PhysicalDamageGainAsChaos", "PhysicalDamageSkillConvertToChaos", "PhysicalDamageConvertToChaos"
			}, enemy = true },
		},
		{ format = "{2:output:LightningEnemyDamage}",
			{ breakdown = "LightningEnemyDamage" }, 
			 { label = "Enemy modifiers", modName = {"Damage", "LightningDamage", "ElementalDamage", "CritChance", "CritMultiplier"}, enemy = true },
			 { label = "Enemy Conversion modifiers", modName = {"PhysicalDamageGainAsLightning", "PhysicalDamageSkillConvertToLightning", "PhysicalDamageConvertToLightning"}, enemy = true },
		},
		{ format = "{2:output:ColdEnemyDamage}",
			{ breakdown = "ColdEnemyDamage" }, 
			 { label = "Enemy modifiers", modName = {"Damage", "ColdDamage", "ElementalDamage", "CritChance", "CritMultiplier"}, enemy = true },
			 { label = "Enemy Conversion modifiers", modName = {"PhysicalDamageGainAsCold", "PhysicalDamageSkillConvertToCold", "PhysicalDamageConvertToCold"}, enemy = true },
		},
		{ format = "{2:output:FireEnemyDamage}", 
			{ breakdown = "FireEnemyDamage" },  
			 { label = "Enemy modifiers", modName = {"Damage", "FireDamage", "ElementalDamage", "CritChance", "CritMultiplier"}, enemy = true },
			 { label = "Enemy Conversion modifiers", modName = {"PhysicalDamageGainAsFire", "PhysicalDamageSkillConvertToFire", "PhysicalDamageConvertToFire"}, enemy = true },
		},
		{ format = "{2:output:ChaosEnemyDamage}",
			{ breakdown = "ChaosEnemyDamage" }, 
			 { label = "Enemy modifiers", modName = {"Damage", "ChaosDamage", "CritChance", "CritMultiplier"}, enemy = true },
			 { label = "Enemy Conversion modifiers", modName = {"PhysicalDamageGainAsChaos", "PhysicalDamageSkillConvertToChaos", "PhysicalDamageConvertToChaos"}, enemy = true },
		},
	},
	{ label = "Taken As",
		{ format = "{2:output:totalTakenDamage}", 
			{ breakdown = "totalTakenDamage" }, 
		},
		{ format = "{2:output:PhysicalTakenDamage}", 
			{ breakdown = "PhysicalTakenDamage" }, 
			{ label = "Physical Taken as", modName = {
				"PhysicalDamageTakenAsLightning", "PhysicalDamageFromHitsTakenAsLightning",
				"PhysicalDamageTakenAsCold", "PhysicalDamageFromHitsTakenAsCold",
				"PhysicalDamageTakenAsFire", "PhysicalDamageFromHitsTakenAsFire",
				"PhysicalDamageTakenAsChaos", "PhysicalDamageFromHitsTakenAsChaos"
			} },
		  	{ label = "Taken as Physical", modName = {
				"LightningDamageTakenAsPhysical", "LightningDamageFromHitsTakenAsPhysical",
				"ColdDamageTakenAsPhysical", "ColdDamageFromHitsTakenAsPhysical",
				"FireDamageTakenAsPhysical", "FireDamageFromHitsTakenAsPhysical",
				"ElementalDamageTakenAsPhysical", "ElementalDamageFromHitsTakenAsPhysical",
				"ChaosDamageTakenAsPhysical", "ChaosDamageFromHitsTakenAsPhysical"
			} },
		},
		{ format = "{2:output:LightningTakenDamage}",
			{ breakdown = "LightningTakenDamage" }, 
			{ label = "Lightning Taken as", modName = {
				"LightningDamageTakenAsPhysical", "LightningDamageFromHitsTakenAsPhysical",
				"LightningDamageTakenAsCold", "LightningDamageFromHitsTakenAsCold",
				"LightningDamageTakenAsFire", "LightningDamageFromHitsTakenAsFire",
				"ElementalDamageTakenAsPhysical", "ElementalDamageFromHitsTakenAsPhysical", "ElementalDamageTakenAsChaos", "ElementalDamageFromHitsTakenAsChaos",
				"LightningDamageTakenAsChaos", "LightningDamageFromHitsTakenAsChaos"
			} },
		  	{ label = "Taken as Lightning", modName = {
				"PhysicalDamageTakenAsLightning", "PhysicalDamageFromHitsTakenAsLightning",
				"ColdDamageTakenAsLightning", "ColdDamageFromHitsTakenAsLightning",
				"FireDamageTakenAsLightning", "FireDamageFromHitsTakenAsLightning",
				"ChaosDamageTakenAsLightning", "ChaosDamageFromHitsTakenAsLightning"
			} },
		},
		{ format = "{2:output:ColdTakenDamage}",
			{ breakdown = "ColdTakenDamage" }, 
			{ label = "Cold Taken as", modName = {
				"ColdDamageTakenAsPhysical", "ColdDamageFromHitsTakenAsPhysical",
				"ColdDamageTakenAsLightning", "ColdDamageFromHitsTakenAsLightning",
				"ColdDamageTakenAsFire", "ColdDamageFromHitsTakenAsFire",
				"ElementalDamageTakenAsPhysical", "ElementalDamageFromHitsTakenAsPhysical", "ElementalDamageTakenAsChaos", "ElementalDamageFromHitsTakenAsChaos",
				"ColdDamageTakenAsChaos", "ColdDamageFromHitsTakenAsChaos"
			} },
		  	{ label = "Taken as Cold", modName = {
				"PhysicalDamageTakenAsCold", "PhysicalDamageFromHitsTakenAsCold",
				"LightningDamageTakenAsCold", "LightningDamageFromHitsTakenAsCold",
				"FireDamageTakenAsCold", "FireDamageFromHitsTakenAsCold",
				"ChaosDamageTakenAsCold", "ChaosDamageFromHitsTakenAsCold"
			} },
		},
		{ format = "{2:output:FireTakenDamage}", 
			{ breakdown = "FireTakenDamage" }, 
			{ label = "Fire Taken as", modName = {
				"FireDamageTakenAsPhysical", "FireDamageFromHitsTakenAsPhysical",
				"FireDamageTakenAsLightning", "FireDamageFromHitsTakenAsLightning",
				"FireDamageTakenAsCold", "FireDamageFromHitsTakenAsCold",
				"ElementalDamageTakenAsPhysical", "ElementalDamageFromHitsTakenAsPhysical", "ElementalDamageTakenAsChaos", "ElementalDamageFromHitsTakenAsChaos",
				"FireDamageTakenAsChaos", "FireDamageFromHitsTakenAsChaos"
			} },
		  	{ label = "Taken as Fire", modName = {
				"PhysicalDamageTakenAsFire", "PhysicalDamageFromHitsTakenAsFire",
				"LightningDamageTakenAsFire", "LightningDamageFromHitsTakenAsFire",
				"ColdDamageTakenAsFire", "ColdDamageFromHitsTakenAsFire",
				"ChaosDamageTakenAsFire", "ChaosDamageFromHitsTakenAsFire"
			} },
		},
		{ format = "{2:output:ChaosTakenDamage}",
			{ breakdown = "ChaosTakenDamage" }, 
			{ label = "Chaos Taken as", modName = {
				"ChaosDamageTakenAsPhysical", "ChaosDamageFromHitsTakenAsPhysical",
				"ChaosDamageTakenAsLightning", "ChaosDamageFromHitsTakenAsLightning",
				"ChaosDamageTakenAsCold", "ChaosDamageFromHitsTakenAsCold",
				"ChaosDamageTakenAsFire", "ChaosDamageFromHitsTakenAsFire"
			} },
		  	{ label = "Taken as Chaos", modName = {
				"PhysicalDamageTakenAsChaos", "PhysicalDamageFromHitsTakenAsChaos",
				"LightningDamageTakenAsChaos", "LightningDamageFromHitsTakenAsChaos",
				"ColdDamageTakenAsChaos", "ColdDamageFromHitsTakenAsChaos",
				"FireDamageTakenAsChaos", "FireDamageFromHitsTakenAsChaos",
				"ElementalDamageTakenAsChaos", "ElementalDamageFromHitsTakenAsChaos"
			} },
		},
	},
} }, { defaultCollapsed = false, label = "Damaging Hits", data = {
	colWidth = 95,
	{ label = "Hit taken Mult.",
		{ format = "" },
		{ format = "x {3:output:PhysicalTakenHitMult}", 
			{ breakdown = "PhysicalTakenHitMult" }, 
			{ modName = { "DamageTaken", "DamageTakenWhenHit", "AttackDamageTaken", "SpellDamageTaken", "PhysicalDamageTaken", "PhysicalDamageTakenWhenHit", "PhysicalDamageTakenAsFire", "PhysicalDamageTakenAsCold", "PhysicalDamageTakenAsLightning", "PhysicalDamageTakenAsChaos" } } 
		},
		{ format = "x {3:output:LightningTakenHitMult}",
			{ breakdown = "LightningTakenHitMult" }, 
			{ modName = { "DamageTaken", "DamageTakenWhenHit", "AttackDamageTaken", "SpellDamageTaken", "LightningDamageTaken", "LightningDamageTakenWhenHit", "ElementalDamageTaken", "ElementalDamageTakenWhenHit", "LightningDamageTakenAsPhysical", "LightningDamageTakenAsFire", "LightningDamageTakenAsCold", "LightningDamageTakenAsChaos", "ElementalDamageTakenAsPhysical", "ElementalDamageTakenAsChaos", "SelfIgnoreLightningResistance" } }
		},
		{ format = "x {3:output:ColdTakenHitMult}",
			{ breakdown = "ColdTakenHitMult" }, 
			{ modName = { "DamageTaken", "DamageTakenWhenHit", "AttackDamageTaken", "SpellDamageTaken", "ColdDamageTaken", "ColdDamageTakenWhenHit", "ElementalDamageTaken", "ElementalDamageTakenWhenHit", "ColdDamageTakenAsPhysical", "ColdDamageTakenAsFire", "ColdDamageTakenAsLightning", "ColdDamageTakenAsChaos", "ElementalDamageTakenAsPhysical", "ElementalDamageTakenAsChaos", "SelfIgnoreColdResistance" } }
		},
		{ format = "x {3:output:FireTakenHitMult}", 
			{ breakdown = "FireTakenHitMult" }, 
			{ modName = { "DamageTaken", "DamageTakenWhenHit", "AttackDamageTaken", "SpellDamageTaken", "FireDamageTaken", "FireDamageTakenWhenHit", "ElementalDamageTaken", "ElementalDamageTakenWhenHit", "FireDamageTakenAsPhysical", "FireDamageTakenAsCold", "FireDamageTakenAsLightning", "FireDamageTakenAsChaos", "ElementalDamageTakenAsPhysical", "ElementalDamageTakenAsChaos", "SelfIgnoreFireResistance" }  } 
		},
		{ format = "x {3:output:ChaosTakenHitMult}",
			{ breakdown = "ChaosTakenHitMult" }, 
			{ modName = { "DamageTaken", "DamageTakenWhenHit", "AttackDamageTaken", "SpellDamageTaken", "ChaosDamageTaken", "ChaosDamageTakenWhenHit", "ChaosDamageTakenAsPhysical", "ChaosDamageTakenAsFire", "ChaosDamageTakenAsCold", "ChaosDamageTakenAsLightning", "SelfIgnoreChaosResistance" } }
		},
	},
	{ label = "Reflect taken", haveOutput = "AnyTakenReflect",
		{ format = "" },
		{ format = "x {2:output:PhysicalTakenReflectMult}", 
			{ breakdown = "PhysicalTakenReflectMult" }, 
			{ modName = { "DamageTaken", "DamageTakenWhenHit", "PhysicalDamageTaken", "PhysicalDamageTakenWhenHit", "PhysicalReflectedDamageTaken", "PhysicalDamageTakenAsFire", "PhysicalDamageTakenAsCold", "PhysicalDamageTakenAsLightning", "PhysicalDamageTakenAsChaos" } } 
		},
		{ format = "x {2:output:LightningTakenReflectMult}",
			{ breakdown = "LightningTakenReflectMult" }, 
			{ modName = { "DamageTaken", "DamageTakenWhenHit", "LightningDamageTaken", "LightningDamageTakenWhenHit", "LightningReflectedDamageTaken", "ElementalDamageTaken", "ElementalDamageTakenWhenHit", "ElementalReflectedDamageTaken", "LightningDamageTakenAsPhysical", "LightningDamageTakenAsFire", "LightningDamageTakenAsCold", "LightningDamageTakenAsChaos", "ElementalDamageTakenAsPhysical", "ElementalDamageTakenAsChaos" } }
		},
		{ format = "x {2:output:ColdTakenReflectMult}",
			{ breakdown = "ColdTakenReflectMult" }, 
			{ modName = { "DamageTaken", "DamageTakenWhenHit", "ColdDamageTaken", "ColdDamageTakenWhenHit", "ColdReflectedDamageTaken", "ElementalDamageTaken", "ElementalDamageTakenWhenHit", "ElementalReflectedDamageTaken", "ColdDamageTakenAsPhysical", "ColdDamageTakenAsFire", "ColdDamageTakenAsLightning", "ColdDamageTakenAsChaos", "ElementalDamageTakenAsPhysical", "ElementalDamageTakenAsChaos" } }
		},
		{ format = "x {2:output:FireTakenReflectMult}", 
			{ breakdown = "FireTakenReflectMult" }, 
			{ modName = { "DamageTaken", "DamageTakenWhenHit", "FireDamageTaken", "FireDamageTakenWhenHit", "FireReflectedDamageTaken", "ElementalDamageTaken", "ElementalDamageTakenWhenHit", "ElementalReflectedDamageTaken", "ElementalDamageTakenOverTime", "FireDamageTakenAsPhysical", "FireDamageTakenAsCold", "FireDamageTakenAsLightning", "FireDamageTakenAsChaos", "ElementalDamageTakenAsPhysical", "ElementalDamageTakenAsChaos" }  } 
		},
		{ format = "x {2:output:ChaosTakenReflectMult}",
			{ breakdown = "ChaosTakenReflectMult" }, 
			{ modName = { "DamageTaken", "DamageTakenWhenHit", "ChaosDamageTaken", "ChaosDamageTakenWhenHit", "ChaosDamageTakenAsPhysical", "ChaosDamageTakenAsFire", "ChaosDamageTakenAsCold", "ChaosDamageTakenAsLightning" } }
		},
	},
	{ label = "Hit taken",
		{ format = "{2:output:totalTakenHit}", 
			{ breakdown = "totalTakenHit" }, 
		},
		{ format = "{2:output:PhysicalTakenHit}", 
			{ breakdown = "PhysicalTakenHit" }, 
		},
		{ format = "{2:output:LightningTakenHit}",
			{ breakdown = "LightningTakenHit" }, 
		},
		{ format = "{2:output:ColdTakenHit}",
			{ breakdown = "ColdTakenHit" }, 
		},
		{ format = "{2:output:FireTakenHit}", 
			{ breakdown = "FireTakenHit" }, 
		},
		{ format = "{2:output:ChaosTakenHit}",
			{ breakdown = "ChaosTakenHit" }, 
		},
	},
	{ label = "PvP Hit taken", haveOutput = "PvPTotalTakenHit",
		{ format = "{2:output:PvPTotalTakenHit}", 
			{ breakdown = "PvPTotalTakenHit" }, 
			{ label = "Enemy PvP Multiplier", modName = "MultiplierPvpDamage", enemy = true }, 
		},
	},
	{ label = "ES Bypass %", haveOutput = "AnyBypass",
		{ format = " "},
		{ format = "{0:output:PhysicalEnergyShieldBypass}%", 
			{ breakdown = "PhysicalEnergyShieldBypass" },
			{ modName = {"PhysicalEnergyShieldBypass", "BlockedDamageDoesntBypassES", "UnblockedDamageDoesBypassES"} },			
		},
		{ format = "{0:output:LightningEnergyShieldBypass}%",
			{ breakdown = "LightningEnergyShieldBypass" }, 
			{ modName = {"LightningEnergyShieldBypass", "BlockedDamageDoesntBypassES", "UnblockedDamageDoesBypassES"} },	
		},
		{ format = "{0:output:ColdEnergyShieldBypass}%",
			{ breakdown = "ColdEnergyShieldBypass" },
			{ modName = {"ColdEnergyShieldBypass", "BlockedDamageDoesntBypassES", "UnblockedDamageDoesBypassES"} },	 
		},
		{ format = "{0:output:FireEnergyShieldBypass}%", 
			{ breakdown = "FireEnergyShieldBypass" }, 
			{ modName = {"FireEnergyShieldBypass", "BlockedDamageDoesntBypassES", "UnblockedDamageDoesBypassES"} },	
		},
		{ format = "{0:output:ChaosEnergyShieldBypass}%",
			{ breakdown = "ChaosEnergyShieldBypass" }, 
			{ modName = {"ChaosEnergyShieldBypass", "ChaosNotBypassEnergyShield", "BlockedDamageDoesntBypassES", "UnblockedDamageDoesBypassES"} },	
		},
	},
	{ haveOutput = "ehpSectionAnySpecificTypes",
		{ format = "Shared:" },
		{ format = "Physical:" },
		{ format = colorCodes.LIGHTNING.."Lightning:" },
		{ format = colorCodes.COLD.."Cold:" },
		{ format = colorCodes.FIRE.."Fire:" },
		{ format = colorCodes.CHAOS.."Chaos:" },
	},
	{ label = "Prevented Life Loss", haveOutput = "preventedLifeLossTotal",
		{ format = "{0:output:preventedLifeLossTotal}",
			{ breakdown = "preventedLifeLossTotal" },
			{ modName = { "LifeLossPrevented", "LifeLossBelowHalfPrevented" } },
		},
	},
	{ label = "Mind over Matter %", haveOutput = "OnlySharedMindOverMatter",
		{ format = "{0:output:sharedMindOverMatter}%", 
			{ breakdown = "sharedMindOverMatter" },
			{ modName = "DamageTakenFromManaBeforeLife" },			
		},
	},
	{ label = "Mind over Matter %", haveOutput = "AnySpecificMindOverMatter",
		{ format = "{0:output:sharedMindOverMatter}%", 
			{ breakdown = "sharedMindOverMatter" },
			{ modName = "DamageTakenFromManaBeforeLife" },			
		},
		{ format = "+{0:output:PhysicalMindOverMatter}%", 
			{ breakdown = "PhysicalMindOverMatter" },
			{ modName = "DamageTakenFromManaBeforeLife" },			
		},
		{ format = "+{0:output:LightningMindOverMatter}%",
			{ breakdown = "LightningMindOverMatter" }, 
			{ modName = {"DamageTakenFromManaBeforeLife", "LightningDamageTakenFromManaBeforeLife"} },	
		},
		{ format = "+{0:output:ColdMindOverMatter}%",
			{ breakdown = "ColdMindOverMatter" },
			{ modName = "DamageTakenFromManaBeforeLife" },	 
		},
		{ format = "+{0:output:FireMindOverMatter}%", 
			{ breakdown = "FireMindOverMatter" }, 
			{ modName = "DamageTakenFromManaBeforeLife" },	
		},
		{ format = "+{0:output:ChaosMindOverMatter}%",
			{ breakdown = "ChaosMindOverMatter" }, 
			{ modName = {"DamageTakenFromManaBeforeLife", "ChaosDamageTakenFromManaBeforeLife"} },	
		},
	},
	{ label = "Guard", haveOutput = "OnlySharedGuard",
		{ format = "{0:output:sharedGuardAbsorb}",
			{ breakdown = "sharedGuardAbsorb" },
			{ modName = { "GuardAbsorbRate", "GuardAbsorbLimit" } },
		},
	},
	{ label = "Guard", haveOutput = "AnyGuard",
		{ format = "{0:output:sharedGuardAbsorb}",
			{ breakdown = "sharedGuardAbsorb" },
			{ modName = { "GuardAbsorbRate", "GuardAbsorbLimit" } },
		},
		{ format = "+{0:output:PhysicalGuardAbsorb}",
			{ breakdown = "PhysicalGuardAbsorb" },
			{ modName = { "PhysicalGuardAbsorbRate", "PhysicalGuardAbsorbLimit" } },
		},
		{ format = "+{0:output:LightningGuardAbsorb}",
			{ breakdown = "LightningGuardAbsorb" },
			{ modName = { "LightningGuardAbsorbRate", "LightningGuardAbsorbLimit" } },
		},
		{ format = "+{0:output:ColdGuardAbsorb}",
			{ breakdown = "ColdGuardAbsorb" },
			{ modName = { "ColdGuardAbsorbRate", "ColdGuardAbsorbLimit" } },
		},
		{ format = "+{0:output:FireGuardAbsorb}",
			{ breakdown = "FireGuardAbsorb" },
			{ modName = { "FireGuardAbsorbRate", "FireGuardAbsorbLimit" } },
		},
		{ format = "+{0:output:ChaosGuardAbsorb}",
			{ breakdown = "ChaosGuardAbsorb" },
			{ modName = { "ChaosGuardAbsorbRate", "ChaosGuardAbsorbLimit" } },
		},
	},
	{ label = "Aegis", haveOutput = "AnyAegis",
		{ format = "{0:output:sharedAegis}",
			{ breakdown = "sharedAegis" },
			{ modName = {"sharedAegisValue"} },
		},
		{ format = "+{0:output:PhysicalAegis}",
			{ breakdown = "PhysicalAegis" },
			{ modName = {"PhysicalAegisValue"} },
		},
		{ format = "+{0:output:LightningAegisDisplay}",
			{ breakdown = "LightningAegis" },
			{ modName = {"LightningAegisValue", "ElementalAegisValue"} },
		},
		{ format = "+{0:output:ColdAegisDisplay}",
			{ breakdown = "ColdAegis" },
			{ modName = {"ColdAegisValue", "ElementalAegisValue"} },
		},
		{ format = "+{0:output:FireAegisDisplay}",
			{ breakdown = "FireAegis" },
			{ modName = {"FireAegisValue", "ElementalAegisValue"} },
		},
		{ format = "+{0:output:ChaosAegis}",
			{ breakdown = "ChaosAegis" },
			{ modName = {"ChaosAegisValue"} },
		},
	},
	{ label = "Frost Shield", haveOutput = "FrostShieldLife",
		{ format = "{0:output:FrostShieldLife}",
			{ breakdown = "FrostShieldLife" },
			{ modName = { "FrostGlobeHealth", "FrostGlobeDamageMitigation" } },
		},
	},
	{ label = "Spectre Ally", haveOutput = "TotalSpectreLife",
		{ format = "{0:output:TotalSpectreLife}",
			{ breakdown = "TotalSpectreLife" },
			{ modName = { "TotalSpectreLife", "takenFromSpectresBeforeYou" } },
		},
	},
	{ label = "Totem Ally", haveOutput = "TotalTotemLife",
		{ format = "{0:output:TotalTotemLife}",
			{ breakdown = "TotalTotemLife" },
			{ modName = { "TotalTotemLife", "takenFromTotemsBeforeYou" } },
		},
	},
	{ label = "Vaal Rejuv. Totem", haveOutput = "TotalVaalRejuvenationTotemLife",
		{ format = "{0:output:TotalVaalRejuvenationTotemLife}",
			{ breakdown = "TotalVaalRejuvenationTotemLife" },
			{ modName = { "TotalVaalRejuvenationTotemLife", "takenFromVaalRejuvenationTotemsBeforeYou", "takenFromTotemsBeforeYou" } },
		},
	},
	{ label = "Soul Link", haveOutput = "AlliedEnergyShield",
		{ format = "{0:output:AlliedEnergyShield}",
			{ breakdown = "AlliedEnergyShield" },
			{ modName = { "AlliedEnergyShield", "TakenFromParentESBeforeYou" } },
		},
	},
	{ label = "Hits before death",{ format = "{2:output:NumberOfDamagingHits}", },
	}
}, }, { defaultCollapsed = false, label = "Effective \"Health\" Pool", data = {
	extra = "{0:output:TotalEHP}",
	{ label = "Unmitigated %", { format = "{0:output:ConfiguredDamageChance}%", 
		{ breakdown = "ConfiguredDamageChance" }, 
		{ label = "Enemy modifiers", modName = { "CannotBeSuppressed", "CannotBeBlocked", "reduceEnemyBlock" }, enemy = true },
	}, },
	{ label = "Mitigated hits", { format = "{2:output:NumberOfMitigatedDamagingHits}", }, },
	{ label = "Enemy miss chance", { format = "{0:output:ConfiguredNotHitChance}%", 
		{ breakdown = "ConfiguredNotHitChance" }, 
		{ label = "Enemy modifiers", modName = { "CannotBeEvaded", "CannotBeDodged", "reduceEnemyDodge" }, enemy = true },
	}, },
	{ label = "Hits before death", { format = "{2:output:TotalNumberOfHits}", { breakdown = "TotalNumberOfHits" }}, },
	{ label = "Effective Hit Pool",{ format = "{0:output:TotalEHP}", { breakdown = "TotalEHP" }, },},
	{ label = "Time before death",{ format = "{2:output:EHPSurvivalTime}s", 
		{ breakdown = "EHPSurvivalTime" }, 
		{ label = "Enemy modifiers", modName = { "TemporalChainsActionSpeed", "ActionSpeed", "Speed", "MinimumActionSpeed" }, enemy = true },
	},}
}, }, { defaultCollapsed = false, label = "Maximum Hit Taken", data = {
	colWidth = 114,
	{
		{ format = colorCodes.PHYS.."Physical:" },
		{ format = colorCodes.LIGHTNING.."Lightning:" },
		{ format = colorCodes.COLD.."Cold:" },
		{ format = colorCodes.FIRE.."Fire:" },
		{ format = colorCodes.CHAOS.."Chaos:" },
	},
	{ label = "Maximum Hit Taken",
		{ format = "{0:output:PhysicalMaximumHitTaken}", 
			{ breakdown = "PhysicalMaximumHitTaken" }, 
		},
		{ format = "{0:output:LightningMaximumHitTaken}",
			{ breakdown = "LightningMaximumHitTaken" }, 
		},
		{ format = "{0:output:ColdMaximumHitTaken}",
			{ breakdown = "ColdMaximumHitTaken" }, 
		},
		{ format = "{0:output:FireMaximumHitTaken}", 
			{ breakdown = "FireMaximumHitTaken" }, 
		},
		{ format = "{0:output:ChaosMaximumHitTaken}",
			{ breakdown = "ChaosMaximumHitTaken" }, 
		},
	}
} }, { defaultCollapsed = true, label = "Recoup and Hit Taken Over Time", data = {
	colWidth = 190,
	extra = colorCodes.LIFE.."{0:output:netLifeRecoupAndLossLostOverTimeAvg}, "..colorCodes.MANA.."{0:output:ManaRecoupRecoveryAvg}, "..colorCodes.ES.."{0:output:EnergyShieldRecoupRecoveryAvg}",
	{
		{ format = colorCodes.LIFE.."Life:" },
		{ format = colorCodes.MANA.."Mana:" },
		{ format = colorCodes.ES.."Energy Shield:" },
	},
	{ label = "Recoup Max.", haveOutput = "anyRecoup", 
		{ format = "{0:output:LifeRecoupRecoveryMax}",
			{ breakdown = "LifeRecoupRecoveryMax" },
			{ label = "Sources", modName = { "LifeRecoup", "PhysicalLifeRecoup", "LightningLifeRecoup", "ColdLifeRecoup", "FireLifeRecoup", "ChaosLifeRecoup", "PhysicalDamageMitigatedLifePseudoRecoup" } },
			{ label = "Recovery modifiers", modName = "LifeRecoveryRate" },
			{ label = "Faster Recoup", modName = "3SecondRecoup" },
		},
		{ format = "{0:output:ManaRecoupRecoveryMax}",
			{ breakdown = "ManaRecoupRecoveryMax" },
			{ label = "Sources", modName = "ManaRecoup" },
			{ label = "Recovery modifiers", modName = "ManaRecoveryRate" },
			{ label = "Faster Recoup", modName = "3SecondRecoup" },
		},
		{ format = "{0:output:EnergyShieldRecoupRecoveryMax}",
			{ breakdown = "EnergyShieldRecoupRecoveryMax" },
			{ label = "Sources", modName = { "EnergyShieldRecoup", "ElementalEnergyShieldRecoup", "PhysicalDamageMitigatedEnergyShieldPseudoRecoup" } },
			{ label = "Recovery modifiers", modName = "EnergyShieldRecoveryRate" },
			{ label = "Faster Recoup", modName = "3SecondRecoup" },
		},
	},
	{ label = "Recoup Avg.", haveOutput = "anyRecoup", 
		{ format = "{0:output:LifeRecoupRecoveryAvg}",
			{ breakdown = "LifeRecoupRecoveryAvg" },
			{ label = "Sources", modName = { "LifeRecoup", "PhysicalLifeRecoup", "LightningLifeRecoup", "ColdLifeRecoup", "FireLifeRecoup", "ChaosLifeRecoup", "PhysicalDamageMitigatedLifePseudoRecoup" } },
			{ label = "Recovery modifiers", modName = "LifeRecoveryRate" },
			{ label = "Faster Recoup", modName = "3SecondRecoup" },
		},
		{ format = "{0:output:ManaRecoupRecoveryAvg}",
			{ breakdown = "ManaRecoupRecoveryAvg" },
			{ label = "Sources", modName = "ManaRecoup" },
			{ label = "Recovery modifiers", modName = "ManaRecoveryRate" },
			{ label = "Faster Recoup", modName = "3SecondRecoup" },
		},
		{ format = "{0:output:EnergyShieldRecoupRecoveryAvg}",
			{ breakdown = "EnergyShieldRecoupRecoveryAvg" },
			{ label = "Sources", modName = { "EnergyShieldRecoup", "ElementalEnergyShieldRecoup", "PhysicalDamageMitigatedEnergyShieldPseudoRecoup" } },
			{ label = "Recovery modifiers", modName = "EnergyShieldRecoveryRate" },
			{ label = "Faster Recoup", modName = "3SecondRecoup" },
		},
	},
	{ label = "Hit Over Time Max.", haveOutput = "preventedLifeLossTotal", 
		{ format = "{0:output:LifeLossLostMax}",
			{ breakdown = "LifeLossLostMax" },
			{ modName = { "LifeLossPrevented", "LifeLossBelowHalfPrevented", "LifeLossBelowHalfLost" } },
		},
	},
	{ label = "Hit Over Time Avg.", haveOutput = "preventedLifeLossTotal", 
		{ format = "{0:output:LifeLossLostAvg}",
			{ breakdown = "LifeLossLostAvg" },
			{ modName = { "LifeLossPrevented", "LifeLossBelowHalfPrevented", "LifeLossBelowHalfLost" } },
		},
	},
	{ label = "Net Max.", haveOutput = "showNetRecoup", 
		{ format = "{0:output:netLifeRecoupAndLossLostOverTimeMax}",
			{ breakdown = "netLifeRecoupAndLossLostOverTimeMax" },
		},
	},
	{ label = "Net Avg.", haveOutput = "showNetRecoup", 
		{ format = "{0:output:netLifeRecoupAndLossLostOverTimeAvg}",
			{ breakdown = "netLifeRecoupAndLossLostOverTimeAvg" },
		},
	},
} }, { defaultCollapsed = false, label = "Dots and Build Degens", data = {
	colWidth = 114,
	{
		{ format = colorCodes.PHYS.."Physical:" },
		{ format = colorCodes.LIGHTNING.."Lightning:" },
		{ format = colorCodes.COLD.."Cold:" },
		{ format = colorCodes.FIRE.."Fire:" },
		{ format = colorCodes.CHAOS.."Chaos:" },
	},
	{ label = "DoT taken",
		{ format = "x {2:output:PhysicalTakenDotMult}", 
			{ breakdown = "PhysicalTakenDotMult" }, 
			{ modName = { "DamageTaken", "DamageTakenOverTime", "PhysicalDamageTaken", "PhysicalDamageTakenOverTime" } },
			{ label = "Physical Taken as", modName = {
				"PhysicalDamageTakenAsLightning",
				"PhysicalDamageTakenAsCold",
				"PhysicalDamageTakenAsFire",
				"PhysicalDamageTakenAsChaos"
			} },
		  	{ label = "Taken as Physical", modName = {
				"LightningDamageTakenAsPhysical",
				"ColdDamageTakenAsPhysical",
				"FireDamageTakenAsPhysical",
				"ElementalDamageTakenAsPhysical",
				"ChaosDamageTakenAsPhysical"
			} },
		},
		{ format = "x {2:output:LightningTakenDotMult}",
			{ breakdown = "LightningTakenDotMult" }, 
			{ modName = { "DamageTaken", "DamageTakenOverTime", "LightningDamageTaken", "LightningDamageTakenOverTime", "ElementalDamageTaken", "ElementalDamageTakenOverTime", "SelfIgnoreLightningResistance" } },
			{ label = "Lightning Taken as", modName = {
				"LightningDamageTakenAsPhysical",
				"LightningDamageTakenAsCold",
				"LightningDamageTakenAsFire",
				"ElementalDamageTakenAsPhysical", "ElementalDamageTakenAsChaos",
				"LightningDamageTakenAsChaos"
			} },
		  	{ label = "Taken as Lightning", modName = {
				"PhysicalDamageTakenAsLightning",
				"ColdDamageTakenAsLightning",
				"FireDamageTakenAsLightning",
				"ChaosDamageTakenAsLightning"
			} },
		},
		{ format = "x {2:output:ColdTakenDotMult}",
			{ breakdown = "ColdTakenDotMult" }, 
			{ modName = { "DamageTaken", "DamageTakenOverTime", "ColdDamageTaken", "ColdDamageTakenOverTime", "ElementalDamageTaken", "ElementalDamageTakenOverTime", "SelfIgnoreColdResistance" } },
			{ label = "Cold Taken as", modName = {
				"ColdDamageTakenAsPhysical",
				"ColdDamageTakenAsLightning",
				"ColdDamageTakenAsFire",
				"ElementalDamageTakenAsPhysical", "ElementalDamageTakenAsChaos",
				"ColdDamageTakenAsChaos"
			} },
		  	{ label = "Taken as Cold", modName = {
				"PhysicalDamageTakenAsCold",
				"LightningDamageTakenAsCold",
				"FireDamageTakenAsCold",
				"ChaosDamageTakenAsCold"
			} },
		},
		{ format = "x {2:output:FireTakenDotMult}", 
			{ breakdown = "FireTakenDotMult" }, 
			{ modName = { "DamageTaken", "DamageTakenOverTime", "FireDamageTaken", "FireDamageTakenOverTime", "ElementalDamageTaken", "ElementalDamageTakenOverTime", "SelfIgnoreFireResistance" }  },
			{ label = "Fire Taken as", modName = {
				"FireDamageTakenAsPhysical",
				"FireDamageTakenAsLightning",
				"FireDamageTakenAsCold",
				"ElementalDamageTakenAsPhysical", "ElementalDamageTakenAsChaos", 
				"FireDamageTakenAsChaos"
			} },
		  	{ label = "Taken as Fire", modName = {
				"PhysicalDamageTakenAsFire",
				"LightningDamageTakenAsFire",
				"ColdDamageTakenAsFire", "FireDamageTakenAsCold",
				"ChaosDamageTakenAsFire"
			} },
		},
		{ format = "x {2:output:ChaosTakenDotMult}",
			{ breakdown = "ChaosTakenDotMult" }, 
			{ modName = { "DamageTaken", "DamageTakenOverTime", "ChaosDamageTaken", "ChaosDamageTakenOverTime", "SelfIgnoreChaosResistance" } },
			{ label = "Chaos Taken as", modName = {
				"ChaosDamageTakenAsPhysical",
				"ChaosDamageTakenAsLightning",
				"ChaosDamageTakenAsCold",
				"ChaosDamageTakenAsFire",
			} },
		  	{ label = "Taken as Chaos", modName = {
				"PhysicalDamageTakenAsChaos",
				"LightningDamageTakenAsChaos",
				"ColdDamageTakenAsChaos",
				"FireDamageTakenAsChaos",
				"ElementalDamageTakenAsChaos"
			} },
		},
	},
	{ label = "Total Pool",
		{ format = "{0:output:PhysicalTotalPool}", 
			{ breakdown = "PhysicalTotalPool" },			
		},
		{ format = "{0:output:LightningTotalPool}",
			{ breakdown = "LightningTotalPool" }, 	
		},
		{ format = "{0:output:ColdTotalPool}",
			{ breakdown = "ColdTotalPool" },
		},
		{ format = "{0:output:FireTotalPool}", 
			{ breakdown = "FireTotalPool" }, 
		},
		{ format = "{0:output:ChaosTotalPool}",
			{ breakdown = "ChaosTotalPool" }, 
		},
	}, 
	{ label = "Effective DoT Pool",
		{ format = "{0:output:PhysicalDotEHP}", 
			{ breakdown = "PhysicalDotEHP" }, 
		},
		{ format = "{0:output:LightningDotEHP}",
			{ breakdown = "LightningDotEHP" }, 
		},
		{ format = "{0:output:ColdDotEHP}",
			{ breakdown = "ColdDotEHP" }, 
		},
		{ format = "{0:output:FireDotEHP}", 
			{ breakdown = "FireDotEHP" }, 
		},
		{ format = "{0:output:ChaosDotEHP}",
			{ breakdown = "ChaosDotEHP" }, 
		},
	},
	{ label = "Degens", haveOutput = "TotalBuildDegen",
		{ format = "{0:output:PhysicalBuildDegen}", 
			{ breakdown = "PhysicalBuildDegen" }, 
			{ modName = "PhysicalDegen", }
		},
		{ format = "{0:output:LightningBuildDegen}",
			{ breakdown = "LightningBuildDegen" }, 
			{ modName = "LightningDegen", }
		},
		{ format = "{0:output:ColdBuildDegen}",
			{ breakdown = "ColdBuildDegen" }, 
			{ modName = "ColdDegen", }
		},
		{ format = "{0:output:FireBuildDegen}", 
			{ breakdown = "FireBuildDegen" }, 
			{ modName = "FireDegen", }
		},
		{ format = "{0:output:ChaosBuildDegen}",
			{ breakdown = "ChaosBuildDegen" }, 
			{ modName = "ChaosDegen", }
		},
	},
	{ label = "Total Degen", haveOutput = "TotalBuildDegen", { format = "{1:output:TotalBuildDegen}", 
		{ breakdown = "TotalBuildDegen" },
		{ label = "Sources", modName = { "PhysicalDegen", "FireDegen", "ColdDegen", "LightningDegen", "ChaosDegen" }, modType = "BASE" },
	}, },
	{ label = "Total Net Recovery", haveOutput = "TotalNetRegen", { format = "{1:output:TotalNetRegen}",
		{ breakdown = "TotalNetRegen" },
	}, },
	{ label = "Net Life Recovery", color = colorCodes.LIFE, haveOutput = "NetLifeRegen", { format = "{1:output:NetLifeRegen}", { breakdown = "NetLifeRegen" }, }, },
	{ label = "Net Mana Recovery", color = colorCodes.MANA, haveOutput = "NetManaRegen", { format = "{1:output:NetManaRegen}", { breakdown = "NetManaRegen" }, }, },
	{ label = "Net ES Recovery", color = colorCodes.ES, haveOutput = "NetEnergyShieldRegen", { format = "{1:output:NetEnergyShieldRegen}", { breakdown = "NetEnergyShieldRegen" }, }, }
} }, { defaultCollapsed = true, label = "Enemy Degens", data = {
	colWidth = 114,
	{
		{ format = colorCodes.PHYS.."Physical:" },
		{ format = colorCodes.LIGHTNING.."Lightning:" },
		{ format = colorCodes.COLD.."Cold:" },
		{ format = colorCodes.FIRE.."Fire:" },
		{ format = colorCodes.CHAOS.."Chaos:" },
	},
	{ label = "Degens", haveOutput = "TotalDegen",
		{ format = "{0:output:PhysicalEnemyDegen}", 
			{ breakdown = "PhysicalEnemyDegen" }, 
		},
		{ format = "{0:output:LightningEnemyDegen}",
			{ breakdown = "LightningEnemyDegen" }, 
		},
		{ format = "{0:output:ColdEnemyDegen}",
			{ breakdown = "ColdEnemyDegen" }, 
		},
		{ format = "{0:output:FireEnemyDegen}", 
			{ breakdown = "FireEnemyDegen" }, 
		},
		{ format = "{0:output:ChaosEnemyDegen}",
			{ breakdown = "ChaosEnemyDegen" }, 
		},
	},
	{ label = "Total Degen", haveOutput = "TotalDegen", { format = "{1:output:TotalDegen}", 
		{ breakdown = "TotalDegen" },
	}, },
	{ label = "Total Net Recovery", haveOutput = "ComprehensiveTotalNetRegen", { format = "{1:output:ComprehensiveTotalNetRegen}",
		{ breakdown = "ComprehensiveTotalNetRegen" },
	}, },
	{ label = "Net Life Recovery", color = colorCodes.LIFE, haveOutput = "ComprehensiveNetLifeRegen", { format = "{1:output:ComprehensiveNetLifeRegen}", { breakdown = "ComprehensiveNetLifeRegen" }, }, },
	{ label = "Net Mana Recovery", color = colorCodes.MANA, haveOutput = "ComprehensiveNetManaRegen", { format = "{1:output:ComprehensiveNetManaRegen}", { breakdown = "ComprehensiveNetManaRegen" }, }, },
	{ label = "Net ES Recovery", color = colorCodes.ES, haveOutput = "ComprehensiveNetEnergyShieldRegen", { format = "{1:output:ComprehensiveNetEnergyShieldRegen}", { breakdown = "ComprehensiveNetEnergyShieldRegen" }, }, }
} }
} },
}
