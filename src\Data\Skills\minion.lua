-- This file is automatically generated, do not edit!
-- Path of Building
--
-- Minion active skills
-- Skill data (c) Grinding Gear Games
--
local skills, mod, flag, skill = ...

skills["MeleeAtAnimationSpeed"] = {
	name = "Basic Attack",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
			},
			stats = {
				"skill_can_fire_arrows",
				"skill_can_fire_wand_projectiles",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MinionMelee"] = {
	name = "Basic Attack",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
			},
			stats = {
				"skill_can_fire_arrows",
				"skill_can_fire_wand_projectiles",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MinionMeleeStep"] = {
	name = "Basic Attack",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
			},
			constantStats = {
				{ "melee_conditional_step_distance", 10 },
			},
			stats = {
				"skill_can_fire_arrows",
				"skill_can_fire_wand_projectiles",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MinionMeleeBow"] = {
	name = "Basic Attack",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				projectile = true,
			},
			constantStats = {
				{ "arrow_projectile_variation", 1002 },
			},
			stats = {
				"skill_can_fire_arrows",
				"skill_can_fire_wand_projectiles",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"maintain_projectile_direction_when_using_contact_position",
				"check_for_targets_between_initiator_and_projectile_source",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["GasShotSkeletonSniperMinion"] = {
	name = "Gas Arrow",
	hidden = true,
	description = "Rain an arrow down from above, creating a cloud of flammable poisonous gas where it lands. The cloud will detonate if hit by a Detonator skill or if an Ignited enemy touches it, creating a fiery explosion.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.Area] = true, [SkillType.ProjectileNumber] = true, [SkillType.ProjectileSpeed] = true, [SkillType.Rain] = true, [SkillType.DamageOverTime] = true, [SkillType.Cooldown] = true, [SkillType.Duration] = true, [SkillType.GroundTargetedProjectile] = true, [SkillType.CreatesGroundEffect] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { attackSpeedMultiplier = 20, storedUses = 1, baseMultiplier = 0.7, cooldown = 8, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Impact",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "sniper_gas_shot_statset_0",
			baseFlags = {
				attack = true,
				projectile = true,
				area = true,
			},
			constantStats = {
				{ "active_skill_base_area_of_effect_radius", 14 },
				{ "active_skill_base_secondary_area_of_effect_radius", 20 },
				{ "command_minion_marker_additional_time_ms", 300 },
			},
			stats = {
				"is_commandable_skill",
				"base_is_projectile",
				"is_area_damage",
				"visual_hit_effect_chaos_is_green",
				"skill_cannot_be_frozen",
				"skill_cannot_be_electrocuted",
				"skill_cannot_be_knocked_back",
				"skill_cannot_be_stunned",
				"quality_display_active_skill_ground_effect_area_of_effect_+%_final_per_second_max_is_gem",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
		[2] = {
			label = "Poison Cloud",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "sniper_gas_shot_statset_1",
			baseFlags = {
				attack = true,
				area = true,
				duration = true,
			},
			constantStats = {
				{ "active_skill_base_area_of_effect_radius", 14 },
				{ "active_skill_base_secondary_area_of_effect_radius", 20 },
				{ "command_minion_marker_additional_time_ms", 300 },
				{ "base_skill_effect_duration", 8000 },
				{ "active_skill_ground_effect_area_of_effect_+%_final_per_second", 20 },
				{ "active_skill_ground_effect_area_of_effect_+%_final_per_second_max", 80 },
			},
			stats = {
				"is_commandable_skill",
				"base_is_projectile",
				"is_area_damage",
				"visual_hit_effect_chaos_is_green",
				"skill_cannot_be_frozen",
				"skill_cannot_be_electrocuted",
				"skill_cannot_be_knocked_back",
				"skill_cannot_be_stunned",
				"quality_display_active_skill_ground_effect_area_of_effect_+%_final_per_second_max_is_gem",
				"display_statset_no_hit_damage",
				"display_statset_hide_usage_stats",
				"display_fake_attack_hit_poison",
				"display_skill_poisons_without_hit",
			},
			levels = {
				[1] = { baseMultiplier = 2.2, actorLevel = 1, },
			},
		},
		[3] = {
			label = "Explosion",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "sniper_gas_shot_statset_2",
			baseFlags = {
				hit = true,
				area = true,
			},
			constantStats = {
				{ "active_skill_base_area_of_effect_radius", 14 },
				{ "active_skill_base_secondary_area_of_effect_radius", 20 },
				{ "command_minion_marker_additional_time_ms", 300 },
				{ "active_skill_base_physical_damage_%_to_convert_to_fire", 100 },
			},
			stats = {
				"is_commandable_skill",
				"base_is_projectile",
				"is_area_damage",
				"visual_hit_effect_chaos_is_green",
				"skill_cannot_be_frozen",
				"skill_cannot_be_electrocuted",
				"skill_cannot_be_knocked_back",
				"skill_cannot_be_stunned",
				"quality_display_active_skill_ground_effect_area_of_effect_+%_final_per_second_max_is_gem",
			},
			levels = {
				[1] = { baseMultiplier = 6.3, actorLevel = 1, },
			},
		},
	}
}
skills["BoneshatterBruteMinion"] = {
	name = "Bonebreaker",
	hidden = true,
	description = "A Strike that causes a Heavy Stun on enemies that are Primed for Stun. Upon causing a Heavy Stun it will also create a Shockwave, dealing a large amount of damage in an area.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.Area] = true, [SkillType.Cooldown] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { attackSpeedMultiplier = 20, storedUses = 1, baseMultiplier = 1.2, cooldown = 4, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Initial Strike",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
			},
			constantStats = {
				{ "melee_range_+", 2 },
				{ "melee_conditional_step_distance", 3 },
			},
			stats = {
				"crushing_blow",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
		[2] = {
			label = "Shockwave",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				area = true,
			},
			constantStats = {
				{ "melee_range_+", 2 },
				{ "melee_conditional_step_distance", 3 },
				{ "active_skill_base_area_of_effect_radius", 20 },
			},
			stats = {
				"crushing_blow",
				"is_area_damage",
			},
			levels = {
				[1] = { baseMultiplier = 2.4, actorLevel = 1, },
			},
		},
	}
}
skills["ArcSkeletonMageMinion"] = {
	name = "Arc",
	hidden = true,
	description = "An arc of Lightning stretches from the caster to a targeted enemy and Chains on to other nearby enemies. Shocked enemies Hit release damaging pulses.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.Chains] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.Lightning] = true, [SkillType.CanRapidFire] = true, [SkillType.Invokable] = true, [SkillType.UsableWhileMoving] = true, [SkillType.Area] = true, [SkillType.AreaSpell] = true, },
	castTime = 1.25,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 9, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Arc",
			baseEffectiveness = 1.2000000476837,
			incrementalEffectiveness = 0.25,
			damageIncrementalEffectiveness = 0.037999998778105,
			statDescriptionScope = "arc",
			baseFlags = {
				spell = true,
				chaining = true,
			},
			constantStats = {
				{ "arc_chain_distance", 35 },
				{ "active_skill_shock_chance_+%_final", 100 },
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
				"number_of_chains",
				"disable_visual_hit_effect",
			},
			levels = {
				[1] = { 0.10000000149012, 1.8999999761581, 2, statInterpolation = { 3, 3, 1, }, actorLevel = 1, },
				[2] = { 0.10000000149012, 1.8999999761581, 3, statInterpolation = { 3, 3, 1, }, actorLevel = 20, },
				[3] = { 0.10000000149012, 1.8999999761581, 4, statInterpolation = { 3, 3, 1, }, actorLevel = 40, },
				[4] = { 0.10000000149012, 1.8999999761581, 5, statInterpolation = { 3, 3, 1, }, actorLevel = 60, },
			},
		},
	}
}
skills["DeathStormSkeletonStormMageMinion"] = {
	name = "Death Storm",
	hidden = true,
	description = "Lightning bolts strike all fallen Skeletons in the area, creating Shocked Ground around them.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Lightning] = true, [SkillType.Area] = true, [SkillType.Cooldown] = true, [SkillType.CreatesGroundEffect] = true, },
	castTime = 2,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 10, storedUses = 1, levelRequirement = 0, cooldown = 5, },
	},
	statSets = {
		[1] = {
			label = "Death Storm",
			baseEffectiveness = 7.5,
			incrementalEffectiveness = 0.25,
			damageIncrementalEffectiveness = 0.041499998420477,
			statDescriptionScope = "death_storm",
			baseFlags = {
				spell = true,
				area = true,
				hit = true,
			},
			constantStats = {
				{ "ground_lightning_art_variation", 1003 },
				{ "active_skill_base_area_of_effect_radius", 18 },
				{ "active_skill_base_secondary_area_of_effect_radius", 18 },
				{ "base_skill_effect_duration", 6000 },
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
				"is_commandable_skill",
				"skill_cannot_be_frozen",
				"skill_cannot_be_electrocuted",
				"skill_cannot_be_knocked_back",
				"skill_cannot_be_stunned",
			},
			levels = {
				[1] = { 0.30000001192093, 1.7000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["FrostBoltSkeletonMageMinion"] = {
	name = "Ice Shard",
	hidden = true,
	skillTypes = { [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.Multicastable] = true, [SkillType.Cold] = true, [SkillType.CanRapidFire] = true, [SkillType.Area] = true, },
	castTime = 1.25,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 11, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Projectile",
			baseEffectiveness = 1.0499999523163,
			incrementalEffectiveness = 0.25,
			damageIncrementalEffectiveness = 0.034499999135733,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1064 },
				{ "base_chance_to_freeze_%", 100 },
				{ "active_skill_hit_damage_freeze_multiplier_+%_final", 100 },
				{ "projectile_maximum_duration_override_ms", 180 },
			},
			stats = {
				"spell_minimum_base_cold_damage",
				"spell_maximum_base_cold_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"check_for_targets_between_initiator_and_projectile_source",
				"maintain_projectile_direction_when_using_contact_position",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
		[2] = {
			label = "Explosion",
			baseEffectiveness = 0.85000002384186,
			incrementalEffectiveness = 0.25,
			damageIncrementalEffectiveness = 0.034499999135733,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				area = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1064 },
				{ "base_chance_to_freeze_%", 100 },
				{ "active_skill_hit_damage_freeze_multiplier_+%_final", 100 },
				{ "projectile_maximum_duration_override_ms", 180 },
				{ "active_skill_base_area_of_effect_radius", 8 },
			},
			stats = {
				"spell_minimum_base_cold_damage",
				"spell_maximum_base_cold_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"check_for_targets_between_initiator_and_projectile_source",
				"maintain_projectile_direction_when_using_contact_position",
				"is_area_damage",
				"display_statset_hide_usage_stats",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["IceBombSkeletonMageMinion"] = {
	name = "Ice Bomb",
	hidden = true,
	description = "Crystallises the air into icy shards that explode outwards when they hit the ground.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Duration] = true, [SkillType.Cold] = true, [SkillType.Minion] = true, [SkillType.Damage] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.Area] = true, [SkillType.AreaSpell] = true, [SkillType.Cascadable] = true, },
	castTime = 1.25,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 12, storedUses = 1, levelRequirement = 0, cooldown = 8, },
	},
	statSets = {
		[1] = {
			label = "Ice Bomb",
			baseEffectiveness = 5,
			incrementalEffectiveness = 0.25,
			damageIncrementalEffectiveness = 0.041499998420477,
			statDescriptionScope = "ice_bomb",
			baseFlags = {
				spell = true,
				duration = true,
			},
			constantStats = {
				{ "active_skill_base_area_of_effect_radius", 26 },
				{ "active_skill_chill_effect_+%_final", 100 },
			},
			stats = {
				"spell_minimum_base_cold_damage",
				"spell_maximum_base_cold_damage",
				"is_commandable_skill",
				"skill_cannot_be_frozen",
				"skill_cannot_be_electrocuted",
				"skill_cannot_be_knocked_back",
				"skill_cannot_be_stunned",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["HealSkeletonClericMinion"] = {
	name = "Heal Buff",
	hidden = true,
	skillTypes = { [SkillType.Buff] = true, [SkillType.Duration] = true, [SkillType.Spell] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Heal",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "add_buff_to_target",
			baseFlags = {
			},
			constantStats = {
				{ "base_skill_effect_duration", 4000 },
			},
			stats = {
				"skeletal_cleric_grants_base_life_regeneration_rate_per_minute",
				"display_minion_heal",
			},
			levels = {
				[1] = { 776, statInterpolation = { 1, }, actorLevel = 1, },
				[2] = { 1410, statInterpolation = { 1, }, actorLevel = 3, },
				[3] = { 2109, statInterpolation = { 1, }, actorLevel = 5, },
				[4] = { 2884, statInterpolation = { 1, }, actorLevel = 7, },
				[5] = { 3695, statInterpolation = { 1, }, actorLevel = 9, },
				[6] = { 4599, statInterpolation = { 1, }, actorLevel = 11, },
				[7] = { 5547, statInterpolation = { 1, }, actorLevel = 13, },
				[8] = { 6591, statInterpolation = { 1, }, actorLevel = 15, },
				[9] = { 7717, statInterpolation = { 1, }, actorLevel = 17, },
				[10] = { 8925, statInterpolation = { 1, }, actorLevel = 19, },
				[11] = { 10184, statInterpolation = { 1, }, actorLevel = 21, },
				[12] = { 11587, statInterpolation = { 1, }, actorLevel = 23, },
				[13] = { 13099, statInterpolation = { 1, }, actorLevel = 25, },
				[14] = { 14673, statInterpolation = { 1, }, actorLevel = 27, },
				[15] = { 16435, statInterpolation = { 1, }, actorLevel = 29, },
				[16] = { 18306, statInterpolation = { 1, }, actorLevel = 31, },
				[17] = { 20314, statInterpolation = { 1, }, actorLevel = 33, },
				[18] = { 22439, statInterpolation = { 1, }, actorLevel = 35, },
				[19] = { 24779, statInterpolation = { 1, }, actorLevel = 37, },
				[20] = { 27243, statInterpolation = { 1, }, actorLevel = 39, },
				[21] = { 29927, statInterpolation = { 1, }, actorLevel = 41, },
				[22] = { 32745, statInterpolation = { 1, }, actorLevel = 43, },
				[23] = { 35801, statInterpolation = { 1, }, actorLevel = 45, },
				[24] = { 39046, statInterpolation = { 1, }, actorLevel = 47, },
				[25] = { 42572, statInterpolation = { 1, }, actorLevel = 49, },
				[26] = { 46315, statInterpolation = { 1, }, actorLevel = 51, },
				[27] = { 50317, statInterpolation = { 1, }, actorLevel = 53, },
				[28] = { 54638, statInterpolation = { 1, }, actorLevel = 55, },
				[29] = { 59219, statInterpolation = { 1, }, actorLevel = 57, },
				[30] = { 64126, statInterpolation = { 1, }, actorLevel = 59, },
				[31] = { 69402, statInterpolation = { 1, }, actorLevel = 61, },
				[32] = { 75060, statInterpolation = { 1, }, actorLevel = 63, },
				[33] = { 81057, statInterpolation = { 1, }, actorLevel = 65, },
				[34] = { 87502, statInterpolation = { 1, }, actorLevel = 67, },
				[35] = { 94367, statInterpolation = { 1, }, actorLevel = 69, },
				[36] = { 101706, statInterpolation = { 1, }, actorLevel = 71, },
				[37] = { 109584, statInterpolation = { 1, }, actorLevel = 73, },
				[38] = { 118001, statInterpolation = { 1, }, actorLevel = 75, },
				[39] = { 126925, statInterpolation = { 1, }, actorLevel = 77, },
				[40] = { 136534, statInterpolation = { 1, }, actorLevel = 79, },
			},
		},
	}
}
skills["FireBombSkeletonMinion"] = {
	name = "Fire Bomb",
	hidden = true,
	description = "Attacks with a bouncing bomb that explodes on hit.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.Area] = true, [SkillType.ProjectileNumber] = true, [SkillType.ProjectileSpeed] = true, [SkillType.Duration] = true, [SkillType.Grenade] = true, [SkillType.CreatesGroundEffect] = true, [SkillType.DetonatesAfterTime] = true, [SkillType.Projectile] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Fire Bomb",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				area = true,
				duration = true,
			},
			constantStats = {
				{ "base_skill_detonation_time", 2000 },
				{ "active_skill_base_physical_damage_%_to_convert_to_fire", 70 },
				{ "active_skill_base_area_of_effect_radius", 16 },
			},
			stats = {
				"base_is_projectile",
				"projectile_ballistic_angle_from_reference_event",
				"is_area_damage",
				"grenades_target_at_half_monster_height",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
		[2] = {
			label = "Hidden",
			baseEffectiveness = 8,
			incrementalEffectiveness = 0.034650001674891,
			damageIncrementalEffectiveness = 0.012719999998808,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "base_skill_detonation_time", 2000 },
				{ "active_skill_base_physical_damage_%_to_convert_to_fire", 70 },
				{ "active_skill_base_area_of_effect_radius", 16 },
				{ "base_secondary_skill_effect_duration", 0 },
			},
			stats = {
				"base_fire_damage_to_deal_per_minute",
				"base_is_projectile",
				"projectile_ballistic_angle_from_reference_event",
				"is_area_damage",
				"grenades_target_at_half_monster_height",
			},
			levels = {
				[1] = { 16.666667039196, statInterpolation = { 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["DestructiveLinkSkeletonBombadierMinion"] = {
	name = "Explosive Demise",
	baseTypeName = "Explosive Demise",
	color = 2,
	description = "Detonate an allied Minion whose current Life is below a threshold, dealing heavy damage to nearby enemies.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Cooldown] = true, [SkillType.Area] = true, [SkillType.Fire] = true, [SkillType.Damage] = true, },
	castTime = 0.6,
	qualityStats = {
		{ "arsonist_destructive_link_%_of_life_as_fire_damage", 0.2 },
	},
	levels = {
		[1] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[2] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[3] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[4] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[5] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[6] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[7] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[8] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[9] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[10] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[11] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[12] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[13] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[14] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[15] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[16] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[17] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[18] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[19] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[20] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[21] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[22] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[23] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[24] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[25] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[26] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[27] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[28] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[29] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[30] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[31] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[32] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[33] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[34] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[35] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[36] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[37] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[38] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[39] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
		[40] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 3, },
	},
	statSets = {
		[1] = {
			label = "Explosive Demise",
			baseEffectiveness = 4,
			incrementalEffectiveness = 0.25,
			damageIncrementalEffectiveness = 0.041499998420477,
			statDescriptionScope = "bombadier_destructive_link",
			baseFlags = {
				area = true,
				spell = true,
			},
			constantStats = {
				{ "active_skill_base_area_of_effect_radius", 24 },
				{ "arsonist_destructive_link_%_of_life_as_fire_damage", 12 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"command_minion_target_ally_below_x_life",
				"is_commandable_skill",
				"is_area_damage",
				"skill_cannot_be_frozen",
				"skill_cannot_be_electrocuted",
				"skill_cannot_be_knocked_back",
				"skill_cannot_be_stunned",
				"quality_display_arsonist_is_gem",
			},
			levels = {
				[1] = { 12, 19, 27, statInterpolation = { 1, 1, 1, }, actorLevel = 1, },
				[2] = { 20, 30, 47, statInterpolation = { 1, 1, 1, }, actorLevel = 3, },
				[3] = { 29, 44, 69, statInterpolation = { 1, 1, 1, }, actorLevel = 5, },
				[4] = { 40, 60, 94, statInterpolation = { 1, 1, 1, }, actorLevel = 7, },
				[5] = { 52, 77, 120, statInterpolation = { 1, 1, 1, }, actorLevel = 9, },
				[6] = { 65, 98, 149, statInterpolation = { 1, 1, 1, }, actorLevel = 11, },
				[7] = { 81, 122, 180, statInterpolation = { 1, 1, 1, }, actorLevel = 13, },
				[8] = { 99, 148, 214, statInterpolation = { 1, 1, 1, }, actorLevel = 15, },
				[9] = { 119, 179, 251, statInterpolation = { 1, 1, 1, }, actorLevel = 17, },
				[10] = { 142, 213, 291, statInterpolation = { 1, 1, 1, }, actorLevel = 19, },
				[11] = { 168, 252, 333, statInterpolation = { 1, 1, 1, }, actorLevel = 21, },
				[12] = { 198, 297, 380, statInterpolation = { 1, 1, 1, }, actorLevel = 23, },
				[13] = { 231, 346, 431, statInterpolation = { 1, 1, 1, }, actorLevel = 25, },
				[14] = { 268, 403, 484, statInterpolation = { 1, 1, 1, }, actorLevel = 27, },
				[15] = { 311, 466, 544, statInterpolation = { 1, 1, 1, }, actorLevel = 29, },
				[16] = { 358, 537, 608, statInterpolation = { 1, 1, 1, }, actorLevel = 31, },
				[17] = { 411, 617, 677, statInterpolation = { 1, 1, 1, }, actorLevel = 33, },
				[18] = { 471, 706, 750, statInterpolation = { 1, 1, 1, }, actorLevel = 35, },
				[19] = { 537, 806, 831, statInterpolation = { 1, 1, 1, }, actorLevel = 37, },
				[20] = { 612, 918, 917, statInterpolation = { 1, 1, 1, }, actorLevel = 39, },
				[21] = { 696, 1043, 1011, statInterpolation = { 1, 1, 1, }, actorLevel = 41, },
				[22] = { 789, 1183, 1110, statInterpolation = { 1, 1, 1, }, actorLevel = 43, },
				[23] = { 893, 1339, 1218, statInterpolation = { 1, 1, 1, }, actorLevel = 45, },
				[24] = { 1009, 1513, 1333, statInterpolation = { 1, 1, 1, }, actorLevel = 47, },
				[25] = { 1138, 1707, 1459, statInterpolation = { 1, 1, 1, }, actorLevel = 49, },
				[26] = { 1282, 1923, 1593, statInterpolation = { 1, 1, 1, }, actorLevel = 51, },
				[27] = { 1442, 2163, 1737, statInterpolation = { 1, 1, 1, }, actorLevel = 53, },
				[28] = { 1620, 2430, 1893, statInterpolation = { 1, 1, 1, }, actorLevel = 55, },
				[29] = { 1818, 2727, 2060, statInterpolation = { 1, 1, 1, }, actorLevel = 57, },
				[30] = { 2038, 3057, 2239, statInterpolation = { 1, 1, 1, }, actorLevel = 59, },
				[31] = { 2282, 3422, 2432, statInterpolation = { 1, 1, 1, }, actorLevel = 61, },
				[32] = { 2552, 3828, 2640, statInterpolation = { 1, 1, 1, }, actorLevel = 63, },
				[33] = { 2852, 4279, 2862, statInterpolation = { 1, 1, 1, }, actorLevel = 65, },
				[34] = { 3185, 4778, 3101, statInterpolation = { 1, 1, 1, }, actorLevel = 67, },
				[35] = { 3554, 5330, 3357, statInterpolation = { 1, 1, 1, }, actorLevel = 69, },
				[36] = { 3962, 5943, 3632, statInterpolation = { 1, 1, 1, }, actorLevel = 71, },
				[37] = { 4414, 6620, 3928, statInterpolation = { 1, 1, 1, }, actorLevel = 73, },
				[38] = { 4913, 7370, 4246, statInterpolation = { 1, 1, 1, }, actorLevel = 75, },
				[39] = { 5466, 8200, 4585, statInterpolation = { 1, 1, 1, }, actorLevel = 77, },
				[40] = { 6078, 9117, 4951, statInterpolation = { 1, 1, 1, }, actorLevel = 79, },
			},
		},
	}
}
skills["EnrageSkeletonReaverMinion"] = {
	name = "Enrage",
	hidden = true,
	description = "Enrage all Skeletal Reavers, causing them to lose life over time and gain Rage on hit. Skeletal Reavers do more Attack Damage and gain increased Attack speed based on their Rage.",
	skillTypes = { [SkillType.Buff] = true, [SkillType.Cooldown] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 4, },
	},
	statSets = {
		[1] = {
			label = "Enrage",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "reaver_enrage_decay_rate_increase_+%_final", 20 },
				{ "reaver_enrage_base_life_%_degen_per_minute", 60 },
				{ "gain_x_rage_on_attack_hit", 2 },
			},
			stats = {
				"is_commandable_skill",
				"infinite_skill_effect_duration",
				"skill_cannot_be_frozen",
				"skill_cannot_be_electrocuted",
				"skill_cannot_be_knocked_back",
				"skill_cannot_be_stunned",
				"base_deal_no_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["GAAncestralJadeHulkLeapImpact"] = {
	name = "Leap Slam",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 1.5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Leap Slam",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				area = true,
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["TCAncestralLeagueKaruiHulk"] = {
	name = "Shield Charge",
	hidden = true,
	description = "Charges at an enemy, bashing it with the character's shield and striking it. This knocks it back and stuns it. Enemies in the way are pushed to the side. Damage and stun are proportional to distance travelled. Cannot be supported by Multistrike.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.Melee] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Movement] = true, [SkillType.Travel] = true, },
	weaponTypes = {
		["None"] = true,
		["One Handed Sword"] = true,
		["One Handed Mace"] = true,
		["Flail"] = true,
		["Spear"] = true,
		["One Handed Axe"] = true,
		["Dagger"] = true,
		["Claw"] = true,
	},
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 7, },
	},
	statSets = {
		[1] = {
			label = "Shield Charge",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
				area = true,
			},
			stats = {
				"ignores_proximity_shield",
				"is_area_damage",
				"base_skill_can_be_blocked",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MPSAncestralTotemSpiritSoulCasterProjectile"] = {
	name = "Projectile Spell",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1.5,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Projectile Spell",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1389 },
				{ "spell_maximum_action_distance_+%", -40 },
			},
			stats = {
				"spell_minimum_base_cold_damage",
				"spell_maximum_base_cold_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
			skills["MinionInstability"] = {
				name = "Minion Instability",
				hidden = true,
				skillTypes = { [SkillType.Damage] = true, },
				qualityStats = {
				},
				levels = {
					[1] = { levelRequirement = 0, },
				},
				preDamageFunc = function(activeSkill, output)
					local skillData = activeSkill.skillData
					skillData.FireBonusMin = output.Life * skillData.selfFireExplosionLifeMultiplier
					skillData.FireBonusMax = output.Life * skillData.selfFireExplosionLifeMultiplier
				end,
				statSets = {
					[1] = {
						label = "Minion Instability",
						incrementalEffectiveness = 0,
						statDescriptionScope = "skill_stat_descriptions",
						baseFlags = {
						},
						baseMods = {
							skill("selfFireExplosionLifeMultiplier", 0.01, { type = "Multiplier", var = "MinionInstabilityBaseDamage" }),
							skill("showAverage", true),
						},
						constantStats = {
						},
						stats = {
						},
						levels = {
							[1] = { },
						},
					},
				}
			}

skills["GAAnimateWeaponMaceSlam"] = {
	name = "Mace Slam",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, [SkillType.Melee] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Area] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { attackSpeedMultiplier = -50, storedUses = 1, baseMultiplier = 1.8, cooldown = 10, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Slam (Mace)",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
				area = true,
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["DTTAnimateWeaponSpearDashStabImpact"] = {
	name = "Spear Dash",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, [SkillType.Melee] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Area] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 1.8, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Spear Dash",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
				area = true,
			},
			stats = {
				"is_area_damage",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["GAAnimateWeaponQuarterstaffSweep"] = {
	name = "Quarterstaff Sweep",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Melee] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Area] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 0.9, storedUses = 1, levelRequirement = 0, cooldown = 10, },
	},
	statSets = {
		[1] = {
			label = "Sweep (Quarterstaff)",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
			},
			stats = {
				"is_area_damage",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}