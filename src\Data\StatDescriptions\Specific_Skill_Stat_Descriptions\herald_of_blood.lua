-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="cannot_cause_bleeding"
		}
	},
	[2]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[3]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Explosion radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Explosion radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Enemies with Blood Loss you kill explode"
			}
		},
		stats={
			[1]="display_herald_of_blood_behaviour"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="Deals {0:+d}% of exploded enemy's Blood Loss\nas unscalable Physical Attack Damage"
			},
			[2]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Deals {0}% of exploded enemy's Blood Loss\nas unscalable Physical Attack Damage"
			}
		},
		stats={
			[1]="skill_herald_of_blood_bleeding_enemies_explode_for_%_blood_loss_as_unscalable_physical_damage",
			[2]="quality_display_herald_of_blood_is_gem"
		}
	},
	[6]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% chance to Aggravate Bleeding on Hit"
			}
		},
		stats={
			[1]="skill_herald_of_blood_explosion_aggravate_bleeding_chance_%"
		}
	},
	["active_skill_area_of_effect_radius"]=2,
	["active_skill_base_area_of_effect_radius"]=3,
	["cannot_cause_bleeding"]=1,
	["display_herald_of_blood_behaviour"]=4,
	parent="skill_stat_descriptions",
	["quality_display_herald_of_blood_is_gem"]=5,
	["skill_herald_of_blood_bleeding_enemies_explode_for_%_blood_loss_as_unscalable_physical_damage"]=5,
	["skill_herald_of_blood_explosion_aggravate_bleeding_chance_%"]=6
}