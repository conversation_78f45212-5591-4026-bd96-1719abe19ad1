-- This file is automatically generated, do not edit!
-- Path of Building
--
-- Intelligence support gems
-- Skill data (c) Grinding Gear Games
--
local skills, mod, flag, skill = ...

skills["SupportAbidingHexPlayer"] = {
	name = "Abiding Hex",
	description = "Supports Curse Skills you cast yourself. Supported Skills will consume Power Charges on use, gaining significant Curse duration if they do.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.AppliesCurse, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Persistent, SkillType.Minion, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Abiding Hex",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_consume_power_charge_to_gain_curse_duration_+%_final", 80 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportAblationPlayer"] = {
	name = "Ablation",
	description = "Supports Offering Skills. Supported Skills Sacrifice a portion of your life on use, but deal much more damage and have more powerful Buffs in return.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Offering, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Ablation",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_ablation_offering_skill_damage_+%_final"] = {
					mod("Damage", "MORE", nil, 0, 0, { type = "SkillType", skillType = SkillType.Offering }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_ablation_offering_skill_damage_+%_final", 30 },
				{ "offering_spells_effect_+%", 30 },
				{ "sacrifice_%_life_on_skill_use", 15 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportAcrimonyPlayer"] = {
	name = "Acrimony",
	description = "Supports Skills which can Damage Enemies. Enemies affected by Damage over time from Supported Skills which was not caused by a Damaging Ailment have reduced Life regeneration rate. Only the strongest instance of this Debuff will apply. Does not Support Skills used by Minions.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.DegenOnlySpellDamage, SkillType.DamageOverTime, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	ignoreMinionTypes = true,
	levels = {
		[1] = { manaMultiplier = 10, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Acrimony",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "skill_enemies_affected_by_non_ailment_damage_over_time_life_regeneration_rate_+%", -50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportAmbrosiaPlayer"] = {
	name = "Ambrosia",
	description = "Supports Skills you use yourself which Damage enemies with Hits. Supported Skills consume a percentage of your maximum Mana Flask charges, Gaining a percentage of Damage as extra Lightning Damage per Mana Flask charge consumed.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowSkill, SkillType.CrossbowAmmoSkill, SkillType.Nova, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Minion, SkillType.UsedByTotem, SkillType.SummonsTotem, SkillType.Triggered, SkillType.Meta, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Ambrosia",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["consume_%_of_maximum_mana_flask_charges_on_skill_use"] = {
					mod("Multiplier:ManaFlaskMaxChargesPercent", "BASE", nil),
				},
				["gain_%_damage_as_lighting_per_mana_flask_charge_consumed"] = {
					mod("DamageGainAsLightning", "BASE", nil, 0, 0, { type = "Multiplier", var = "ManaFlaskChargeConsumed"}),
				},
			},
			baseFlags = {
			},
			baseMods = {
				mod("Multiplier:ManaFlaskChargeConsumed", "BASE", 1, 0, 0, { type = "PercentStat", stat = "ManaFlask1MaxCharges", percentVar = "ManaFlaskMaxChargesPercent", floor = true }),
				mod("Multiplier:ManaFlaskChargeConsumed", "BASE", 1, 0, 0, { type = "PercentStat", stat = "ManaFlask2MaxCharges", percentVar = "ManaFlaskMaxChargesPercent", floor = true }),
			},
			constantStats = {
				{ "consume_%_of_maximum_mana_flask_charges_on_skill_use", 20 },
				{ "gain_%_damage_as_lighting_per_mana_flask_charge_consumed", 3 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportAmbushPlayer"] = {
	name = "Ambush",
	description = "Supports any skill that Hits enemies, making it more likely to Critically Hit enemies on full life.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Ambush",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_ambush_critical_strike_chance_vs_enemies_on_full_life_+%_final"] = {
					mod("CritChance", "MORE", nil, 0, 0, { type = "ActorCondition", actor = "enemy", var = "FullLife" }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_ambush_critical_strike_chance_vs_enemies_on_full_life_+%_final", 100 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportArcaneSurgePlayer"] = {
	name = "Arcane Surge",
	description = "Supports Spells you cast yourself, tracking the mana you spend to cast them. Spending enough mana grants a burst of Mana Regeneration and Cast Speed.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Spell, },
	addSkillTypes = { SkillType.Duration, },
	excludeSkillTypes = { SkillType.Trapped, SkillType.RemoteMined, SkillType.SummonsTotem, SkillType.UsedByTotem, SkillType.Triggered, SkillType.HasReservation, SkillType.ReservationBecomesCost, SkillType.NOT, SkillType.AND, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Arcane Surge",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_arcane_surge_base_duration_ms", 10000 },
				{ "support_arcane_surge_gain_buff_on_%_of_maximum_mana_use_threshold", 100 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportFasterCastPlayer"] = {
	name = "Arcane Tempo",
	description = "Supports Spells, causing them to cast faster.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Spell, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Instant, SkillType.FixedCastTime, SkillType.NoAttackOrCastTime, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Arcane Tempo",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_faster_casting_cast_speed_+%_final"] = {
					mod("Speed", "MORE", nil, ModFlag.Cast),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_faster_casting_cast_speed_+%_final", 20 },
				{ "support_faster_casting_damage_+%_final", 0 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportAstralProjectionPlayer"] = {
	name = "Astral Projection",
	description = "Supports Nova Skills, causing those Skills to take place at the targeted location when used instead of around you.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Nova, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Astral Projection",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_astral_projection_aoe_+%_final"] = {
					mod("AreaOfEffect", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_astral_projection_aoe_+%_final", -25 },
			},
			stats = {
				"nova_skills_cast_at_target_location",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportBiddingPlayer"] = {
	name = "Bidding",
	description = "Supports Minion Skills. Supported Minions deal significantly more damage with their Command Skills.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.CommandableMinion, SkillType.CommandsMinions, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 30, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Bidding",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_command_skill_damage_+%_final", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportBitingFrostPlayer"] = {
	name = "Biting Frost",
	description = "Supports any skill that Hits enemies, causing them to deal more damage to Frozen enemies but consume their Freeze. Cannot support skills that Consume Freeze.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.SkillConsumesFreeze, SkillType.SupportedByElementalDischarge, },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Biting Frost",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_active_skill_consume_enemy_freeze_to_gain_damage_+%_final"] = {
					mod("Damage", "MORE", nil, 0, 0, { type = "ActorCondition", actor = "enemy", var = "Frozen" }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_active_skill_consume_enemy_freeze_to_gain_damage_+%_final", 50 },
			},
			stats = {
				"never_freeze",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportBoneShrapnelPlayer"] = {
	name = "Bone Shrapnel",
	description = "Supports Skills which Hit Enemies. Supported Skills trigger Bone Shrapnel explosions when killing Pinned Enemies.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowSkill, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.SkillGrantedBySupport, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Support",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			stats = {
				"trigger_bone_shrapnel_explosion_on_killing_pinned_enemy",
				"cannot_pin",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["TriggeredBoneShrapnelPlayer"] = {
	name = "Bone Shrapnel Explosion",
	hidden = true,
	description = "Deal Physical Area Damage based off of the maximum Life of the Pinned target slain.",
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Triggered] = true, [SkillType.Damage] = true, [SkillType.Area] = true, [SkillType.Physical] = true, [SkillType.SkillGrantedBySupport] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 13, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Bone Shrapnel Explosion",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "triggered_bone_shrapnel",
			baseFlags = {
			},
			constantStats = {
				{ "active_skill_base_area_of_effect_radius", 20 },
				{ "support_bone_shrapnel_physical_damage_equal_to_%_monster_life", 25 },
				{ "triggered_by_bone_shrapnel_support_%", 100 },
			},
			stats = {
				"cannot_pin",
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportBurgeonPlayer"] = {
	name = "Burgeon",
	description = "Supports Channelling Skills you use yourself, causing them to deal more damage the longer they have been Channelled.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Channel, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Minion, SkillType.UsedByTotem, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Burgeon",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_chanelling_damage_+%_final_per_second_channelling"] = {
					mod("Damage", "MORE", nil, 0, 0, { type = "Condition", var = "Channelling" }, { type = "Multiplier", var = "ChannellingTime", limitVar = "BurgeonDamageCap", limitTotal = true }),
				},
				["support_channelling_damage_cap"] = {
					mod("Multiplier:BurgeonDamageCap", "BASE"),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_chanelling_damage_+%_final_per_second_channelling", 10 },
				{ "support_channelling_damage_cap", 40 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportBurningRunesPlayer"] = {
	name = "Burning Inscription",
	description = "Supports any Skill which creates Runic Inscriptions when Cast, causing those Runic Inscriptions to trigger Burning Inscription when they expire. Burning Inscription is a Spell which creates Ignited Ground based off of your Maximum Mana.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.AppliesCurse, SkillType.CreatesGroundRune, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 10, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Support",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			stats = {
				"triggers_burning_runes_on_placing_ground_rune",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}skills["TriggeredBurningRunesPlayer"] = {
	name = "Burning Inscription",
	hidden = true,
	description = "Create Ignited Ground Igniting Enemies based on a percentage of your Maximum Mana.",
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Triggered] = true, [SkillType.Damage] = true, [SkillType.Area] = true, [SkillType.Fire] = true, [SkillType.SkillGrantedBySupport] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { levelRequirement = 0, },
		[3] = { levelRequirement = 0, },
		[4] = { levelRequirement = 0, },
		[5] = { levelRequirement = 0, },
		[6] = { levelRequirement = 0, },
		[7] = { levelRequirement = 0, },
		[8] = { levelRequirement = 0, },
		[9] = { levelRequirement = 0, },
		[10] = { levelRequirement = 0, },
		[11] = { levelRequirement = 0, },
		[12] = { levelRequirement = 0, },
		[13] = { levelRequirement = 0, },
		[14] = { levelRequirement = 0, },
		[15] = { levelRequirement = 0, },
		[16] = { levelRequirement = 0, },
		[17] = { levelRequirement = 0, },
		[18] = { levelRequirement = 0, },
		[19] = { levelRequirement = 0, },
		[20] = { levelRequirement = 0, },
		[21] = { levelRequirement = 0, },
		[22] = { levelRequirement = 0, },
		[23] = { levelRequirement = 0, },
		[24] = { levelRequirement = 0, },
		[25] = { levelRequirement = 0, },
		[26] = { levelRequirement = 0, },
		[27] = { levelRequirement = 0, },
		[28] = { levelRequirement = 0, },
		[29] = { levelRequirement = 0, },
		[30] = { levelRequirement = 0, },
		[31] = { levelRequirement = 0, },
		[32] = { levelRequirement = 0, },
		[33] = { levelRequirement = 0, },
		[34] = { levelRequirement = 0, },
		[35] = { levelRequirement = 0, },
		[36] = { levelRequirement = 0, },
		[37] = { levelRequirement = 0, },
		[38] = { levelRequirement = 0, },
		[39] = { levelRequirement = 0, },
		[40] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Burning Inscription",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "burning_runes",
			statMap = {
				["support_burning_runes_base_fire_damage_equal_to_%_maximum_mana"] = {
					mod("IgniteFireHitDamage", "OVERRIDE", nil, 0, 0, { type = "PercentStat", stat = "Mana", percent = 1 }),
				},
			},
			baseFlags = {
				duration = true,
			},
			baseMods = {
				mod("EnemyIgniteChance", "BASE", 100),
				mod("IgniteStacks", "OVERRIDE", 1),
				flag("NeverCrit"),
			},
			constantStats = {
				{ "triggered_by_burning_runes_support_%", 100 },
				{ "active_skill_base_area_of_effect_radius", 20 },
				{ "base_skill_effect_duration", 4000 },
				{ "support_burning_runes_base_fire_damage_equal_to_%_maximum_mana", 20 },
				{ "ground_fire_art_variation", 0 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 3.4519999027252, },
				[3] = { actorLevel = 6.7670001983643, },
				[4] = { actorLevel = 10.307999610901, },
				[5] = { actorLevel = 14.074999809265, },
				[6] = { actorLevel = 18.068000793457, },
				[7] = { actorLevel = 22.287000656128, },
				[8] = { actorLevel = 26.732000350952, },
				[9] = { actorLevel = 31.40299987793, },
				[10] = { actorLevel = 36.299999237061, },
				[11] = { actorLevel = 41.423000335693, },
				[12] = { actorLevel = 46.771999359131, },
				[13] = { actorLevel = 52.34700012207, },
				[14] = { actorLevel = 58.147998809814, },
				[15] = { actorLevel = 64.175003051758, },
				[16] = { actorLevel = 70.428001403809, },
				[17] = { actorLevel = 76.906997680664, },
				[18] = { actorLevel = 83.611999511719, },
				[19] = { actorLevel = 90.542999267578, },
				[20] = { actorLevel = 97.699996948242, },
				[21] = { actorLevel = 105.08300018311, },
				[22] = { actorLevel = 112.69200134277, },
				[23] = { actorLevel = 120.52700042725, },
				[24] = { actorLevel = 128.58799743652, },
				[25] = { actorLevel = 136.875, },
				[26] = { actorLevel = 145.38800048828, },
				[27] = { actorLevel = 154.12699890137, },
				[28] = { actorLevel = 163.09199523926, },
				[29] = { actorLevel = 172.28300476074, },
				[30] = { actorLevel = 181.69999694824, },
				[31] = { actorLevel = 191.34300231934, },
				[32] = { actorLevel = 201.21200561523, },
				[33] = { actorLevel = 211.30700683594, },
				[34] = { actorLevel = 221.62800598145, },
				[35] = { actorLevel = 232.17500305176, },
				[36] = { actorLevel = 242.94799804688, },
				[37] = { actorLevel = 253.94700622559, },
				[38] = { actorLevel = 265.17199707031, },
				[39] = { actorLevel = 276.62298583984, },
				[40] = { actorLevel = 288.29998779297, },
			},
		},
	}
}
skills["SupportCapacitorPlayer"] = {
	name = "Capacitor",
	description = "Supports Invocation Skills which Trigger other Skills. Supported Skills have significantly higher Maximum Energy.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Invocation, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 30, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Capacitor",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "skill_maximum_energy_+%", 80 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportCatharsisPlayer"] = {
	name = "Catharsis",
	description = "Supports any Skill you use yourself, causing supported Skill to detonate Volatility that is present on you. Supported Skill can only be used while you have a certain amount of Volatility and cannot support Skills which already have another Condition. Does not support Triggered Skills.",
	color = 3,
	support = true,
	requireSkillTypes = { },
	addSkillTypes = { SkillType.HasUsageCondition, SkillType.SupportedByCleanse, },
	excludeSkillTypes = { SkillType.HasUsageCondition, SkillType.SupportedByCleanse, SkillType.NOT, SkillType.AND, SkillType.HasReservation, SkillType.UsedByTotem, SkillType.Triggered, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Catharsis",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "skill_conditional_requires_X_volatility", 20 },
			},
			stats = {
				"skill_detonate_random_volatility_amount_on_use",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportAddedChaosDamagePlayer"] = {
	name = "Chaos Infusion",
	description = "Supports Attacks, causing them to Gain Chaos Damage but deal less Damage of other Types.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
		[2] = { manaMultiplier = 20, levelRequirement = 3, },
		[3] = { manaMultiplier = 20, levelRequirement = 6, },
		[4] = { manaMultiplier = 20, levelRequirement = 10, },
		[5] = { manaMultiplier = 20, levelRequirement = 14, },
		[6] = { manaMultiplier = 20, levelRequirement = 18, },
		[7] = { manaMultiplier = 20, levelRequirement = 22, },
		[8] = { manaMultiplier = 20, levelRequirement = 26, },
		[9] = { manaMultiplier = 20, levelRequirement = 31, },
		[10] = { manaMultiplier = 20, levelRequirement = 36, },
		[11] = { manaMultiplier = 20, levelRequirement = 41, },
		[12] = { manaMultiplier = 20, levelRequirement = 46, },
		[13] = { manaMultiplier = 20, levelRequirement = 52, },
		[14] = { manaMultiplier = 20, levelRequirement = 58, },
		[15] = { manaMultiplier = 20, levelRequirement = 64, },
		[16] = { manaMultiplier = 20, levelRequirement = 66, },
		[17] = { manaMultiplier = 20, levelRequirement = 72, },
		[18] = { manaMultiplier = 20, levelRequirement = 78, },
		[19] = { manaMultiplier = 20, levelRequirement = 84, },
		[20] = { manaMultiplier = 20, levelRequirement = 90, },
		[21] = { manaMultiplier = 20, levelRequirement = 90, },
		[22] = { manaMultiplier = 20, levelRequirement = 90, },
		[23] = { manaMultiplier = 20, levelRequirement = 90, },
		[24] = { manaMultiplier = 20, levelRequirement = 90, },
		[25] = { manaMultiplier = 20, levelRequirement = 90, },
		[26] = { manaMultiplier = 20, levelRequirement = 90, },
		[27] = { manaMultiplier = 20, levelRequirement = 90, },
		[28] = { manaMultiplier = 20, levelRequirement = 90, },
		[29] = { manaMultiplier = 20, levelRequirement = 90, },
		[30] = { manaMultiplier = 20, levelRequirement = 90, },
		[31] = { manaMultiplier = 20, levelRequirement = 90, },
		[32] = { manaMultiplier = 20, levelRequirement = 90, },
		[33] = { manaMultiplier = 20, levelRequirement = 90, },
		[34] = { manaMultiplier = 20, levelRequirement = 90, },
		[35] = { manaMultiplier = 20, levelRequirement = 90, },
		[36] = { manaMultiplier = 20, levelRequirement = 90, },
		[37] = { manaMultiplier = 20, levelRequirement = 90, },
		[38] = { manaMultiplier = 20, levelRequirement = 90, },
		[39] = { manaMultiplier = 20, levelRequirement = 90, },
		[40] = { manaMultiplier = 20, levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Chaos Infusion",
			baseEffectiveness = 0.47119998931885,
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_chaos_support_non_chaos_damage_+%_final"] = {
					mod("ColdDamage", "MORE", nil),
					mod("LightningDamage", "MORE", nil),
					mod("FireDamage", "MORE", nil),
					mod("PhysicalDamage", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "non_skill_base_all_damage_%_to_gain_as_chaos_with_attacks", 25 },
				{ "support_chaos_support_non_chaos_damage_+%_final", -50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportChaosMasteryPlayer"] = {
	name = "Chaos Mastery",
	description = "Supports Chaos skills, granting them an additional level.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Chaos, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Chaos Mastery",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "supported_chaos_skill_gem_level_+", 1 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportChaoticFreezePlayer"] = {
	name = "Chaotic Freeze",
	description = "Supports Spells that deal non-Ailment Chaos damage over time. Enemies taking damage over time from Debuffs caused by supported skills will suffer Freeze build up from Chaos damage Hits from any source. Does not Support Skills used by Minions.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Spell, SkillType.DamageOverTime, SkillType.Chaos, SkillType.AND, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	ignoreMinionTypes = true,
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Chaotic Freeze",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			stats = {
				"support_chaotic_freeze_dots_allow_enemies_to_be_frozen_by_chaos_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportClarityPlayer"] = {
	name = "Clarity",
	description = "Supports Persistent Buff Skills, causing you to gain increased Mana Regeneration while the Skill is active.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Persistent, SkillType.Buff, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { spiritReservationFlat = 10, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Clarity",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_clarity_mana_regeneration_rate_+%"] = {
					mod("ManaRegen", "INC", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Clarity" }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_clarity_mana_regeneration_rate_+%", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportColdExposurePlayer"] = {
	name = "Cold Exposure",
	description = "Supports any skill that Hits enemies, causing it to inflict Cold Exposure when it Critically Hits an enemy.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { SkillType.Duration, },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Cold Exposure",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["inflict_cold_exposure_for_x_ms_on_cold_crit"] = {
					mod("ColdExposureChance", "BASE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "inflict_cold_exposure_for_x_ms_on_cold_crit", 8000 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportAddedColdDamagePlayer"] = {
	name = "Cold Infusion",
	description = "Supports Attacks, causing them to Gain Cold Damage but deal less Fire and Lightning Damage.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Cold Infusion",
			baseEffectiveness = 0.58050000667572,
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_fire_and_lightning_damage_+%_final"] = {
					mod("FireDamage", "MORE", nil),
					mod("LightningDamage", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "non_skill_base_all_damage_%_to_gain_as_cold_with_attacks", 25 },
				{ "support_fire_and_lightning_damage_+%_final", -50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportColdMasteryPlayer"] = {
	name = "Cold Mastery",
	description = "Supports Cold skills, granting them an additional level.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Cold, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Cold Mastery",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "supported_cold_skill_gem_level_+", 1 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportColdPenetrationPlayer"] = {
	name = "Cold Penetration",
	description = "Supports any skill that Hits enemies, making those Hits Penetrate enemy Cold resistance.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Cold Penetration",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "base_reduce_enemy_cold_resistance_%", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportCommandment"] = {
	name = "Commandment",
	description = "Supports Minions which have Command Skills. Supported Minions have massively increased cooldown recovery speed for Command Skills they use, but deal no damage with non-Command Skills.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.CommandableMinion, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Commandment",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_minion_damage_with_non_command_skills_+%_final"] = {
					mod("MinionModifier", "LIST", { mod = mod("Damage", "MORE", nil, 0, 0, {type = "Condition", var = "CommandableSkill", neg = true}) }),
				},
				["minion_command_skill_cooldown_speed_+%"] = {
					mod("MinionModifier", "LIST", { mod = mod("CooldownRecovery", "INC", nil, 0, 0, {type = "Condition", var = "CommandableSkill"}) }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_minion_damage_with_non_command_skills_+%_final", -100 },
				{ "minion_command_skill_cooldown_speed_+%", 100 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportConcentratedEffectPlayer"] = {
	name = "Concentrated Effect",
	description = "Supports any skill with an area of effect, causing that area to be smaller but any area damage it deals to be higher.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Area, SkillType.MinionsCanExplode, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Concentrated Effect",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_concentrated_effect_skill_area_of_effect_+%_final"] = {
					mod("AreaOfEffect", "MORE", nil),
				},
				["support_area_concentrate_area_damage_+%_final"] = {
					mod("Damage", "MORE", nil, ModFlag.Area),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_concentrated_effect_skill_area_of_effect_+%_final", -50 },
				{ "support_area_concentrate_area_damage_+%_final", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportConsideredCastingPlayer"] = {
	name = "Considered Casting",
	description = "Supports Spell Skills that Hit Enemies and you cast yourself, boosting Damage at the cost of Cast Speed.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Spell, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.FixedCastTime, SkillType.Trapped, SkillType.RemoteMined, SkillType.SummonsTotem, SkillType.Triggered, SkillType.UsedByTotem, SkillType.HasReservation, SkillType.ReservationBecomesCost, SkillType.NOT, SkillType.AND, },
	levels = {
		[1] = { manaMultiplier = 15, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Considered Casting",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_slow_cast_cast_speed_+%_final"] = {
					mod("Speed", "MORE", nil, ModFlag.Cast),
				},
				["support_slow_cast_spell_damage_+%_final"] = {
					mod("Damage", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_slow_cast_cast_speed_+%_final", -15 },
				{ "support_slow_cast_spell_damage_+%_final", 35 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportControlledDestructionPlayer"] = {
	name = "Controlled Destruction",
	description = "Supports Spells that Hit enemies, boosting their damage but preventing them from dealing Critical Hits.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Spell, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Controlled Destruction",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_controlled_destruction_spell_damage_+%_final"] = {
					mod("Damage", "MORE", nil, ModFlag.Hit),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_controlled_destruction_spell_damage_+%_final", 25 },
			},
			stats = {
				"global_cannot_crit",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportCorpseConservationPlayer"] = {
	name = "Corpse Conservation",
	description = "Supports skills that consume Corpses, giving them a chance to take effect without destroying the Corpse.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.TargetsDestructibleCorpses, SkillType.TargetsDestructibleRareCorpses, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Corpse Conservation",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "base_chance_to_not_consume_corpse_%", 25 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportCoursingCurrentPlayer"] = {
	name = "Coursing Current",
	description = "Supports any skill that Hits enemies, giving it a chance to also Shock nearby enemies when you Shock an enemy.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Coursing Current",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_%_chance_to_shock_nearby_enemy_on_shock", 50 },
				{ "shock_nearby_enemy_base_area_of_effect_radius", 25 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportCracklingBarrierPlayer"] = {
	name = "Crackling Barrier",
	description = "Supports Channelling Skills. While Channelling Supported Skills, you gain Lightning Thorns based off of your maximum Mana.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Channel, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Minion, SkillType.UsedByTotem, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Crackling Barrier",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "return_%_of_maximum_mana_as_lightning_damage_to_attacker_while_channelling", 15 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportCrazedMinionsPlayer"] = {
	name = "Crazed Minions",
	description = "Supports Minion Skills which summon Reviving Minions. Supported Minions deal significantly more Damage if they have Revived Recently.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Minion, SkillType.Persistent, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.MinionsAreUndamagable, },
	levels = {
		[1] = { manaMultiplier = 15, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Crazed Minions",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_crazed_minions_minion_damage_+%_final_if_revived_recently", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportCursedGroundPlayer"] = {
	name = "Cursed Ground",
	description = "Supports Curse Skills. Supported Curse Skills no longer have an activation delay, instead creating areas which Curse Enemies so long as they are within them.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.AppliesCurse, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Cursed Ground",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			stats = {
				"curse_apply_as_curse_zone",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportDanseMacabrePlayer"] = {
	name = "Danse Macabre",
	description = "Supports Offering Skills. Supported Skills have increased Buff effect and deal more damage, but will target an additional skeletal Minion when cast. If there is not an additional skeletal Minion to target, no damage or Buff effect will be granted.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Offering, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Danse Macabre",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_danse_macabre_offering_skill_damage_+%_final_if_consumed_additional_skeleton"] = {
					mod("Damage", "MORE", nil, 0, 0, { type = "SkillType", skillType = SkillType.Offering }),
				},
				["offering_spells_effect_+%_if_consumed_additional_skeleton"] = {
					mod("BuffEffect", "INC", nil, 0, 0, { type = "SkillType", skillType = SkillType.Offering }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_danse_macabre_offering_skill_damage_+%_final_if_consumed_additional_skeleton", 30 },
				{ "offering_spells_effect_+%_if_consumed_additional_skeleton", 30 },
			},
			stats = {
				"skill_offering_targets_an_additional_skeleton",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportDeathmarchPlayer"] = {
	name = "Deathmarch",
	description = "Supports Skills which create Persistent Minions. Supported Minions will restore Life to nearby Allied Minions on death, as well as removing Ailments from those Minions.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Minion, SkillType.Persistent, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.MinionsAreUndamagable, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Deathmarch",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "minion_on_death_heal_radius", 30 },
				{ "minion_on_death_heal_nearby_minions_for_%_of_life_and_remove_ailments", 25 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportDecayingHexPlayer"] = {
	name = "Decaying Hex",
	description = "Supports Curse skills. Cursed enemies' bodies decay, taking Chaos damage over time.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.AppliesCurse, },
	addSkillTypes = { SkillType.Chaos, SkillType.Damage, SkillType.DamageOverTime, },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 30, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Decaying Hex",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_decaying_hex_base_chaos_damage_per_minute_as_%_of_intelligence_for_8_seconds"] = {
					skill("decay", nil, { type = "PercentStat", stat = "Int", percent = 1 }),
					div = 60,
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_decaying_hex_base_chaos_damage_per_minute_as_%_of_intelligence_for_8_seconds", 6000 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportLastingFrostPlayer"] = {
	name = "Deep Freeze",
	description = "Supports any skill that Hits enemies, causing Freeze it inflicts to last longer.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Deep Freeze",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "freeze_duration_+%", 50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportDerangePlayer"] = {
	name = "Derange",
	description = "Supports Skills which you use yourself which can deal damage. Supported Skills gain an Energy Shield cost equal to your Intelligence, while also dealing more non-Ailment Damage over time based off of your Intelligence. Cannot support Channelling Skills.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.DamageOverTime, SkillType.DegenOnlySpellDamage, SkillType.CrossbowAmmoSkill, SkillType.CrossbowSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Minion, SkillType.UsedByTotem, SkillType.SummonsTotem, SkillType.Triggered, SkillType.Meta, SkillType.Channel, SkillType.Persistent, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Derange",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_shield_sacrifice_damage_over_time_+%_final_per_100_intelligence"] = {
					mod("Damage", "MORE", nil, ModFlag.Dot, 0, { type = "PerStat", stat = "Int", div = 100 }),
				},
			},

			baseFlags = {
			},
			constantStats = {
				{ "support_shield_sacrifice_damage_over_time_+%_final_per_100_intelligence", 8 },
			},
			stats = {
				"gain_energy_shield_cost_equal_to_intelligence",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportDissipatePlayer"] = {
	name = "Dissipate",
	description = "Supports Skills which create Remnants. Created Remnants are more powerful, but vanish quickly.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.GeneratesRemnants, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Dissipate",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "remnant_effect_+%", 35 },
				{ "remnants_vanish_after_X_ms", 3000 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportDrainedAilmentPlayer"] = {
	name = "Drain Ailments",
	description = "Supports Spells that deal non-Ailment damage over time, causing them to consume Ailments on enemies it applies damage over time Debuffs to in order to deal substantially more damage over time.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Spell, SkillType.DamageOverTime, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Drain Ailments",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_drained_ailment_damage_over_time_+%_final_if_ailment_consumed"] = {
					mod("Damage", "MORE", nil, ModFlag.Dot, 0, { type = "Condition", var = "AilmentConsumed" }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_drained_ailment_damage_over_time_+%_final_if_ailment_consumed", 35 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportElementalArmyPlayer"] = {
	name = "Elemental Army",
	description = "Supports skills which create Minions, bolstering the Elemental Resistances of those Minions.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.CreatesMinion, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Elemental Army",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "minion_elemental_resistance_%", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportElementalDischargePlayer"] = {
	name = "Elemental Discharge",
	description = "Supports any Spell that Hits enemies, causing it to consume Elemental Ailments on hit to trigger an Elemental Discharge. Cannot support the skills of Minions.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Spell, SkillType.Damage, SkillType.AND, },
	addSkillTypes = { SkillType.SupportedByElementalDischarge, },
	excludeSkillTypes = { SkillType.SkillConsumesFreeze, SkillType.SkillConsumesIgnite, SkillType.SkillConsumesShock, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Support",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			stats = {
				"consume_ignite_freeze_shock_on_hit_to_trigger_elemental_discharge",
				"active_skill_never_freeze_shock_ignite",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["TriggeredElementalDischargePlayer"] = {
	name = "Elemental Discharge",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.SkillGrantedBySupport] = true, [SkillType.Triggerable] = true, [SkillType.Cooldown] = true, [SkillType.Triggered] = true, [SkillType.Area] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 10, storedUses = 1, levelRequirement = 0, cooldown = 1, },
	},
	statSets = {
		[1] = {
			label = "Elemental Discharge",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "triggered_elemental_discharge",
			statMap = {
				["spell_minimum_base_fire_damage_as_%_of_intelligence"] = {
					mod("FireMin", "BASE", nil, 0, KeywordFlag.Spell, { type = "PercentStat", stat = "Int", percent = 1 }, { type = "Condition", var = "IgniteConsumed" }),
				},
				["spell_maximum_base_fire_damage_as_%_of_intelligence"] = {
					mod("FireMax", "BASE", nil, 0, KeywordFlag.Spell, { type = "PercentStat", stat = "Int", percent = 1 }, { type = "Condition", var = "IgniteConsumed" }),
				},
				["spell_minimum_base_cold_damage_as_%_of_intelligence"] = {
					mod("ColdMin", "BASE", nil, 0, KeywordFlag.Spell, { type = "PercentStat", stat = "Int", percent = 1 }, { type = "Condition", var = "FreezeConsumed" }),
				},
				["spell_maximum_base_cold_damage_as_%_of_intelligence"] = {
					mod("ColdMax", "BASE", nil, 0, KeywordFlag.Spell, { type = "PercentStat", stat = "Int", percent = 1 }, { type = "Condition", var = "FreezeConsumed" }),
				},
				["spell_minimum_base_lightning_damage_as_%_of_intelligence"] = {
					mod("LightningMin", "BASE", nil, 0, KeywordFlag.Spell, { type = "PercentStat", stat = "Int", percent = 1 }, { type = "Condition", var = "ShockConsumed" }),
				},
				["spell_maximum_base_lightning_damage_as_%_of_intelligence"] = {
					mod("LightningMax", "BASE", nil, 0, KeywordFlag.Spell, { type = "PercentStat", stat = "Int", percent = 1 }, { type = "Condition", var = "ShockConsumed" }),
				},
			},
			baseFlags = {
				spell = true,
				area = true,
			},
			constantStats = {
				{ "triggered_by_supported_spell_consuming_ignite_freeze_shock_on_hit_%", 100 },
				{ "active_skill_base_area_of_effect_radius", 20 },
			},
			stats = {
				"spell_minimum_base_fire_damage_as_%_of_intelligence",
				"spell_maximum_base_fire_damage_as_%_of_intelligence",
				"spell_minimum_base_cold_damage_as_%_of_intelligence",
				"spell_maximum_base_cold_damage_as_%_of_intelligence",
				"spell_minimum_base_lightning_damage_as_%_of_intelligence",
				"spell_maximum_base_lightning_damage_as_%_of_intelligence",
				"is_area_damage",
			},
			levels = {
				[1] = { 40, 60, 80, 120, 1, 120, statInterpolation = { 1, 1, 1, 1, 1, 1, }, actorLevel = 1, },
			},
		},
	}
}
skills["SupportElementalFocusPlayer"] = {
	name = "Elemental Focus",
	description = "Supports any skill that Hits enemies, causing it to deal more Elemental Damage but be unable to inflict Elemental Ailments.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Elemental Focus",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_gem_elemental_damage_+%_final"] = {
					mod("ElementalDamage", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_gem_elemental_damage_+%_final", 25 },
			},
			stats = {
				"cannot_inflict_elemental_ailments",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportEmbitterPlayer"] = {
	name = "Embitter",
	description = "Supports Skills which can Damage Enemies with Hits, causing all sources of Damage Gained to instead be treated as Damage Gained as extra Cold for Supported Skills.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowSkill, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.DegenOnlySpellDamage, },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Embitter",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			stats = {
				"all_damage_gained_as_cold_instead",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportEnergyBarrierPlayer"] = {
	name = "Energy Barrier",
	description = "Supports any skill that you can use, causing Energy Shield recharge to begin immediately if you are Stunned while using it.",
	color = 3,
	support = true,
	requireSkillTypes = { },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Instant, SkillType.Persistent, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Energy Barrier",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_no_energy_shield_recharge_delay_for_duration_ms_on_stunned", 2000 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportEnergyRetentionPlayer"] = {
	name = "Energy Retention",
	description = "Supports Meta Skills. Supported Skills gain a chance to partially Refund a portion of Energy cost when Triggering or Invoking other Skills.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.GeneratesEnergy, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 10, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Energy Retention",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "trigger_skills_refund_half_energy_spent_chance_%", 35 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportEnormityPlayer"] = {
	name = "Enormity",
	description = "Supports Skills which summon Persistent Minions. Supported Minions are larger, have more Life, and deal more Damage, but cost significantly more Spirit.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Minion, SkillType.Persistent, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { spiritReservationFlat = 10, reservationMultiplier = 30, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Enormity",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_titanblood_minion_damage_+%_final"] = {
					mod("MinionModifier", "LIST", { mod = mod("Damage", "MORE", nil) }),
				},
				["support_titanblood_minion_life_+%_final"] = {
					mod("MinionModifier", "LIST", { mod = mod("Life", "MORE", nil) }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "minion_actor_scale_+%", 20 },
				{ "support_titanblood_minion_damage_+%_final", 20 },
				{ "support_titanblood_minion_life_+%_final", 20 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportEssenceHarvestPlayer"] = {
	name = "Essence Harvest",
	description = "Supports Skills which create Reviving Persistent Minions. When those Minions die, you restore a percentage of your maximum Mana.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Minion, SkillType.Persistent, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Buff, SkillType.OngoingSkill, SkillType.MinionsAreUndamagable, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { reservationMultiplier = 10, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Essence Harvest",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "recover_mana_%_on_minion_death", 4 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportExcisePlayer"] = {
	name = "Excise",
	description = "Supports any damaging Skill that you use yourself, granting it significantly higher chance to Critically Hit, but causing it to gain a long cooldown. Cannot support Skills which already have a cooldown.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { SkillType.SupportedByExcise, SkillType.Cooldown, },
	excludeSkillTypes = { SkillType.Cooldown, SkillType.SupportedByExcise, SkillType.NOT, SkillType.AND, SkillType.SummonsTotem, SkillType.UsedByTotem, SkillType.Trapped, SkillType.RemoteMined, SkillType.Persistent, SkillType.Vaal, },
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 8, },
	},
	statSets = {
		[1] = {
			label = "Excise",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_crit_cooldown_crit_chance_+%_final"] = {
					mod("CritChance", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_hourglass_display_cooldown_time_ms", 8000 },
				{ "support_crit_cooldown_crit_chance_+%_final", 40 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportExecratePlayer"] = {
	name = "Execrate",
	description = "Supports any damaging Skill that you use yourself, granting it significantly higher chance to inflict Ailments on hit, but causing it to gain a long cooldown. Cannot support Skills which already have a cooldown.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { SkillType.SupportedByExecrate, SkillType.Cooldown, },
	excludeSkillTypes = { SkillType.Cooldown, SkillType.SupportedByExecrate, SkillType.NOT, SkillType.AND, SkillType.SummonsTotem, SkillType.UsedByTotem, SkillType.Trapped, SkillType.RemoteMined, SkillType.Persistent, SkillType.Vaal, },
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 8, },
	},
	statSets = {
		[1] = {
			label = "Execrate",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_ailment_cooldown_ailment_chance_+%_final"] = {
					mod("EnemyIgniteChance", "MORE", nil),
					mod("EnemyShockChance", "MORE", nil),
					mod("BleedChance", "MORE", nil),
					mod("PoisonChance", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_hourglass_display_cooldown_time_ms", 8000 },
				{ "support_ailment_cooldown_ailment_chance_+%_final", 100 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportExpansePlayer"] = {
	name = "Expanse",
	description = "Supports any Skill that you use yourself, granting it significantly higher Area of Effect but causing it to gain a long cooldown. Cannot support Skills which already have a cooldown.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Area, SkillType.MinionsCanExplode, },
	addSkillTypes = { SkillType.SupportedByExpanse, SkillType.Cooldown, },
	excludeSkillTypes = { SkillType.Cooldown, SkillType.SupportedByExpanse, SkillType.NOT, SkillType.AND, SkillType.SummonsTotem, SkillType.UsedByTotem, SkillType.Trapped, SkillType.RemoteMined, SkillType.Persistent, SkillType.Vaal, },
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 6, },
	},
	statSets = {
		[1] = {
			label = "Expanse",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_aoe_cooldown_aoe_+%_final"] = {
					mod("AreaOfEffect", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_hourglass_display_cooldown_time_ms", 8000 },
				{ "support_aoe_cooldown_aoe_+%_final", 50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportExtractionPlayer"] = {
	name = "Extraction",
	description = "Supports Spell Skills which cause Damaging Hits. Using supported Spells will cause you to Sacrifice a portion of your Life on use, gaining extra Chaos Damage in exchange. Does not support Persistent Skills, Minion Skills, or Triggered Skills.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Spell, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.HasReservation, SkillType.Persistent, SkillType.Triggered, SkillType.Attack, SkillType.Minion, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Extraction",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "sacrifice_%_life_on_skill_use", 10 },
				{ "non_skill_base_all_damage_%_to_gain_as_chaos", 35 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportFeedingFrenzyPlayer"] = {
	name = "Feeding Frenzy",
	description = "Supports skills which create Minions, making them deal and take more damage. Cannot support skills which create undamageable Minions.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.CreatesMinion, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.MinionsAreUndamagable, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Feeding Frenzy",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["feeding_frenzy_minion_damage_+%_final"] = {
				mod("MinionModifier", "LIST", { mod = mod("Damage", "MORE", nil) }),
				},
				["feeding_frenzy_minion_damage_taken_+%_final"] = {
					mod("MinionModifier", "LIST", { mod = mod("DamageTaken", "MORE", nil) }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "feeding_frenzy_minion_damage_taken_+%_final", 20 },
				{ "feeding_frenzy_minion_damage_+%_final", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportFieryDeathPlayer"] = {
	name = "Fiery Death",
	description = "Supports any skill that Hits enemies, causing enemies it Ignites to have a chance to explode on death.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.SkillGrantedBySupport, },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Support",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "chance_to_trigger_fiery_death_on_ignited_enemy_death_%", 60 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}skills["TriggeredFieryDeathPlayer"] = {
	name = "Fiery Death",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.SkillGrantedBySupport] = true, [SkillType.Triggerable] = true, [SkillType.Triggered] = true, [SkillType.Fire] = true, [SkillType.TargetsDestructibleCorpses] = true, [SkillType.Area] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 8, levelRequirement = 0, manaMultiplier = 20, },
	},
	statSets = {
		[1] = {
			label = "Explosion",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "triggered_fiery_death",
			baseFlags = {
				spell = true,
				area = true,
			},
			baseMods = {
				skill("explodeCorpse", true),
				skill("corpseExplosionDamageType", "Fire"),
			},
			constantStats = {
				{ "triggered_by_fiery_death_support_%", 100 },
				{ "active_skill_base_area_of_effect_radius", 20 },
				{ "corpse_explosion_monster_life_%", 10 },
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportFireMasteryPlayer"] = {
	name = "Fire Mastery",
	description = "Supports Fire skills, granting them an additional level.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Fire, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Fire Mastery",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "supported_fire_skill_gem_level_+", 1 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportFlukePlayer"] = {
	name = "Fluke",
	description = "Supports Skills which Trigger other Skills. Skills Triggered by Supported Skills have their Damage randomly raised or lowered when Triggered, as well as recovering a random amount of their cost when Triggered.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Triggers, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Fluke",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_randomise_lower_recover_%_cost_on_use", 0 },
				{ "support_randomise_higher_recover_%_cost_on_use", 20 },
				{ "support_randomise_lower_damage_+%_final", -40 },
				{ "support_randomise_higher_damage_+%_final", 60 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportFocusedCursePlayer"] = {
	name = "Focused Curse",
	description = "Supports Curse skills, causing them to Curse enemies more quickly after being cast.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.AppliesCurse, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Focused Curse",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "base_curse_delay_+%", -30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportManaFountainPlayer"] = {
	name = "Font of Mana",
	description = "Supports Skills which create stationary objects. Objects created by supported Skills generate a Font of Mana, creating an Aura which grants Mana regeneration.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.SupportedByFountains, SkillType.Orb, SkillType.SummonsTotem, SkillType.Offering, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Minion, SkillType.Persistent, SkillType.AND, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Font of Mana",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_mana_fountain_mana_regeneration_rate_+%"] = {
					mod("ManaRegen", "INC", nil, 0, 0, { type = "GlobalEffect", effectType = "Aura" }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_mana_fountain_mana_regeneration_rate_+%", 40 },
				{ "support_mana_fountain_radius", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportWallFortressPlayer"] = {
	name = "Fortress",
	description = "Supports skills that create walls in a line, causing them to be created in a circle instead but deal less Hit damage.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Wall, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Fortress",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_wall_fortress_hit_damage_+%_final"] = {
					mod("Damage", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "wall_maximum_length_+%", 100 },
				{ "support_wall_fortress_hit_damage_+%_final", -50 },
			},
			stats = {
				"wall_is_created_in_a_circle_instead",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportFreezeforkPlayer"] = {
	name = "Freezefork",
	description = "Supports Skills which can fire Projectiles. Projectiles from Supported Skills always Fork when the initial Projectile Hits Frozen Enemies, but Supported Skills cannot build up Freeze themselves.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Projectile, },
	addSkillTypes = { SkillType.SupportedByFreezefork, },
	excludeSkillTypes = { SkillType.ProjectileNoCollision, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Freezefork",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "chance_to_fork_from_frozen_enemy_%", 100 },
			},
			stats = {
				"never_freeze",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportChillingIcePlayer"] = {
	name = "Frost Nexus",
	description = "Supports any skill that Hits enemies, causing it to create patches of Chilled Ground around Frozen enemies.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Frost Nexus",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "chilling_ice_freezing_area_base_radius", 30 },
				{ "chilling_ice_create_chilled_ground_on_freeze_base_duration_ms", 8000 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportFrostfirePlayer"] = {
	name = "Frostfire",
	description = "Supports any skill that Hits enemies, causing it to Consume Freeze on Igniting a Frozen enemy to boost the effect of the Ignite.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.SkillConsumesIgnite, SkillType.SupportedByElementalDischarge, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Frostfire",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["ignite_effect_+%_final_against_frozen_enemies"] = {
					mod("AilmentMagnitude", "MORE", nil, 0, KeywordFlag.Ignite, { type = "ActorCondition", actor = "enemy", var = "Frozen" }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "ignite_effect_+%_final_against_frozen_enemies", 100 },
			},
			stats = {
				"never_freeze",
				"remove_freeze_on_ignite",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportGlaciationPlayer"] = {
	name = "Glaciation",
	description = "Supports any skill that Hits enemies, making it more effective at Freezing enemies.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Glaciation",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_hypothermia_hit_damage_freeze_multiplier_+%_final", 50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportGlacierPlayer"] = {
	name = "Glacier",
	description = "Supports any Skill which creates Ice Crystals, causing them to be created with higher Life.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.IceCrystal, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Glacier",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_glacier_ice_crystal_maximum_life_+%_final"] = {
					mod("IceCrystalLife", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_glacier_ice_crystal_maximum_life_+%_final", 100 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportCurseEffectPlayer"] = {
	name = "Heightened Curse",
	description = "Supports Curse Skills, magnifying their power.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.AppliesCurse, SkillType.IsBlasphemy, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { reservationMultiplier = 40, manaMultiplier = 40, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Heightened Curse",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "curse_effect_+%", 25 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportHexBloomPlayer"] = {
	name = "Hex Bloom",
	description = "Supports Curses, spreading their effects when a Cursed enemy dies.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.AppliesCurse, },
	addSkillTypes = { SkillType.Area, },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Hex Bloom",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "transfer_hexes_to_X_nearby_enemies_on_kill", 1 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportHinderPlayer"] = {
	name = "Hinder",
	description = "Supports Spells that deal non-Ailment Chaos damage over time, causing damage over time they inflict to also Hinder enemies. Does not Support Skills used by Minions.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Spell, SkillType.DamageOverTime, SkillType.Chaos, SkillType.AND, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	ignoreMinionTypes = true,
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Hinder",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			stats = {
				"support_hinder_dots_also_apply_hinder",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportHourglassPlayer"] = {
	name = "Hourglass",
	description = "Supports any damaging skill that you use yourself, causing it to deal significantly more damage but gain a long cooldown. Cannot support Skills which already have a cooldown.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.DegenOnlySpellDamage, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { SkillType.Cooldown, SkillType.SupportedByHourglass, },
	excludeSkillTypes = { SkillType.Cooldown, SkillType.SupportedByHourglass, SkillType.NOT, SkillType.AND, SkillType.SummonsTotem, SkillType.UsedByTotem, SkillType.Trapped, SkillType.RemoteMined, SkillType.Persistent, SkillType.Vaal, },
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 10, },
	},
	statSets = {
		[1] = {
			label = "Hourglass",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_hourglass_damage_+%_final"] = {
					mod("Damage", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_hourglass_damage_+%_final", 30 },
				{ "support_hourglass_display_cooldown_time_ms", 10000 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportIceBitePlayer"] = {
	name = "Ice Bite",
	description = "Supports Attacks you use yourself. Freezing an enemy with supported skills infuses all of your Attacks with Cold damage for a short time.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { SkillType.Duration, },
	excludeSkillTypes = { SkillType.Trapped, SkillType.RemoteMined, SkillType.SummonsTotem, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Ice Bite",
			baseEffectiveness = 0.51819998025894,
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_ice_bite_buff_grant_%_added_cold_attack_damage"] = {
					mod("DamageGainAsCold", "BASE", nil, ModFlag.Attack, 0, { type = "Condition", var = "FrozenEnemyRecently" }, { type = "GlobalEffect", effectType = "Buff", effectName = "Ice Bite" }),
				},
				["support_ice_bite_base_buff_duration"] = {
					mod("Duration", "BASE", nil, 0, 0, { type = "Condition", var = "FrozenEnemyRecently" }, { type = "GlobalEffect", effectType = "Buff" }),
					div = 1000,
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_ice_bite_buff_grant_%_added_cold_attack_damage", 35 },
				{ "support_ice_bite_base_buff_duration", 5000 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportIciclePlayer"] = {
	name = "Icicle",
	description = "Supports any Skill which creates Ice Crystals, causing them to be created with lower Life.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.IceCrystal, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Icicle",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_icicle_ice_crystal_maximum_life_+%_final"] = {
					mod("IceCrystalLife", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_icicle_ice_crystal_maximum_life_+%_final", -50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["ViciousHexSupportPlayer"] = {
	name = "Impending Doom",
	description = "Supports Curse skills you cast yourself, causing them to trigger Doom Blast on Cursed enemies when the Curse expires.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.AppliesCurse, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Triggered, SkillType.InbuiltTrigger, SkillType.Aura, },
	levels = {
		[1] = { manaMultiplier = 30, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Support",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			stats = {
				"trigger_vicious_hex_explosion_when_curse_ends",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}skills["DoomBlastPlayer"] = {
	name = "Doom Blast",
	hidden = true,
	description = "Deal Chaos damage in an area around the previously Cursed enemy.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Area] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, [SkillType.Triggered] = true, [SkillType.AreaSpell] = true, [SkillType.Chaos] = true, [SkillType.Cooldown] = true, [SkillType.InbuiltTrigger] = true, [SkillType.SkillGrantedBySupport] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 7, storedUses = 3, levelRequirement = 0, cooldown = 0.15, },
	},
	statSets = {
		[1] = {
			label = "Doom Blast",
			baseEffectiveness = 1.7200000286102,
			incrementalEffectiveness = 0.14000000059605,
			damageIncrementalEffectiveness = 0.006800000090152,
			statDescriptionScope = "doom_blast",
			statMap = {
				["impending_doom_base_added_chaos_damage_%_of_current_mana"] = {
					mod("ChaosMin", "BASE", nil, 0, 0, { type = "PercentStat", stat = "Mana", percentVar = "CurrentManaPercentage" }),
					mod("ChaosMax", "BASE", nil, 0, 0, { type = "PercentStat", stat = "Mana", percentVar = "CurrentManaPercentage" }),
					div = 100,
				},
			},
			baseFlags = {
				spell = true,
				area = true,
			},
			baseMods = {
				skill("currentManaPercentage", true),
			},
			constantStats = {
				{ "impending_doom_base_added_chaos_damage_%_of_current_mana", 15 },
				{ "active_skill_base_area_of_effect_radius", 20 },
			},
			stats = {
				"triggered_vicious_hex_explosion",
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportImpetusPlayer"] = {
	name = "Impetus",
	description = "Supports Meta Skills. Supported Skills generate Energy significantly faster.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.GeneratesEnergy, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 15, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Impetus",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "energy_generated_+%", 40 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportInevitableCriticalsPlayer"] = {
	name = "Inevitable Critical",
	description = "Supports any skill you use yourself that Hits enemies, causing it to accumulate Critical Hit chance over time. ",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Trapped, SkillType.RemoteMined, SkillType.SummonsTotem, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Inevitable Critical",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_inevitable_criticals_critical_strike_chance_+%_per_second"] = {
					mod("CritChance", "INC", nil, 0, 0, { type = "Multiplier", var = "SecondsSinceInevitableCrit", limitVar = "InevitableCritCap", limitTotal = true }),
				},
				["support_inevitable_criticals_critical_strike_chance_+%_cap"] = {
					mod("Multiplier:InevitableCritCap", "BASE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_inevitable_criticals_critical_strike_chance_+%_per_second", 25 },
				{ "support_inevitable_criticals_critical_strike_chance_+%_cap", 100 },
			},
			stats = {
				"supported_by_inevitable_criticals",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportIntenseAgonyPlayer"] = {
	name = "Intense Agony",
	description = "Supports Spells that deal non-Ailment damage over time, causing them to have a shorter duration but deal substantially more damage over time with Debuffs inflicted against enemies on full life.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Spell, SkillType.DamageOverTime, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Intense Agony",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_chaotic_assassination_damage_over_time_+%_final_against_full_life_enemies"] = {
					mod("Damage", "MORE", nil, ModFlag.Dot, 0, { type = "ActorCondition", actor = "enemy", var = "FullLife" }),
				},
				["support_chaotic_assassination_skill_effect_duration_+%_final"] = {
					mod("Duration", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_chaotic_assassination_damage_over_time_+%_final_against_full_life_enemies", 50 },
				{ "support_chaotic_assassination_skill_effect_duration_+%_final", -25 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportLastGaspPlayer"] = {
	name = "Last Gasp",
	description = "Supports skills that create Persistent Minions, causing those Minions to fight on for a short duration before dying when they are fatally wounded.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.CreatesMinion, SkillType.Persistent, SkillType.AND, },
	addSkillTypes = { SkillType.Duration, },
	excludeSkillTypes = { SkillType.MinionsAreUndamagable, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Last Gasp",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_last_gasp_duration_ms", 4000 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportLightningMasteryPlayer"] = {
	name = "Lightning Mastery",
	description = "Supports Lightning skills, granting them an additional level.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Lightning, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Lightning Mastery",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "supported_lightning_skill_gem_level_+", 1 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportLoyaltyPlayer"] = {
	name = "Loyalty",
	description = "Supports Skills that create Companions which can be damaged. Minions created by Supported Skills take a portion of Hit Damage you would otherwise have taken, but have lowered maximum Life.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Companion, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.MinionsAreUndamagable, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Loyalty",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_trusty_companion_minion_life_+%_final"] = {
					mod("MinionModifier", "LIST", { mod = mod("Life", "MORE", nil) }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_trusty_companion_minion_life_+%_final", -30 },
				{ "companion_takes_%_damage_before_you_from_support", 10 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportMagnetismPlayer"] = {
	name = "Magnetism",
	description = "Supports Skills which create Remnants, allowing those Remnants to be collected from further away.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.GeneratesRemnants, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { spiritReservationFlat = 10, manaMultiplier = 10, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Magnetism",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "remnant_pickup_range_+%", 100 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportIncreasedAreaOfEffectPlayer"] = {
	name = "Magnified Effect",
	description = "Supports any skill with an area of effect, making it larger.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Area, SkillType.MinionsCanExplode, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 30, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Magnified Effect",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_area_of_effect_+%_final", 30 },
				{ "support_increased_area_damage_+%_final", 0 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportManaFlarePlayer"] = {
	name = "Mana Flare",
	description = "Supports any Spell that Hits enemies, causing it to trigger a Mana Flare on Critical Hit. The Mana Flare consumes your Mana to deal Fire damage. Cannot support the skills of Minions.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Spell, SkillType.Damage, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.SkillGrantedBySupport, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Support",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			stats = {
				"support_trigger_mana_flare_on_crit",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}skills["TriggeredManaFlarePlayer"] = {
	name = "Mana Flare",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.SkillGrantedBySupport] = true, [SkillType.Triggerable] = true, [SkillType.Cooldown] = true, [SkillType.Triggered] = true, [SkillType.Area] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 7, storedUses = 1, levelRequirement = 0, cooldown = 1, },
	},
	statSets = {
		[1] = {
			label = "Mana Flare",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "triggered_mana_flare",
			statMap = {
				["support_mana_flare_%_of_current_mana_consumed"] = {
					mod("FireMin", "BASE", nil, 0, 0, { type = "PercentStat", stat = "Mana", percentVar = "CurrentManaPercentage" }),
					mod("FireMax", "BASE", nil, 0, 0, { type = "PercentStat", stat = "Mana", percentVar = "CurrentManaPercentage" }),
					div = 100,
				},
			},
			baseFlags = {
				spell = true,
				area = true,
			},
			baseMods = {
				skill("currentManaPercentage", true),
			},
			constantStats = {
				{ "triggered_by_mana_flare_support_%", 100 },
				{ "support_mana_flare_%_of_current_mana_consumed", 25 },
				{ "active_skill_base_area_of_effect_radius", 20 },
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportMinionInstabilityPlayer"] = {
	name = "Minion Instability",
	description = "Supports skills which create Minions, causing them to violently explode when reduced to Low Life.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.CreatesMinion, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.MinionsAreUndamagable, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Minion Instability",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["explode_on_low_life_%_maximum_life_to_deal"] = {
					mod("MinionModifier", "LIST", { mod = mod("Multiplier:MinionInstabilityBaseDamage", "BASE", nil) }),
					mod("ExtraMinionSkill", "LIST", { skillId = "MinionInstability" }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "explode_on_low_life_%_maximum_life_to_deal", 15 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportMinionMasteryPlayer"] = {
	name = "Minion Mastery",
	description = "Supports Minion skills, granting them an additional level.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Minion, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Minion Mastery",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "supported_minion_skill_gem_level_+", 1 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportMinionPactPlayer"] = {
	name = "Minion Pact",
	description = "Supports damaging skills you use yourself, draining the life from one of your Minions on use in order to boost the skill's damage. Only takes effect if you have a Minion with enough life to drain the full amount.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.DamageOverTime, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Trapped, SkillType.RemoteMined, SkillType.SummonsTotem, SkillType.Persistent, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Minion Pact",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_minion_pact_damage_+%_final"] = {
					mod("Damage", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_minion_pact_user_life_%_removed_from_nearby_minion", 100 },
				{ "support_minion_pact_damage_+%_final", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportMusterPlayer"] = {
	name = "Muster",
	description = "Supports Skills which summon Minions. Supported Minions deal more Damage the more different kinds of Persistent Minions you have summoned.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Minion, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Muster",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_varied_troops_damage_+%_final_per_different_persistent_ominion"] = {
					mod("MinionModifier", "LIST", { mod = mod("Damage", "MORE", nil, 0, 0, { type = "Multiplier", actor = "parent", var = "PersistentMinionTypes" }) }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_varied_troops_damage_+%_final_per_different_persistent_ominion", 7 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportMysticismPlayer"] = {
	name = "Mysticism",
	description = "Supports Persistent Buff Skills, causing you to deal increased Spell Damage while on full Energy Shield while the Supported Skill is active.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Persistent, SkillType.Buff, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { spiritReservationFlat = 15, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Mysticism",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_spell_damage_spirit_cost_spell_damage_+%_on_full_energy_shield"] = {
					mod("Damage", "INC", nil, ModFlag.Spell, 0, { type = "Condition", var = "FullEnergyShield" }, { type = "GlobalEffect", effectType = "Buff", effectName = "Mysticism" }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_spell_damage_spirit_cost_spell_damage_+%_on_full_energy_shield", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportNadirPlayer"] = {
	name = "Nadir",
	description = "Supports Skills which gain Stages or Seals. Supported Skills have lower Maximum Stage or Seal count, but have a Minimum Stage or Seal count they cannot fall below.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.GainsStages, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Nadir",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "skill_X_fewer_maximum_stages", 1 },
				{ "skill_X_minimum_stages", 1 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportPhysicalMasteryPlayer"] = {
	name = "Physical Mastery",
	description = "Supports Physical skills, granting them an additional level.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Physical, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Physical Mastery",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "supported_physical_skill_gem_level_+", 1 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportPinpointCriticalPlayer"] = {
	name = "Pinpoint Critical",
	description = "Supports any skill that Hits Enemies, causing it to Critically Hit more often but deal less damage with Critical Hits.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
		[2] = { levelRequirement = 0, },
		[3] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Pinpoint Critical",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_pinpoint_critical_strike_chance_+%_final"] = {
					mod("CritChance", "MORE", nil),
				},
				["support_pinpoint_critical_strike_multiplier_+%_final"] = {
					mod("CritMultiplier", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_pinpoint_critical_strike_chance_+%_final", 60 },
				{ "support_pinpoint_critical_strike_multiplier_+%_final", -30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportPotentialPlayer"] = {
	name = "Potential",
	description = "Supports Skills that you use yourself. Supported Skills will consume a Power Charge on use if possible, and will be much more likely to Critically Hit if they do. Supported Skills cannot generate Power Charges.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.CrossbowAmmoSkill, SkillType.Attack, },
	addSkillTypes = { SkillType.SupportedByPotential, },
	excludeSkillTypes = { SkillType.Minion, SkillType.SummonsTotem, SkillType.UsedByTotem, SkillType.Persistent, SkillType.SkillConsumesPowerChargesOnUse, SkillType.SupportedByPotential, SkillType.NOT, SkillType.AND, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Potential",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["skill_consume_power_charge_to_gain_critical_strike_chance_+%_final"] = {
					mod("CritChance", "MORE", nil, 0, 0, { type = "Multiplier", var = "RemovablePowerCharge", limit = 1 }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "skill_consume_power_charge_to_gain_critical_strike_chance_+%_final", 40 },
			},
			stats = {
				"skill_cannot_generate_power_charges",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportProfanityPlayer"] = {
	name = "Profanity",
	description = "Supports Skills you use yourself. Supported Skills consume a Corpse on use, dealing more Chaos Damage if they do. Cannot support Triggered Skills or Reservation Skills.",
	color = 3,
	support = true,
	requireSkillTypes = { },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Minion, SkillType.Triggered, SkillType.HasReservation, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Profanity",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_chaos_damage_+%_final_if_corpse_consumed_on_use", 30 },
				{ "skill_consume_corpse_within_X_units_on_use", 50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportRimePlayer"] = {
	name = "Rime",
	description = "Supports Skills which create Ground Surfaces, causing Enemies Chilled by those Surfaces to be more easily Frozen, but at the cost of some Chill magnitude.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.CreatesGroundEffect, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Rime",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "chilled_ground_applies_%_freeze_multiplier_taken", -20 },
				{ "support_winterblast_chill_effect_+%_final", -30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportTempestuousTempoPlayer"] = {
	name = "Rising Tempest",
	description = "Supports Skills which deal Damage, raising Elemental Damage dealt for each Skill used Recently of a different Elemental type. Cannot support Minion Skills.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Minion, SkillType.Persistent, },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Rising Tempest",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_elemental_damage_+%_final_per_different_elemental_skill_used_recently"] = {
					mod("ElementalDamage", "MORE", nil, 0, 0, { type = "Multiplier", var = "DifferentElementalSkillUsedRecently" }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_elemental_damage_+%_final_per_different_elemental_skill_used_recently", 12 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportRitualisticCursePlayer"] = {
	name = "Ritualistic Curse",
	description = "Supports Curse skills, causing them to cover a larger area but take longer to Curse enemies.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.AppliesCurse, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Ritualistic Curse",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "base_curse_delay_+%", 30 },
				{ "curse_area_of_effect_+%", 50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportEnergyShieldOnShockKillPlayer"] = {
	name = "Shock Siphon",
	description = "Supports any skill that Hits enemies, causing you to recover Energy Shield when it kills a Shocked enemy.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Shock Siphon",
			baseEffectiveness = 0.3740000128746,
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_recover_%_maximum_energy_shield_killing_shocked_enemies"] = {
					mod("EnergyShieldOnKill", "BASE", nil, 0, 0, { type = "PercentStat", stat = "EnergyShield", percent = 1 }, { type = "ActorCondition", actor = "enemy", var = "Shocked" })
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_recover_%_maximum_energy_shield_killing_shocked_enemies", 4 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportManaLeechPlayer"] = {
	name = "Soul Thief",
	description = "Supports Attacks, causing their Physical damage to Leech Mana.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Soul Thief",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "base_mana_leech_from_physical_attack_damage_permyriad", 800 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportSpellCascadePlayer"] = {
	name = "Spell Cascade",
	description = "Supports Spells that affect an area around you or a target location, causing those Spells to also target additional areas on either side of the target area, along a randomly chosen axis. Cannot support Channelling skills or Remote skills.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Cascadable, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.SummonsTotem, SkillType.UsedByTotem, SkillType.Trapped, SkillType.RemoteMined, SkillType.Vaal, SkillType.Channel, SkillType.Attack, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Spell Cascade",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_spell_cascade_area_of_effect_+%_final"] = {
					mod("AreaOfEffect", "MORE", nil),
				},
				["support_spell_cascade_damage_+%_final"] = {
					mod("Damage", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_spell_cascade_number_of_cascades_per_side", 1 },
				{ "support_spell_cascade_area_of_effect_+%_per_cascade", -20 },
				{ "support_spell_cascade_area_of_effect_+%_final", -20 },
				{ "support_spell_cascade_damage_+%_final", -30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportSpellEchoPlayer"] = {
	name = "Spell Echo",
	description = "Supports Spells that affect an area around you or a target location. Supported Spells echo, causing their effects to happen again after a short delay. Cannot support Channelling skills Remote skills or skills which are Triggered.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Cascadable, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.SummonsTotem, SkillType.UsedByTotem, SkillType.Trapped, SkillType.RemoteMined, SkillType.Vaal, SkillType.Channel, SkillType.Attack, SkillType.Triggered, },
	levels = {
		[1] = { manaMultiplier = 50, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Spell Echo",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_multicast_cast_speed_+%_final"] = {
					mod("Speed", "MORE", nil, ModFlag.Cast),
				},
				["support_spell_echo_area_of_effect_+%_final"] = {
					mod("AreaOfEffect", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_spell_echo_cascade_delay_ms", 500 },
				{ "support_multicast_cast_speed_+%_final", -30 },
				{ "support_spell_echo_number_of_echo_cascades", 1 },
				{ "support_spell_echo_area_of_effect_+%_final", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportStormfirePlayer"] = {
	name = "Stormfire",
	description = "Supports any skill that Hits enemies, prolonging Shocks they inflict on Ignited enemies.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Stormfire",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			stats = {
				"stormfire_support_shocks_from_skill_do_not_expire_on_ingited_targets",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportStripAwayPlayer"] = {
	name = "Strip Away",
	description = "Supports Skills which apply Exposure to Enemies, increasing the effect of applied Exposure.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Spell, SkillType.DegenOnlySpellDamage, SkillType.CrossbowAmmoSkill, SkillType.Attack, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Strip Away",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_exposure_effect_+%", 20 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportStrongHeartedPlayer"] = {
	name = "Strong Hearted",
	description = "Supports Persistent Buff Skills, causing Shock applied to you to last for a shorter duration while the Supported Skill is active.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Persistent, SkillType.Buff, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { spiritReservationFlat = 15, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Strong Hearted",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_shock_protection_spirit_cost_shock_duration_on_self_+%_final"] = {
					mod("SelfShockDuration", "MORE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Strong Hearted" }),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_shock_protection_spirit_cost_shock_duration_on_self_+%_final", -50 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportSuffusePlayer"] = {
	name = "Suffuse",
	description = "Supports Skills that create Ground Surfaces, causing those Surfaces to grow over time, up to a limit.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.CreatesGroundEffect, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 10, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Suffuse",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_ground_effect_area_of_effect_+%_final_per_second", 20 },
				{ "support_ground_effect_area_of_effect_+%_final_per_second_max", 100 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportIncreasedCriticalDamagePlayer"] = {
	name = "Supercritical",
	description = "Supports any skill that Hits enemies, causing it to deal more damage with Critical Hits but have less Critical Hit chance.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Supercritical",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_critical_damage_critical_strike_chance_+%_final"] = {
					mod("CritChance", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "base_critical_strike_multiplier_+", 100 },
				{ "support_critical_damage_critical_strike_chance_+%_final", -20 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportUnbendingPlayer"] = {
	name = "Unbending",
	description = "Supports Spell Skills you use yourself. While using Supported Skills, a percentage of Damage taken is Recouped as Mana, with the percentage scaling higher the longer the Cast time of the Supported Skill. Cannot Support Channelling Skills.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Spell, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Minion, SkillType.UsedByTotem, SkillType.SummonsTotem, SkillType.Channel, },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Unbending",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "damage_taken_%_recouped_as_mana_while_performing_spell_per_250_ms_cast_time", 8 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportUnleashPlayer"] = {
	name = "Unleash",
	description = "Supports Spells you cast yourself, making their effect reoccur when cast. Cannot support Channelling Skills or Skills with a Cooldown.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.CanRapidFire, SkillType.Spell, SkillType.AND, },
	addSkillTypes = { SkillType.HasSeals, SkillType.SupportedByUnleash, },
	excludeSkillTypes = { SkillType.SummonsTotem, SkillType.UsedByTotem, SkillType.Trapped, SkillType.RemoteMined, SkillType.Triggered, SkillType.HasReservation, SkillType.Vaal, SkillType.Instant, SkillType.Channel, SkillType.Cooldown, SkillType.Attack, SkillType.ComboStacking, SkillType.HasSeals, SkillType.SupportedByUnleash, SkillType.NOT, SkillType.AND, },
	levels = {
		[1] = { PvPDamageMultiplier = -40, levelRequirement = 0, manaMultiplier = 50, },
	},
	statSets = {
		[1] = {
			label = "Unleash",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_anticipation_rapid_fire_count"] = {
					mod("SealCount", "BASE", nil),
				},
				["unleash_support_seal_gain_frequency_as_%_of_total_cast_time"] = {
					mod("SealGainFrequency", "BASE", nil),
				},
				["support_spell_rapid_fire_repeat_use_damage_+%_final"] = {
					mod("SealRepeatPenalty", "MORE", nil),
				},
			},
			baseFlags = {
			},
			baseMods = {
				flag("HasSeals"),
			},
			constantStats = {
				{ "support_spell_rapid_fire_repeat_use_damage_+%_final", -50 },
				{ "unleash_support_seal_gain_frequency_as_%_of_total_cast_time", 200 },
				{ "support_anticipation_rapid_fire_count", 2 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportUpwellingPlayer"] = {
	name = "Upwelling",
	description = "Supports Persistent Buff Skills, causing your Minions to deal increased Damage while you are not on full Mana while the Supported Skill is active.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Persistent, SkillType.Buff, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	levels = {
		[1] = { spiritReservationFlat = 15, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Upwelling",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_minion_damage_spirit_cost_minion_damage_+%_while_missing_mana", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportVerglasPlayer"] = {
	name = "Verglas",
	description = "Supports Skills you use yourself which can Hit Enemies. Supported Skills Gain a percentage of their Damage as Cold Damage when you destroy Ice Crystals.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowSkill, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Minion, SkillType.SummonsTotem, SkillType.UsedByTotem, SkillType.DegenOnlySpellDamage, },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Verglas",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_crystalshatter_buff_damage_%_gained_as_extra_cold_per_2000_crystal_life", 1 },
				{ "support_crystalshatter_buff_duration", 6000 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportVolatilePowerPlayer"] = {
	name = "Volatile Power",
	description = "Supports Skills which you use yourself. On using Supported Skills while they are Empowered, you gain Volatility.",
	color = 3,
	support = true,
	requireSkillTypes = { },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.UsedByTotem, SkillType.Triggered, SkillType.Meta, SkillType.Persistent, },
	ignoreMinionTypes = true,
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Volatile Power",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "gain_X_volatility_on_empowered_skill_use", 5 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportVolatilityPlayer"] = {
	name = "Volatility",
	description = "Supports Skills you use yourself which can cause Damaging Hits. Supported Skills grant Volatility on Critical Hit.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Attack, SkillType.Damage, SkillType.CrossbowSkill, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.Minion, SkillType.Persistent, SkillType.AND, SkillType.UsedByTotem, },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Volatility",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "instability_on_critical_%_chance", 100 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportWildfirePlayer"] = {
	name = "Wildfire",
	description = "Supports any skill that Hits enemies, Spreading Ignites it inflicts to nearby enemies after a delay.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { SkillType.Area, },
	excludeSkillTypes = { },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
		[2] = { manaMultiplier = 20, levelRequirement = 3, },
		[3] = { manaMultiplier = 20, levelRequirement = 6, },
		[4] = { manaMultiplier = 20, levelRequirement = 10, },
		[5] = { manaMultiplier = 20, levelRequirement = 14, },
		[6] = { manaMultiplier = 20, levelRequirement = 18, },
		[7] = { manaMultiplier = 20, levelRequirement = 22, },
		[8] = { manaMultiplier = 20, levelRequirement = 26, },
		[9] = { manaMultiplier = 20, levelRequirement = 31, },
		[10] = { manaMultiplier = 20, levelRequirement = 36, },
		[11] = { manaMultiplier = 20, levelRequirement = 41, },
		[12] = { manaMultiplier = 20, levelRequirement = 46, },
		[13] = { manaMultiplier = 20, levelRequirement = 52, },
		[14] = { manaMultiplier = 20, levelRequirement = 58, },
		[15] = { manaMultiplier = 20, levelRequirement = 64, },
		[16] = { manaMultiplier = 20, levelRequirement = 66, },
		[17] = { manaMultiplier = 20, levelRequirement = 72, },
		[18] = { manaMultiplier = 20, levelRequirement = 78, },
		[19] = { manaMultiplier = 20, levelRequirement = 84, },
		[20] = { manaMultiplier = 20, levelRequirement = 90, },
		[21] = { manaMultiplier = 20, levelRequirement = 90, },
		[22] = { manaMultiplier = 20, levelRequirement = 90, },
		[23] = { manaMultiplier = 20, levelRequirement = 90, },
		[24] = { manaMultiplier = 20, levelRequirement = 90, },
		[25] = { manaMultiplier = 20, levelRequirement = 90, },
		[26] = { manaMultiplier = 20, levelRequirement = 90, },
		[27] = { manaMultiplier = 20, levelRequirement = 90, },
		[28] = { manaMultiplier = 20, levelRequirement = 90, },
		[29] = { manaMultiplier = 20, levelRequirement = 90, },
		[30] = { manaMultiplier = 20, levelRequirement = 90, },
		[31] = { manaMultiplier = 20, levelRequirement = 90, },
		[32] = { manaMultiplier = 20, levelRequirement = 90, },
		[33] = { manaMultiplier = 20, levelRequirement = 90, },
		[34] = { manaMultiplier = 20, levelRequirement = 90, },
		[35] = { manaMultiplier = 20, levelRequirement = 90, },
		[36] = { manaMultiplier = 20, levelRequirement = 90, },
		[37] = { manaMultiplier = 20, levelRequirement = 90, },
		[38] = { manaMultiplier = 20, levelRequirement = 90, },
		[39] = { manaMultiplier = 20, levelRequirement = 90, },
		[40] = { manaMultiplier = 20, levelRequirement = 90, },
	},
	statSets = {
		[1] = {
			label = "Wildfire",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_ignite_proliferation_radius", 15 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportWildshardsPlayer"] = {
	name = "Wildshards",
	description = "Supports Spell Skills that fire Projectiles. Supported Skills have a chance to fire many additional Projectiles in a circle.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Spell, SkillType.Projectile, SkillType.AND, },
	addSkillTypes = { },
	excludeSkillTypes = { SkillType.ProjectilesNumberModifiersNotApplied, },
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Wildshards",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "spell_skill_%_chance_to_fire_8_additional_projectiles_in_nova", 20 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportWitheringTouchPlayer"] = {
	name = "Withering Touch",
	description = "Supports any skill that Hits enemies, giving it a chance to Wither enemies on Hit.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Damage, SkillType.Attack, SkillType.CrossbowAmmoSkill, },
	addSkillTypes = { SkillType.Duration, },
	excludeSkillTypes = { },
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Withering Touch",
			incrementalEffectiveness = 0.092720001935959,
			statDescriptionScope = "gem_stat_descriptions",
			statMap = {
				["support_withering_touch_damage_+%_final"] = {
					mod("Damage", "MORE", nil),
				},
			},
			baseFlags = {
			},
			constantStats = {
				{ "support_withering_touch_damage_+%_final", -25 },
				{ "support_withered_base_duration_ms", 4000 },
				{ "withered_on_hit_chance_%_for_every_100%_target_ailment_threshold_dealt_as_chaos_damage", 175 },
			},
			stats = {
				"wither_on_hit_chance_rollovercapped",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SupportZenithPlayer"] = {
	name = "Zenith",
	description = "Supports Spell Skills. Supported Skills deal more damage while you are above 90% of your Maximum Mana. Does not Support Skills used by Minions.",
	color = 3,
	support = true,
	requireSkillTypes = { SkillType.Spell, SkillType.Damage, SkillType.AND, SkillType.DegenOnlySpellDamage, },
	addSkillTypes = { },
	excludeSkillTypes = { },
	ignoreMinionTypes = true,
	levels = {
		[1] = { manaMultiplier = 20, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Zenith",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "gem_stat_descriptions",
			baseFlags = {
			},
			constantStats = {
				{ "support_spell_damage_+%_final_while_above_90%_maximum_mana", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}