-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="Ice Crystals have {0} maximum Life"
			},
			[2]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Ice Crystal has {0} maximum Life"
			}
		},
		stats={
			[1]="frost_wall_maximum_life",
			[2]="frozen_locus_crystal_display_stat"
		}
	},
	["frost_wall_maximum_life"]=1,
	["frozen_locus_crystal_display_stat"]=1,
	parent="skill_stat_descriptions"
}