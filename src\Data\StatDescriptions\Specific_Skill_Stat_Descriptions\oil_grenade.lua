-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Oil spray radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Oil spray radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[3]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Oil and Oil Ground Duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Oil and Oil Ground Duration is {0} seconds"
			}
		},
		stats={
			[1]="base_secondary_skill_effect_duration"
		}
	},
	[4]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Fuse duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fuse duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[5]={
		[1]={
		},
		stats={
			[1]="grenade_fuse_duration"
		}
	},
	[6]={
		[1]={
		},
		stats={
			[1]="secondary_skill_effect_duration"
		}
	},
	[7]={
		[1]={
		},
		stats={
			[1]="secondary_skill_ground_effect_duration"
		}
	},
	[8]={
		[1]={
		},
		stats={
			[1]="secondary_skill_ground_effect_duration"
		}
	},
	["active_skill_area_of_effect_radius"]=1,
	["active_skill_base_area_of_effect_radius"]=2,
	["base_secondary_skill_effect_duration"]=3,
	["base_skill_effect_duration"]=4,
	["grenade_fuse_duration"]=5,
	parent="skill_stat_descriptions",
	["secondary_skill_effect_duration"]=6,
	["secondary_skill_ground_effect_duration"]=8
}