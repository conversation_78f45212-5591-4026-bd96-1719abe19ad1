-- Path of Building
--
-- Active Dexterity skill gems
-- Skill data (c) Grinding Gear Games
--
local skills, mod, flag, skill = ...


#skill AlchemistsBoonPlayer
#set AlchemistsBoonPlayer
#flags area aura
statMap = {
	["skill_alchemists_boon_generate_x_charges_for_any_flask_per_minute"] = {
		mod("FlaskChargesGenerated", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Aura", effectName = "Alchemists Boon" }),
		div = 60,
	},
	--["recovery_from_flasks_applies_to_allies_in_presence_%"] = {
	-- how to apply this in calc perform?
		--mod("FlasksApplyToMinionPercent", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Aura" }),
	--},
},
#baseMod skill("radius", 60)
#mods
#skillEnd

#skill BarragePlayer
#set BarragePlayer
#flags spell duration buff
statMap = {
	--["empower_barrage_maximum_cooldown_ms"] = {
		-- how to implement max cooldown?
		--mod("Cooldown", "MAX", nil),
		--div = 1000,
	--},
	["empower_barrage_base_number_of_barrage_repeats"] = {
		-- need to implement BarrageRepeats
		mod("BarrageRepeats", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Barrage" }),
		flag("SequentialProjectiles", { type = "GlobalEffect", effectType = "Buff", effectName = "Barrage" }),
	},
	["empower_barrage_number_of_barrage_repeats_per_frenzy_charge"] = {
		mod("BarrageRepeats", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Barrage" }, { type = "Multiplier", var = "RemovableFrenzyCharge"}),
	},
	["empower_barrage_cooldown_%of_attack_time"] = {
		-- how to set attack time for this cooldown?
	},
	["empower_barrage_damage_-%_final_with_repeated_projectiles"] = {
		mod("BarrageRepeatDamage", "MORE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Barrage" }),
		mult = -1
	},
	["quality_display_barrage_is_gem"] = {
		-- Display only
	},
},
#mods
#skillEnd

#skill BloodHuntPlayer
#set BloodHuntPlayer
#flags attack melee area
#mods
#set BloodHuntExplosionPlayer
#flags attack melee area
#mods
#skillEnd

#skill BloodhoundsMarkPlayer
#set BloodhoundsMarkPlayer
#flags
#mods
#skillEnd

#skill BloodhoundsMarkExplosionPlayer
#set BloodhoundsMarkExplosionPlayer
#flags nonWeaponAttack
#mods
#skillEnd

#skill CombatFrenzyPlayer
#set CombatFrenzyPlayer
#flags buff
statMap = {
	["skill_combat_frenzy_x_ms_cooldown"] = {
		mod("CombatFrenzyCooldown", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Combat Frenzy" }),
		div = 1000,
	},
},
#mods
#skillEnd

#minionList
#skill SummonBeastPlayer
#set SummonBeastPlayer
#flags spell minion summonBeast duration permanentMinion
#mods
#skillEnd

#skill CullTheWeakPlayer
#set CullTheWeakPlayer
#flags attack melee area
#mods
#skillEnd

#skill DetonatingArrowPlayer
preDamageFunc = function(activeSkill, output)
	activeSkill.skillData.hitTimeMultiplier = activeSkill.skillModList:Sum("BASE", activeSkill.skillCfg, "Multiplier:DetonatingArrowStage")
end,
#set DetonatingArrowPlayer
#flags attack projectile channelRelease
statMap = {
	["detonating_arrow_all_damage_%_to_gain_as_fire_per_stage"] = {
		mod("DamageGainAsFire", "BASE", nil, 0, 0, { type = "Multiplier", var = "DetonatingArrowStage" }),
	},
	["detonating_arrow_max_number_of_stages"] = {
		mod("Multiplier:DetonatingArrowMaxStages", "BASE", nil),
	},
},
#mods
#set DetonatingArrowExplosionPlayer
#flags attack area channelRelease projectile
statMap = {
	["detonating_arrow_max_number_of_stages"] = {
		mod("Multiplier:DetonatingArrowMaxStages", "BASE", nil),
	},
},
#mods
#skillEnd

#skill DisengagePlayer
#set DisengagePlayer
#flags attack area melee
#mods
#set DisengageShockwavePlayer
#flags attack area melee
#mods
#skillEnd

#skill ElectrocutingArrowPlayer
#set ElectrocutingArrowPlayer
#flags attack projectile duration
statMap = {
	["quality_display_base_skill_effect_duration_is_gem"] = {
		-- Display only
	},
},
#mods
#skillEnd

#skill ElementalSiphonPlayer
#set ElementalSiphonPlayer
#flags attack area melee
#mods
#set ElementalSiphonColdPlayer
#flags
#mods
#set ElementalSiphonFirePlayer
#flags
#mods
#set ElementalSiphonLightningPlayer
#flags
#mods
#skillEnd

#skill ElementalSunderingPlayer
#set ElementalSunderingPlayer
#flags
#mods
#set ElementalSunderingColdPlayer
#flags attack area
#mods
#set ElementalSunderingFirePlayer
#flags attack area
#mods
#set ElementalSunderingLightningPlayer
#flags attack area
#mods
#skillEnd

#skill EscapeShotPlayer
#set EscapeShotPlayer
#flags attack projectile area
#mods
#skillEnd

#skill ExplosiveSpearPlayer
#set ExplosiveSpearPlayer
#flags attack projectile
#mods
#set ExplosiveSpearExplodePlayer
#flags attack area projectile
#mods
#set ExplosiveSpearInfusedExplodePlayer
#flags attack area duration projectile
#mods
#skillEnd

#skill FangsOfFrostPlayer
#set FangsOfFrostPlayer
#flags attack melee area
#mods
#set FangsOfFrostBurstPlayer
#flags attack melee area duration
#mods
#skillEnd

#skill FreezingSalvoPlayer
#set FreezingSalvoPlayer
#flags attack projectile area
#mods
#skillEnd

#skill GasArrowPlayer
#set GasArrowPlayer
#flags attack projectile area
#mods
#set GasArrowGasDegenPlayer
#flags attack area duration projectile
#mods
#set GasArrowGasExplosionPlayer
#flags attack area projectile
#mods
#skillEnd

#skill GlacialLancePlayer
#set GlacialLancePlayer
#flags attack projectile duration
#mods
#set GlacialLanceWallsPlayer
#flags attack projectile duration area
#mods
#skillEnd

#skill HeraldOfPlaguePlayer
#set HeraldOfPlaguePlayer
#flags
statMap = {
	["herald_of_agony_poison_on_enemies_you_kill_spread_to_enemies_within_x"] = {
		mod("PoisonProlifRange", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Herald of Plague" }),
	},
},
#mods
#skillEnd

#skill HeraldOfThunderPlayer
#set HeraldOfThunderPlayer
#flags
statMap = {
	["herald_of_thunder_storm_max_hits"] = {
		mod("HeraldOfThunderHits", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Herald of Thunder" }),
	},
},
#mods
#set HeraldOfThunderOnKillPlayer
#flags attack
#mods
#skillEnd

#skill IceShotPlayer
#set IceShotPlayer
#flags attack projectile
#mods
#set IceShotShardPlayer
#flags attack projectile
#mods
#skillEnd

#skill LightningArrowPlayer
#set LightningArrowPlayer
#flags attack projectile
#mods
#set LightningArrowArcPlayer
#flags attack projectile
#mods
#skillEnd

#skill LightningRodPlayer
#set LightningRodPlayer
#flags attack projectile area
#mods
#skillEnd

#skill LightningSpearPlayer
#set LightningSpearPlayer
#flags attack projectile
statMap = {
	["lightning_spear_damage_+%_final_when_charged"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "MultiplierThreshold", var = "RemovableFrenzyCharge", threshold = 1 }),
	},
	["lightning_spear_additional_number_to_split_when_charged"] = {
		mod("SplitCount", "BASE", nil, 0, 0, { type = "MultiplierThreshold", var = "RemovableFrenzyCharge", threshold = 1 }),
	},
	["lightning_burst_display"] = {
		-- Display Only
	},
},
#mods
#set LightningSpearSecondaryProjectilePlayer
#flags attack projectile
#mods
#skillEnd

#skill MagneticSalvoPlayer
#set MagneticSalvoPlayer
#flags attack projectile
#mods
#set MagneticSalvoEmpoweredPlayer
#flags attack projectile
#mods
#skillEnd

#skill PlagueBearerPlayer
#set PlagueBearerPlayer
#flags
statMap = {
	["plague_bearer_maximum_stored_poison_damage"] = {
		mod("PlagueBearerMaxDamage", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Plague Bearer" }),
	},
},
#mods
#skillEnd

#skill PlagueBearerNovaPlayer
#set PlagueBearerNovaPlayer
#flags area
#mods
#skillEnd

#skill PoisonBurstArrowPlayer
#set PoisonBurstArrowPlayer
#flags attack projectile
#mods
#set PoisonBurstArrowCloudPlayer
#flags attack projectile area
#mods
#skillEnd

#skill PrimalStrikesPlayer
#set PrimalStrikesPlayer
#flags attack melee area
#mods
#set PrimalStrikesStagWavePlayer
#flags attack melee area
#mods
#set PrimalStrikesFinalPlayer
#flags attack melee area
#mods
#skillEnd

#skill RainOfArrowsPlayer
#set RainOfArrowsPlayer
#flags attack projectile area
statMap = {
	["rain_of_arrows_projectile_count_multiplier_if_any_frenzy_charge_spent"] = {
		mod("ProjectileNumber", "MORE", nil, 0, 0, { type = "Multiplier", var = "RemovableFrenzyCharge", limit = 1 }),
	},
	["rain_of_arrows_projectile_count_multiplier_per_frenzy_charge"] = {
		mod("ProjectileNumber", "MORE", nil, 0, 0, { type = "Multiplier", var = "RemovableFrenzyCharge" }),
	},
},
#mods
#skillEnd

#skill RakePlayer
#set RakePlayer
#flags attack melee area
statMap = {
	["base_dash_additional_damage_from_distance_+%_final"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "DistanceRamp", ramp = {{0,0},{50,1}} }),
	},
	["quality_display_spear_puncture_is_gem"] = {
		-- Display only
	},
},
#mods
#skillEnd

#skill RapidAssaultPlayer
#set RapidAssaultPlayer
#flags attack melee area
#mods
#set RapidAssaultFinalHitPlayer
#flags attack melee area
#mods
#set RapidAssaultDetonation
#flags attack area
#mods
#skillEnd

#minionList SummonedRhoa
#skill RhoaMountPlayer
#set RhoaMountPlayer
#flags minion
#mods
#skillEnd

#skill ShockchainArrowPlayer
#set ShockchainArrowPlayer
#flags attack projectile
#mods
#set ShockchainArrowBeamPlayer
#flags attack projectile
#mods
#set ShockchainArrowExplosionPlayer
#flags attack projectile
#mods
#skillEnd

#skill SnipePlayer
preDamageFunc = function(activeSkill, output)
	activeSkill.skillData.hitTimeMultiplier = activeSkill.skillData.channelPercentOfAttackTime
end,
#set SnipePlayer
#flags attack projectile channelRelease
#mods
#set SnipeExplosionPlayer
#flags attack projectile area
#baseMod mod("Condition:PerfectTiming", "FLAG", true)
#mods
#skillEnd

#skill SnipersMarkPlayer
#set SnipersMarkPlayer
#flags spell duration mark
statMap = {
	["enemy_additional_critical_strike_multiplier_against_self"] = {
		mod("SelfCritMultiplier", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Curse" }),
	},
},
#baseMod skill("debuff", true)
#mods
#skillEnd

#skill SpearOfSolarisPlayer
#set SpearOfSolarisImpactPlayer
#flags attack projectile area
#mods
#set SpearOfSolarisPulsePlayer
#flags attack projectile area
statMap = {
	["solaris_spear_pulse_delay_ms"] = {
		skill("hitTimeOverride", nil),
		div = 1000,
	},
	["solaris_spear_number_of_pulses"] = {
		-- Display only
	},
},
#mods
#set SpearOfSolarisGroundPlayer
#flags attack projectile area
#baseMod mod("EnemyIgniteChance", "BASE", 100)
#mods
#skillEnd

#skill SpearfieldPlayer
#set SpearfieldPlayer
#flags attack area melee duration
#mods
#set SpearfieldHazardPlayer
#flags attack area
statMap = {
	["spearfield_spear_damage_+%_final_after_half_seconds"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "Condition", var = "SpearOlderThanHalfSecond" }),
	},
	["base_skill_show_average_damage_instead_of_dps"] = {
	},
},
#mods
#skillEnd

#skill SpiralVolleyPlayer
#set SpiralVolleyPlayer
#flags attack projectile
statMap = {
	["spiral_volley_damage_+%_final_when_frenzy_charges_consumed"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "MultiplierThreshold", var = "RemovableFrenzyCharge", threshold = 1 }),
	},
	["spiral_volley_damage_+%_final_per_frenzy_charge_consumed"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "Multiplier", var = "RemovableFrenzyCharge" }),
	},
	["spiral_volley_X_chains_per_frenzy_charge_consumed"] = {
		mod("ChainCountMax", "BASE", nil, 0, 0, { type = "Multiplier", var = "RemovableFrenzyCharge" }),
	},
},
#mods
#skillEnd

#skill StormLancePlayer
#set StormLancePlayer
#flags projectile attack
#mods
#set StormLanceInfusedPlayer
#flags projectile attack duration
statMap = {
	["overcharged_spear_base_frequency_ms"] = {
		skill("hitTimeOverride", nil),
		div = 1000,
	},
},
#mods
#set StormLanceBeamPlayer
#flags projectile attack duration
statMap = {
	["overcharged_spear_base_frequency_ms"] = {
		skill("hitTimeOverride", nil),
		div = 1000,
	},
},
#mods
#set StormLanceDetonatedBeamPlayer
#flags projectile attack
#mods
#skillEnd

#skill StormcallerArrowPlayer
#set StormcallerArrowPlayer
#flags attack projectile
#mods
#set StormcallerArrowBoltPlayer
#flags attack projectile area
#mods
#skillEnd

#skill TameBeastPlayer
#set TameBeastPlayer
#flags
#mods
#skillEnd

#skill ThunderousLeapPlayer
#set ThunderousLeapPlayer
#flags attack area melee
#mods
#skillEnd

#skill TornadoShotPlayer
#set TornadoShotPlayer
#flags attack projectile area
#mods
#set TornadoShotNovaPlayer
#flags attack projectile area duration
#baseMod skill("dotIsArea", true)
#mods
#skillEnd

#skill ToxicGrowthPlayer
#set ToxicGrowthPlayer
#flags attack projectile area
#mods
#skillEnd

#skill TrailOfCaltropsPlayer
#set TrailOfCaltropsPlayer
#flags
#mods
#skillEnd

#skill TriggeredTrailOfCaltropsPlayer
#set TriggeredTrailOfCaltropsPlayer
#flags attack projectile duration
#mods
#skillEnd

#skill TrinityPlayer
#set TrinityPlayer
#flags buff duration
statMap = {
	["trinity_damage_+%_final_to_grant_per_50_resonance"] = {
		mod("ElementalDamage", "MORE", nil, 0, 0, { type = "Multiplier", var = "ResonanceCount", div = 30 },{ type = "GlobalEffect", effectType = "Buff", effectName = "Trinity" }),
	},
	["trinity_attack_speed_+%_while_all_resonance_is_at_least_250_to_grant"] = {
		mod("Speed", "INC", nil, 0, 0, { type = "MultiplierThreshold", var = "ResonanceCount", threshold = 250 },{ type = "GlobalEffect", effectType = "Buff", effectName = "Trinity" }),
	},
	["quality_display_trinity_is_gem"] = {
		-- Display only
	},
	["trinity_loss_per_hit"] = {
		-- Display only
	},
},
#mods
#skillEnd

#skill TwisterPlayer
#set TwisterPlayer
#flags attack area duration projectile
statMap = {
	["twister_gain_%_elemental_damage_of_corresponding_type"] = {
		mod("SkillDamageGainAsCold", "BASE", nil, 0, 0, { type = "Condition", var = "TwisterCold"}),
		mod("SkillDamageGainAsFire", "BASE", nil, 0, 0, { type = "Condition", var = "TwisterFire"}),
		mod("SkillDamageGainAsLightning", "BASE", nil, 0, 0, { type = "Condition", var = "TwisterLightning"}),
	},
	["twister_damage_+%_final_per_whirling_slash_stage"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "Multiplier", var = "WhirlwindStages" }),
	},
},
#mods
#skillEnd

#skill VineArrowPlayer
#set VineArrowPlayer
#flags attack projectile
#mods
#set VineArrowFlowerPlayer
#flags attack projectile duration
statMap = {
	["active_skill_base_slow_debuff_movement_speed_+%_final"] = {
		mod("MovementSpeed", "INC", nil, 0, 0, { type = "GlobalEffect", effectType = "Debuff", effectName = "Vine Arrow"}),
	},
},
#mods
#skillEnd

#skill VoltaicMarkPlayer
#set VoltaicMarkPlayer
#flags spell
statMap = {
	["thaumaturgist_mark_enemies_shocked_chance_+%_final"] = {
		mod("SelfShockChance", "MORE", nil, 0, 0, { type = "GlobalEffect", effectType = "Curse" }),
	},
	["thaumaturgist_mark_enemy_shock_effect_+%_taken"] = {
		mod("SelfShockMagnitude", "INC", nil, 0, 0, { type = "GlobalEffect", effectType = "Curse" }),
	},
},
#mods
#skillEnd

#skill WhirlingSlashPlayer
#set WhirlingSlashPlayer
#flags attack area melee
statMap = {
	["sandstorm_swipe_storm_damage_+%_final_per_stage"] = {
		-- Display Only
	},
},
#mods
#set WhirlingSlashSandstormPlayer
#flags attack area melee
statMap = {
	["sandstorm_swipe_storm_damage_+%_final_per_stage"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "Multiplier", var = "WhirlwindStageAfterFirst", limit = 2 })
	},
	["sandstorm_swipe_storm_radius_+_per_stage"] = {
		mod("radiusExtra", "MORE", nil, 0, 0, { type = "Multiplier", var = "WhirlwindStageAfterFirst", limit = 2 })
	},
	["sandstorm_swipe_max_stages"] = {
		-- Display Only
	},
},
#mods
#skillEnd

#skill WhirlwindLancePlayer
#set WhirlwindLancePlayer
#flags attack projectile
#mods
#set WhirlwindLanceStormPlayer
#flags attack duration melee
statMap = {
	["sandstorm_swipe_storm_damage_+%_final_per_stage"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "Multiplier", var = "WhirlwindStageAfterFirst", limit = 3 })
	},
	["sandstorm_swipe_storm_radius_+_per_stage"] = {
		mod("radiusExtra", "MORE", nil, 0, 0, { type = "Multiplier", var = "WhirlwindStageAfterFirst", limit = 3 })
	},
	["sandstorm_swipe_max_stages"] = {
		-- Display Only
	},
},
#mods
#skillEnd

#skill TriggeredVoltaicMarkNovaPlayer
#set TriggeredVoltaicMarkNovaPlayer
#flags nonWeaponAttack
#mods
#skillEnd

#skill WindDancerPlayer
#set WindDancerPlayer
#flags
statMap = {
	["wind_dancer_evasion_rating_+%_final_per_stage"] = {
		mod("Evasion", "MORE", nil, 0, 0, { type = "Multiplier", var = "WindDancerStacks", limitVar = "WindDancerStacksLimit" }, { type = "GlobalEffect", effectType = "Buff", effectName = "Wind Dancer"}),
	},
	["wind_dancer_maximum_number_of_stages"] = {
		mod("Multiplier:WindDancerStacksLimit", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Wind Dancer"}),
	},
},
#mods
#skillEnd

#skill TriggeredWindDancerPlayer
#set TriggeredWindDancerPlayer
#flags attack area melee
statMap = {
	["wind_dancer_damage_+%_final_per_stage"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "Multiplier", var = "WindDancerStacks", limitVar = "WindDancerStacksLimit" }),
	},
	["wind_dancer_area_of_effect_+%_final_per_stage"] = {
		mod("AreaOfEffect", "MORE", nil, 0, 0, { type = "Multiplier", var = "WindDancerStacks", limitVar = "WindDancerStacksLimit" }),
	},
	["wind_dancer_knockback_+%_final_per_stage"] = {
		mod("EnemyKnockbackDistance", "MORE", nil, 0, 0, { type = "Multiplier", var = "WindDancerStacks", limitVar = "WindDancerStacksLimit" }),
	},
},
#mods
#skillEnd

#skill WindSerpentsFuryPlayer
#set WindSerpentsFuryPlayer
#flags attack area melee
#mods
#set WindSerpentsFurySnakePlayer
#flags attack area melee
#mods
#set WindSerpentsFuryKnockbackExplosionPlayer
#flags attack area melee
#mods
#skillEnd