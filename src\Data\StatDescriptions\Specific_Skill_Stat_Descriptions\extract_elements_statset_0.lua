-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d} metre to Pulse radius"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d} metres to Pulse radius"
			},
			[3]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Pulse radius is {0} metre"
			},
			[4]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Pulse radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius",
			[2]="quality_display_active_skill_base_area_of_effect_radius_is_gem"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Consumes one Ailment"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Consumes up to {0} Ailments"
			}
		},
		stats={
			[1]="elemental_sundering_number_of_explosion_allowed"
		}
	},
	["active_skill_area_of_effect_radius"]=1,
	["active_skill_base_area_of_effect_radius"]=2,
	["elemental_sundering_number_of_explosion_allowed"]=3,
	parent="skill_stat_descriptions",
	["quality_display_active_skill_base_area_of_effect_radius_is_gem"]=2
}