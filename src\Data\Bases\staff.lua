-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games
local itemBases = ...

itemBases["Ashen Staff"] = {
	type = "Staff",
	quality = 20,
	socketLimit = 3,
	tags = { no_physical_spell_mods = true, no_lightning_spell_mods = true, no_cold_spell_mods = true, no_chaos_spell_mods = true, staff = true, twohand = true, default = true, },
	implicit = "Grants Skill: Level (1-20) Firebolt",
	implicitModTypes = { },
	req = { },
}
itemBases["Gelid Staff"] = {
	type = "Staff",
	quality = 20,
	socketLimit = 3,
	tags = { no_fire_spell_mods = true, no_lightning_spell_mods = true, staff = true, no_physical_spell_mods = true, no_chaos_spell_mods = true, twohand = true, default = true, },
	implicit = "Grants Skill: Level (1-20) Freezing Shards",
	implicitModTypes = { },
	req = { },
}
itemBases["Voltaic Staff"] = {
	type = "Staff",
	quality = 20,
	socketLimit = 3,
	tags = { no_fire_spell_mods = true, no_physical_spell_mods = true, staff = true, no_chaos_spell_mods = true, no_cold_spell_mods = true, twohand = true, default = true, },
	implicit = "Grants Skill: Level (1-20) Lightning Bolt",
	implicitModTypes = { },
	req = { },
}
itemBases["Spriggan Staff"] = {
	type = "Staff",
	quality = 20,
	socketLimit = 3,
	tags = { default = true, twohand = true, staff = true, },
	implicit = "Grants Skill: Level (1-20) Firebolt",
	implicitModTypes = { },
	req = { },
}
itemBases["Pyrophyte Staff"] = {
	type = "Staff",
	quality = 20,
	socketLimit = 3,
	tags = { no_physical_spell_mods = true, no_lightning_spell_mods = true, no_cold_spell_mods = true, no_chaos_spell_mods = true, staff = true, twohand = true, default = true, },
	implicit = "Grants Skill: Level (1-20) Living Bomb",
	implicitModTypes = { },
	req = { },
}
itemBases["Chiming Staff"] = {
	type = "Staff",
	quality = 20,
	socketLimit = 3,
	tags = { default = true, twohand = true, staff = true, },
	implicit = "Grants Skill: Level (1-20) Sigil of Power",
	implicitModTypes = { },
	req = { },
}
itemBases["Rending Staff"] = {
	type = "Staff",
	quality = 20,
	socketLimit = 3,
	tags = { no_fire_spell_mods = true, no_lightning_spell_mods = true, staff = true, no_physical_spell_mods = true, no_cold_spell_mods = true, twohand = true, default = true, },
	implicit = "Grants Skill: Level (1-20) Soulrend",
	implicitModTypes = { },
	req = { },
}
itemBases["Reaping Staff"] = {
	type = "Staff",
	quality = 20,
	socketLimit = 3,
	tags = { no_fire_spell_mods = true, no_lightning_spell_mods = true, staff = true, no_chaos_spell_mods = true, no_cold_spell_mods = true, twohand = true, default = true, },
	implicit = "Grants Skill: Level (1-20) Reap",
	implicitModTypes = { },
	req = { },
}
itemBases["Icicle Staff"] = {
	type = "Staff",
	quality = 20,
	socketLimit = 3,
	tags = { no_fire_spell_mods = true, no_lightning_spell_mods = true, staff = true, no_physical_spell_mods = true, no_chaos_spell_mods = true, twohand = true, default = true, },
	implicit = "Grants Skill: Level (1-20) Firebolt",
	implicitModTypes = { },
	req = { },
}
itemBases["Roaring Staff"] = {
	type = "Staff",
	quality = 20,
	socketLimit = 3,
	tags = { default = true, twohand = true, staff = true, },
	implicit = "Grants Skill: Level (1-20) Unleash",
	implicitModTypes = { },
	req = { },
}
itemBases["Paralysing Staff"] = {
	type = "Staff",
	quality = 20,
	socketLimit = 3,
	tags = { no_fire_spell_mods = true, no_physical_spell_mods = true, staff = true, no_chaos_spell_mods = true, no_cold_spell_mods = true, twohand = true, default = true, },
	implicit = "Grants Skill: Level (1-20) Shock Nova",
	implicitModTypes = { },
	req = { },
}
itemBases["Cleric Staff"] = {
	type = "Staff",
	quality = 20,
	socketLimit = 3,
	tags = { default = true, twohand = true, staff = true, },
	implicit = "Grants Skill: Level (1-20) Consecrate",
	implicitModTypes = { },
	req = { },
}
itemBases["Dark Staff"] = {
	type = "Staff",
	quality = 20,
	socketLimit = 3,
	tags = { default = true, twohand = true, staff = true, },
	implicit = "Grants Skill: Level (1-20) Dark Pact",
	implicitModTypes = { },
	req = { },
}
itemBases["Permafrost Staff"] = {
	type = "Staff",
	quality = 20,
	socketLimit = 3,
	tags = { default = true, twohand = true, staff = true, },
	implicit = "Grants Skill: Level (1-20) Heart of Ice",
	implicitModTypes = { },
	req = { },
}

itemBases["Wrapped Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, ezomyte_basetype = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 7, PhysicalMax = 12, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { },
}
itemBases["Long Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, ezomyte_basetype = true, weapon = true, twohand = true, default = true, },
	implicit = "16% increased Melee Strike Range with this weapon",
	implicitModTypes = { { "attack" }, },
	weapon = { PhysicalMin = 9, PhysicalMax = 18, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { dex = 10, },
}
itemBases["Gothic Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, ezomyte_basetype = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 16, PhysicalMax = 26, CritChanceBase = 11.5, AttackRateBase = 1.4, Range = 13, },
	req = { level = 11, dex = 22, int = 11, },
}
itemBases["Crackling Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, maraketh_basetype = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { LightningMin = 13, LightningMax = 54, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 16, dex = 30, int = 14, },
}
itemBases["Crescent Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, maraketh_basetype = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 19, PhysicalMax = 39, CritChanceBase = 10, AttackRateBase = 1.5, Range = 13, },
	req = { level = 20, dex = 37, int = 16, },
}
itemBases["Steelpoint Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, maraketh_basetype = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 28, PhysicalMax = 51, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 28, dex = 51, int = 22, },
}
itemBases["Slicing Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, vaal_basetype = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 29, PhysicalMax = 60, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 33, dex = 60, int = 25, },
}
itemBases["Barrier Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, vaal_basetype = true, weapon = true, twohand = true, default = true, },
	implicit = "+(10-15)% to Block chance",
	implicitModTypes = { { "block" }, },
	weapon = { PhysicalMin = 33, PhysicalMax = 55, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 37, dex = 67, int = 27, },
}
itemBases["Hefty Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 39, PhysicalMax = 80, CritChanceBase = 10, AttackRateBase = 1.35, Range = 13, },
	req = { level = 45, dex = 81, int = 33, },
}
itemBases["Smooth Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 62, PhysicalMax = 84, CritChanceBase = 0, AttackRateBase = 1.5, Range = 13, },
	req = { level = 47, dex = 84, int = 34, },
}
itemBases["Anima Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 47, PhysicalMax = 79, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 52, dex = 93, int = 37, },
}
itemBases["Graceful Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 47, PhysicalMax = 87, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 56, dex = 100, int = 40, },
}
itemBases["Wyrm Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 57, PhysicalMax = 94, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 65, dex = 116, int = 45, },
}
itemBases["Reaching Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicit = "16% increased Melee Strike Range with this weapon",
	implicitModTypes = { { "attack" }, },
	weapon = { PhysicalMin = 35, PhysicalMax = 72, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 45, dex = 81, int = 33, },
}
itemBases["Barbarous Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 43, PhysicalMax = 72, CritChanceBase = 11.5, AttackRateBase = 1.4, Range = 13, },
	req = { level = 48, dex = 86, int = 35, },
}
itemBases["Arcing Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { LightningMin = 30, LightningMax = 57, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 51, dex = 91, int = 36, },
}
itemBases["Waxing Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 40, PhysicalMax = 83, CritChanceBase = 10, AttackRateBase = 1.5, Range = 13, },
	req = { level = 55, dex = 98, int = 39, },
}
itemBases["Bladed Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 45, PhysicalMax = 94, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 59, dex = 105, int = 42, },
}
itemBases["Guardian Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicit = "+(10-15)% to Block chance",
	implicitModTypes = { { "block" }, },
	weapon = { PhysicalMin = 49, PhysicalMax = 82, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 62, dex = 110, int = 44, },
}
itemBases["Sinister Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 55, PhysicalMax = 92, CritChanceBase = 11.5, AttackRateBase = 1.4, Range = 13, },
	req = { level = 67, dex = 135, int = 53, },
}
itemBases["Lunar Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 50, PhysicalMax = 103, CritChanceBase = 10, AttackRateBase = 1.5, Range = 13, },
	req = { level = 72, dex = 150, int = 59, },
}
itemBases["Striking Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicit = "16% increased Melee Strike Range with this weapon",
	implicitModTypes = { { "attack" }, },
	weapon = { PhysicalMin = 53, PhysicalMax = 111, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 77, dex = 165, int = 64, },
}
itemBases["Bolting Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { LightningMin = 43, LightningMax = 172, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 78, dex = 165, int = 64, },
}
itemBases["Aegis Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicit = "+(10-15)% to Block chance",
	implicitModTypes = { { "block" }, },
	weapon = { PhysicalMin = 58, PhysicalMax = 97, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 79, dex = 165, int = 64, },
}
itemBases["Razor Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 65, PhysicalMax = 108, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 77, dex = 165, int = 64, },
}
itemBases["Reaching Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicit = "16% increased Melee Strike Range with this weapon",
	implicitModTypes = { { "attack" }, },
	weapon = { PhysicalMin = 35, PhysicalMax = 72, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 45, dex = 81, int = 33, },
}
itemBases["Barbarous Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 43, PhysicalMax = 72, CritChanceBase = 11.5, AttackRateBase = 1.4, Range = 13, },
	req = { level = 48, dex = 86, int = 35, },
}
itemBases["Arcing Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { LightningMin = 30, LightningMax = 57, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 51, dex = 91, int = 36, },
}
itemBases["Waxing Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 40, PhysicalMax = 83, CritChanceBase = 10, AttackRateBase = 1.5, Range = 13, },
	req = { level = 55, dex = 98, int = 39, },
}
itemBases["Bladed Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 45, PhysicalMax = 94, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 59, dex = 105, int = 42, },
}
itemBases["Guardian Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicit = "+(10-15)% to Block chance",
	implicitModTypes = { { "block" }, },
	weapon = { PhysicalMin = 49, PhysicalMax = 82, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 62, dex = 110, int = 44, },
}
itemBases["Sinister Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 55, PhysicalMax = 92, CritChanceBase = 11.5, AttackRateBase = 1.4, Range = 13, },
	req = { level = 67, dex = 135, int = 53, },
}
itemBases["Lunar Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 50, PhysicalMax = 103, CritChanceBase = 10, AttackRateBase = 1.5, Range = 13, },
	req = { level = 72, dex = 150, int = 59, },
}
itemBases["Striking Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicit = "16% increased Melee Strike Range with this weapon",
	implicitModTypes = { { "attack" }, },
	weapon = { PhysicalMin = 53, PhysicalMax = 111, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 77, dex = 165, int = 64, },
}
itemBases["Bolting Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { LightningMin = 43, LightningMax = 172, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 78, dex = 165, int = 64, },
}
itemBases["Aegis Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicit = "+(10-15)% to Block chance",
	implicitModTypes = { { "block" }, },
	weapon = { PhysicalMin = 58, PhysicalMax = 97, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 79, dex = 165, int = 64, },
}
itemBases["Razor Quarterstaff"] = {
	type = "Staff",
	subType = "Warstaff",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, warstaff = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 65, PhysicalMax = 108, CritChanceBase = 10, AttackRateBase = 1.4, Range = 13, },
	req = { level = 77, dex = 165, int = 64, },
}
