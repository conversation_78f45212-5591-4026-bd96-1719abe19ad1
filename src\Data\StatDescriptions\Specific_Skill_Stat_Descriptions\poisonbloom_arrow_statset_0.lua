-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="active_skill_base_secondary_area_of_effect_radius"
		}
	},
	[2]={
		stats={
			[1]="poisonbloom_arrow_bloom_max_burst_damage_+%_final_from_stored_poison"
		}
	},
	[3]={
		stats={
			[1]="poisonbloom_arrow_max_additional_burst_base_radius_+"
		}
	},
	[4]={
		stats={
			[1]="base_skill_detonation_time"
		}
	},
	["active_skill_base_secondary_area_of_effect_radius"]=1,
	["base_skill_detonation_time"]=4,
	parent="skill_stat_descriptions",
	["poisonbloom_arrow_bloom_max_burst_damage_+%_final_from_stored_poison"]=2,
	["poisonbloom_arrow_max_additional_burst_base_radius_+"]=3
}