-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="base_projectiles_cannot_chain"
		}
	},
	[2]={
		stats={
			[1]="quality_display_base_number_of_projectiles_is_gem"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fires {0} fragments per shot"
			}
		},
		stats={
			[1]="base_number_of_projectiles",
			[2]="skill_can_fire_arrows"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Beam Chains to {0} enemy"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Beam Chains to {0} enemies"
			}
		},
		stats={
			[1]="number_of_chains"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Multiple fragments can Hit the same target\nMultiple Fragments hitting a target simultaneously will combine their damage into a single Hit"
			}
		},
		stats={
			[1]="projectiles_can_shotgun"
		}
	},
	["base_number_of_projectiles"]=3,
	["base_projectiles_cannot_chain"]=1,
	["number_of_chains"]=4,
	parent="skill_stat_descriptions",
	["projectiles_can_shotgun"]=5,
	["quality_display_base_number_of_projectiles_is_gem"]=2,
	["skill_can_fire_arrows"]=3
}