-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Spreads the most Damaging Poison on enemies you kill in a {0} metre radius"
			}
		},
		stats={
			[1]="herald_of_agony_poison_on_enemies_you_kill_spread_to_enemies_within_x"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0}% chance to Hinder enemies on spreading Poison to them"
			}
		},
		stats={
			[1]="hinder_chance_%_on_spreading_poioson"
		}
	},
	["herald_of_agony_poison_on_enemies_you_kill_spread_to_enemies_within_x"]=1,
	["hinder_chance_%_on_spreading_poioson"]=2,
	parent="skill_stat_descriptions"
}