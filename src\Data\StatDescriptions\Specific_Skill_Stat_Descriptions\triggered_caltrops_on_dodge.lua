-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="base_number_of_projectiles"
		}
	},
	[2]={
		stats={
			[1]="triggered_by_generic_ongoing_trigger"
		}
	},
	[3]={
		stats={
			[1]="active_skill_base_secondary_area_of_effect_radius"
		}
	},
	[4]={
		stats={
			[1]="active_skill_secondary_area_of_effect_radius"
		}
	},
	[5]={
		[1]={
		},
		stats={
			[1]="total_number_of_projectiles_to_fire"
		}
	},
	["active_skill_base_secondary_area_of_effect_radius"]=3,
	["active_skill_secondary_area_of_effect_radius"]=4,
	["base_number_of_projectiles"]=1,
	parent="specific_skill_stat_descriptions/triggered_caltrops",
	["total_number_of_projectiles_to_fire"]=5,
	["triggered_by_generic_ongoing_trigger"]=2
}