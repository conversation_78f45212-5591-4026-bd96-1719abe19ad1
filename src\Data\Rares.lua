-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

			return {
-- Helmet
[[
Armour Helmet
Paragon Greathelm
Crafted: true
Prefix: LocalIncreasedPhysicalDamageReductionRating5
Prefix: LocalIncreasedPhysicalDamageReductionRatingPercent5
Prefix: IncreasedLife7
]],[[
Evasion Helmet
Trapper Hood
Crafted: true
Prefix: LocalIncreasedEvasionRating5
Prefix: LocalIncreasedEvasionRatingPercent5
Prefix: IncreasedLife7
]],[[
Energy Shield Helmet
Kamasan Tiara
Crafted: true
Prefix: IncreasedLife7
Prefix: LocalIncreasedEnergyShieldPercent5
Prefix: LocalIncreasedEnergyShield7
]],[[
Armour/Evasion Helmet
Champion Helm
Crafted: true
Prefix: LocalBaseArmourAndEvasionRating3
Prefix: LocalIncreasedArmourAndEvasion5
Prefix: IncreasedLife7
]],[[
Armour/Energy Shield Helmet
Divine Crown
Crafted: true
Prefix: LocalBaseArmourAndEnergyShield3
Prefix: LocalIncreasedArmourAndEnergyShield5
Prefix: IncreasedLife7
]],[[
Evasion/Energy Shield Helmet
Soaring Mask
Crafted: true
Prefix: LocalBaseEvasionRatingAndEnergyShield3
Prefix: LocalIncreasedEvasionAndEnergyShield5_
Prefix: IncreasedLife7
]],
-- Gloves
[[
Armour Gloves
Vaal Mitts
Crafted: true
Prefix: LocalIncreasedPhysicalDamageReductionRating4
Prefix: LocalIncreasedPhysicalDamageReductionRatingPercent5
Prefix: IncreasedLife6
]],[[
Evasion Gloves
Barbed Bracers
Crafted: true
Prefix: LocalIncreasedEvasionRating3
Prefix: LocalIncreasedEvasionRatingPercent5
Prefix: IncreasedLife6
]],[[
Energy Shield Gloves
Vaal Gloves
Crafted: true
Prefix: IncreasedLife6
Prefix: LocalIncreasedEnergyShieldPercent5
Prefix: LocalIncreasedEnergyShield6
]],[[
Armour/Evasion Gloves
Cultist Gauntlets
Crafted: true
Prefix: LocalBaseArmourAndEvasionRating3
Prefix: LocalIncreasedArmourAndEvasion5
Prefix: IncreasedLife6
]],[[
Armour/Energy Shield Gloves
Gleaming Cuffs
Crafted: true
Prefix: LocalBaseArmourAndEnergyShield3_
Prefix: LocalIncreasedArmourAndEnergyShield5
Prefix: IncreasedLife6
]],[[
Evasion/Energy Shield Gloves
Vaal Wraps
Crafted: true
Prefix: LocalBaseEvasionRatingAndEnergyShield3
Prefix: LocalIncreasedEvasionAndEnergyShield5_
Prefix: IncreasedLife6
]],
-- Body Armour
[[
Armour Chest
Glorious Plate
Crafted: true
Prefix: LocalIncreasedPhysicalDamageReductionRating5
Prefix: LocalIncreasedPhysicalDamageReductionRatingPercent5
Prefix: IncreasedLife9
]],[[
Evasion Chest
Exquisite Vest
Crafted: true
Prefix: LocalIncreasedEvasionRating5
Prefix: LocalIncreasedEvasionRatingPercent5
Prefix: IncreasedLife9
]],[[
Energy Shield Chest
Havoc Raiment
Crafted: true
Prefix: IncreasedLife9
Prefix: LocalIncreasedEnergyShieldPercent5
Prefix: LocalIncreasedEnergyShield10
]],[[
Armour/Evasion Chest
Slayer Armour
Crafted: true
Prefix: LocalBaseArmourAndEvasionRating5
Prefix: LocalIncreasedArmourAndEvasion5
Prefix: IncreasedLife9
]],[[
Armour/Energy Shield Chest
Zenith Vestments
Crafted: true
Prefix: LocalBaseArmourAndEnergyShield5
Prefix: LocalIncreasedArmourAndEnergyShield5
Prefix: IncreasedLife9
]],[[
Evasion/Energy Shield Chest
Torment Jacket
Crafted: true
Prefix: LocalBaseEvasionRatingAndEnergyShield5_
Prefix: LocalIncreasedEvasionAndEnergyShield5_
Prefix: IncreasedLife9
]],[[
Movement Speed Chest
Falconer's Jacket
Crafted: true
Prefix: LocalIncreasedEvasionRating5
Prefix: LocalIncreasedEvasionRatingPercent5
Prefix: IncreasedLife9
]],[[
Spirit Chest
Occultist Mantle
Crafted: true
Prefix: LocalBaseArmourAndEnergyShield5
Prefix: LocalIncreasedArmourAndEnergyShield5
Prefix: IncreasedSpirit6
]],
-- Boots
[[
Armour Boots
Vaal Greaves
Crafted: true
Prefix: LocalIncreasedPhysicalDamageReductionRatingPercent5
Prefix: IncreasedLife6
Prefix: MovementVelocity5
]],[[
Evasion Boots
Dragonscale Boots
Crafted: true
Prefix: LocalIncreasedEvasionRatingPercent5
Prefix: IncreasedLife6
Prefix: MovementVelocity5
]],[[
Energy Shield Boots
Sandsworn Sandals
Crafted: true
Prefix: IncreasedLife6
Prefix: LocalIncreasedEnergyShieldPercent5
Prefix: MovementVelocity5
]],[[
Armour/Evasion Boots
Fortress Sabatons
Crafted: true
Prefix: LocalIncreasedArmourAndEvasion5
Prefix: IncreasedLife6
Prefix: MovementVelocity5
]],[[
Armour/Energy Shield Boots
Warlock Leggings
Crafted: true
Prefix: LocalIncreasedArmourAndEnergyShield5
Prefix: IncreasedLife6
Prefix: MovementVelocity5
]],[[
Evasion/Energy Shield Boots
Quickslip Shoes
Crafted: true
Prefix: LocalIncreasedEvasionAndEnergyShield5_
Prefix: IncreasedLife6
Prefix: MovementVelocity5
]],
-- Shields
[[
Armour Shield
Vaal Tower Shield
Crafted: true
Prefix: LocalIncreasedBlockPercentage5
Prefix: LocalIncreasedPhysicalDamageReductionRatingPercent5
Prefix: IncreasedLife8
]],[[
Evasion Shield
Ancient Buckler
Crafted: true
Prefix: LocalIncreasedBlockPercentage5
Prefix: LocalIncreasedEvasionRatingPercent5
Prefix: IncreasedLife8
]],[[
Armour/Evasion Shield
Soaring Targe
Crafted: true
Prefix: LocalIncreasedBlockPercentage5
Prefix: LocalIncreasedArmourAndEvasion5
Prefix: IncreasedLife8
]],[[
Armour/Energy Shield Shield
Vaal Crest Shield
Crafted: true
Prefix: LocalIncreasedBlockPercentage5
Prefix: LocalIncreasedArmourAndEnergyShield5
Prefix: IncreasedLife8
]],
-- Focus
[[
+2 Energy Shield
Hallowed Focus
Crafted: true
Prefix: LocalIncreasedEnergyShield8
Prefix: LocalIncreasedEnergyShieldPercent6
Suffix: GlobalSpellGemsLevel2
]],
-- Amulets
[[
Strength Amulet
Amber Amulet
Crafted: true
Suffix: Strength5
]],[[
Dexterity Amulet
Jade Amulet
Crafted: true
Suffix: Dexterity5
]],[[
Intelligence Amulet
Lapis Amulet
Crafted: true
Suffix: Intelligence5
]],[[
Attribute Amulet
Stellar Amulet
Crafted: true
Suffix: AllAttributes5
]],[[
Spirit Amulet
Solar Amulet
Crafted: true
Prefix: IncreasedSpirit4 
]],[[
Mana Amulet
Azure Amulet
Crafted: true
Prefix: IncreasedMana7
Suffix: Intelligence5
Suffix: ManaRegeneration4
]],[[
Energy Shield Amulet
Lunar Amulet
Crafted: true
Prefix: IncreasedEnergyShield7
Prefix: IncreasedEnergyShieldPercent5
Suffix: Intelligence5
]],
-- Rings
[[
Mana Ring
Lazuli Ring
Crafted: true
Prefix: IncreasedMana7
Suffix: Intelligence5
Suffix: ManaRegeneration4
]],[[
Resistance Ring
Ruby Ring
Crafted: true
Suffix: FireResist5
Suffix: ColdResist5
Suffix: LightningResist5
]],[[
Resistance Ring
Sapphire Ring
Crafted: true
Suffix: FireResist5
Suffix: ColdResist5
Suffix: LightningResist5
]],[[
Resistance Ring
Topaz Ring
Crafted: true
Suffix: FireResist5
Suffix: ColdResist5
Suffix: LightningResist5
]],[[
Resistance Ring
Prismatic Ring
Crafted: true
Suffix: FireResist5
Suffix: ColdResist5
Suffix: LightningResist5
]],[[
Chaos Resistance Ring
Amethyst Ring
Crafted: true
Suffix: ChaosResist4
]],[[
Ring
Unset Ring
Crafted: true
]],[[
Ring
Pearl Ring
Crafted: true
Suffix: IncreasedCastSpeed3
]],[[
Ring
Emerald Ring
Crafted: true
Prefix: IncreasedAccuracy6
]],[[
Ring
Iron Ring
Crafted: true
Prefix: AddedPhysicalDamage5
Prefix: IncreasedAccuracy4
]],
-- Belts
[[
Belt
Heavy Belt
Crafted: true
Prefix: IncreasedLife7
Suffix: Strength7
Suffix: StunThreshold7
]],[[
Belt
Plate Belt
Crafted: true
Prefix: IncreasedPhysicalDamageReductionRating5
Prefix: IncreasedLife7
]],[[
Belt
Fine Belt
Crafted: true
Prefix: IncreasedLife7
]],
-- Quivers
[[
+2 Poison Quiver
Toxic Quiver
Crafted: true
Prefix: DamageWithBows4
Suffix: IncreasedAttackSpeed3
Suffix: GlobalProjectileSkillGemLevel2
]],[[
+2 Critical Quiver
Visceral Quiver
Crafted: true
Prefix: DamageWithBows4
Suffix: AttackCriticalStrikeMultiplier3
Suffix: AttackCriticalStrikeChance3
Suffix: GlobalProjectileSkillGemLevel2
]],
-- Sceptres
[[
+4 Minion Sceptre
Rattling Sceptre
Crafted: true
Prefix: LocalIncreasedSpiritPercent5
Prefix: NearbyAlliesAllDamage5
Suffix: GlobalMinionSpellSkillGemLevelWeapon4
]],[[
Discipline Sceptre
Stoic Sceptre
Crafted: true
]],[[
Malice Sceptre
Omen Sceptre
Crafted: true
Suffix: NearbyAlliesCriticalMultiplier5
Suffix: NearbyAlliesCriticalStrikeChance5
]],[[
Purity Aura Sceptre
Shrine Sceptre
Variant: Purity of Fire
Variant: Purity of Ice
Variant: Purity of Lightning
Crafted: true
Prefix: LocalIncreasedSpiritPercent6
Suffix: NearbyAlliesAllResistances5
Implicits: 3
{variant:1}Grants Skill: Level (1-20) Purity of Fire
{variant:2}Grants Skill: Level (1-20) Purity of Ice
{variant:3}Grants Skill: Level (1-20) Purity of Lightning 
]],
-- Weapons

-- Bows
[[
+4 Physical Bow
Warmonger Bow
Crafted: true
Prefix: LocalIncreasedPhysicalDamagePercent5
Prefix: LocalIncreasedPhysicalDamagePercentAndAccuracyRating5
Prefix: LocalAddedPhysicalDamage6
Suffix: LocalIncreasedAttackSpeed2
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalProjectileSkillGemLevelWeapon4
]],[[
+4 Elemental Bow
Warmonger Bow
Crafted: true
Prefix: LocalAddedFireDamage8
Prefix: LocalAddedColdDamage8
Prefix: LocalAddedLightningDamage8
Suffix: LocalIncreasedAttackSpeed2
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalProjectileSkillGemLevelWeapon4
]],[[
+4 Physical Bow
Gemini Bow
Crafted: true
Prefix: LocalIncreasedPhysicalDamagePercent5
Prefix: LocalIncreasedPhysicalDamagePercentAndAccuracyRating5
Prefix: LocalAddedPhysicalDamage6
Suffix: LocalIncreasedAttackSpeed2
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalProjectileSkillGemLevelWeapon4
]],[[
+4 Elemental Bow
Gemini Bow
Crafted: true
Prefix: LocalAddedFireDamage8
Prefix: LocalAddedColdDamage8
Prefix: LocalAddedLightningDamage8
Suffix: LocalIncreasedAttackSpeed2
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalProjectileSkillGemLevelWeapon4
]],[[
+4 Physical Bow
Guardian Bow
Crafted: true
Prefix: LocalIncreasedPhysicalDamagePercent5
Prefix: LocalIncreasedPhysicalDamagePercentAndAccuracyRating5
Prefix: LocalAddedPhysicalDamage6
Suffix: LocalIncreasedAttackSpeed2
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalProjectileSkillGemLevelWeapon4
]],[[
+4 Elemental Bow
Guardian Bow
Crafted: true
Prefix: LocalAddedFireDamage8
Prefix: LocalAddedColdDamage8
Prefix: LocalAddedLightningDamage8
Suffix: LocalIncreasedAttackSpeed2
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalProjectileSkillGemLevelWeapon4
]],[[
+4 Chaos Bow
Fanatic Bow
Crafted: true
Prefix: LocalAddedFireDamage8
Prefix: WeaponElementalDamage4
Suffix: LocalIncreasedAttackSpeed2
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalProjectileSkillGemLevelWeapon4
]],
-- Crossbows
[[
+6 Physical Crossbow
Desolate Crossbow
Crafted: true
Prefix: LocalIncreasedPhysicalDamagePercent5
Prefix: LocalIncreasedPhysicalDamagePercentAndAccuracyRating5
Prefix: LocalAddedPhysicalDamage6
Suffix: LocalIncreasedAttackSpeed2
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalProjectileSkillGemLevelTwoHandWeapon4
]],[[
+6 Elemental Crossbow
Desolate Crossbow
Crafted: true
Prefix: LocalAddedFireDamageTwoHand8_
Prefix: LocalAddedColdDamageTwoHand8
Prefix: LocalAddedLightningDamageTwoHand8
Suffix: LocalIncreasedAttackSpeed2
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalProjectileSkillGemLevelTwoHandWeapon4
]],[[
+6 Physical Crossbow
Siege Crossbow
Crafted: true
Prefix: LocalIncreasedPhysicalDamagePercent5
Prefix: LocalIncreasedPhysicalDamagePercentAndAccuracyRating5
Prefix: LocalAddedPhysicalDamage6
Suffix: LocalIncreasedAttackSpeed2
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalProjectileSkillGemLevelTwoHandWeapon4
]],[[
+6 Elemental Crossbow
Siege Crossbow
Crafted: true
Prefix: LocalAddedFireDamageTwoHand8_
Prefix: LocalAddedColdDamageTwoHand8
Prefix: LocalAddedLightningDamageTwoHand8
Suffix: LocalIncreasedAttackSpeed2
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalProjectileSkillGemLevelTwoHandWeapon4
]],[[
+6 Physical Crossbow
Gemini Crossbow
Crafted: true
Prefix: LocalIncreasedPhysicalDamagePercent5
Prefix: LocalIncreasedPhysicalDamagePercentAndAccuracyRating5
Prefix: LocalAddedPhysicalDamage6
Suffix: LocalIncreasedAttackSpeed2
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalProjectileSkillGemLevelTwoHandWeapon4
]],[[
+6 Elemental Crossbow
Gemini Crossbow
Crafted: true
Prefix: LocalAddedFireDamageTwoHand8_
Prefix: LocalAddedColdDamageTwoHand8
Prefix: LocalAddedLightningDamageTwoHand8
Suffix: LocalIncreasedAttackSpeed2
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalProjectileSkillGemLevelTwoHandWeapon4
]],[[
+6 Physical Crossbow
Flexed Crossbow
Crafted: true
Prefix: LocalIncreasedPhysicalDamagePercent5
Prefix: LocalIncreasedPhysicalDamagePercentAndAccuracyRating5
Prefix: LocalAddedPhysicalDamage6
Suffix: LocalIncreasedAttackSpeed2
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalProjectileSkillGemLevelTwoHandWeapon4
]],[[
+6 Elemental Crossbow
Flexed Crossbow
Crafted: true
Prefix: LocalAddedFireDamageTwoHand8_
Prefix: LocalAddedColdDamageTwoHand8
Prefix: LocalAddedLightningDamageTwoHand8
Suffix: LocalIncreasedAttackSpeed2
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalProjectileSkillGemLevelTwoHandWeapon4
]],
-- One Hand Maces
[[
Physical 1H Mace
Marauding Mace
Crafted: true
Prefix: LocalIncreasedPhysicalDamagePercent5
Prefix: LocalIncreasedPhysicalDamagePercentAndAccuracyRating5
Prefix: LocalAddedPhysicalDamage6
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelWeapon4
]],[[
Elemental 1H Mace
Marauding Mace
Crafted: true
Prefix: LocalAddedFireDamage8
Prefix: LocalAddedColdDamage8
Prefix: LocalAddedLightningDamage8
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelWeapon4
]],[[
Physical 1H Mace
Strife Pick
Crafted: true
Prefix: LocalIncreasedPhysicalDamagePercent5
Prefix: LocalIncreasedPhysicalDamagePercentAndAccuracyRating5
Prefix: LocalAddedPhysicalDamage6
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelWeapon4
]],[[
Elemental 1H Mace
Strife Pick
Crafted: true
Prefix: LocalAddedFireDamage8
Prefix: LocalAddedColdDamage8
Prefix: LocalAddedLightningDamage8
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelWeapon4
]],[[
Physical 1H Mace
Fortified Hammer
Crafted: true
Prefix: LocalIncreasedPhysicalDamagePercent5
Prefix: LocalIncreasedPhysicalDamagePercentAndAccuracyRating5
Prefix: LocalAddedPhysicalDamage6
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelWeapon4
]],[[
Elemental 1H Mace
Fortified Hammer
Crafted: true
Prefix: LocalAddedFireDamage8
Prefix: LocalAddedColdDamage8
Prefix: LocalAddedLightningDamage8
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelWeapon4
]],
-- Two Hand Maces
[[
Physical 2H Mace
Massive Greathammer
Crafted: true
Prefix: LocalIncreasedPhysicalDamagePercent5
Prefix: LocalIncreasedPhysicalDamagePercentAndAccuracyRating5
Prefix: LocalAddedPhysicalDamageTwoHand6
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelTwoHandWeapon4
]],[[
Elemental 2H Mace
Massive Greathammer
Crafted: true
Prefix: LocalAddedFireDamageTwoHand8_
Prefix: LocalAddedColdDamageTwoHand8
Prefix: LocalAddedLightningDamageTwoHand8
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelTwoHandWeapon4
]],[[
Physical 2H Mace
Ruination Maul
Crafted: true
Prefix: LocalIncreasedPhysicalDamagePercent5
Prefix: LocalIncreasedPhysicalDamagePercentAndAccuracyRating5
Prefix: LocalAddedPhysicalDamageTwoHand6
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelTwoHandWeapon4
]],[[
Elemental 2H Mace
Ruination Maul
Crafted: true
Prefix: LocalAddedFireDamageTwoHand8_
Prefix: LocalAddedColdDamageTwoHand8
Prefix: LocalAddedLightningDamageTwoHand8
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelTwoHandWeapon4
]],[[
Physical 2H Mace
Fanatic Greathammer
Crafted: true
Prefix: LocalIncreasedPhysicalDamagePercent5
Prefix: LocalIncreasedPhysicalDamagePercentAndAccuracyRating5
Prefix: LocalAddedPhysicalDamageTwoHand6
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelTwoHandWeapon4
]],[[
Elemental 2H Mace
Fanatic Greathammer
Crafted: true
Prefix: LocalAddedFireDamageTwoHand8_
Prefix: LocalAddedColdDamageTwoHand8
Prefix: LocalAddedLightningDamageTwoHand8
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelTwoHandWeapon4
]],[[
Physical 2H Mace
Ironwood Greathammer
Crafted: true
Prefix: LocalIncreasedPhysicalDamagePercent5
Prefix: LocalIncreasedPhysicalDamagePercentAndAccuracyRating5
Prefix: LocalAddedPhysicalDamageTwoHand6
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelTwoHandWeapon4
]],[[
Elemental 2H Mace
Ironwood Greathammer
Crafted: true
Prefix: LocalAddedFireDamageTwoHand8_
Prefix: LocalAddedColdDamageTwoHand8
Prefix: LocalAddedLightningDamageTwoHand8
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelTwoHandWeapon4
]],
-- Warstaves
[[
Physical Staff
Razor Quarterstaff
Crafted: true
Prefix: LocalIncreasedPhysicalDamagePercent5
Prefix: LocalIncreasedPhysicalDamagePercentAndAccuracyRating5
Prefix: LocalAddedPhysicalDamageTwoHand6
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelTwoHandWeapon4
]],[[
Elemental Staff
Razor Quarterstaff
Crafted: true
Prefix: LocalAddedFireDamageTwoHand8_
Prefix: LocalAddedColdDamageTwoHand8
Prefix: LocalAddedLightningDamageTwoHand8
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelTwoHandWeapon4
]],[[
Physical Staff
Aegis Quarterstaff
Crafted: true
Prefix: LocalIncreasedPhysicalDamagePercent5
Prefix: LocalIncreasedPhysicalDamagePercentAndAccuracyRating5
Prefix: LocalAddedPhysicalDamageTwoHand6
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelTwoHandWeapon4
]],[[
Elemental Staff
Aegis Quarterstaff
Crafted: true
Prefix: LocalAddedFireDamageTwoHand8_
Prefix: LocalAddedColdDamageTwoHand8
Prefix: LocalAddedLightningDamageTwoHand8
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelTwoHandWeapon4
]],[[
Physical Staff
Striking Quarterstaff
Crafted: true
Prefix: LocalIncreasedPhysicalDamagePercent5
Prefix: LocalIncreasedPhysicalDamagePercentAndAccuracyRating5
Prefix: LocalAddedPhysicalDamageTwoHand6
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelTwoHandWeapon4
]],[[
Elemental Staff
Striking Quarterstaff
Crafted: true
Prefix: LocalAddedFireDamageTwoHand8_
Prefix: LocalAddedColdDamageTwoHand8
Prefix: LocalAddedLightningDamageTwoHand8
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelTwoHandWeapon4
]],[[
Elemental Staff
Bolting Quarterstaff
Crafted: true
Prefix: LocalAddedFireDamageTwoHand8_
Prefix: LocalAddedColdDamageTwoHand8
Prefix: LocalAddedLightningDamageTwoHand8
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelTwoHandWeapon4
]],
-- Spell Staves
[[
Fire Staff
Ashen Staff
Crafted: true
Prefix: SpellDamageOnTwoHandWeapon6
Prefix: SpellDamageGainedAsFire5
Prefix: FireDamagePrefixOnTwoHandWeapon6
Suffix: GlobalFireSpellGemsLevelTwoHandWeapon4
]],[[
Fire Staff
Pyrophyte Staff
Crafted: true
Prefix: SpellDamageOnTwoHandWeapon6
Prefix: SpellDamageGainedAsFire5
Prefix: FireDamagePrefixOnTwoHandWeapon6
]],[[
Cold Staff
Gelid Staff
Crafted: true
Prefix: SpellDamageOnTwoHandWeapon6
Prefix: SpellDamageGainedAsCold5
Prefix: ColdDamagePrefixOnTwoHandWeapon6
Suffix: GlobalColdSpellGemsLevelTwoHandWeapon4
Suffix: FreezeDamageIncrease4
]],[[
Lightning Staff
Voltaic Staff
Crafted: true
Prefix: SpellDamageOnTwoHandWeapon6
Prefix: SpellDamageGainedAsLightning5
Prefix: LightningDamagePrefixOnTwoHandWeapon6
]],[[
Elemental Staff
Chiming Staff
Crafted: true
Prefix: SpellDamageOnTwoHandWeapon6
Prefix: SpellDamageAndManaOnTwoHandWeapon6
Prefix: IncreasedManaTwoHandWeapon9
Suffix: GlobalSpellGemsLevelTwoHandWeapon3
Suffix: IncreasedCastSpeedTwoHand5
]],
-- Wands
[[
Chaos Wand
Withered Wand
Crafted: true
Prefix: ChaosDamagePrefixOnWeapon6
Prefix: SpellDamageOnWeapon6
Prefix: SpellDamageAndManaOnWeapon5
Suffix: GlobalChaosSpellGemsLevelWeapon4
]],[[
Physical Wand
Bone Wand
Crafted: true
Prefix: PhysicalDamagePrefixOnWeapon6
Prefix: SpellDamageOnWeapon6
Prefix: SpellDamageAndManaOnWeapon5
Suffix: GlobalPhysicalSpellGemsLevelWeapon4
]],[[
Fire Wand
Volatile Wand
Crafted: true
Prefix: FireDamagePrefixOnWeapon6
Prefix: SpellDamageOnWeapon6
Prefix: SpellDamageAndManaOnWeapon5
Suffix: GlobalFireSpellGemsLevelWeapon4
]],[[
Elemental Wand
Attuned Wand
Crafted: true
Prefix: FireDamagePrefixOnWeapon6
Prefix: ColdDamagePrefixOnWeapon6
Prefix: LightningDamagePrefixOnWeapon6
Suffix: GlobalSpellGemsLevelWeapon4
]],[[
Elemental Wand
Siphoning Wand
Crafted: true
Prefix: FireDamagePrefixOnWeapon6
Prefix: ColdDamagePrefixOnWeapon6
Prefix: LightningDamagePrefixOnWeapon6
Suffix: GlobalSpellGemsLevelWeapon4
]],
-- Not in game yet.
---- One Hand Swords
---setBase Dark Blade, Physical %s, [ ]
---setBase Dark Blade, Elemental %s, [ ]
---- Two Hand Swords
---setBase Ultra Greatsword, Physical %s, [ ]
---setBase Ultra Greatsword, Elemental %s, [ ]
--- Claws
---setBase Talon Claw, Physical %s, [ ]
---setBase Talon Claw, Elemental %s, [ ]
--- Daggers
---setBase Cinquedea, Physical %s, [ ]
---setBase Cinquedea, Elemental %s, [ ]
---setBase Mail Breaker, Physical %s, [ ]
---setBase Mail Breaker, Elemental %s, [ ]
---setBase Bloodletting Dagger, Physical %s, [ ]
---setBase Bloodletting Dagger, Elemental %s, [ ]
-- Spears
[[
+4 Physical Spear
Spiked Spear
Crafted: true
Prefix: LocalIncreasedPhysicalDamagePercent5
Prefix: LocalIncreasedPhysicalDamagePercentAndAccuracyRating5
Prefix: LocalAddedPhysicalDamage6
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelWeapon4
]],[[
+4 Elemental Spear
Spiked Spear
Crafted: true
Prefix: LocalAddedFireDamage8
Prefix: LocalAddedColdDamage8
Prefix: LocalAddedLightningDamage8
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelWeapon4
]],[[
+4 Lightning Spear
Flying Spear
Crafted: true
Prefix: WeaponElementalDamage5
Prefix: LocalAddedLightningDamage8
Suffix: LocalIncreasedAttackSpeed3
Suffix: LocalCriticalStrikeChance3
Suffix: GlobalMeleeSkillGemLevelWeapon4
]],---- Flails
---setBase Abyssal Flail, Physical %s, [ ] -- For some reason Flails and Spears don't grab the BaseGroup like maces and axes do.
---setBase Abyssal Flail, Elemental %s, [ ]
---- One Hand Axes
---setBase Dread Hatchet, Physical %s, [ ]
---setBase Dread Hatchet, Elemental %s, [ ]
---- Two Hand Axes
---setBase Vile Greataxe, Physical %s, [ ]
---setBase Vile Greataxe, Elemental %s, [ ]

-- Jewels
[[
Jewel
Ruby
Crafted: true
]],[[
Jewel
Emerald
Crafted: true
]],[[
Jewel
Sapphire
Crafted: true
]],
-- Jewels
[[
Radius Jewel
Time-Lost Ruby
Crafted: true
]],[[
Radius Jewel
Time-Lost Emerald
Crafted: true
]],[[
Radius Jewel
Time-Lost Sapphire
Crafted: true
]],
-- Flasks
			[[
			Rarity: MAGIC
			Seething Ultimate Life Flask of the Verdant
			Crafted: true
			Prefix: {range:0.5}FlaskFullInstantRecovery1
			Suffix: {range:0.88}FlaskFillChargesPerMinute2
			Quality: 20
			LevelReq: 60
			Implicits: 0
			50% reduced Amount Recovered
			Instant Recovery
			Gains 0.20 Charges per Second
			]],
			[[
			Rarity: MAGIC
			Seething Ultimate Mana Flask of the Verdant
			Crafted: true
			Prefix: {range:0.5}FlaskFullInstantRecovery1
			Suffix: {range:0.954}FlaskFillChargesPerMinute2
			Quality: 20
			LevelReq: 60
			Implicits: 0
			50% reduced Amount Recovered
			Instant Recovery
			Gains 0.20 Charges per Second
			]],
			[[
			Rarity: MAGIC
			Potent Ultimate Life Flask of the Distiller
			Crafted: true
			Prefix: {range:0.338}FlaskIncreasedRecoveryAmount7
			Suffix: {range:0.887}FlaskChargesUsed4__
			Quality: 20
			LevelReq: 60
			Implicits: 0
			72% increased Amount Recovered
			26% reduced Charges per use
			]],
			[[
			Rarity: MAGIC
			Potent Ultimate Mana Flask of the Distiller
			Crafted: true
			Prefix: {range:0.338}FlaskIncreasedRecoveryAmount7
			Suffix: {range:0.887}FlaskChargesUsed4__
			Quality: 20
			LevelReq: 60
			Implicits: 0
			72% increased Amount Recovered
			26% reduced Charges per use
			]],

			}
