-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games
local itemBases = ...


itemBases["Splintered Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, ezomyte_basetype = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 18, MovementPenalty = 0.03, },
	req = { },
}
itemBases["Painted Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, ezomyte_basetype = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 29, MovementPenalty = 0.03, },
	req = { level = 6, str = 13, },
}
itemBases["Braced Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, ezomyte_basetype = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 41, MovementPenalty = 0.03, },
	req = { level = 12, str = 23, },
}
itemBases["Barricade Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 50, MovementPenalty = 0.03, },
	req = { level = 16, str = 30, },
}
itemBases["Effigial Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 60, MovementPenalty = 0.03, },
	req = { level = 21, str = 40, },
}
itemBases["Rampart Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 75, MovementPenalty = 0.03, },
	req = { level = 28, str = 52, },
}
itemBases["Heraldric Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, vaal_basetype = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 85, MovementPenalty = 0.03, },
	req = { level = 33, str = 61, },
}
itemBases["Stone Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, vaal_basetype = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 91, MovementPenalty = 0.03, },
	req = { level = 36, str = 66, },
}
itemBases["Crucible Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 110, MovementPenalty = 0.03, },
	req = { level = 45, str = 83, },
}
itemBases["Ancestor Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 121, MovementPenalty = 0.03, },
	req = { level = 50, str = 92, },
}
itemBases["Phalanx Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 125, MovementPenalty = 0.03, },
	req = { level = 52, str = 95, },
}
itemBases["Defiant Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 137, MovementPenalty = 0.03, },
	req = { level = 58, str = 106, },
}
itemBases["Blacksteel Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 152, MovementPenalty = 0.03, },
	req = { level = 65, str = 118, },
}
itemBases["Aged Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 110, MovementPenalty = 0.03, },
	req = { level = 45, str = 83, },
}
itemBases["Metalworked Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 116, MovementPenalty = 0.03, },
	req = { level = 48, str = 88, },
}
itemBases["Cultist Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 123, MovementPenalty = 0.03, },
	req = { level = 51, str = 94, },
}
itemBases["Bulwark Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 131, MovementPenalty = 0.03, },
	req = { level = 55, str = 101, },
}
itemBases["Noble Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 139, MovementPenalty = 0.03, },
	req = { level = 59, str = 108, },
}
itemBases["Goldworked Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 144, MovementPenalty = 0.03, },
	req = { level = 61, str = 112, },
}
itemBases["Royal Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 152, MovementPenalty = 0.03, },
	req = { level = 65, str = 118, },
}
itemBases["Fortress Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 167, MovementPenalty = 0.03, },
	req = { level = 70, str = 129, },
}
itemBases["Vaal Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 182, MovementPenalty = 0.03, },
	req = { level = 75, str = 139, },
}
itemBases["Aged Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 110, MovementPenalty = 0.03, },
	req = { level = 45, str = 83, },
}
itemBases["Metalworked Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 116, MovementPenalty = 0.03, },
	req = { level = 48, str = 88, },
}
itemBases["Cultist Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 123, MovementPenalty = 0.03, },
	req = { level = 51, str = 94, },
}
itemBases["Bulwark Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 131, MovementPenalty = 0.03, },
	req = { level = 55, str = 101, },
}
itemBases["Noble Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 139, MovementPenalty = 0.03, },
	req = { level = 59, str = 108, },
}
itemBases["Goldworked Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 144, MovementPenalty = 0.03, },
	req = { level = 61, str = 112, },
}
itemBases["Royal Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 152, MovementPenalty = 0.03, },
	req = { level = 65, str = 118, },
}
itemBases["Fortress Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 167, MovementPenalty = 0.03, },
	req = { level = 70, str = 129, },
}
itemBases["Vaal Tower Shield"] = {
	type = "Shield",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { str_armour = true, str_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 26, Armour = 182, MovementPenalty = 0.03, },
	req = { level = 75, str = 139, },
}

itemBases["Leather Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, ezomyte_basetype = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 10, },
	req = { },
}
itemBases["Wooden Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, ezomyte_basetype = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 16, },
	req = { level = 5, dex = 11, },
}
itemBases["Plated Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, ezomyte_basetype = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 26, },
	req = { level = 11, dex = 22, },
}
itemBases["Iron Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 33, },
	req = { level = 16, dex = 30, },
}
itemBases["Ridged Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 43, },
	req = { level = 22, dex = 42, },
}
itemBases["Spiked Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 49, },
	req = { level = 26, dex = 49, },
}
itemBases["Ringed Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, vaal_basetype = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 60, },
	req = { level = 33, dex = 61, },
}
itemBases["Edged Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, vaal_basetype = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 70, },
	req = { level = 39, dex = 72, },
}
itemBases["Laminate Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 79, },
	req = { level = 45, dex = 83, },
}
itemBases["Pearl Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 84, },
	req = { level = 48, dex = 88, },
}
itemBases["Ornate Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 90, },
	req = { level = 52, dex = 95, },
}
itemBases["Array Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 95, },
	req = { level = 55, dex = 101, },
}
itemBases["Aegis Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 111, },
	req = { level = 65, dex = 118, },
}
itemBases["Oak Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 79, },
	req = { level = 45, dex = 83, },
}
itemBases["Painted Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 84, },
	req = { level = 48, dex = 88, },
}
itemBases["Coiled Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 89, },
	req = { level = 51, dex = 94, },
}
itemBases["Spikeward Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 95, },
	req = { level = 55, dex = 101, },
}
itemBases["Jingling Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 101, },
	req = { level = 59, dex = 108, },
}
itemBases["Bladeguard Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 105, },
	req = { level = 61, dex = 112, },
}
itemBases["Ornate Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 111, },
	req = { level = 65, dex = 118, },
}
itemBases["Gutspike Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 122, },
	req = { level = 70, dex = 129, },
}
itemBases["Ancient Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 133, },
	req = { level = 75, dex = 139, },
}
itemBases["Oak Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 79, },
	req = { level = 45, dex = 83, },
}
itemBases["Painted Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 84, },
	req = { level = 48, dex = 88, },
}
itemBases["Coiled Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 89, },
	req = { level = 51, dex = 94, },
}
itemBases["Spikeward Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 95, },
	req = { level = 55, dex = 101, },
}
itemBases["Jingling Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 101, },
	req = { level = 59, dex = 108, },
}
itemBases["Bladeguard Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 105, },
	req = { level = 61, dex = 112, },
}
itemBases["Ornate Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 111, },
	req = { level = 65, dex = 118, },
}
itemBases["Gutspike Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 122, },
	req = { level = 70, dex = 129, },
}
itemBases["Ancient Buckler"] = {
	type = "Shield",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { buckler = true, dex_shield = true, dex_armour = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Parry",
	implicitModTypes = { },
	armour = { BlockChance = 20, Evasion = 133, },
	req = { level = 75, dex = 139, },
}

itemBases["Hardwood Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, ezomyte_basetype = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 10, Evasion = 7, MovementPenalty = 0.015, },
	req = { },
}
itemBases["Pelage Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, ezomyte_basetype = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 18, Evasion = 14, MovementPenalty = 0.015, },
	req = { level = 8, str = 10, dex = 10, },
}
itemBases["Studded Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 27, Evasion = 23, MovementPenalty = 0.015, },
	req = { level = 16, str = 18, dex = 18, },
}
itemBases["Crescent Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 39, Evasion = 34, MovementPenalty = 0.015, },
	req = { level = 26, str = 27, dex = 27, },
}
itemBases["Chiseled Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, vaal_basetype = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 47, Evasion = 41, MovementPenalty = 0.015, },
	req = { level = 33, str = 34, dex = 34, },
}
itemBases["Feathered Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, vaal_basetype = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 51, Evasion = 46, MovementPenalty = 0.015, },
	req = { level = 37, str = 38, dex = 38, },
}
itemBases["Stratified Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 61, Evasion = 54, MovementPenalty = 0.015, },
	req = { level = 45, str = 46, dex = 46, },
}
itemBases["Carved Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 63, Evasion = 57, MovementPenalty = 0.015, },
	req = { level = 47, str = 48, dex = 48, },
}
itemBases["Mosaic Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 69, Evasion = 62, MovementPenalty = 0.015, },
	req = { level = 52, str = 53, dex = 53, },
}
itemBases["Aureate Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 74, Evasion = 68, MovementPenalty = 0.015, },
	req = { level = 57, str = 58, dex = 58, },
}
itemBases["Grand Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 84, Evasion = 76, MovementPenalty = 0.015, },
	req = { level = 65, str = 65, dex = 65, },
}
itemBases["Ironwood Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 61, Evasion = 54, MovementPenalty = 0.015, },
	req = { level = 45, str = 46, dex = 46, },
}
itemBases["Fur-lined Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 64, Evasion = 58, MovementPenalty = 0.015, },
	req = { level = 48, str = 49, dex = 49, },
}
itemBases["Mercenary Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 67, Evasion = 61, MovementPenalty = 0.015, },
	req = { level = 51, str = 52, dex = 52, },
}
itemBases["Polished Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 72, Evasion = 65, MovementPenalty = 0.015, },
	req = { level = 55, str = 55, dex = 55, },
}
itemBases["Stone Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 77, Evasion = 70, MovementPenalty = 0.015, },
	req = { level = 59, str = 59, dex = 59, },
}
itemBases["Avian Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 80, Evasion = 73, MovementPenalty = 0.015, },
	req = { level = 62, str = 62, dex = 62, },
}
itemBases["Mammoth Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 84, Evasion = 76, MovementPenalty = 0.015, },
	req = { level = 65, str = 65, dex = 65, },
}
itemBases["Baroque Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 92, Evasion = 84, MovementPenalty = 0.015, },
	req = { level = 70, str = 70, dex = 70, },
}
itemBases["Soaring Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 100, Evasion = 91, MovementPenalty = 0.015, },
	req = { level = 75, str = 76, dex = 76, },
}
itemBases["Ironwood Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 61, Evasion = 54, MovementPenalty = 0.015, },
	req = { level = 45, str = 46, dex = 46, },
}
itemBases["Fur-lined Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 64, Evasion = 58, MovementPenalty = 0.015, },
	req = { level = 48, str = 49, dex = 49, },
}
itemBases["Mercenary Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 67, Evasion = 61, MovementPenalty = 0.015, },
	req = { level = 51, str = 52, dex = 52, },
}
itemBases["Polished Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 72, Evasion = 65, MovementPenalty = 0.015, },
	req = { level = 55, str = 55, dex = 55, },
}
itemBases["Stone Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 77, Evasion = 70, MovementPenalty = 0.015, },
	req = { level = 59, str = 59, dex = 59, },
}
itemBases["Avian Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 80, Evasion = 73, MovementPenalty = 0.015, },
	req = { level = 62, str = 62, dex = 62, },
}
itemBases["Mammoth Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 84, Evasion = 76, MovementPenalty = 0.015, },
	req = { level = 65, str = 65, dex = 65, },
}
itemBases["Baroque Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 92, Evasion = 84, MovementPenalty = 0.015, },
	req = { level = 70, str = 70, dex = 70, },
}
itemBases["Soaring Targe"] = {
	type = "Shield",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, str_dex_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 100, Evasion = 91, MovementPenalty = 0.015, },
	req = { level = 75, str = 76, dex = 76, },
}

itemBases["Blazon Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, ezomyte_basetype = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 10, EnergyShield = 6, MovementPenalty = 0.015, },
	req = { },
}
itemBases["Sigil Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, ezomyte_basetype = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 17, EnergyShield = 8, MovementPenalty = 0.015, },
	req = { level = 7, str = 10, int = 10, },
}
itemBases["Emblem Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 27, EnergyShield = 12, MovementPenalty = 0.015, },
	req = { level = 16, str = 18, int = 18, },
}
itemBases["Jingling Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 41, EnergyShield = 16, MovementPenalty = 0.015, },
	req = { level = 28, str = 30, int = 30, },
}
itemBases["Sectarian Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, vaal_basetype = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 47, EnergyShield = 18, MovementPenalty = 0.015, },
	req = { level = 33, str = 34, int = 34, },
}
itemBases["Omen Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, vaal_basetype = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 50, EnergyShield = 19, MovementPenalty = 0.015, },
	req = { level = 36, str = 37, int = 37, },
}
itemBases["Wayward Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 61, EnergyShield = 22, MovementPenalty = 0.015, },
	req = { level = 45, str = 46, int = 46, },
}
itemBases["Seer Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 65, EnergyShield = 23, MovementPenalty = 0.015, },
	req = { level = 49, str = 50, int = 50, },
}
itemBases["Stoic Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 69, EnergyShield = 24, MovementPenalty = 0.015, },
	req = { level = 52, str = 53, int = 53, },
}
itemBases["Empyreal Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 74, EnergyShield = 26, MovementPenalty = 0.015, },
	req = { level = 57, str = 58, int = 58, },
}
itemBases["Deified Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 84, EnergyShield = 29, MovementPenalty = 0.015, },
	req = { level = 65, str = 65, int = 65, },
}
itemBases["Painted Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 61, EnergyShield = 22, MovementPenalty = 0.015, },
	req = { level = 45, str = 46, int = 46, },
}
itemBases["Engraved Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 64, EnergyShield = 23, MovementPenalty = 0.015, },
	req = { level = 48, str = 49, int = 49, },
}
itemBases["Descry Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 67, EnergyShield = 24, MovementPenalty = 0.015, },
	req = { level = 51, str = 52, int = 52, },
}
itemBases["Dekharan Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 72, EnergyShield = 25, MovementPenalty = 0.015, },
	req = { level = 55, str = 55, int = 55, },
}
itemBases["Quartered Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 77, EnergyShield = 27, MovementPenalty = 0.015, },
	req = { level = 59, str = 59, int = 59, },
}
itemBases["Glowering Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 80, EnergyShield = 28, MovementPenalty = 0.015, },
	req = { level = 62, str = 62, int = 62, },
}
itemBases["Intricate Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 84, EnergyShield = 29, MovementPenalty = 0.015, },
	req = { level = 65, str = 65, int = 65, },
}
itemBases["Sekheman Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 92, EnergyShield = 32, MovementPenalty = 0.015, },
	req = { level = 70, str = 70, int = 70, },
}
itemBases["Vaal Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 100, EnergyShield = 35, MovementPenalty = 0.015, },
	req = { level = 75, str = 76, int = 76, },
}
itemBases["Painted Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 61, EnergyShield = 22, MovementPenalty = 0.015, },
	req = { level = 45, str = 46, int = 46, },
}
itemBases["Engraved Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 64, EnergyShield = 23, MovementPenalty = 0.015, },
	req = { level = 48, str = 49, int = 49, },
}
itemBases["Descry Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 67, EnergyShield = 24, MovementPenalty = 0.015, },
	req = { level = 51, str = 52, int = 52, },
}
itemBases["Dekharan Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 72, EnergyShield = 25, MovementPenalty = 0.015, },
	req = { level = 55, str = 55, int = 55, },
}
itemBases["Quartered Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 77, EnergyShield = 27, MovementPenalty = 0.015, },
	req = { level = 59, str = 59, int = 59, },
}
itemBases["Glowering Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 80, EnergyShield = 28, MovementPenalty = 0.015, },
	req = { level = 62, str = 62, int = 62, },
}
itemBases["Intricate Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 84, EnergyShield = 29, MovementPenalty = 0.015, },
	req = { level = 65, str = 65, int = 65, },
}
itemBases["Sekheman Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 92, EnergyShield = 32, MovementPenalty = 0.015, },
	req = { level = 70, str = 70, int = 70, },
}
itemBases["Vaal Crest Shield"] = {
	type = "Shield",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, str_int_shield = true, armour = true, default = true, shield = true, },
	implicit = "Grants Skill: Level (1-20) Raise Shield",
	implicitModTypes = { },
	armour = { BlockChance = 25, Armour = 100, EnergyShield = 35, MovementPenalty = 0.015, },
	req = { level = 75, str = 76, int = 76, },
}

itemBases["Golden Flame"] = {
	type = "Shield",
	quality = 20,
	socketLimit = 2,
	tags = { not_for_sale = true, demigods = true, armour = true, default = true, shield = true, },
	implicit = "+(11-19)% to Chaos Resistance",
	implicitModTypes = { { "chaos", "resistance" }, },
	armour = { BlockChance = 25, },
	req = { level = 15, },
}
