-- This file is automatically generated, do not edit!

local data = ...
-- From DefaultMonsterStats.dat
data.monsterEvasionTable = { 11, 14, 17, 20, 24, 27, 31, 35, 38, 42, 46, 50, 54, 59, 63, 67, 72, 76, 81, 86, 91, 96, 101, 106, 111, 117, 122, 128, 134, 140, 146, 152, 158, 165, 171, 178, 185, 191, 199, 206, 213, 221, 228, 236, 244, 252, 261, 269, 278, 286, 295, 304, 314, 323, 333, 343, 353, 363, 373, 384, 395, 406, 417, 429, 440, 452, 464, 477, 489, 502, 515, 528, 542, 556, 570, 584, 598, 613, 628, 644, 659, 675, 691, 708, 724, 742, 759, 777, 795, 813, 832, 850, 870, 889, 909, 930, 951, 972, 993, 1015, }
data.monsterAccuracyTable = { 51, 56, 60, 65, 70, 75, 80, 86, 91, 97, 103, 110, 116, 123, 130, 137, 145, 152, 160, 169, 177, 186, 195, 204, 214, 224, 234, 245, 256, 267, 279, 291, 304, 317, 330, 344, 358, 372, 387, 403, 419, 435, 452, 470, 488, 506, 526, 545, 566, 587, 608, 630, 653, 677, 701, 726, 752, 778, 806, 834, 863, 892, 923, 955, 987, 1020, 1055, 1090, 1126, 1164, 1202, 1242, 1283, 1324, 1368, 1412, 1457, 1504, 1552, 1602, 1653, 1705, 1759, 1814, 1871, 1930, 1990, 2052, 2115, 2180, 2247, 2316, 2387, 2460, 2535, 2612, 2690, 2772, 2855, 2941, }
data.monsterLifeTable = { 15, 20, 24, 28, 33, 38, 45, 50, 58, 67, 78, 89, 103, 118, 134, 158, 178, 200, 224, 249, 276, 305, 335, 366, 400, 434, 472, 510, 551, 593, 637, 683, 731, 790, 853, 921, 995, 1074, 1160, 1253, 1353, 1462, 1578, 1705, 1841, 1967, 2101, 2244, 2395, 2556, 2726, 2909, 3102, 3307, 3525, 3756, 4002, 4264, 4540, 4834, 5147, 5478, 5829, 6203, 6513, 6904, 7318, 7757, 8222, 8716, 9239, 9793, 10381, 11003, 11664, 12363, 13105, 13892, 14725, 15609, 16545, 17538, 18590, 19705, 20888, 22141, 23469, 24878, 26370, 27953, 29630, 31407, 33292, 35289, 37407, 39651, 42030, 44552, 47225, 50059, }
data.monsterAllyLifeTable = { 51, 83, 116, 150, 186, 223, 261, 300, 341, 382, 426, 471, 517, 565, 614, 665, 718, 772, 828, 886, 945, 1007, 1070, 1135, 1203, 1272, 1344, 1417, 1493, 1571, 1652, 1734, 1820, 1907, 1998, 2091, 2186, 2285, 2386, 2490, 2598, 2708, 2821, 2938, 3058, 3181, 3307, 3438, 3571, 3709, 3850, 3995, 4144, 4298, 4455, 4617, 4783, 4953, 5128, 5308, 5493, 5682, 5877, 6077, 6282, 6492, 6708, 6930, 7157, 7391, 7630, 7876, 8128, 8387, 8652, 8924, 9203, 9489, 9783, 10084, 10393, 10710, 11034, 11367, 11708, 12058, 12417, 12785, 13161, 13548, 13944, 14350, 14766, 15192, 15629, 16076, 16535, 17005, 17486, 17980, }
data.monsterDamageTable = { 9.1599998474121, 10.260000228882, 11.390000343323, 12.569999694824, 13.779999732971, 15.029999732971, 16.319999694824, 17.64999961853, 19.020000457764, 20.440000534058, 21.89999961853, 23.409999847412, 24.969999313354, 26.569999694824, 28.229999542236, 29.930000305176, 31.690000534058, 33.5, 35.369998931885, 37.290000915527, 39.270000457764, 41.310001373291, 43.409999847412, 45.569999694824, 47.799999237061, 50.090000152588, 52.450000762939, 54.880001068115, 57.369998931885, 59.939998626709, 62.590000152588, 65.309997558594, 68.099998474121, 70.980003356934, 73.940002441406, 76.980003356934, 80.110000610352, 83.319999694824, 86.629997253418, 90.019996643066, 93.51000213623, 97.099998474121, 100.79000091553, 104.56999969482, 108.45999908447, 112.45999908447, 116.56999969482, 120.7799987793, 125.12000274658, 129.55999755859, 134.13000488281, 138.82000732422, 143.63999938965, 148.58000183105, 153.66000366211, 158.86999511719, 164.21000671387, 169.69999694824, 175.33999633789, 181.11999511719, 187.05000305176, 193.13999938965, 199.38000488281, 205.78999328613, 212.36000061035, 219.11000061035, 226.0299987793, 233.11999511719, 240.39999389648, 247.86000061035, 255.52000427246, 263.36999511719, 271.42001342773, 279.67999267578, 288.14001464844, 296.82000732422, 305.7200012207, 314.83999633789, 324.19000244141, 333.7799987793, 343.60000610352, 353.67001342773, 364, 374.57998657227, 385.42001342773, 396.5299987793, 407.92001342773, 419.57998657227, 431.54000854492, 443.79000854492, 456.33999633789, 469.20001220703, 482.38000488281, 495.86999511719, 509.70001220703, 523.85998535156, 538.36999511719, 553.22998046875, 568.46002197266, 584.04998779297, }
data.monsterAllyDamageTable = { 3.1099998950958, 4.4200000762939, 5.8200001716614, 7.3099999427795, 8.9200000762939, 10.630000114441, 12.460000038147, 14.420000076294, 16.510000228882, 18.729999542236, 21.10000038147, 23.620000839233, 26.309999465942, 29.159999847412, 32.189998626709, 35.419998168945, 38.830001831055, 42.459999084473, 46.310001373291, 50.389999389648, 54.709999084473, 59.290000915527, 64.139999389648, 69.269996643066, 74.690002441406, 80.430000305176, 86.5, 92.910003662109, 99.690002441406, 106.83999633789, 114.40000152588, 122.37000274658, 130.78999328613, 139.66999816895, 149.03999328613, 158.91000366211, 169.32000732422, 180.28999328613, 191.86000061035, 204.03999328613, 216.86000061035, 230.36999511719, 244.60000610352, 259.57000732422, 275.32000732422, 291.89999389648, 309.33999633789, 327.69000244141, 346.98001098633, 367.26998901367, 388.58999633789, 411.01000976562, 434.57000732422, 459.32000732422, 485.32998657227, 512.65997314453, 541.34997558594, 571.48999023438, 603.14001464844, 636.36999511719, 671.26000976562, 707.86999511719, 746.29998779297, 786.63000488281, 828.94000244141, 873.34002685547, 919.90997314453, 968.76000976562, 1019.9899902344, 1073.7199707031, 1130.0600585938, 1189.1300048828, 1251.0600585938, 1315.9799804688, 1384.0300292969, 1455.3399658203, 1530.0799560547, 1608.4000244141, 1690.4599609375, 1776.4300537109, 1866.5, 1960.8399658203, 2059.6599121094, 2163.1599121094, 2271.5600585938, 2385.0600585938, 2503.9099121094, 2628.3601074219, 2758.6398925781, 2895.0300292969, 3037.8000488281, 3187.2399902344, 3343.6599121094, 3507.3500976562, 3678.6599121094, 3857.9299316406, 4045.5100097656, 4241.7700195312, 4447.1098632812, 4661.9301757812, }
data.monsterArmourTable = { 3, 6, 8, 10, 13, 16, 19, 22, 26, 30, 34, 39, 43, 49, 54, 60, 67, 73, 81, 89, 97, 106, 116, 126, 137, 149, 161, 174, 189, 204, 220, 237, 255, 274, 295, 317, 340, 364, 391, 418, 448, 479, 512, 547, 585, 624, 666, 711, 758, 808, 861, 917, 976, 1039, 1105, 1176, 1250, 1329, 1412, 1500, 1594, 1692, 1796, 1906, 2023, 2146, 2276, 2413, 2558, 2712, 2874, 3044, 3225, 3416, 3617, 3829, 4053, 4290, 4540, 4803, 5081, 5375, 5684, 6011, 6355, 6718, 7101, 7505, 7930, 8379, 8852, 9351, 9877, 10431, 11015, 11630, 12279, 12962, 13682, 14441, }
data.monsterAilmentThresholdTable = { 15, 20, 24, 28, 34, 39, 46, 52, 60, 70, 81, 95, 110, 126, 144, 171, 193, 218, 245, 275, 306, 340, 376, 413, 455, 497, 543, 590, 641, 695, 752, 812, 874, 950, 1033, 1123, 1220, 1326, 1442, 1568, 1705, 1854, 2015, 2192, 2384, 2564, 2757, 2966, 3188, 3426, 3681, 3955, 4247, 4560, 4895, 5254, 5638, 6049, 6489, 6959, 7462, 8001, 8576, 9193, 9723, 10382, 11085, 11837, 12639, 13497, 14413, 15390, 16435, 17549, 18742, 20013, 21372, 22824, 24373, 26029, 27796, 29684, 31700, 33852, 36153, 38608, 41230, 44033, 47023, 50219, 53630, 57272, 61164, 65318, 69757, 74494, 79554, 84958, 90729, 96892, }

-- From MinionGemLevelScaling.dat
data.minionLevelTable = { 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50, 52, 54, 56, 58, 60, 62, 64, 66, 68, 70, 72, 74, 76, 78, 80, }

-- From GameConstants.dat
data.gameConstants = {
	["MonsterAccuracyBase"] = 47,
	["MonsterAccuracyIncremental"] = 321,
	["MonsterAccuracyImprovement"] = 2.1,
	["MonsterArmourBase"] = 2,
	["MonsterArmourIncremental"] = 1.75,
	["MonsterArmourStrengthRatioMultiplier"] = 3,
	["MonsterArmourImprovement"] = 4.5,
	["MonsterEvasionBase"] = 8,
	["MonsterEvasionIncremental"] = 3,
	["MonsterEvasionDexterityRatioMultiplier"] = 2,
	["MonsterEvasionImprovement"] = 1.2,
	["OldLeechModsEffectiveness"] = 20,
	["AscendancyRespecCost"] = 5,
	["SkillDamageBaseEffectiveness"] = 3.885209,
	["SkillDamageIncrementalEffectiveness"] = 0,
	["MaxHideoutDoodads"] = 750,
	["PassiveTreeJewelDistanceMultiplier"] = 1.2,
	["PartyQuantityBonusPerAdditionalPlayer"] = 0.2,
	["PartyQuantityBonusPerAdditionalPlayerHardMode"] = 0.2,
	["PartyRarityBonusPerAdditionalPlayer"] = 0,
	["PartyRarityBonusPerAdditionalPlayerHardMode"] = 0,
	["PartyUniqueBonusPerAdditionalPlayer"] = 0.3,
	["LightStunMinimumChance"] = 20,
	["LightStunRatioScale"] = 92,
	["LightStunMinimumChancePlayer"] = 12,
	["LightStunRatioScalePlayer"] = 83,
	["HeavyStunDamageScale"] = 0.87,
	["HeavyStunThresholdModifier"] = 500,
	["HeavyStunModifierDuration"] = 16.5,
	["HeavyStunDamageScalePlayer"] = 1,
	["HeavyStunThresholdModifierPlayer"] = 100,
	["HeavyStunModifierDurationPlayer"] = 10,
	["FreezeDamageScale"] = 2.1,
	["FreezeDamageScalePlayer"] = 2,
	["FreezeDuration"] = 4,
	["FreezeDurationPlayer"] = 2,
	["FreezeThresholdModifier"] = 500,
	["FreezeModifierDuration"] = 14,
	["ElectrocuteDamageScale"] = 1.7,
	["ElectrocuteDuration"] = 5000,
	["ElectrocuteDurationPlayer"] = 2000,
	["ElectrocuteThresholdModifier"] = 500,
	["ElectrocuteModifierDuration"] = 15,
	["PinDamageScale"] = 4.2,
	["PinDuration"] = 3,
	["PinDurationPlayer"] = 2,
	["PinThresholdModifier"] = 500,
	["PinModifierDuration"] = 13,
	["MonsterIntRatioCovertLifeToEnergyShield"] = 100,
	["ShockChanceMultiplier"] = 25,
	["IgniteChanceMultiplier"] = 25,
	["MiscAilmentChanceMultiplier"] = 25,
	["AilmentChanceExponent"] = 1,
	["ChillEffectMultiplier"] = 60,
	["ChillMaxEffect"] = 50,
	["SellPriceMultiplier"] = 0.11,
	["MobilePriceMultiplier"] = 0.25,
	["UnidentifiedMagicMultiplier"] = 1.5,
	["UnidentifiedRareMultiplier"] = 6,
	["IdentifiedMagicMultiplier"] = 1,
	["IdentifiedRareMultiplier"] = 2,
	["AtlasPassiveRespecCost"] = 5000,
	["EndgameStartLevel"] = 65,
	["BleedingHitDamagePercentPerMinute"] = 900,
	["IgniteHitDamagePercentPerMinute"] = 1200,
	["PoisonHitDamagePercentPerMinute"] = 1200,
	["JaggedGroundMagnitude"] = -20,
	["BaseBleedingDuration"] = 5,
	["BaseChillDuration"] = 2,
	["BaseShockDuration"] = 4,
	["BasePoisonDuration"] = 2,
	["BaseIgniteDuration"] = 4,
	["BaseBloodstainedAfterBleedingDuration"] = 6,
	["BloodstainedMultiplierWhenMovingOrBleedingAggravated"] = 2,
	["UnholyMightBaseMagnitude"] = 30,
	["ImpalePercentage"] = 0.3,
	["ImpaleMaximumStacks"] = 60,
	["PlayerAilmentThresholdLifeFactor"] = 0.5,
	["BaseShieldRegenCooldownTimeMs"] = 4000,
	["CullingStrikeNormalThreshold"] = 30,
	["CullingStrikeMagicThreshold"] = 20,
	["CullingStrikeRareThreshold"] = 10,
	["CullingStrikeUniqueThreshold"] = 5,
}
-- From Metadata/Characters/Character.ot
data.characterConstants = {
	["level"] = 1,
	["is_player"] = 1,
	["energy_shield_recharge_rate_per_minute_%"] = 750,
	["character_inherent_mana_regeneration_rate_per_minute_%"] = 240,
	["base_maximum_all_resistances_%"] = 75,
	["maximum_physical_damage_reduction_%"] = 90,
	["object_inherent_base_maximum_block_%_from_ot"] = 75,
	["base_maximum_spell_block_%"] = 75,
	["max_viper_strike_orbs"] = 4,
	["max_fuse_arrow_orbs"] = 5,
	["max_fire_beam_stacks"] = 8,
	["base_evasion_rating"] = 27,
	["base_maximum_chance_to_evade_%"] = 95,
	["life_per_level"] = 12,
	["mana_per_level"] = 4,
	["accuracy_rating_per_level"] = 3,
	["evasion_rating_per_level"] = 300,
	["base_critical_hit_damage_bonus"] = 100,
	["strength_per_level"] = 0,
	["dexterity_per_level"] = 0,
	["intelligence_per_level"] = 0,
	["max_endurance_charges"] = 3,
	["max_frenzy_charges"] = 3,
	["max_power_charges"] = 3,
	["maximum_righteous_charges"] = 5,
	["maximum_blood_scythe_charges"] = 5,
	["base_number_of_traps_allowed"] = 15,
	["base_number_of_remote_mines_allowed"] = 15,
	["max_charged_attack_stacks"] = 6,
	["max_talisman_degen_stacks"] = 20,
	["max_frost_nova_stacks"] = 20,
	["max_rampage_stacks"] = 1000,
	["damage_+%_per_10_rampage_stacks"] = 2,
	["movement_velocity_+%_per_10_rampage_stacks"] = 1,
	["pvp_shield_damage_+%_final"] = -15,
	["minions_have_labyrinth_trap_degen_effect_+%"] = -90,
	["minions_are_immune_to_labyrinth_degen_effect"] = 0,
	["minion_damage_taken_+%_from_spike_traps_final"] = -90,
	["minion_damage_taken_+%_from_arrow_traps_final"] = 0,
	["minion_damage_taken_+%_from_guillotine_traps_final"] = -90,
	["traps_explode_on_timeout"] = 1,
	["maximum_rage"] = 30,
	["max_delve_degen_stacks"] = 5000,
	["max_azurite_debuff_stacks"] = 10,
	["melee_variation"] = 1,
	["base_total_number_of_sigils_allowed"] = 3,
	["enable_movement_skill_animation_skipping"] = 1,
	["melee_hit_damage_stun_multiplier_+%"] = 50,
	["non_physical_hit_damage_stun_multiplier_+%"] = -33,
	["additional_insanity_effects_while_delirious"] = 1,
	["max_steel_ammo"] = 12,
	["chance_to_deal_triple_damage_%_per_brutal_charge"] = 3,
	["stun_threshold_+%_per_brutal_charge"] = 10,
	["elemental_damage_taken_goes_to_energy_shield_over_4_seconds_%_per_absorption_charge"] = 12,
	["actor_scale_+%_limit"] = 100,
	["mines_invulnerable_for_duration_ms"] = 2000,
	["traps_invulnerable_for_duration_ms"] = 2000,
	["damage_taken_when_hit_+%_final_per_fortification"] = -1,
	["base_max_fortification"] = 20,
	["base_presence_radius"] = 40,
	["mtx_max_killstreak_stacks"] = 1000,
	["mtx_max_killcounter_stacks"] = 30000,
	["melee_strike_bonus_attack_distance"] = 2,
	["melee_defer_damage_prediction"] = 1,
	["accuracy_rating_+%_final_at_max_distance_scaled"] = -90,
	["base_weapon_swap_duration_ms"] = 250,
	["poise_decay_%_per_second"] = 50,
	["enable_weapon_sets"] = true,
	["base_number_of_weapon_sets"] = 2,
	["quadruped_head_turn_duration_ms"] = 100,
	["has_quadruped_head_control"] = 0,
	["base_pay_cost_over_start_of_skill_animation"] = 1,
	["base_knockback_speed_+%"] = -30,
	["base_maximum_number_of_stored_corpses"] = 10,
	["base_block_angle_degrees"] = 210,
	["base_heavy_stun_duration_ms"] = 3000,
	["global_resummon_time_ms"] = 7500,
	["disable_minion_formation"] = 1,
	["can_evade_spells"] = 1,
	["crossbow_ammo_switch_time_ms"] = 300,
	["light_stun_threshold_+%_final_per_number_of_times_stunned_in_past_duration_from_ot"] = 50,
	["x_ms_for_number_of_times_stunned_in_past_duration_from_ot"] = 4000,
	["rage_loss_delay_ms"] = 2000,
	["stun_base_duration_override_ms"] = 500,
	["object_inherent_armour_break_amount_+%_final_against_normal_monsters"] = 200,
	["object_inherent_armour_break_amount_+%_final_against_magic_monsters"] = 100,
	["object_inherent_evasion_break_amount_+%_final_against_normal_monsters"] = 200,
	["object_inherent_evasion_break_amount_+%_final_against_magic_monsters"] = 100,
	["fixed_frost_wall_limit"] = 60,
	["player_allow_dodge_roll_cancel"] = 1,
	["maximum_cold_infusion_stacks"] = 6,
	["maximum_fire_infusion_stacks"] = 6,
	["maximum_lightning_infusion_stacks"] = 6,
	["maximum_chaos_infusion_stacks"] = 6,
	["base_infusion_duration_ms"] = 15000,
	["maximum_caltrops_allowed"] = 20,
	["maximum_volatility_allowed"] = 200,
	["base_speed"] = 37,
}
-- From Metadata/Monsters/Monster.ot
data.monsterConstants = {
	["item_drop_slots"] = 1,
	["energy_shield_recharge_rate_per_minute_%"] = 750,
	["base_maximum_mana"] = 200,
	["maximum_physical_damage_reduction_%"] = 75,
	["base_maximum_all_resistances_%"] = 75,
	["monster_base_flask_charges_percent_of_monster_power"] = 50,
	["base_maximum_chance_to_evade_%"] = 95,
	["base_critical_hit_damage_bonus"] = 30,
	["max_endurance_charges"] = 3,
	["max_frenzy_charges"] = 3,
	["max_power_charges"] = 3,
	["object_inherent_base_maximum_block_%_from_ot"] = 75,
	["base_maximum_spell_block_%"] = 75,
	["movement_velocity_cap"] = 128,
	["max_azurite_debuff_stacks"] = 10,
	["ignore_skill_weapon_restrictions"] = 1,
	["melee_hit_damage_stun_multiplier_+%"] = 33,
	["non_physical_hit_damage_stun_multiplier_+%"] = -50,
	["scale_melee_range_to_actor_scale"] = 1,
	["use_melee_pattern_range"] = 1,
	["melee_swing_not_scaled_by_area_modifiers"] = 1,
	["actor_scale_+%_limit"] = 200,
	["base_heavy_stun_duration_ms"] = 2000,
	["melee_defer_damage_prediction"] = 1,
	["poise_decay_delay_ms"] = 8000,
	["poise_decay_%_per_second"] = 5,
	["base_block_angle_degrees"] = 180,
	["action_attack_or_cast_time_uses_animation_length"] = 1,
	["slow_potency_+%_final_per_additional_player"] = -10,
	["check_for_targets_between_initiator_and_projectile_source"] = 1,
	["maximum_life_+%_final_per_additional_player"] = 50,
	["maximum_energy_shield_+%_final_per_additional_player"] = 50,
	["poise_threshold_+%_final_per_additional_player"] = 50,
	["stun_base_duration_override_ms"] = 500,
	["bleeding_moving_damage_%_of_base_override"] = 200,
}
-- From PlayerMinionIntrinsicStats.dat
data.playerMinionIntrinsicStats = {
	["stun_base_duration_override_ms"] = 500,
	["accuracy_rating_+%_final_at_max_distance_scaled"] = -90,
	["accuracy_rating_per_level"] = 10,
	["base_critical_hit_damage_bonus"] = 70,
}
-- From MonsterVarieties.dat combined with SkillTotemVariations.dat
data.totemLifeMult = { [1] = 1, [2] = 1, [3] = 1, [5] = 1, [12] = 1, [18] = 1, [19] = 1, [22] = 1, [15] = 1.2, [23] = 1, [24] = 1, }
data.monsterVarietyLifeMult = {
	["Drowned"] = 1,
	["Lumbering Dead"] = 1,
	["Vile Imp"] = 0.65,
	["Fungal Rattler"] = 1,
	["Wretched Rattler"] = 1,
	["Decrepit Mercenary"] = 1.2,
	["Rust Skeleton"] = 1,
	["Risen Maraketh"] = 1,
	["Ancient Ezomyte"] = 1,
	["Risen Rattler"] = 1,
	["Risen Farmhand"] = 1,
	["Burning Dead"] = 1,
	["Risen Arbalest"] = 1.35,
	["Unearthed Skeletal Swordsman"] = 1,
	["Unearthed Skeletal Warrior"] = 1,
	["Unearthed Skeletal Archer"] = 1,
	["Unearthed Urchin"] = 0.65,
	["Rotting Hulk"] = 2.5,
	["Bog Hulk"] = 2.5,
	["Sandscoured Dead"] = 1,
	["Dread Servant"] = 1.5,
	["Desiccated Lich"] = 1.5,
	["Sandflesh Skeleton"] = 1,
	["Sandflesh Warrior"] = 1,
	["Sandflesh Mage"] = 1,
	["Maraketh Undead"] = 1,
	["Vaal Skeletal Warrior"] = 1,
	["Vaal Skeletal Squire"] = 1,
	["Vaal Skeletal Priest"] = 1,
	["Vaal Skeletal Archer"] = 1,
	["Rattling Gibbet"] = 2,
	["Prowling Skeleton"] = 1.25,
	["Eaten Rat"] = 0.9,
	["Eternal Prisoner"] = 1,
	["Rotting Guard"] = 1,
}
-- From MonsterMapDifficulty.dat
data.mapLevelLifeMult = { [66] = 1, [67] = 1, [68] = 1, [69] = 1, [70] = 1, [71] = 1, [72] = 1, [73] = 1, [74] = 1, [75] = 1, [76] = 1, [77] = 1, [78] = 1, [79] = 1, [80] = 1, [81] = 1, [82] = 1, [83] = 1, [84] = 1, [85] = 1, [86] = 1, [87] = 1, [88] = 1, [89] = 1, [90] = 1, }
-- From MonsterMapBossDifficulty.dat
data.mapLevelBossLifeMult = { [1] = 1, [2] = 1, [3] = 1, [4] = 1, [5] = 1, [6] = 1, [7] = 1, [8] = 1, [9] = 1, [10] = 1, [11] = 1, [12] = 1, [13] = 1, [14] = 1, [15] = 1, [16] = 1, [17] = 1, [18] = 1, [19] = 1, [20] = 1, [21] = 1, [22] = 1, [23] = 1, [24] = 1, [25] = 1, [26] = 1, [27] = 1, [28] = 1, [29] = 1, [30] = 1, [31] = 1, [32] = 1, [33] = 1, [34] = 1, [35] = 1, [36] = 1, [37] = 1, [38] = 1, [39] = 1, [40] = 1, [41] = 1, [42] = 1, [43] = 1, [44] = 1, [45] = 1, [46] = 1, [47] = 1, [48] = 1, [49] = 1, [50] = 1, [51] = 1, [52] = 1, [53] = 1, [54] = 1, [55] = 1, [56] = 1, [57] = 1, [58] = 1, [59] = 1, [60] = 1, [61] = 1, [62] = 1, [63] = 1, [64] = 1, [65] = 1, [66] = 1, [67] = 1, [68] = 1, [69] = 1, [70] = 1, [71] = 1, [72] = 1, [73] = 1, [74] = 1, [75] = 1, [76] = 1, [77] = 1, [78] = 1, [79] = 1, [80] = 1, [81] = 1, [82] = 1, [83] = 1, [84] = 1, [85] = 1, [86] = 1, [87] = 1, [88] = 1, [89] = 1, [90] = 1, }
data.mapLevelBossAilmentMult = { [1] = 1, [2] = 1, [3] = 1, [4] = 1, [5] = 1, [6] = 1, [7] = 1, [8] = 1, [9] = 1, [10] = 1, [11] = 1, [12] = 1, [13] = 1, [14] = 1, [15] = 1, [16] = 1, [17] = 1, [18] = 1, [19] = 1, [20] = 1, [21] = 1, [22] = 1, [23] = 1, [24] = 1, [25] = 1, [26] = 1, [27] = 1, [28] = 1, [29] = 1, [30] = 1, [31] = 1, [32] = 1, [33] = 1, [34] = 1, [35] = 1, [36] = 1, [37] = 1, [38] = 1, [39] = 1, [40] = 1, [41] = 1, [42] = 1, [43] = 1, [44] = 1, [45] = 1, [46] = 1, [47] = 1, [48] = 1, [49] = 1, [50] = 1, [51] = 1, [52] = 1, [53] = 1, [54] = 1, [55] = 1, [56] = 1, [57] = 1, [58] = 1, [59] = 1, [60] = 1, [61] = 1, [62] = 1, [63] = 1, [64] = 1, [65] = 1, [66] = 1, [67] = 1, [68] = 1, [69] = 1, [70] = 1, [71] = 1, [72] = 1, [73] = 1, [74] = 1, [75] = 1, [76] = 1, [77] = 1, [78] = 1, [79] = 1, [80] = 1, [81] = 1, [82] = 1, [83] = 1, [84] = 1, [85] = 1, [86] = 1, [87] = 1, [88] = 1, [89] = 1, [90] = 1, }
