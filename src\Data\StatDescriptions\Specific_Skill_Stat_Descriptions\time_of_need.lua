-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} second duration between Blessings"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[2]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Blessing recovers {0} Life and removes Curses and Elemental Ailments"
			}
		},
		stats={
			[1]="skill_time_of_need_gain_x_life"
		}
	},
	["base_skill_effect_duration"]=1,
	parent="skill_stat_descriptions",
	["skill_effect_duration"]=2,
	["skill_time_of_need_gain_x_life"]=3
}