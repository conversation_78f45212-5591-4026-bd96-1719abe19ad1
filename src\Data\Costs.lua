-- This file is automatically generated, do not edit!
-- Skill costs data (c) Grinding Gear Games

return {
	[1] = {
		Resource = "Mana",
		Stat = "base_mana_cost",
		ResourceString = "{0} Mana",
		Divisor = 1,
	},
	[2] = {
		Resource = "Life",
		Stat = "base_life_cost",
		ResourceString = "{0} Life",
		Divisor = 1,
	},
	[3] = {
		Resource = "ES",
		Stat = "base_es_cost",
		ResourceString = "{0} Energy Shield",
		Divisor = 1,
	},
	[4] = {
		Resource = "Rage",
		Stat = "base_rage_cost",
		ResourceString = "{0} Rage",
		Divisor = 1,
	},
	[5] = {
		Resource = "ManaPercent",
		Stat = "base_mana_cost_%",
		ResourceString = "{0}% Mana",
		Divisor = 1,
	},
	[6] = {
		Resource = "LifePercent",
		Stat = "base_life_cost_%",
		ResourceString = "{0}% Life",
		Divisor = 1,
	},
	[7] = {
		Resource = "UnreservedManaPercent",
		Stat = "base_unreserved_mana_cost_%",
		ResourceString = "{0}% Unreserved Mana ",
		Divisor = 1,
	},
	[8] = {
		Resource = "ManaPerMinute",
		Stat = "base_mana_cost_per_minute",
		ResourceString = "{0} Mana per second",
		Divisor = 60,
	},
	[9] = {
		Resource = "LifePerMinute",
		Stat = "base_life_cost_per_minute",
		ResourceString = "{0} Life per second",
		Divisor = 60,
	},
	[10] = {
		Resource = "ManaPercentPerMinute",
		Stat = "base_mana_cost_%_per_minute",
		ResourceString = "{0}% Mana per second",
		Divisor = 60,
	},
	[11] = {
		Resource = "LifePercentPerMinute",
		Stat = "base_life_cost_%_per_minute",
		ResourceString = "{0}% Life per second",
		Divisor = 60,
	},
	[12] = {
		Resource = "ESPerMinute",
		Stat = "base_es_cost_per_minute",
		ResourceString = "{0} Energy Shield per second",
		Divisor = 60,
	},
	[13] = {
		Resource = "ESPercentPerMinute",
		Stat = "base_es_cost_%_per_minute",
		ResourceString = "{0}% Energy Shield per second",
		Divisor = 60,
	},
	[14] = {
		Resource = "ESPercent",
		Stat = "base_es_cost_%",
		ResourceString = "{0}% Energy Shield",
		Divisor = 1,
	},
	[15] = {
		Resource = "Soul",
		Stat = " ",
		ResourceString = "{0} Souls Per Use",
		Divisor = 1,
	}
}
