return {
	abyssbosslifescalingperlevel={
	},
	abyssobjects={
	},
	abyssregions={
	},
	abysstheme={
	},
	accountquestflags={
	},
	achievementitemrewards={
	},
	achievementitems={
		[1]={
			list=false,
			name="Id",
			refTo="Achievements",
			type="String",
			width=240
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[5]={
			list=false,
			name="CompletionsRequired",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=false,
			name="AchievementsKey",
			refTo="Achievements",
			type="Key",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		}
	},
	achievements={
	},
	achievementsetrewards={
	},
	achievementsets={
	},
	achievementsetsdisplay={
	},
	actiontypes={
		[1]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=250
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="UInt16",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		}
	},
	activesettings={
	},
	activeskillrequirementicons={
	},
	activeskillrequirements={
		[1]={
			list=false,
			name="Id",
			refTo="GrantedEffects",
			type="Key",
			width=180
		},
		[2]={
			list=false,
			name="BuffDefinition",
			refTo="buffdefinitions",
			type="Key",
			width=300
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[7]={
			list=false,
			name="StatRequrement",
			refTo="Stats",
			type="Key",
			width=300
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		}
	},
	activeskills={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=190
		},
		[2]={
			list=false,
			name="DisplayName",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="Description",
			refTo="",
			type="String",
			width=500
		},
		[4]={
			list=false,
			name="ActionType",
			refTo="ActionTypes",
			type="Key",
			width=170
		},
		[5]={
			list=false,
			name="Icon",
			refTo="",
			type="String",
			width=100
		},
		[6]={
			list=true,
			name="TargetTypes",
			refTo="ActiveSkillTargetTypes",
			type="Enum",
			width=100
		},
		[7]={
			list=true,
			name="SkillTypes",
			refTo="ActiveSkillType",
			type="Key",
			width=330
		},
		[8]={
			list=false,
			name="WebsiteDescription",
			refTo="",
			type="String",
			width=150
		},
		[9]={
			list=false,
			name="WebsiteImage",
			refTo="",
			type="String",
			width=150
		},
		[10]={
			list=false,
			name="HideOnWebsite",
			refTo="",
			type="Bool",
			width=80
		},
		[11]={
			list=false,
			name="GrantedEffect",
			refTo="",
			type="String",
			width=150
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=40
		},
		[13]={
			list=false,
			name="SkillTotem",
			refTo="",
			type="Int",
			width=80
		},
		[14]={
			list=false,
			name="ManuallyCast",
			refTo="",
			type="Bool",
			width=100
		},
		[15]={
			list=true,
			name="SkillSpecificStat",
			refTo="Stats",
			type="Key",
			width=250
		},
		[16]={
			list=true,
			name="GenericStat",
			refTo="Stats",
			type="Key",
			width=250
		},
		[17]={
			list=true,
			name="MinionSkillTypes",
			refTo="ActiveSkillType",
			type="Key",
			width=420
		},
		[18]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[19]={
			list=false,
			name="isGem",
			refTo="",
			type="Bool",
			width=70
		},
		[20]={
			list=true,
			name="SecondarySkillSpecificStat",
			refTo="Stats",
			type="Key",
			width=400
		},
		[21]={
			list=false,
			name="SkillMine",
			refTo="SkillMines",
			type="Int",
			width=80
		},
		[22]={
			list=false,
			name="TargetingBehaviours",
			refTo="AlternateSkillTargetingBehaviours",
			type="Key",
			width=250
		},
		[23]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[24]={
			list=false,
			name="TotemAI",
			refTo="",
			type="String",
			width=250
		},
		[25]={
			list=true,
			name="StatContextFlags",
			refTo="VirtualStatContextFlags",
			type="Key",
			width=150
		},
		[26]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[27]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[28]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[29]={
			list=false,
			name="RegularVersion",
			refTo="ActiveSkills",
			type="Key",
			width=150
		},
		[30]={
			list=false,
			name="Video",
			refTo="",
			type="String",
			width=150
		},
		[31]={
			list=false,
			name="Audio",
			refTo="CharacterAudioEvents",
			type="Key",
			width=150
		},
		[32]={
			list=false,
			name="CompanionAIScript",
			refTo="",
			type="String",
			width=150
		},
		[33]={
			list=false,
			name="MinionType",
			refTo="MinionType",
			type="Key",
			width=150
		},
		[34]={
			list=false,
			name="SecondaryAudio",
			refTo="CharacterAudioEvents",
			type="Key",
			width=150
		},
		[35]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[36]={
			list=false,
			name="ShortDescription",
			refTo="",
			type="String",
			width=540
		},
		[37]={
			list=false,
			name="StatDescriptionType",
			refTo="",
			type="Int",
			width=150
		},
		[38]={
			list=false,
			name="StatDescription",
			refTo="",
			type="String",
			width=610
		},
		[39]={
			list=false,
			name="WeaponRestrictions",
			refTo="ActiveSkillWeaponRequirement",
			type="Key",
			width=150
		}
	},
	activeskilltype={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=250
		},
		[2]={
			list=false,
			name="FlagStat",
			refTo="Stats",
			type="Key",
			width=150
		}
	},
	activeskillweaponrequirement={
		[1]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=true,
			name="WeaponClass",
			refTo="WieldableClasses",
			type="Key",
			width=300
		},
		[3]={
			list=false,
			name="String",
			refTo="clientstrings",
			type="Key",
			width=150
		}
	},
	acts={
	},
	addbufftotargetvarieties={
		[1]={
			list=false,
			name="BuffDefinitions",
			refTo="BuffDefinitions",
			type="Key",
			width=250
		},
		[2]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=true,
			name="StatsKeys",
			refTo="Stats",
			type="Key",
			width=1210
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[7]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[8]={
			list=true,
			name="Stat",
			refTo="Stats",
			type="Key",
			width=150
		},
		[9]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		}
	},
	additionallifescaling={
		[1]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[2]={
			list=false,
			name="DATFilename",
			refTo="",
			type="String",
			width=240
		},
		[3]={
			list=false,
			name="DATFilepath",
			refTo="",
			type="String",
			width=290
		}
	},
	additionallifescalingperlevel={
	},
	additionalmonsterpacksfromstats={
	},
	additionalmonsterpacksstatmode={
	},
	advancedcraftingbenchcustomtags={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=100
		},
		[2]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=220
		},
		[3]={
			list=false,
			name="Index",
			refTo="",
			type="Int",
			width=100
		}
	},
	advancedcraftingbenchtabfiltertypes={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="Icon",
			refTo="",
			type="String",
			width=150
		},
		[4]={
			list=true,
			name="Tags",
			refTo="Tags",
			type="Key",
			width=250
		},
		[5]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=150
		}
	},
	advancedskillstutorial={
	},
	aegisvariations={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="MitigatesPhysicalDmg",
			refTo="",
			type="Bool",
			width=150
		},
		[3]={
			list=false,
			name="MitigatesFireDmg",
			refTo="",
			type="Bool",
			width=150
		},
		[4]={
			list=false,
			name="MitigatesColdDmg",
			refTo="",
			type="Bool",
			width=150
		},
		[5]={
			list=false,
			name="MitigatesLightningDmg",
			refTo="",
			type="Bool",
			width=150
		},
		[6]={
			list=false,
			name="MitigatesChaosDmg",
			refTo="",
			type="Bool",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="BuffDefinitions",
			type="Key",
			width=150
		},
		[8]={
			list=false,
			name="",
			refTo="BuffDefinitions",
			type="Key",
			width=150
		},
		[9]={
			list=false,
			name="",
			refTo="BuffDefinitions",
			type="Key",
			width=190
		},
		[10]={
			list=true,
			name="Animation",
			refTo="MiscAnimated",
			type="Key",
			width=150
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[12]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	afflictionbalanceperlevel={
	},
	afflictionendgameareas={
	},
	afflictionendgamewavemods={
	},
	afflictionfixedmods={
	},
	afflictionrandommodcategories={
	},
	afflictionrandomspawns={
	},
	afflictionrewardmapmods={
	},
	afflictionrewardtypes={
	},
	afflictionrewardtypevisuals={
	},
	afflictionsplitdemons={
	},
	afflictionstartdialogue={
	},
	alternatebehaviourtypes={
	},
	alternatepassiveadditions={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=260
		},
		[2]={
			list=false,
			name="AlternateTreeVersionsKey",
			refTo="AlternateTreeVersions",
			type="Key",
			width=150
		},
		[3]={
			list=false,
			name="SpawnWeight",
			refTo="",
			type="Int",
			width=80
		},
		[4]={
			list=true,
			name="StatsKeys",
			refTo="Stats",
			type="Key",
			width=200
		},
		[5]={
			list=false,
			name="Stat1",
			refTo="",
			type="Interval",
			width=70
		},
		[6]={
			list=false,
			name="Stat2",
			refTo="",
			type="Interval",
			width=70
		},
		[7]={
			list=false,
			name="Stat3",
			refTo="",
			type="Interval",
			width=70
		},
		[8]={
			list=true,
			name="PassiveType",
			refTo="",
			type="Int",
			width=70
		},
		[9]={
			list=false,
			name="SpawnWeight",
			refTo="",
			type="Int",
			width=90
		}
	},
	alternatepassiveskills={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=230
		},
		[2]={
			list=false,
			name="AlternateTreeVersionsKey",
			refTo="AlternateTreeVersions",
			type="Key",
			width=140
		},
		[3]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[4]={
			list=true,
			name="PassiveType",
			refTo="",
			type="Int",
			width=70
		},
		[5]={
			list=true,
			name="StatsKeys",
			refTo="Stats",
			type="Key",
			width=460
		},
		[6]={
			list=false,
			name="Stat1",
			refTo="",
			type="Interval",
			width=70
		},
		[7]={
			list=false,
			name="Stat2",
			refTo="",
			type="Interval",
			width=70
		},
		[8]={
			list=false,
			name="Stat3",
			refTo="",
			type="Interval",
			width=70
		},
		[9]={
			list=false,
			name="Stat4",
			refTo="",
			type="Interval",
			width=70
		},
		[10]={
			list=false,
			name="Stat5",
			refTo="",
			type="Interval",
			width=70
		},
		[11]={
			list=false,
			name="Stat6",
			refTo="",
			type="Interval",
			width=70
		},
		[12]={
			list=false,
			name="SpawnWeight",
			refTo="",
			type="Int",
			width=90
		},
		[13]={
			list=false,
			name="ConquerorIndex",
			refTo="",
			type="Int",
			width=90
		},
		[14]={
			list=false,
			name="Random",
			refTo="",
			type="Interval",
			width=150
		},
		[15]={
			list=false,
			name="FlavourText",
			refTo="",
			type="String",
			width=150
		},
		[16]={
			list=false,
			name="DDSIcon",
			refTo="",
			type="String",
			width=150
		},
		[17]={
			list=true,
			name="AchievementItemsKeys",
			refTo="",
			type="Key",
			width=150
		},
		[18]={
			list=false,
			name="ConquerorVersion",
			refTo="",
			type="Int",
			width=150
		},
		[19]={
			list=false,
			name="ConquerorSpawnWeight",
			refTo="",
			type="Int",
			width=150
		}
	},
	alternatequalitycurrencydecayfactors={
	},
	alternatequalitytypes={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=200
		},
		[2]={
			list=false,
			name="Description",
			refTo="",
			type="String",
			width=300
		},
		[3]={
			list=false,
			name="BaseItemType",
			refTo="BaseItemTypes",
			type="Key",
			width=200
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=230
		},
		[5]={
			list=false,
			name="HASH16",
			refTo="",
			type="UInt16",
			width=150
		}
	},
	alternateskilltargetingbehaviours={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=270
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=60
		},
		[3]={
			list=false,
			name="ClientStrings",
			refTo="ClientStrings",
			type="Key",
			width=220
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[7]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=50
		}
	},
	alternatetreeart={
	},
	alternatetreepassivesizes={
	},
	alternatetreeversions={
		[1]={
			list=false,
			name="ConquerorType",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="SmallAttributeReplaced",
			refTo="",
			type="Bool",
			width=150
		},
		[3]={
			list=false,
			name="SmallNormalPassiveReplaced ",
			refTo="",
			type="Bool",
			width=150
		},
		[4]={
			list=false,
			name="SmallAttributePassiveSkillAdditions",
			refTo="",
			type="Interval",
			width=170
		},
		[5]={
			list=false,
			name="NotableAdditions",
			refTo="",
			type="Interval",
			width=150
		},
		[6]={
			list=false,
			name="SmallNormalPassiveSkillAdditions",
			refTo="",
			type="Interval",
			width=170
		},
		[7]={
			list=false,
			name="NotableReplacementSpawnWeight ",
			refTo="",
			type="Int",
			width=200
		}
	},
	ancestraltrialunits={
	},
	animatedobjectflags={
	},
	animateweaponuniques={
		[1]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=200
		},
		[2]={
			list=false,
			name="VisualIdentity",
			refTo="ItemVisualIdentity",
			type="Key",
			width=200
		},
		[3]={
			list=false,
			name="ItemClass",
			refTo="ItemClasses",
			type="Key",
			width=200
		},
		[4]={
			list=false,
			name="MinLevelReq",
			refTo="",
			type="Int",
			width=150
		}
	},
	animation={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		}
	},
	applydamagefunctions={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=250
		}
	},
	archetyperewards={
	},
	archetypes={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		}
	},
	architectlifescalingperlevel={
	},
	archnemesismetarewards={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=220
		}
	},
	archnemesismodcomboachievements={
		[1]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	archnemesismods={
		[1]={
			list=false,
			name="Id",
			refTo="Mods",
			type="Key",
			width=310
		},
		[2]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="Visual",
			refTo="ArchnemesisModVisuals",
			type="Key",
			width=150
		},
		[4]={
			list=true,
			name="TextStyles",
			refTo="",
			type="String",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		}
	},
	archnemesismodvisuals={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		}
	},
	archnemesisrecipes={
		[1]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	areadifficultystats={
	},
	areainfluencedoodads={
	},
	areastatsperdifficulty={
	},
	areatransitionanimations={
	},
	areatransitionanimationtypes={
	},
	areatransitioninfo={
	},
	areatype={
	},
	armourclasses={
	},
	armoursurfacetypes={
	},
	armourtypes={
		[1]={
			list=false,
			name="BaseItemType",
			refTo="BaseItemTypes",
			type="Key",
			width=400
		},
		[2]={
			list=false,
			name="Armour",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=false,
			name="Evasion",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=false,
			name="EnergyShield",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="MovementPenalty",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	arrowspearoverride={
	},
	arrowstatreference={
	},
	ascendancy={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=90
		},
		[2]={
			list=false,
			name="Index",
			refTo="",
			type="Int",
			width=50
		},
		[3]={
			list=true,
			name="Class",
			refTo="Characters",
			type="Key",
			width=250
		},
		[4]={
			list=false,
			name="FlavourRect",
			refTo="",
			type="String",
			width=120
		},
		[5]={
			list=false,
			name="FlavourColour",
			refTo="",
			type="String",
			width=100
		},
		[6]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=90
		},
		[7]={
			list=false,
			name="Flavour",
			refTo="",
			type="String",
			width=150
		},
		[8]={
			list=false,
			name="Audio",
			refTo="",
			type="String",
			width=480
		},
		[9]={
			list=false,
			name="PassiveTreeImage",
			refTo="",
			type="String",
			width=150
		},
		[10]={
			list=false,
			name="distanceTree",
			refTo="",
			type="Int",
			width=180
		},
		[11]={
			list=false,
			name="TreeRegionAngle",
			refTo="",
			type="Int",
			width=100
		},
		[12]={
			list=false,
			name="BackgroundImage",
			refTo="",
			type="Int",
			width=150
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[14]={
			list=false,
			name="isDisabled",
			refTo="",
			type="Bool",
			width=150
		}
	},
	atlasawakeningstats={
	},
	atlasbasetypedrops={
	},
	atlasentities={
	},
	atlasexilebossarenas={
	},
	atlasexileinfluence={
	},
	atlasexileinfluencedata={
	},
	atlasexileinfluenceoutcomes={
	},
	atlasexileinfluenceoutcometypes={
	},
	atlasexileinfluencepacks={
	},
	atlasexileinfluencesets={
	},
	atlasexileregionquestflags={
	},
	atlasexiles={
	},
	atlasfavouredmapslots={
	},
	atlasfog={
	},
	atlasinfluencedata={
	},
	atlasinfluenceoutcomes={
	},
	atlasinfluenceoutcometypes={
	},
	atlasinfluencesets={
	},
	atlasmemoryline={
	},
	atlasmods={
		[1]={
			list=false,
			name="Mod",
			refTo="Mods",
			type="Key",
			width=320
		},
		[2]={
			list=false,
			name="Tier",
			refTo="",
			type="Int",
			width=70
		}
	},
	atlasmodtiers={
	},
	atlasnode={
	},
	atlasnodedefinition={
	},
	atlaspassiveskillsubtrees={
	},
	atlaspassiveskilltreegrouptype={
	},
	atlaspoem={
	},
	atlaspositions={
	},
	atlasprimordialaltarchoices={
	},
	atlasprimordialaltarchoicetypes={
	},
	atlasprimordialbosses={
	},
	atlasprimordialbossinfluence={
	},
	atlasprimordialbossoptions={
	},
	atlasquadrant={
	},
	atlasregions={
	},
	atlasregionupgraderegions={
	},
	atlasregionupgradesinventorylayout={
	},
	atlassector={
	},
	atlasskillgraphs={
	},
	atlasupgradesinventorylayout={
	},
	attributerequirements={
		[1]={
			list=false,
			name="BaseType",
			refTo="BaseItemTypes",
			type="Key",
			width=400
		},
		[2]={
			list=false,
			name="ReqStr",
			refTo="",
			type="Int",
			width=100
		},
		[3]={
			list=false,
			name="ReqInt",
			refTo="",
			type="Int",
			width=100
		},
		[4]={
			list=false,
			name="ReqDex",
			refTo="",
			type="Int",
			width=100
		}
	},
	attributes={
	},
	awarddisplay={
	},
	azmerifeaturerooms={
	},
	azmerilifescalingperlevel={
		[1]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	azmeriwoodsdusttype={
		[1]={
			list=false,
			name="AzmeriLeader",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="BaseItemType",
			refTo="BaseItemTypes",
			type="Key",
			width=150
		},
		[3]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[4]={
			list=false,
			name="UISymbol",
			refTo="",
			type="String",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[7]={
			list=false,
			name="Stat",
			refTo="Stats",
			type="Key",
			width=250
		},
		[8]={
			list=false,
			name="UiArt",
			refTo="",
			type="String",
			width=150
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=190
		}
	},
	backenderrors={
	},
	ballisticbouncebehaviour={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=220
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		}
	},
	ballisticbounceoverride={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=270
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		}
	},
	baseitemtypes={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=560
		},
		[2]={
			list=false,
			name="ItemClass",
			refTo="ItemClasses",
			type="Key",
			width=130
		},
		[3]={
			list=false,
			name="Width",
			refTo="",
			type="Int",
			width=40
		},
		[4]={
			list=false,
			name="Height",
			refTo="",
			type="Int",
			width=40
		},
		[5]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=160
		},
		[6]={
			list=false,
			name="BaseType",
			refTo="",
			type="String",
			width=290
		},
		[7]={
			list=false,
			name="DropLevel",
			refTo="",
			type="Int",
			width=60
		},
		[8]={
			list=false,
			name="FlavourTextKey",
			refTo="FlavourText",
			type="Key",
			width=100
		},
		[9]={
			list=true,
			name="ImplicitMods",
			refTo="Mods",
			type="Key",
			width=470
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[11]={
			list=false,
			name="SoundEffect",
			refTo="SoundEffects",
			type="Key",
			width=80
		},
		[12]={
			list=true,
			name="Tags",
			refTo="Tags",
			type="Key",
			width=150
		},
		[13]={
			list=false,
			name="ModDomain",
			refTo="ModDomains",
			type="Enum",
			width=80
		},
		[14]={
			list=false,
			name="Hidden",
			refTo="",
			type="Int",
			width=50
		},
		[15]={
			list=false,
			name="ItemVisualIdentityKey",
			refTo="ItemVisualIdentity",
			type="Key",
			width=150
		},
		[16]={
			list=false,
			name="HASH32",
			refTo="",
			type="UInt",
			width=100
		},
		[17]={
			list=true,
			name="VendorRecipeAchievement",
			refTo="AchievementItems",
			type="Key",
			width=180
		},
		[18]={
			list=false,
			name="Inflection",
			refTo="",
			type="String",
			width=80
		},
		[19]={
			list=false,
			name="EquipAchievement",
			refTo="AchievementItems",
			type="Key",
			width=150
		},
		[20]={
			list=false,
			name="IsCorrupted",
			refTo="",
			type="Bool",
			width=90
		},
		[21]={
			list=true,
			name="IdentifyAchievement",
			refTo="AchievementItems",
			type="Key",
			width=150
		},
		[22]={
			list=true,
			name="IdentifyMagicAchievement",
			refTo="AchievementItems",
			type="Key",
			width=150
		},
		[23]={
			list=false,
			name="FragmentBaseItemTypesKey",
			refTo="BaseItemTypes",
			type="ShortKey",
			width=150
		},
		[24]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[25]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=100
		},
		[26]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=100
		},
		[27]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[28]={
			list=false,
			name="TradeMarketCategory",
			refTo="TradeMarketCategory",
			type="Key",
			width=150
		},
		[29]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[30]={
			list=true,
			name="Achievement",
			refTo="AchievementItems",
			type="Key",
			width=280
		},
		[31]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[32]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		}
	},
	battlepasses={
	},
	battlepassrewards={
	},
	battlepassrewardtypes={
	},
	battlepasstracks={
	},
	bestiarycapturablemonsters={
	},
	bestiaryencounters={
	},
	bestiaryfamilies={
	},
	bestiarygenus={
	},
	bestiarygroups={
	},
	bestiarynets={
	},
	bestiaryrecipecategories={
	},
	bestiaryrecipecomponent={
	},
	bestiaryrecipeitemcreation={
	},
	bestiaryrecipes={
	},
	betrayalchoiceactions={
	},
	betrayalchoices={
	},
	betrayaldialogue={
	},
	betrayaldialoguecue={
	},
	betrayalflags={
	},
	betrayalforts={
	},
	betrayaljobs={
	},
	betrayalranks={
	},
	betrayalrelationshipstate={
	},
	betrayaltargetflags={
	},
	betrayaltargetjobachievements={
	},
	betrayaltargetlifescalingperlevel={
	},
	betrayaltargets={
	},
	betrayaltraitorrewards={
	},
	betrayalupgrades={
	},
	betrayalupgradeslots={
	},
	betrayalwalllifescalingperlevel={
	},
	beyonddemons={
	},
	beyondfactions={
	},
	bindablevirtualkeys={
	},
	blightbalanceperlevel={
	},
	blightbosslifescalingperlevel={
	},
	blightchesttypes={
	},
	blightcraftingitems={
		[1]={
			list=false,
			name="Oil",
			refTo="BaseItemTypes",
			type="Key",
			width=300
		},
		[2]={
			list=false,
			name="Tier",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=true,
			name="Achievements",
			refTo="",
			type="Key",
			width=150
		},
		[4]={
			list=false,
			name="UseType",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="NameShort",
			refTo="",
			type="String",
			width=150
		},
		[6]={
			list=false,
			name="MapMod",
			refTo="Mods",
			type="Key",
			width=150
		}
	},
	blightcraftingrecipes={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="BlightCraftingResultsKey",
			refTo="BlightCraftingResults",
			type="Key",
			width=150
		},
		[3]={
			list=true,
			name="Recipe",
			refTo="BlightCraftingItems",
			type="Key",
			width=810
		}
	},
	blightcraftingresults={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="ModsKey",
			refTo="Mods",
			type="Key",
			width=150
		},
		[3]={
			list=false,
			name="PassiveSkillsKey",
			refTo="PassiveSkills",
			type="Key",
			width=260
		}
	},
	blightcraftingtypes={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		}
	},
	blightcraftinguniques={
	},
	blightedsporeauras={
	},
	blightencountertypes={
	},
	blightencounterwaves={
	},
	blightrewardtypes={
	},
	blightstashtablayout={
	},
	blighttopologies={
	},
	blighttopologynodes={
	},
	blighttowerauras={
	},
	blighttowers={
	},
	blighttowersperlevel={
	},
	bloodhiteffects={
	},
	bloodlines={
	},
	bloodtypes={
	},
	boltstatreference={
	},
	breachartvariations={
	},
	breachbosslifescalingperlevel={
	},
	breachelement={
	},
	breachstones={
	},
	breachstoneupgrades={
	},
	buffdefinitions={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=200
		},
		[2]={
			list=false,
			name="Description",
			refTo="",
			type="String",
			width=440
		},
		[3]={
			list=false,
			name="Invisible",
			refTo="",
			type="Bool",
			width=50
		},
		[4]={
			list=false,
			name="Removable",
			refTo="",
			type="Bool",
			width=60
		},
		[5]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=170
		},
		[6]={
			list=true,
			name="Stats",
			refTo="Stats",
			type="Key",
			width=610
		},
		[7]={
			list=false,
			name="CancelOnDeath",
			refTo="",
			type="Bool",
			width=90
		},
		[8]={
			list=false,
			name="MergeMode",
			refTo="BuffMergeModes",
			type="Enum",
			width=70
		},
		[9]={
			list=false,
			name="ShowCount",
			refTo="",
			type="Bool",
			width=70
		},
		[10]={
			list=false,
			name="MaxStat",
			refTo="Stats",
			type="Key",
			width=250
		},
		[11]={
			list=false,
			name="CurrentStat",
			refTo="Stats",
			type="Key",
			width=170
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=60
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Enum",
			width=60
		},
		[14]={
			list=false,
			name="Visual",
			refTo="BuffVisuals",
			type="Key",
			width=180
		},
		[15]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=40
		},
		[16]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=40
		},
		[17]={
			list=false,
			name="",
			refTo="",
			type="Enum",
			width=50
		},
		[18]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=40
		},
		[19]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=40
		},
		[20]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=40
		},
		[21]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[22]={
			list=false,
			name="Limit",
			refTo="",
			type="Int",
			width=40
		},
		[23]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=40
		},
		[24]={
			list=false,
			name="Implementation",
			refTo="",
			type="String",
			width=180
		},
		[25]={
			list=false,
			name="isRecovery",
			refTo="",
			type="Bool",
			width=60
		},
		[26]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=40
		},
		[27]={
			list=false,
			name="MinStat",
			refTo="Stats",
			type="Key",
			width=180
		},
		[28]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=40
		},
		[29]={
			list=false,
			name="UIStackMode",
			refTo="",
			type="Int",
			width=80
		},
		[30]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[31]={
			list=false,
			name="IsSkillBuff",
			refTo="",
			type="Bool",
			width=70
		},
		[32]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[33]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[34]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[35]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[36]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[37]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[38]={
			list=true,
			name="Stats2",
			refTo="Stats",
			type="Key",
			width=250
		},
		[39]={
			list=true,
			name="GrantedFlags",
			refTo="Stats",
			type="Key",
			width=250
		},
		[40]={
			list=true,
			name="GrantedStats",
			refTo="Stats",
			type="Key",
			width=450
		},
		[41]={
			list=true,
			name="ConditionStats",
			refTo="Stats",
			type="Key",
			width=150
		},
		[42]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[43]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[44]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[45]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[46]={
			list=false,
			name="",
			refTo="Stats",
			type="Key",
			width=100
		},
		[47]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[48]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=50
		},
		[49]={
			list=true,
			name="Stats",
			refTo="Stats",
			type="Key",
			width=150
		},
		[50]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[51]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[52]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=50
		}
	},
	buffgroups={
	},
	buffmergemodes={
	},
	buffstackuimodes={
	},
	bufftemplates={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=380
		},
		[2]={
			list=false,
			name="BuffDefinition",
			refTo="BuffDefinitions",
			type="Key",
			width=400
		},
		[3]={
			list=true,
			name="StatValues",
			refTo="",
			type="Int",
			width=100
		},
		[4]={
			list=false,
			name="AuraRadius",
			refTo="",
			type="Int",
			width=80
		},
		[5]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=true,
			name="",
			refTo="Mods",
			type="Int",
			width=150
		},
		[7]={
			list=false,
			name="Visual",
			refTo="BuffVisuals",
			type="Key",
			width=350
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=50
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[10]={
			list=true,
			name="Stats",
			refTo="Stats",
			type="Key",
			width=350
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[14]={
			list=false,
			name="",
			refTo="Stats",
			type="Key",
			width=150
		},
		[15]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		}
	},
	buffvisualartvariations={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=250
		}
	},
	buffvisualorbart={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=250
		},
		[2]={
			list=false,
			name="MiscAnimated",
			refTo="MiscAnimated",
			type="Key",
			width=150
		},
		[3]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		}
	},
	buffvisualorbs={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=250
		},
		[2]={
			list=false,
			name="OrbType",
			refTo="BuffVisualOrbTypes",
			type="Key",
			width=150
		},
		[3]={
			list=true,
			name="OrbArt",
			refTo="BuffVisualOrbArt",
			type="Key",
			width=250
		},
		[4]={
			list=true,
			name="OrbArtPlayer",
			refTo="BuffVisualOrbArt",
			type="Key",
			width=250
		},
		[5]={
			list=true,
			name="OrbArt2",
			refTo="BuffVisualOrbArt",
			type="Key",
			width=150
		}
	},
	buffvisualorbtypes={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=70
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=50
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=50
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=50
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[8]={
			list=false,
			name="RadiusStat",
			refTo="Stats",
			type="Key",
			width=200
		},
		[9]={
			list=false,
			name="HeightStat",
			refTo="Stats",
			type="Key",
			width=200
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[14]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		},
		[15]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[16]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		}
	},
	buffvisuals={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=250
		},
		[2]={
			list=false,
			name="Icon",
			refTo="",
			type="String",
			width=250
		},
		[3]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[4]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[5]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[7]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[10]={
			list=false,
			name="Description",
			refTo="",
			type="String",
			width=150
		},
		[11]={
			list=false,
			name="EPKFile",
			refTo="",
			type="String",
			width=150
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[14]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[15]={
			list=true,
			name="EPKFiles",
			refTo="",
			type="String",
			width=150
		},
		[16]={
			list=true,
			name="Orbs",
			refTo="BuffVisualOrbs",
			type="Key",
			width=150
		},
		[17]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[18]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[19]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[20]={
			list=false,
			name="",
			refTo="",
			type="ShortKey",
			width=150
		},
		[21]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		},
		[22]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		},
		[23]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[24]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[25]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=150
		}
	},
	buffvisualsartvariations={
		[1]={
			list=false,
			name="Buff",
			refTo="",
			type="String",
			width=250
		},
		[2]={
			list=true,
			name="Visuals",
			refTo="BuffVisuals",
			type="Key",
			width=350
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		}
	},
	buffvisualsetentries={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[3]={
			list=false,
			name="Visual",
			refTo="BuffVisuals",
			type="Key",
			width=250
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		}
	},
	buffvisualshapeshiftoverride={
	},
	caravanstops={
	},
	chanceableitemclasses={
	},
	characteraudioevents={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Event",
			refTo="Questflags",
			type="Key",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=true,
			name="GoddessAudio",
			refTo="CharacterTextAudio",
			type="Key",
			width=150
		},
		[6]={
			list=true,
			name="JackTheAxeAudio",
			refTo="CharacterTextAudio",
			type="Key",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[9]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=70
		}
	},
	charactercreationbutton={
	},
	charactercreationdialogue={
	},
	charactercreationicons={
	},
	charactereventtextaudio={
		[1]={
			list=false,
			name="Event",
			refTo="CharacterAudioEvents",
			type="Key",
			width=250
		},
		[2]={
			list=false,
			name="Character",
			refTo="Characters",
			type="Key",
			width=250
		},
		[3]={
			list=true,
			name="",
			refTo="CharacterTextAudio",
			type="Key",
			width=300
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=100
		}
	},
	charactermeleeskills={
		[1]={
			list=false,
			name="MainHandItem",
			refTo="WieldableClasses",
			type="Key",
			width=200
		},
		[2]={
			list=false,
			name="OffHandItem",
			refTo="WieldableClasses",
			type="Key",
			width=200
		},
		[3]={
			list=true,
			name="SkillGem",
			refTo="SkillGems",
			type="Key",
			width=700
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		}
	},
	characterpaneldescriptionmodes={
	},
	characterpanelstatcontexts={
	},
	characterpanelstats={
	},
	characterpaneltabs={
	},
	characters={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=250
		},
		[2]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=70
		},
		[3]={
			list=false,
			name="AnimatedObject",
			refTo="",
			type="String",
			width=270
		},
		[4]={
			list=false,
			name="ActorFile",
			refTo="",
			type="String",
			width=270
		},
		[5]={
			list=false,
			name="BaseMaxLife",
			refTo="",
			type="Int",
			width=100
		},
		[6]={
			list=false,
			name="BaseMaxMana",
			refTo="",
			type="Int",
			width=100
		},
		[7]={
			list=false,
			name="WeaponSpeed",
			refTo="",
			type="Int",
			width=100
		},
		[8]={
			list=false,
			name="MinDamage",
			refTo="",
			type="Int",
			width=100
		},
		[9]={
			list=false,
			name="MaxDamage",
			refTo="",
			type="Int",
			width=100
		},
		[10]={
			list=false,
			name="MaxAttackDistance",
			refTo="",
			type="Int",
			width=120
		},
		[11]={
			list=false,
			name="Icon",
			refTo="",
			type="String",
			width=80
		},
		[12]={
			list=false,
			name="IntegerId",
			refTo="",
			type="Int",
			width=80
		},
		[13]={
			list=false,
			name="BaseStrength",
			refTo="",
			type="Int",
			width=100
		},
		[14]={
			list=false,
			name="BaseDexterity",
			refTo="",
			type="Int",
			width=100
		},
		[15]={
			list=false,
			name="BaseIntelligence",
			refTo="",
			type="Int",
			width=100
		},
		[16]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[17]={
			list=false,
			name="Description",
			refTo="",
			type="String",
			width=150
		},
		[18]={
			list=false,
			name="StartSkillGem",
			refTo="BaseItemTypes",
			type="Key",
			width=150
		},
		[19]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=30
		},
		[20]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[21]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[22]={
			list=false,
			name="CharacterSize",
			refTo="",
			type="Int",
			width=80
		},
		[23]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[24]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[25]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[26]={
			list=false,
			name="IntroSoundFile",
			refTo="",
			type="String",
			width=150
		},
		[27]={
			list=true,
			name="StartWeapons",
			refTo="BaseItemTypes",
			type="Key",
			width=150
		},
		[28]={
			list=false,
			name="Gender",
			refTo="",
			type="String",
			width=60
		},
		[29]={
			list=false,
			name="TraitDescription",
			refTo="",
			type="String",
			width=150
		},
		[30]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=30
		},
		[31]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=30
		},
		[32]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=30
		},
		[33]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[34]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[35]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[36]={
			list=false,
			name="PassiveTreeImage",
			refTo="",
			type="String",
			width=150
		},
		[37]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[38]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[39]={
			list=false,
			name="AttrsAsId",
			refTo="",
			type="String",
			width=150
		},
		[40]={
			list=false,
			name="LoginScreen",
			refTo="",
			type="String",
			width=150
		},
		[41]={
			list=false,
			name="PlayerCritter",
			refTo="",
			type="String",
			width=150
		},
		[42]={
			list=false,
			name="PlayerEffect",
			refTo="",
			type="String",
			width=150
		},
		[43]={
			list=false,
			name="AfterImage",
			refTo="",
			type="String",
			width=150
		},
		[44]={
			list=false,
			name="Mirage",
			refTo="MonsterVarieties",
			type="Key",
			width=150
		},
		[45]={
			list=false,
			name="CloneImmobile",
			refTo="MonsterVarieties",
			type="Key",
			width=150
		},
		[46]={
			list=false,
			name="ReplicateClone",
			refTo="MonsterVarieties",
			type="Key",
			width=150
		},
		[47]={
			list=false,
			name="LightningClone",
			refTo="MonsterVarieties",
			type="Key",
			width=150
		},
		[48]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=50
		},
		[49]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=60
		},
		[50]={
			list=false,
			name="SkillTreeBackground",
			refTo="",
			type="String",
			width=150
		},
		[51]={
			list=false,
			name="Clone",
			refTo="MonsterVarieties",
			type="Key",
			width=150
		},
		[52]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=30
		},
		[53]={
			list=false,
			name="MirageWarrior",
			refTo="MonsterVarieties",
			type="Key",
			width=150
		},
		[54]={
			list=false,
			name="DoubleTwo",
			refTo="MonsterVarieties",
			type="Key",
			width=150
		},
		[55]={
			list=false,
			name="DarkExile",
			refTo="MonsterVarieties",
			type="Key",
			width=150
		},
		[56]={
			list=false,
			name="Attr",
			refTo="",
			type="String",
			width=150
		},
		[57]={
			list=false,
			name="AttrLowercase",
			refTo="",
			type="String",
			width=150
		},
		[58]={
			list=false,
			name="Script",
			refTo="",
			type="String",
			width=150
		},
		[59]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[60]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[61]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=50
		},
		[62]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=50
		},
		[63]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=50
		},
		[64]={
			list=false,
			name="BaseClass",
			refTo="",
			type="String",
			width=150
		},
		[65]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[66]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[67]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[68]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		},
		[69]={
			list=false,
			name="GemCuttingIcon1",
			refTo="",
			type="String",
			width=150
		},
		[70]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		},
		[71]={
			list=false,
			name="GemCuttingIcon1",
			refTo="",
			type="String",
			width=150
		}
	},
	characterstartitems={
	},
	characterstartqueststate={
	},
	characterstartstates={
	},
	characterstartstateset={
	},
	charactertextaudio={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Text",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="SoundFile",
			refTo="",
			type="String",
			width=150
		},
		[4]={
			list=false,
			name="ParrotSoundFile",
			refTo="",
			type="String",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	charactervariationgroups={
	},
	chargevariations={
	},
	chaticons={
	},
	chestclusters={
	},
	chesteffects={
	},
	chestitemtemplates={
	},
	chests={
	},
	classpassiveskilloverrides={
		[1]={
			list=false,
			name="Character",
			refTo="Characters",
			type="Key",
			width=200
		},
		[2]={
			list=false,
			name="OriginalNode",
			refTo="PassiveSkills",
			type="Key",
			width=200
		},
		[3]={
			list=false,
			name="SwitchedNode",
			refTo="PassiveSkills",
			type="Key",
			width=200
		}
	},
	clientlakedifficulty={
	},
	clientleagueaction={
	},
	clientstrings={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=320
		},
		[2]={
			list=false,
			name="Text",
			refTo="",
			type="String",
			width=670
		},
		[3]={
			list=false,
			name="XBoxText",
			refTo="",
			type="String",
			width=150
		},
		[4]={
			list=false,
			name="XBoxText2",
			refTo="",
			type="String",
			width=90
		},
		[5]={
			list=false,
			name="HASH32",
			refTo="",
			type="UInt",
			width=150
		},
		[6]={
			list=false,
			name="PlaystationText",
			refTo="",
			type="String",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		}
	},
	clientstrings2={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=280
		},
		[2]={
			list=false,
			name="Text",
			refTo="",
			type="String",
			width=300
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[5]={
			list=false,
			name="HASH32",
			refTo="",
			type="UInt",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		}
	},
	clientuiscreens={
	},
	cloneshot={
	},
	colours={
		[1]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Red",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=false,
			name="Green",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=false,
			name="Blue",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="RGB_String",
			refTo="",
			type="String",
			width=150
		}
	},
	combatuiprompts={
	},
	commands={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=300
		},
		[2]={
			list=false,
			name="Command",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[4]={
			list=false,
			name="EnglishCommand",
			refTo="",
			type="String",
			width=150
		},
		[5]={
			list=false,
			name="Description",
			refTo="",
			type="String",
			width=600
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		}
	},
	completionnotifications={
	},
	componentarmour={
		[1]={
			list=false,
			name="BaseItemType",
			refTo="BaseItemTypes",
			type="Key",
			width=320
		},
		[2]={
			list=false,
			name="Armour",
			refTo="",
			type="Int",
			width=70
		},
		[3]={
			list=false,
			name="Evasion",
			refTo="",
			type="Int",
			width=70
		},
		[4]={
			list=false,
			name="EnergyShield",
			refTo="",
			type="Int",
			width=70
		},
		[5]={
			list=false,
			name="MovementPenalty",
			refTo="",
			type="Int",
			width=100
		}
	},
	componentattributerequirements={
		[1]={
			list=false,
			name="BaseItemType",
			refTo="",
			type="String",
			width=310
		},
		[2]={
			list=false,
			name="Str",
			refTo="",
			type="Int",
			width=40
		},
		[3]={
			list=false,
			name="Dex",
			refTo="",
			type="Int",
			width=40
		},
		[4]={
			list=false,
			name="Int",
			refTo="",
			type="Int",
			width=40
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	componentcharges={
		[1]={
			list=false,
			name="BaseItemType",
			refTo="",
			type="String",
			width=250
		},
		[2]={
			list=false,
			name="Max",
			refTo="",
			type="Int",
			width=50
		},
		[3]={
			list=false,
			name="PerUse",
			refTo="",
			type="Int",
			width=50
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		}
	},
	componentweapon={
	},
	conditionalachievements={
	},
	cooldownbypasstypes={
	},
	cooldowngroups={
	},
	coreleagues={
	},
	corpseexplosiongibs={
	},
	corpsesinkvariations={
	},
	corpsetypetags={
		[1]={
			list=false,
			name="Tag",
			refTo="Tags",
			type="Key",
			width=150
		},
		[2]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="Buff",
			refTo="BuffDefinitions",
			type="Key",
			width=180
		},
		[4]={
			list=false,
			name="Icon",
			refTo="",
			type="String",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		}
	},
	cosmeticsequippanelmode={
	},
	costtypes={
		[1]={
			list=false,
			name="Resource",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Stat",
			refTo="Stats",
			type="Key",
			width=150
		},
		[3]={
			list=false,
			name="ResourceString",
			refTo="",
			type="String",
			width=190
		},
		[4]={
			list=false,
			name="Divisor",
			refTo="",
			type="Int",
			width=50
		},
		[5]={
			list=false,
			name="PerMinute",
			refTo="",
			type="Bool",
			width=100
		}
	},
	craftablemodtypes={
		[1]={
			list=false,
			name="Id",
			refTo="ModType",
			type="Key",
			width=320
		},
		[2]={
			list=false,
			name="HASH16",
			refTo="",
			type="UInt16",
			width=150
		}
	},
	craftingbenchcustomactions={
	},
	craftingbenchoptions={
		[1]={
			list=false,
			name="HideoutNPC",
			refTo="HideoutNPCs",
			type="Key",
			width=300
		},
		[2]={
			list=false,
			name="Order",
			refTo="",
			type="Int",
			width=50
		},
		[3]={
			list=false,
			name="Mod",
			refTo="Mods",
			type="Key",
			width=260
		},
		[4]={
			list=true,
			name="Cost_BaseItemTypes",
			refTo="BaseItemTypes",
			type="Key",
			width=150
		},
		[5]={
			list=true,
			name="CostValue",
			refTo="",
			type="Int",
			width=60
		},
		[6]={
			list=false,
			name="RequiredLevel",
			refTo="",
			type="Int",
			width=100
		},
		[7]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=200
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[9]={
			list=true,
			name="ItemClasses",
			refTo="ItemClasses",
			type="Key",
			width=130
		},
		[10]={
			list=false,
			name="Links",
			refTo="",
			type="Int",
			width=50
		},
		[11]={
			list=false,
			name="Colours",
			refTo="",
			type="String",
			width=50
		},
		[12]={
			list=false,
			name="Sockets",
			refTo="",
			type="Int",
			width=50
		},
		[13]={
			list=false,
			name="ItemQuantity",
			refTo="",
			type="Int",
			width=80
		},
		[14]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=100
		},
		[15]={
			list=false,
			name="Description",
			refTo="",
			type="String",
			width=150
		},
		[16]={
			list=false,
			name="IsDisabled",
			refTo="",
			type="Bool",
			width=70
		},
		[17]={
			list=false,
			name="IsAreaOption",
			refTo="",
			type="Bool",
			width=80
		},
		[18]={
			list=true,
			name="RecipeIds",
			refTo="",
			type="Int",
			width=60
		},
		[19]={
			list=false,
			name="Tier",
			refTo="",
			type="Int",
			width=50
		},
		[20]={
			list=true,
			name="ItemCategories",
			refTo="CraftingItemClassCategories",
			type="Key",
			width=750
		},
		[21]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=60
		},
		[22]={
			list=false,
			name="UnlockCategories",
			refTo="CraftingBenchUnlockCategories",
			type="Key",
			width=150
		},
		[23]={
			list=false,
			name="UnveilsRequired",
			refTo="",
			type="Int",
			width=100
		},
		[24]={
			list=false,
			name="UnveilsRequired2",
			refTo="",
			type="Int",
			width=100
		},
		[25]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[26]={
			list=true,
			name="KalandraAchievement",
			refTo="AchievementItems",
			type="Key",
			width=150
		},
		[27]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[28]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[29]={
			list=false,
			name="VeiledMods",
			refTo="Mods",
			type="Key",
			width=470
		},
		[30]={
			list=false,
			name="AddEnchantment",
			refTo="Mods",
			type="Key",
			width=150
		},
		[31]={
			list=false,
			name="SortCategory",
			refTo="CraftingBenchSortCategories",
			type="Key",
			width=150
		},
		[32]={
			list=false,
			name="ModType",
			refTo="ModType",
			type="Key",
			width=330
		},
		[33]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[34]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[35]={
			list=false,
			name="Stat1",
			refTo="Stats",
			type="Key",
			width=150
		},
		[36]={
			list=false,
			name="Stat2",
			refTo="Stats",
			type="Key",
			width=150
		},
		[37]={
			list=false,
			name="Stat3",
			refTo="Stats",
			type="Key",
			width=150
		}
	},
	craftingbenchsortcategories={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Name",
			refTo="ClientStrings",
			type="Key",
			width=300
		},
		[3]={
			list=false,
			name="IsVisible",
			refTo="",
			type="Bool",
			width=150
		}
	},
	craftingbenchspecificoptionid={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	craftingbenchunlockcategories={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=290
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=70
		},
		[3]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[4]={
			list=false,
			name="UnlockType",
			refTo="",
			type="String",
			width=150
		},
		[5]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=false,
			name="ObtainedFrom",
			refTo="",
			type="String",
			width=150
		}
	},
	craftingitemclasscategories={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=110
		},
		[2]={
			list=true,
			name="ItemClasses",
			refTo="ItemClasses",
			type="Key",
			width=610
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=100
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=130
		}
	},
	crossbowskillboltoverride={
		[1]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=400
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		}
	},
	crucibledifficulty={
	},
	crucibleendgamemonsterpacks={
	},
	cruciblelifescalingperlevel={
		[1]={
			list=false,
			name="Level",
			refTo="",
			type="Int",
			width=150
		},
		[2]={
			list=false,
			name="LifeMore",
			refTo="",
			type="Int",
			width=150
		}
	},
	crucibleplayerclassoffsets={
	},
	crucibletags={
		[1]={
			list=false,
			name="Tags",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		}
	},
	currencyexchange={
	},
	currencyexchangecategories={
	},
	currencyitems={
	},
	currencystashtablayout={
	},
	currencyuseeffects={
	},
	currencyusetypes={
	},
	customleaguemods={
	},
	customleaguemonsterreplacements={
	},
	customleagueroomreplacements={
	},
	customleaguetemplate={
	},
	daemonspawningdata={
	},
	damagecalculationtypes={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=210
		},
		[2]={
			list=true,
			name="PhysStats",
			refTo="Stats",
			type="Key",
			width=150
		},
		[3]={
			list=true,
			name="FireStats",
			refTo="Stats",
			type="Key",
			width=150
		},
		[4]={
			list=true,
			name="LightningStats",
			refTo="Stats",
			type="Key",
			width=150
		},
		[5]={
			list=true,
			name="ColdStats",
			refTo="Stats",
			type="Key",
			width=150
		},
		[6]={
			list=true,
			name="ChaosStats",
			refTo="Stats",
			type="Key",
			width=150
		},
		[7]={
			list=false,
			name="CritChance",
			refTo="Stats",
			type="Key",
			width=150
		},
		[8]={
			list=false,
			name="CritDamage",
			refTo="Stats",
			type="Key",
			width=200
		},
		[9]={
			list=false,
			name="AlwaysCritFlag",
			refTo="Stats",
			type="Key",
			width=150
		},
		[10]={
			list=false,
			name="NeverCritFlag",
			refTo="Stats",
			type="Key",
			width=100
		},
		[11]={
			list=false,
			name="SuppressChance",
			refTo="Stats",
			type="Key",
			width=100
		},
		[12]={
			list=false,
			name="LuckySuppressFlag",
			refTo="Stats",
			type="Key",
			width=100
		},
		[13]={
			list=false,
			name="SuppressEffect",
			refTo="Stats",
			type="Key",
			width=180
		},
		[14]={
			list=false,
			name="StunThreshold",
			refTo="Stats",
			type="Key",
			width=100
		},
		[15]={
			list=false,
			name="IncStunDuration",
			refTo="Stats",
			type="Key",
			width=100
		},
		[16]={
			list=false,
			name="MoreStunDuration",
			refTo="Stats",
			type="Key",
			width=100
		},
		[17]={
			list=false,
			name="CannotBlockFlag",
			refTo="Stats",
			type="Key",
			width=100
		},
		[18]={
			list=false,
			name="SkillCannotBeBlocked",
			refTo="Stats",
			type="Key",
			width=120
		},
		[19]={
			list=false,
			name="IncBlock",
			refTo="Stats",
			type="Key",
			width=100
		},
		[20]={
			list=false,
			name="IncProjectileBlock",
			refTo="Stats",
			type="Key",
			width=100
		},
		[21]={
			list=false,
			name="BlockLuckyFlag",
			refTo="Stats",
			type="Key",
			width=100
		},
		[22]={
			list=false,
			name="IncEnemyReducedBlock",
			refTo="Stats",
			type="Key",
			width=100
		},
		[23]={
			list=false,
			name="DeflectChance",
			refTo="Stats",
			type="Key",
			width=100
		},
		[24]={
			list=false,
			name="DeflectPercent",
			refTo="Stats",
			type="Key",
			width=100
		},
		[25]={
			list=false,
			name="DeflectLuckyFlag",
			refTo="Stats",
			type="Key",
			width=100
		},
		[26]={
			list=false,
			name="GlobalKnockbackFlag",
			refTo="Stats",
			type="Key",
			width=120
		},
		[27]={
			list=false,
			name="KnockbackOnCritlFlag",
			refTo="Stats",
			type="Key",
			width=150
		},
		[28]={
			list=false,
			name="OffhandKnocbackChance",
			refTo="Stats",
			type="Key",
			width=160
		},
		[29]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[30]={
			list=false,
			name="VirtualStatFlag",
			refTo="VirtualStatContextFlags",
			type="Key",
			width=200
		},
		[31]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[32]={
			list=false,
			name="IsAttack",
			refTo="",
			type="Bool",
			width=80
		},
		[33]={
			list=false,
			name="StunMultiplier",
			refTo="Stats",
			type="Key",
			width=150
		},
		[34]={
			list=false,
			name="OffHandStunMultiplier",
			refTo="Stats",
			type="Key",
			width=150
		},
		[35]={
			list=false,
			name="FakeHitType",
			refTo="DamageCalculationTypes",
			type="ShortKey",
			width=320
		},
		[36]={
			list=false,
			name="IsFakeHit",
			refTo="",
			type="Bool",
			width=150
		},
		[37]={
			list=false,
			name="EffectiveDoubleCritChance",
			refTo="Stats",
			type="Key",
			width=150
		},
		[38]={
			list=false,
			name="",
			refTo="Stats",
			type="Key",
			width=370
		}
	},
	damageeffectvariations={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=300
		},
		[2]={
			list=true,
			name="MiscAnimated",
			refTo="MiscAnimated",
			type="Key",
			width=150
		},
		[3]={
			list=true,
			name="EffectFile",
			refTo="",
			type="String",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[6]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[7]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[8]={
			list=false,
			name="MiscEffect",
			refTo="misceffectpacks",
			type="Key",
			width=200
		}
	},
	damagehiteffects={
	},
	damagehittypes={
	},
	damageparticleeffects={
	},
	damageparticleeffecttypes={
	},
	damagewhenhiteffects={
	},
	dances={
	},
	daressopitfights={
	},
	default={
	},
	defaultmonsterstats={
		[1]={
			list=false,
			name="Level",
			refTo="",
			type="String",
			width=50
		},
		[2]={
			list=false,
			name="Damage",
			refTo="",
			type="Float",
			width=70
		},
		[3]={
			list=false,
			name="Evasion",
			refTo="",
			type="Int",
			width=70
		},
		[4]={
			list=false,
			name="Accuracy",
			refTo="",
			type="Int",
			width=70
		},
		[5]={
			list=false,
			name="MonsterLife",
			refTo="",
			type="Int",
			width=70
		},
		[6]={
			list=false,
			name="XP",
			refTo="",
			type="Int",
			width=70
		},
		[7]={
			list=false,
			name="MinionLife",
			refTo="",
			type="Int",
			width=70
		},
		[8]={
			list=false,
			name="Armour",
			refTo="",
			type="Int",
			width=70
		},
		[9]={
			list=false,
			name="ResistancePart",
			refTo="",
			type="Int",
			width=100
		},
		[10]={
			list=false,
			name="MinionDamage",
			refTo="",
			type="Float",
			width=100
		},
		[11]={
			list=false,
			name="EvasiveEvasion",
			refTo="",
			type="Int",
			width=100
		},
		[12]={
			list=false,
			name="AilmentThreshold",
			refTo="",
			type="Int",
			width=100
		},
		[13]={
			list=false,
			name="PoiseThreshold",
			refTo="",
			type="Int",
			width=100
		},
		[14]={
			list=false,
			name="AltLife2",
			refTo="",
			type="Int",
			width=70
		},
		[15]={
			list=false,
			name="LeechResistance",
			refTo="",
			type="Int",
			width=100
		},
		[16]={
			list=false,
			name="MoreElementalDamage",
			refTo="",
			type="Int",
			width=140
		},
		[17]={
			list=false,
			name="MoreChaosDamage",
			refTo="",
			type="Int",
			width=150
		},
		[18]={
			list=false,
			name="RareUniqueLeechResistance",
			refTo="",
			type="Int",
			width=150
		}
	},
	deliriumstashtablayout={
	},
	delveazuriteshop={
	},
	delvebiomes={
	},
	delvecatchupdepths={
	},
	delvecraftingmodifierdescriptions={
	},
	delvecraftingmodifiers={
	},
	delvecraftingtags={
	},
	delvedynamite={
	},
	delvefeaturerewards={
	},
	delvefeatures={
	},
	delveflares={
	},
	delvelevelscaling={
	},
	delvemonsterspawners={
	},
	delveresourceperlevel={
	},
	delverewardtierconstants={
	},
	delverobotvariations={
	},
	delverooms={
	},
	delvestashtablayout={
	},
	delveupgrades={
	},
	delveupgradetype={
	},
	descendancy={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=400
		}
	},
	descentexiles={
	},
	descentrewardchests={
	},
	descentstarterchest={
	},
	destructivedamageeffects={
	},
	dialogueevent={
	},
	directions={
	},
	displayminionmonstertype={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="Int",
			width=50
		},
		[2]={
			list=false,
			name="MonsterVarieties",
			refTo="MonsterVarieties",
			type="Key",
			width=400
		}
	},
	divinationcardart={
	},
	divinationcardstashtablayout={
	},
	doors={
	},
	dronebasetypes={
	},
	dronetypes={
	},
	dropeffects={
	},
	dropmodifiers={
	},
	droppool={
	},
	dropreplacementcustomreplacements={
	},
	dropreplacementcustomtargets={
	},
	dynamicstashslots={
	},
	eclipsemods={
	},
	effectdrivenskill={
		[1]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[2]={
			list=true,
			name="",
			refTo="miscanimated",
			type="Key",
			width=500
		},
		[3]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[9]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[14]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[15]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[16]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[17]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[18]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[19]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[20]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[21]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[22]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[23]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[24]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[25]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[26]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[27]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[28]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[29]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		}
	},
	effectiveness={
	},
	effectivenesscostconstants={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Value",
			refTo="",
			type="Float",
			width=150
		}
	},
	einharmissions={
	},
	einharpackfallback={
	},
	elderbossarenas={
	},
	elderguardians={
	},
	eldermapbossoverride={
	},
	endgamecorruptionmods={
		[1]={
			list=false,
			name="Id",
			refTo="Mods",
			type="Key",
			width=250
		},
		[2]={
			list=true,
			name="ModWeight",
			refTo="",
			type="Int",
			width=150
		}
	},
	endgamemapbiomes={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=100
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=80
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=80
		},
		[4]={
			list=false,
			name="GroundType1",
			refTo="",
			type="String",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=80
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=80
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=80
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=80
		},
		[9]={
			list=false,
			name="GroundType2",
			refTo="",
			type="String",
			width=150
		},
		[10]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=80
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[14]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[15]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[16]={
			list=false,
			name="GroundTypeCorrupted1",
			refTo="",
			type="String",
			width=150
		},
		[17]={
			list=false,
			name="GroundTypeCorrupted2",
			refTo="",
			type="String",
			width=150
		},
		[18]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[19]={
			list=false,
			name="GroundType3",
			refTo="",
			type="String",
			width=150
		}
	},
	endgamemapcompletionquests={
	},
	endgamemapcontent={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=true,
			name="SpawnChance",
			refTo="",
			type="Int",
			width=100
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[7]={
			list=true,
			name="InherentStat",
			refTo="Stats",
			type="Key",
			width=220
		},
		[8]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[9]={
			list=false,
			name="Icon",
			refTo="",
			type="String",
			width=300
		},
		[10]={
			list=false,
			name="PrecursorTabletStats",
			refTo="Stats",
			type="Key",
			width=230
		},
		[11]={
			list=true,
			name="Stats",
			refTo="Stats",
			type="Key",
			width=280
		},
		[12]={
			list=true,
			name="StatsValue",
			refTo="",
			type="Int",
			width=80
		},
		[13]={
			list=false,
			name="Description",
			refTo="",
			type="String",
			width=150
		},
		[14]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=100
		},
		[15]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=220
		},
		[16]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[17]={
			list=false,
			name="ObjectiveStart",
			refTo="",
			type="String",
			width=150
		},
		[18]={
			list=false,
			name="ObjectiveComplete",
			refTo="",
			type="String",
			width=150
		}
	},
	endgamemapcontentset={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=true,
			name="EndGameMapContentKey",
			refTo="EndGameMapContent",
			type="Key",
			width=350
		}
	},
	endgamemapdecorations={
	},
	endgamemaplocation={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=260
		},
		[2]={
			list=true,
			name="Biomes",
			refTo="EndGameMapBiomes",
			type="Key",
			width=210
		},
		[3]={
			list=true,
			name="ConnectedBiomes",
			refTo="EndGameMapBiomes",
			type="Key",
			width=190
		}
	},
	endgamemappins={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="UnavailablePin",
			refTo="MiscAnimated",
			type="Key",
			width=150
		},
		[3]={
			list=false,
			name="AvailablePin",
			refTo="MiscAnimated",
			type="Key",
			width=150
		},
		[4]={
			list=false,
			name="ActivePin",
			refTo="MiscAnimated",
			type="Key",
			width=150
		},
		[5]={
			list=false,
			name="FailedPin",
			refTo="MiscAnimated",
			type="Key",
			width=150
		},
		[6]={
			list=false,
			name="CompletePin",
			refTo="MiscAnimated",
			type="Key",
			width=150
		}
	},
	endgamemaps={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="Int",
			width=60
		},
		[2]={
			list=false,
			name="BossVersion",
			refTo="WorldAreas",
			type="Key",
			width=190
		},
		[3]={
			list=false,
			name="NonBossVersion",
			refTo="WorldAreas",
			type="Key",
			width=220
		},
		[4]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=160
		},
		[5]={
			list=true,
			name="NativePacks",
			refTo="MonsterPacks",
			type="Key",
			width=230
		},
		[6]={
			list=false,
			name="FlavourText",
			refTo="",
			type="String",
			width=450
		},
		[7]={
			list=false,
			name="MinWatchstoneTier",
			refTo="",
			type="Int",
			width=120
		},
		[8]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[9]={
			list=false,
			name="DefaultMapPin",
			refTo="EndGameMapPins",
			type="Key",
			width=150
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=340
		},
		[11]={
			list=false,
			name="ContentSetKey",
			refTo="EndGameMapContentSet",
			type="Key",
			width=150
		},
		[12]={
			list=false,
			name="CorruptedMapPin",
			refTo="EndGameMapPins",
			type="Key",
			width=150
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[14]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[15]={
			list=false,
			name="CompletedMapPin",
			refTo="EndGameMapPins",
			type="Key",
			width=150
		},
		[16]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[17]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=150
		}
	},
	endlessledgechests={
	},
	environmentfootprints={
	},
	environments={
	},
	environmenttransitions={
	},
	essences={
		[1]={
			list=false,
			name="BaseItemType",
			refTo="BaseItemTypes",
			type="Key",
			width=370
		},
		[2]={
			list=false,
			name="HASH32",
			refTo="",
			type="UInt",
			width=100
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[5]={
			list=true,
			name="MonsterMod1",
			refTo="Mods",
			type="Key",
			width=250
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=70
		},
		[7]={
			list=false,
			name="MonsterMod2",
			refTo="Mods",
			type="Key",
			width=260
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[9]={
			list=false,
			name="ModTag",
			refTo="Tags",
			type="Key",
			width=70
		},
		[10]={
			list=false,
			name="GreaterVariant",
			refTo="Essences",
			type="ShortKey",
			width=250
		},
		[11]={
			list=false,
			name="EssenceTier",
			refTo="",
			type="Int",
			width=80
		},
		[12]={
			list=false,
			name="MapMod",
			refTo="Stats",
			type="Key",
			width=250
		},
		[13]={
			list=false,
			name="CraftedMod",
			refTo="Mods",
			type="Key",
			width=150
		},
		[14]={
			list=true,
			name="ItemClasses",
			refTo="ItemClasses",
			type="Key",
			width=150
		},
		[15]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=40
		}
	},
	essencestashtablayout={
	},
	essencetype={
		[1]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=90
		},
		[2]={
			list=false,
			name="TypeTier",
			refTo="",
			type="Int",
			width=60
		},
		[3]={
			list=false,
			name="CorruptOnly",
			refTo="",
			type="Bool",
			width=100
		},
		[4]={
			list=false,
			name="WordsKey",
			refTo="Words",
			type="Key",
			width=150
		}
	},
	eventcoins={
	},
	eventseason={
	},
	eventseasonrewards={
	},
	evergreenachievements={
	},
	evergreenachievementtypes={
	},
	executegeal={
	},
	expandingpulse={
	},
	expeditionareas={
	},
	expeditionbalanceperlevel={
	},
	expeditioncurrency={
	},
	expeditiondealfamilies={
	},
	expeditiondeals={
	},
	expeditiondealsdialogue={
	},
	expeditionfactions={
	},
	expeditionmarkerscommon={
	},
	expeditionnpcs={
	},
	expeditionrelicmodcategories={
	},
	expeditionrelicmods={
	},
	expeditionrelics={
	},
	expeditionstoragelayout={
	},
	expeditionterrainfeatures={
	},
	experiencelevels={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="UInt",
			width=150
		}
	},
	explodingstormbuffs={
	},
	extraterrainfeaturefamily={
	},
	extraterrainfeatures={
	},
	fixedhideoutdoodads={
	},
	fixedhideoutdoodadtypes={
	},
	fixedmissions={
	},
	flasks={
		[1]={
			list=false,
			name="BaseItemType",
			refTo="BaseItemTypes",
			type="Key",
			width=250
		},
		[2]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="FlaskType",
			type="Enum",
			width=50
		},
		[4]={
			list=false,
			name="LifePerUse",
			refTo="",
			type="Int",
			width=80
		},
		[5]={
			list=false,
			name="ManaPerUse",
			refTo="",
			type="Int",
			width=80
		},
		[6]={
			list=false,
			name="RecoveryTime",
			refTo="",
			type="Int",
			width=80
		},
		[7]={
			list=false,
			name="RecoveryTime2",
			refTo="",
			type="Int",
			width=150
		},
		[8]={
			list=false,
			name="Buff",
			refTo="BuffDefinitions",
			type="Key",
			width=260
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[10]={
			list=true,
			name="UtilityBuffs",
			refTo="UtilityFlaskBuffs",
			type="Key",
			width=240
		}
	},
	flaskstashbasetypeordering={
	},
	flasktype={
	},
	flavourtext={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=200
		},
		[2]={
			list=false,
			name="HASH16",
			refTo="",
			type="UInt16",
			width=70
		},
		[3]={
			list=false,
			name="Text",
			refTo="",
			type="String",
			width=500
		}
	},
	flavourtextimages={
	},
	footprints={
	},
	footstepaudio={
	},
	fragmentstashtablayout={
	},
	gambleprices={
	},
	gameconstants={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=300
		},
		[2]={
			list=false,
			name="Value",
			refTo="",
			type="Int",
			width=100
		},
		[3]={
			list=false,
			name="Divisor",
			refTo="",
			type="Int",
			width=100
		}
	},
	gamelogos={
	},
	gameobjecttasks={
	},
	gameobjecttasksfromstats={
	},
	gamepadbutton={
	},
	gamepadbuttonbindaction={
	},
	gamepadbuttoncombination={
	},
	gamepaditemactiontypes={
	},
	gamepadthumbstick={
	},
	gamepadtype={
	},
	gamestats={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		}
	},
	gemeffects={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=200
		},
		[2]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=220
		},
		[3]={
			list=false,
			name="GrantedEffect",
			refTo="GrantedEffects",
			type="Key",
			width=150
		},
		[4]={
			list=false,
			name="Description",
			refTo="",
			type="String",
			width=200
		},
		[5]={
			list=false,
			name="SecondarySupportName",
			refTo="GrantedEffects",
			type="String",
			width=150
		},
		[6]={
			list=true,
			name="Tags",
			refTo="GemTags",
			type="Key",
			width=500
		},
		[7]={
			list=false,
			name="HungryLoopMod",
			refTo="Mods",
			type="Key",
			width=280
		},
		[8]={
			list=false,
			name="ItemColor",
			refTo="",
			type="Int",
			width=150
		},
		[9]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[10]={
			list=true,
			name="AdditionalGrantedEffects",
			refTo="GrantedEffects",
			type="Key",
			width=150
		},
		[11]={
			list=false,
			name="SpiritGem",
			refTo="",
			type="Bool",
			width=150
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[13]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[14]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	gemitemvisualeffect={
	},
	gemitemvisualidentity={
	},
	gemtags={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="LocalLevelStat",
			refTo="Stats",
			type="Key",
			width=270
		},
		[4]={
			list=false,
			name="LocalQualityStat",
			refTo="Stats",
			type="Key",
			width=290
		},
		[5]={
			list=false,
			name="GlobalSpellLevelStat",
			refTo="Stats",
			type="Key",
			width=230
		}
	},
	gemtypes={
	},
	gemvisualeffect={
	},
	genericbuffauras={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		}
	},
	genericleaguerewardtypes={
	},
	genericleaguerewardtypevisuals={
	},
	genericskillindicator={
	},
	geometryattack={
	},
	geometrychannel={
	},
	geometryprojectiles={
	},
	geometrytrigger={
	},
	giftwrapartvariations={
	},
	globalaudioconfig={
	},
	goldactscaling={
	},
	goldbasetypeprices={
		[1]={
			list=false,
			name="BaseItemTypeKey",
			refTo="BaseItemTypes",
			type="Key",
			width=530
		},
		[2]={
			list=false,
			name="GoldPrice",
			refTo="",
			type="Int",
			width=150
		}
	},
	goldconstants={
	},
	goldinherentskillpricesperlevel={
		[1]={
			list=false,
			name="",
			refTo="ActiveSkills",
			type="Key",
			width=340
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	goldmodprices={
		[1]={
			list=false,
			name="Id",
			refTo="Mods",
			type="Key",
			width=390
		},
		[2]={
			list=false,
			name="Value",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=false,
			name="Weight?",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=true,
			name="SpawnTags",
			refTo="Tags",
			type="Key",
			width=300
		},
		[7]={
			list=true,
			name="SpawnWeights",
			refTo="",
			type="Int",
			width=270
		},
		[8]={
			list=false,
			name="CraftableModTypes",
			refTo="CraftableModTypes",
			type="Key",
			width=250
		},
		[9]={
			list=true,
			name="CraftingTags",
			refTo="AdvancedCraftingBenchCustomTags",
			type="Key",
			width=250
		}
	},
	goldrespecprices={
		[1]={
			list=false,
			name="Level",
			refTo="",
			type="Int",
			width=100
		},
		[2]={
			list=false,
			name="Cost",
			refTo="",
			type="Int",
			width=100
		}
	},
	goldvisualidentities={
	},
	grandmasters={
	},
	grantedeffectlabels={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Label",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		}
	},
	grantedeffectqualitystats={
		[1]={
			list=false,
			name="GrantedEffect",
			refTo="GrantedEffects",
			type="Key",
			width=150
		},
		[2]={
			list=true,
			name="GrantedStats",
			refTo="Stats",
			type="Key",
			width=460
		},
		[3]={
			list=true,
			name="StatValues",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=true,
			name="AddTypes",
			refTo="ActiveSkillType",
			type="Enum",
			width=100
		},
		[5]={
			list=true,
			name="AddMinionTypes",
			refTo="ActiveSkillType",
			type="Enum",
			width=240
		},
		[6]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	grantedeffectqualitytypes={
	},
	grantedeffects={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=300
		},
		[2]={
			list=false,
			name="IsSupport",
			refTo="",
			type="Bool",
			width=60
		},
		[3]={
			list=true,
			name="SupportTypes",
			refTo="ActiveSkillType",
			type="Key",
			width=250
		},
		[4]={
			list=false,
			name="SupportGemLetter",
			refTo="",
			type="String",
			width=100
		},
		[5]={
			list=true,
			name="AddTypes",
			refTo="ActiveSkillType",
			type="Key",
			width=90
		},
		[6]={
			list=true,
			name="ExcludeTypes",
			refTo="ActiveSkillType",
			type="Key",
			width=150
		},
		[7]={
			list=false,
			name="SupportGemsOnly",
			refTo="",
			type="Bool",
			width=100
		},
		[8]={
			list=false,
			name="Hash32",
			refTo="",
			type="UInt",
			width=80
		},
		[9]={
			list=false,
			name="CannotBeSupported",
			refTo="",
			type="Bool",
			width=110
		},
		[10]={
			list=false,
			name="LifeLeech?",
			refTo="",
			type="Int",
			width=70
		},
		[11]={
			list=false,
			name="CastTime",
			refTo="",
			type="Int",
			width=70
		},
		[12]={
			list=false,
			name="ActiveSkill",
			refTo="ActiveSkills",
			type="Key",
			width=250
		},
		[13]={
			list=false,
			name="IgnoreMinionTypes",
			refTo="",
			type="Bool",
			width=100
		},
		[14]={
			list=false,
			name="CooldownNotRecoverDuringActive",
			refTo="",
			type="Bool",
			width=180
		},
		[15]={
			list=true,
			name="AddMinionTypes",
			refTo="ActiveSkillType",
			type="Key",
			width=150
		},
		[16]={
			list=false,
			name="Animation",
			refTo="Animation",
			type="Key",
			width=100
		},
		[17]={
			list=false,
			name="MultiPartAchievement",
			refTo="MultiPartAchievements",
			type="Key",
			width=150
		},
		[18]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=40
		},
		[19]={
			list=false,
			name="RegularVariant",
			refTo="",
			type="ShortKey",
			width=50
		},
		[20]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[21]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[22]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[23]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[24]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[25]={
			list=false,
			name="GrantedEffectStatSets",
			refTo="GrantedEffectStatSets",
			type="Key",
			width=250
		},
		[26]={
			list=true,
			name="AdditionalStatSets",
			refTo="GrantedEffectStatSets",
			type="Key",
			width=500
		},
		[27]={
			list=false,
			name="Audio",
			refTo="",
			type="String",
			width=100
		},
		[28]={
			list=true,
			name="CostType",
			refTo="CostTypes",
			type="Key",
			width=150
		},
		[29]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[30]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[31]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[32]={
			list=true,
			name="WeaponRestrictions",
			refTo="ActiveSkillWeaponRequirement",
			type="Key",
			width=290
		}
	},
	grantedeffectsperlevel={
		[1]={
			list=false,
			name="GrantedEffect",
			refTo="GrantedEffects",
			type="Key",
			width=250
		},
		[2]={
			list=false,
			name="Level",
			type="Int",
			width=50
		},
		[3]={
			list=false,
			name="CostMultiplier",
			refTo="",
			type="Int",
			width=80
		},
		[4]={
			list=false,
			name="StoredUses",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="Cooldown",
			refTo="",
			type="Int",
			width=80
		},
		[6]={
			list=false,
			name="CooldownBypassType",
			refTo="CooldownBypassTypes",
			type="Enum",
			width=130
		},
		[7]={
			list=false,
			name="VaalSouls",
			refTo="Stats",
			type="Int",
			width=100
		},
		[8]={
			list=false,
			name="VaalStoredUses",
			refTo="",
			type="Int",
			width=120
		},
		[9]={
			list=false,
			name="CooldownGroup",
			refTo="",
			type="Int",
			width=90
		},
		[10]={
			list=false,
			name="PvPDamageMultiplier",
			refTo="",
			type="Int",
			width=120
		},
		[11]={
			list=false,
			name="SoulGainPreventionDuration",
			refTo="",
			type="Int",
			width=150
		},
		[12]={
			list=false,
			name="AttackSpeedMultiplier",
			refTo="",
			type="Int",
			width=130
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=90
		},
		[14]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[15]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[16]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[17]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[18]={
			list=false,
			name="AttackTime",
			refTo="",
			type="Int",
			width=150
		},
		[19]={
			list=false,
			name="SpiritReservation",
			refTo="",
			type="Int",
			width=150
		},
		[20]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[21]={
			list=true,
			name="CostAmounts",
			refTo="",
			type="Int",
			width=240
		},
		[22]={
			list=false,
			name="ActorLevel",
			refTo="",
			type="Float",
			width=100
		},
		[23]={
			list=false,
			name="ReservationMultiplier",
			refTo="",
			type="Int",
			width=150
		}
	},
	grantedeffectstatsets={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=300
		},
		[2]={
			list=false,
			name="LabelType",
			refTo="GrantedEffectLabels",
			type="Key",
			width=150
		},
		[3]={
			list=true,
			name="ImplicitStats",
			refTo="Stats",
			type="Key",
			width=600
		},
		[4]={
			list=true,
			name="ConstantStats",
			refTo="Stats",
			type="Key",
			width=800
		},
		[5]={
			list=true,
			name="ConstantStatsValues",
			refTo="",
			type="Int",
			width=200
		},
		[6]={
			list=false,
			name="BaseEffectiveness",
			refTo="",
			type="Float",
			width=120
		},
		[7]={
			list=false,
			name="IncrementalEffectiveness",
			refTo="",
			type="Float",
			width=130
		},
		[8]={
			list=false,
			name="DamageIncrementalEffectiveness",
			refTo="",
			type="Float",
			width=200
		},
		[9]={
			list=true,
			name="RemoveStats",
			refTo="Stats",
			type="Key",
			width=870
		},
		[10]={
			list=false,
			name="UseSetAttackMulti",
			refTo="",
			type="Bool",
			width=130
		}
	},
	grantedeffectstatsetsperlevel={
		[1]={
			list=false,
			name="GrantedEffectStatSets",
			refTo="GrantedEffectStatSets",
			type="Key",
			width=250
		},
		[2]={
			list=false,
			name="GemLevel",
			refTo="",
			type="Int",
			width=70
		},
		[3]={
			list=false,
			name="AttackCritChance",
			refTo="",
			type="Int",
			width=100
		},
		[4]={
			list=false,
			name="OffhandCritChance",
			refTo="",
			type="Int",
			width=120
		},
		[5]={
			list=true,
			name="BaseResolvedValues",
			refTo="Stats",
			type="Int",
			width=110
		},
		[6]={
			list=true,
			name="AdditionalStatsValues",
			refTo="Stats",
			type="Int",
			width=150
		},
		[7]={
			list=true,
			name="GrantedEffect",
			refTo="GrantedEffects",
			type="Key",
			width=190
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[10]={
			list=true,
			name="AdditionalBooleanStats",
			refTo="Stats",
			type="Key",
			width=200
		},
		[11]={
			list=true,
			name="FloatStats",
			refTo="Stats",
			type="Key",
			width=400
		},
		[12]={
			list=true,
			name="InterpolationBases",
			refTo="EffectivenessCostConstants",
			type="Key",
			width=150
		},
		[13]={
			list=true,
			name="AdditionalStats",
			refTo="Stats",
			type="Key",
			width=400
		},
		[14]={
			list=true,
			name="StatInterpolations",
			refTo="",
			type="Int",
			width=150
		},
		[15]={
			list=true,
			name="FloatStatsValues",
			refTo="",
			type="Float",
			width=270
		},
		[16]={
			list=false,
			name="ActorLevel",
			refTo="",
			type="Float",
			width=150
		},
		[17]={
			list=false,
			name="BaseMultiplier",
			refTo="",
			type="Int",
			width=150
		}
	},
	grantedskillsocketnumbers={
		[1]={
			list=false,
			name="PlayerLevel",
			refTo="",
			type="Int",
			width=150
		},
		[2]={
			list=false,
			name="NumSupportSockets",
			refTo="",
			type="Int",
			width=150
		}
	},
	graphicalitemreceptacle={
	},
	graphicalitemreceptacleslot={
	},
	groundeffecteffecttypes={
	},
	groundeffects={
	},
	groundeffecttypes={
	},
	hapticevents={
	},
	harbingermaps={
	},
	harbingers={
	},
	hardmodeextracontentchances={
	},
	harvestcolours={
	},
	harvestcraftcostscalingbybasetype={
	},
	harvestcraftfilters={
	},
	harvestcraftoptionicons={
	},
	harvestcraftoptions={
	},
	harvestcrafttiers={
	},
	harvestdurability={
	},
	harvestencounterscaling={
	},
	harvestinfrastructure={
	},
	harvestinfrastructurecategories={
	},
	harvestlifescalingperlevel={
	},
	harvestmetacraftingoptions={
	},
	harvestobjects={
	},
	harvestperlevelvalues={
	},
	harvestplantboosterfamilies={
	},
	harvestplantboosters={
	},
	harvestseeditems={
	},
	harvestseeds={
	},
	harvestseedtypes={
	},
	harvestspecialcraftcosts={
	},
	harvestspecialcraftoptions={
	},
	harveststoragelayout={
	},
	heistareaformationlayout={
	},
	heistareas={
	},
	heistbalanceperlevel={
	},
	heistblueprintwindowtypes={
	},
	heistchestrewardtypes={
	},
	heistchests={
	},
	heistchesttypes={
	},
	heistchokepointformation={
	},
	heistconstants={
	},
	heistcontracts={
	},
	heistdoodadnpcs={
	},
	heistdoors={
	},
	heistequipment={
	},
	heistformationmarkertype={
	},
	heistgeneration={
	},
	heistintroareas={
	},
	heistjobs={
	},
	heistjobsexperienceperlevel={
	},
	heistlocktype={
	},
	heistnpcauras={
	},
	heistnpcblueprinttypes={
	},
	heistnpcdialogue={
	},
	heistnpcs={
	},
	heistnpcstats={
	},
	heistobjectives={
	},
	heistobjectivevaluedescriptions={
	},
	heistpatrolpacks={
	},
	heistquestcontracts={
	},
	heistrevealingnpcs={
	},
	heistrooms={
	},
	heistroomtypes={
	},
	heiststoragelayout={
	},
	heistvaluescaling={
	},
	hellscapeaoreplacements={
	},
	hellscapeareapacks={
	},
	hellscapeexperiencelevels={
	},
	hellscapefactions={
	},
	hellscapeimmunemonsters={
	},
	hellscapeitemmodificationtiers={
	},
	hellscapelifescalingperlevel={
	},
	hellscapemodificationinventorylayout={
	},
	hellscapemods={
	},
	hellscapemonsterpacks={
	},
	hellscapepassives={
	},
	hellscapepassivetree={
	},
	hideoutcraftingbenchdoodads={
	},
	hideoutcraftingbenchinterfacevisuals={
	},
	hideoutdoodadcategory={
	},
	hideoutdoodads={
	},
	hideoutdoodadtags={
	},
	hideoutnpcs={
		[1]={
			list=false,
			name="NPC",
			refTo="NPCs",
			type="Key",
			width=370
		}
	},
	hideoutrarity={
	},
	hideoutresistpenalties={
		[1]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Value",
			refTo="",
			type="Int",
			width=100
		}
	},
	hideouts={
	},
	hideoutstashdoodads={
	},
	hideoutwaypointdoodads={
	},
	hudenergyshieldvisuals={
	},
	hudlifevisuals={
	},
	hudvisualsfromstat={
	},
	impactsounddata={
	},
	incubators={
	},
	incursionarchitect={
	},
	incursionbrackets={
	},
	incursionchestrewards={
	},
	incursionchests={
	},
	incursionroomadditionalbossdrops={
	},
	incursionroombossfightevents={
	},
	incursionrooms={
	},
	incursionuniqueupgradecomponents={
	},
	incursionuniqueupgrades={
	},
	indexableskillgems={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=220
		},
		[2]={
			list=false,
			name="HASH16",
			refTo="",
			type="UInt16",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		}
	},
	indexablesupportgems={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="Int",
			width=80
		},
		[2]={
			list=false,
			name="BaseItemType",
			refTo="SkillGems",
			type="Key",
			width=300
		},
		[3]={
			list=false,
			name="Skill",
			refTo="",
			type="String",
			width=200
		}
	},
	indicatorconditions={
	},
	influenceexalts={
	},
	influencemodupgrades={
	},
	influencetags={
		[1]={
			list=false,
			name="ItemClass",
			refTo="ItemClasses",
			type="Key",
			width=150
		},
		[2]={
			enumBase=1,
			list=false,
			name="InfluenceType",
			refTo="influencetypes",
			type="Enum",
			width=150
		},
		[3]={
			list=false,
			name="tags",
			refTo="Tags",
			type="Key",
			width=150
		}
	},
	influencetypes={
	},
	invasionmonstergroups={
	},
	invasionmonsterrestrictions={
	},
	invasionmonsterroles={
	},
	invasionmonstersperarea={
	},
	inventories={
	},
	inventoryid={
	},
	inventorytype={
	},
	itemclasscategories={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=180
		},
		[2]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		}
	},
	itemclasses={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="TradeMarketCategory",
			refTo="TradeMarketCategory",
			type="Key",
			width=120
		},
		[4]={
			list=false,
			name="ItemClassCategory",
			refTo="ItemClassCategories",
			type="Key",
			width=150
		},
		[5]={
			list=false,
			name="RemovedIfLeavesArea",
			refTo="",
			type="Bool",
			width=150
		},
		[6]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=20
		},
		[7]={
			list=true,
			name="Achievements",
			refTo="AchievementItems",
			type="Key",
			width=150
		},
		[8]={
			list=false,
			name="AllocateToMapOwner",
			refTo="",
			type="Bool",
			width=130
		},
		[9]={
			list=false,
			name="AlwaysAllocate",
			refTo="",
			type="Bool",
			width=120
		},
		[10]={
			list=false,
			name="CanHaveVeiledMods",
			refTo="",
			type="Bool",
			width=130
		},
		[11]={
			list=false,
			name="PickedUpQuest",
			refTo="QuestFlags",
			type="Key",
			width=110
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[13]={
			list=false,
			name="AlwaysShow",
			refTo="",
			type="Bool",
			width=100
		},
		[14]={
			list=false,
			name="CanBeCorrupted",
			refTo="",
			type="Bool",
			width=100
		},
		[15]={
			list=false,
			name="CanHaveIncubators",
			refTo="",
			type="Bool",
			width=120
		},
		[16]={
			list=false,
			name="CanHaveInfluence",
			refTo="",
			type="Bool",
			width=120
		},
		[17]={
			list=false,
			name="CanBeDoubleCorrupted",
			refTo="",
			type="Bool",
			width=130
		},
		[18]={
			list=false,
			name="CanHaveAspects",
			refTo="",
			type="Bool",
			width=110
		},
		[19]={
			list=false,
			name="CanTransferSkin",
			refTo="",
			type="Bool",
			width=110
		},
		[20]={
			list=false,
			name="ItemStance",
			refTo="ItemStances",
			type="Key",
			width=90
		},
		[21]={
			list=false,
			name="CanScourge",
			refTo="",
			type="Bool",
			width=80
		},
		[22]={
			list=false,
			name="CanUpgradeRarity",
			refTo="",
			type="Bool",
			width=110
		},
		[23]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[24]={
			list=true,
			name="MaxInventoryDimensions",
			refTo="",
			type="Int",
			width=150
		},
		[25]={
			list=true,
			name="Flags",
			refTo="",
			type="Int",
			width=80
		},
		[26]={
			list=false,
			name="Unmodifiable",
			refTo="",
			type="Bool",
			width=100
		},
		[27]={
			list=false,
			name="CanBeFractured",
			refTo="",
			type="Bool",
			width=100
		},
		[28]={
			list=false,
			name="EquipAchievements",
			refTo="AchievementItems",
			type="Key",
			width=120
		},
		[29]={
			list=false,
			name="UsedInMapDevice",
			refTo="",
			type="Bool",
			width=150
		},
		[30]={
			list=false,
			name="X",
			refTo="",
			type="Bool",
			width=70
		}
	},
	itemclassflags={
	},
	itemcostperlevel={
	},
	itemcosts={
	},
	itemcreationtemplatecustomaction={
	},
	itemdisenchantvalues={
	},
	itemexperienceperlevel={
		[1]={
			list=false,
			name="ItemExperienceType",
			refTo="ItemExperienceTypes",
			type="Key",
			width=410
		},
		[2]={
			list=false,
			name="Level",
			refTo="",
			type="Int",
			width=50
		},
		[3]={
			list=false,
			name="Experience",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=false,
			name="PlayerLevel",
			refTo="",
			type="Int",
			width=150
		}
	},
	itemexperiencetypes={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=270
		}
	},
	itemframetype={
	},
	iteminherentskills={
		[1]={
			list=false,
			name="BaseItemType",
			refTo="BaseItemTypes",
			type="Key",
			width=450
		},
		[2]={
			list=true,
			name="Skill",
			refTo="SkillGems",
			type="Key",
			width=400
		},
		[3]={
			list=false,
			name="Mainhand",
			refTo="",
			type="Bool",
			width=50
		}
	},
	itemisedcorpse={
		[1]={
			list=false,
			name="BaseItem",
			refTo="BaseItemTypes",
			type="Key",
			width=400
		},
		[2]={
			list=false,
			name="Monster",
			refTo="MonsterVarieties",
			type="Key",
			width=500
		},
		[3]={
			list=false,
			name="MonsterAbilities",
			refTo="",
			type="String",
			width=600
		},
		[4]={
			list=false,
			name="MonsterCategory",
			refTo="CorpseTypeTags",
			type="Key",
			width=130
		}
	},
	itemisedvisualeffect={
	},
	itemisedvisualeffectexclusivetypes={
	},
	itemnotecode={
	},
	itemsetnames={
	},
	itemshoptype={
	},
	itemspirit={
		[1]={
			list=false,
			name="BaseItemType",
			refTo="BaseItemTypes",
			type="Key",
			width=430
		},
		[2]={
			list=false,
			name="Value",
			refTo="",
			type="Int",
			width=150
		}
	},
	itemstances={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		}
	},
	itemsynthesiscorruptedmods={
		[1]={
			list=false,
			name="ItemClass",
			refTo="ItemClasses",
			type="Key",
			width=170
		},
		[2]={
			list=true,
			name="ImplicitMods",
			refTo="Mods",
			type="Key",
			width=910
		}
	},
	itemsynthesismods={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=120
		},
		[2]={
			list=false,
			name="Stat",
			refTo="Stats",
			type="Key",
			width=350
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=true,
			name="ItemClasses",
			refTo="ItemClasses",
			type="Key",
			width=440
		},
		[5]={
			list=true,
			name="ImplicitMod",
			refTo="Mods",
			type="Key",
			width=450
		}
	},
	itemthemes={
	},
	itemtoggleable={
	},
	itemtradedata={
	},
	itemvisualeffect={
	},
	itemvisualheldbodymodel={
	},
	itemvisualheldbodymodeloverridebyitemaffiliatedattributes={
	},
	itemvisualidentity={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=350
		},
		[2]={
			list=false,
			name="DDSFile",
			refTo="",
			type="String",
			width=370
		},
		[3]={
			list=false,
			name="AOFile",
			refTo="",
			type="String",
			width=610
		},
		[4]={
			list=false,
			name="InventorySound",
			refTo="SoundEffects",
			type="Key",
			width=150
		},
		[5]={
			list=false,
			name="Hash",
			refTo="",
			type="UInt16",
			width=70
		},
		[6]={
			list=false,
			name="AOFile2",
			refTo="",
			type="String",
			width=150
		},
		[7]={
			list=true,
			name="MarauderSMFiles",
			refTo="",
			type="String",
			width=150
		},
		[8]={
			list=true,
			name="RangerSMFiles",
			refTo="",
			type="String",
			width=150
		},
		[9]={
			list=true,
			name="WitchSMFiles",
			refTo="",
			type="String",
			width=150
		},
		[10]={
			list=true,
			name="DuelistDexSMFiles",
			refTo="",
			type="String",
			width=150
		},
		[11]={
			list=true,
			name="TemplarSMFiles",
			refTo="",
			type="String",
			width=150
		},
		[12]={
			list=true,
			name="ShadowSMFiles",
			refTo="",
			type="String",
			width=150
		},
		[13]={
			list=true,
			name="ScionSMFiles",
			refTo="",
			type="String",
			width=150
		},
		[14]={
			list=false,
			name="MarauderShape",
			refTo="",
			type="String",
			width=150
		},
		[15]={
			list=false,
			name="RangerShape",
			refTo="",
			type="String",
			width=150
		},
		[16]={
			list=false,
			name="WitchShape",
			refTo="",
			type="String",
			width=150
		},
		[17]={
			list=false,
			name="DuelistShape",
			refTo="",
			type="String",
			width=150
		},
		[18]={
			list=false,
			name="TemplarShape",
			refTo="",
			type="String",
			width=150
		},
		[19]={
			list=false,
			name="ShadowShape",
			refTo="",
			type="String",
			width=150
		},
		[20]={
			list=false,
			name="ScionShape",
			refTo="",
			type="String",
			width=150
		},
		[21]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[22]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[23]={
			list=true,
			name="Pickup_AchievementItemsKeys",
			refTo="AchievementItems",
			type="Key",
			width=170
		},
		[24]={
			list=true,
			name="SMFiles",
			refTo="",
			type="String",
			width=150
		},
		[25]={
			list=true,
			name="Identify_AchievementItemsKeys",
			refTo="AchievementItems",
			type="Key",
			width=170
		},
		[26]={
			list=false,
			name="EPKFile",
			refTo="",
			type="String",
			width=150
		},
		[27]={
			list=true,
			name="Corrupt_AchievementItemsKeys",
			refTo="AchievementItems",
			type="Key",
			width=160
		},
		[28]={
			list=false,
			name="IsAlternateArt",
			refTo="",
			type="Bool",
			width=150
		},
		[29]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[30]={
			list=false,
			name="CreateCorruptedJewelAchievementItemsKey",
			refTo="AchievementItems",
			type="Key",
			width=230
		},
		[31]={
			list=false,
			name="AnimationLocation",
			refTo="",
			type="String",
			width=150
		},
		[32]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[33]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[34]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[35]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[36]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[37]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[38]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[39]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[40]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[41]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[42]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[43]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[44]={
			list=false,
			name="IsAtlasOfWorldsMapIcon",
			refTo="",
			type="Bool",
			width=150
		},
		[45]={
			list=false,
			name="IsTier16Icon",
			refTo="",
			type="Bool",
			width=150
		},
		[46]={
			list=true,
			name="Achievements",
			refTo="AchievementItems",
			type="Key",
			width=500
		},
		[47]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[48]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[49]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=480
		},
		[50]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[51]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[52]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[53]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[54]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[55]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[56]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[57]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[58]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[59]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[60]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[61]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[62]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[63]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[64]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[65]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[66]={
			list=false,
			name="",
			refTo="Stats",
			type="Key",
			width=150
		},
		[67]={
			list=false,
			name="",
			refTo="Stats",
			type="Key",
			width=150
		},
		[68]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[69]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[70]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[71]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[72]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[73]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[74]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[75]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[76]={
			list=false,
			name="AudioEvent",
			refTo="CharacterAudioEvents",
			type="Key",
			width=150
		},
		[77]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[78]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[79]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[80]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[81]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[82]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[83]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[84]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[85]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		}
	},
	itemvisualreplacement={
	},
	jobassassinationspawnergroups={
	},
	jobraidbrackets={
	},
	keywordpopups={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="Description",
			refTo="",
			type="String",
			width=380
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=330
		}
	},
	killstreakthresholds={
	},
	kioskmodecharactertutorials={
	},
	kiraclevels={
	},
	labyrinthareas={
	},
	labyrinthbonusitems={
	},
	labyrinthexclusiongroups={
	},
	labyrinthizarochests={
	},
	labyrinthnodeoverrides={
	},
	labyrinthrewards={
	},
	labyrinthrewardtypes={
	},
	labyrinths={
	},
	labyrinthsecreteffects={
	},
	labyrinthsecretlocations={
	},
	labyrinthsecrets={
	},
	labyrinthsection={
	},
	labyrinthsectionlayout={
	},
	labyrinthtrials={
	},
	labyrinthtrinkets={
	},
	lakebosslifescalingperlevel={
	},
	lakemetaoptions={
	},
	lakemetaoptionsunlocktext={
	},
	lakeroomcompletion={
	},
	lakerooms={
	},
	languages={
	},
	leaguecategory={
	},
	leagueflag={
	},
	leagueflags={
	},
	leagueinfo={
	},
	leagueinfopanelversions={
	},
	leaguenames={
	},
	leagueprogressquestflags={
	},
	leaguequestflags={
	},
	leaguestaticrewards={
	},
	leaguetrophy={
	},
	legacyatlasinfluenceoutcomes={
	},
	legionbalanceperlevel={
	},
	legionchestcounts={
	},
	legionchests={
	},
	legionchesttypes={
	},
	legionfactions={
	},
	legionmonstercounts={
	},
	legionmonstertypes={
	},
	legionmonstervarieties={
	},
	legionranks={
	},
	legionranktypes={
	},
	legionrewards={
	},
	legionrewardtypes={
	},
	legionrewardtypevisuals={
	},
	levelrelativeplayerscaling={
		[1]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	loginareas={
	},
	magicmonsterlifescalingperlevel={
	},
	mapcompletionachievements={
	},
	mapconnections={
	},
	mapcreationinformation={
	},
	mapcurrencyinventorylayout={
	},
	mapdevicerecipes={
	},
	mapdevices={
	},
	mapfragmentfamilies={
	},
	mapfragmentmods={
	},
	mapinhabitants={
	},
	mappins={
	},
	mappurchasecosts={
	},
	maps={
	},
	mapseries={
	},
	mapseriestiers={
	},
	mapstashspecialtypeentries={
	},
	mapstashtablayout={
	},
	mapstashuniquemapinfo={
	},
	mapstatachievements={
	},
	mapstatconditions={
	},
	mapstatsfrommapstats={
	},
	maptierachievements={
	},
	maptiers={
	},
	masterhideoutlevels={
	},
	mavendialog={
	},
	mavenfights={
	},
	mavenjewelradiuskeystones={
		[1]={
			list=false,
			name="Keystone_Key",
			refTo="PassiveSkills",
			type="Key",
			width=300
		}
	},
	melee={
	},
	meleetrails={
	},
	memorylinetype={
	},
	metamorphlifescalingperlevel={
	},
	metamorphosismetamonsters={
	},
	metamorphosismetaskills={
	},
	metamorphosismetaskilltypes={
	},
	metamorphosisrewardtypeitemsclient={
	},
	metamorphosisrewardtypes={
	},
	metamorphosisscaling={
	},
	metamorphosisstashtablayout={
	},
	micromigrationdata={
	},
	microtransactionappliedinventoryitemartvariations={
	},
	microtransactioncategory={
	},
	microtransactioncategoryid={
	},
	microtransactioncharacterportraitvariations={
	},
	microtransactionchargevariations={
	},
	microtransactioncombineformula={
	},
	microtransactionconditionalapparitionevents={
	},
	microtransactionconditionalapparitioneventtype={
	},
	microtransactionconditionalapparitionorientation={
	},
	microtransactionconditionalapparitionposition={
	},
	microtransactionconditionalapparitions={
	},
	microtransactioncounters={
	},
	microtransactioncursorvariations={
	},
	microtransactionequippediconvariations={
	},
	microtransactionfireworksvariations={
	},
	microtransactiongemcategory={
	},
	microtransactionjewelvariations={
	},
	microtransactionlevelupeffects={
	},
	microtransactionobjecteffects={
	},
	microtransactiononkillbeams={
	},
	microtransactiononkillconditions={
	},
	microtransactiononkilleffects={
	},
	microtransactiononopenchesteffects={
	},
	microtransactionperiodiccharactereffectvariations={
	},
	microtransactionplayershieldvariations={
	},
	microtransactionportalvariations={
	},
	microtransactionraritydisplay={
	},
	microtransactionrecyclecategories={
	},
	microtransactionrecycleoutcomes={
	},
	microtransactionrecyclesalvagevalues={
	},
	microtransactionskillgemeffectslottypes={
	},
	microtransactionslot={
	},
	microtransactionslotadditionaldefaultoptions={
	},
	microtransactionslotid={
	},
	microtransactionsocialframevariations={
	},
	minimapicons={
	},
	minioncommands={
	},
	miniongemlevelscaling={
		[1]={
			list=false,
			name="GemLevel",
			refTo="",
			type="Int",
			width=150
		},
		[2]={
			list=false,
			name="MinionLevel",
			refTo="",
			type="Int",
			width=150
		}
	},
	minionstats={
		[1]={
			list=false,
			name="MinionStat",
			refTo="Stats",
			type="Key",
			width=300
		},
		[2]={
			list=true,
			name="PlayerStat",
			refTo="Stats",
			type="Key",
			width=300
		},
		[3]={
			list=true,
			name="MinionType",
			refTo="MinionType",
			type="Key",
			width=200
		},
		[4]={
			list=true,
			name="MinionType2",
			refTo="MinionType",
			type="Key",
			width=200
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[6]={
			list=false,
			name="CompanionStat",
			refTo="",
			type="Bool",
			width=150
		}
	},
	miniontype={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=250
		},
		[2]={
			list=false,
			name="LimitStat",
			refTo="stats",
			type="Key",
			width=420
		},
		[3]={
			list=false,
			name="ActiveCountStat",
			refTo="stats",
			type="Key",
			width=310
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=70
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[14]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[15]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[16]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[17]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[18]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[19]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[20]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=100
		}
	},
	miniqueststates={
	},
	miscanimated={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=200
		},
		[2]={
			list=false,
			name="AOFile",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=true,
			name="PreloadGroup",
			refTo="",
			type="Key",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=100
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=100
		},
		[6]={
			list=false,
			name="HASH32",
			refTo="",
			type="UInt",
			width=100
		}
	},
	miscanimatedartvariations={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=250
		},
		[2]={
			list=true,
			name="MiscAnimated",
			refTo="MiscAnimated",
			type="Key",
			width=300
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=false,
			name="Variant",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="Stat",
			refTo="Stats",
			type="Key",
			width=300
		}
	},
	miscbeams={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=250
		},
		[2]={
			list=false,
			name="MiscAnimated",
			refTo="MiscAnimated",
			type="Key",
			width=250
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		},
		[4]={
			list=true,
			name="PreloadGroup",
			refTo="PreloadGroups",
			type="Key",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		}
	},
	miscbeamsartvariations={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=250
		},
		[2]={
			list=true,
			name="MiscBeams",
			refTo="MiscBeams",
			type="Key",
			width=300
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=false,
			name="Variant",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="Stat",
			refTo="Stats",
			type="Key",
			width=300
		}
	},
	misccooldowns={
	},
	misceffectpacks={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=250
		},
		[2]={
			list=false,
			name="EPKFile",
			refTo="",
			type="String",
			width=750
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[6]={
			list=true,
			name="PreloadGroups",
			refTo="PreloadGroups",
			type="Key",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[8]={
			list=false,
			name="PlayerOnly_EPKFile",
			refTo="",
			type="String",
			width=600
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		}
	},
	misceffectpacksartvariations={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=250
		},
		[2]={
			list=true,
			name="MiscEffectPacks",
			refTo="MiscEffectPacks",
			type="Key",
			width=300
		},
		[3]={
			list=false,
			name="Variant",
			refTo="",
			type="Int",
			width=80
		}
	},
	miscobjects={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=250
		},
		[2]={
			list=false,
			name="EffectVirtualPath",
			refTo="",
			type="String",
			width=250
		},
		[3]={
			list=true,
			name="PreloadGroups",
			refTo="PreloadGroups",
			type="Key",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="UInt",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="UInt",
			width=150
		}
	},
	miscobjectsartvariations={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=250
		},
		[2]={
			list=true,
			name="Object",
			refTo="MiscObjects",
			type="Key",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[5]={
			list=false,
			name="Stat",
			refTo="Stats",
			type="Key",
			width=300
		}
	},
	miscparticles={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=250
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[4]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=150
		}
	},
	miscparticlesartvariations={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=200
		},
		[2]={
			list=true,
			name="MiscParticles",
			refTo="MiscParticles",
			type="Key",
			width=150
		},
		[3]={
			list=false,
			name="Variant",
			refTo="",
			type="Int",
			width=80
		}
	},
	miscprojectilemod={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=80
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=300
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=200
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=80
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=80
		}
	},
	miscprojectilemodartvariations={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[5]={
			list=true,
			name="MiscProjectileMod",
			refTo="MiscProjectileMod",
			type="Key",
			width=150
		}
	},
	missionfavourperlevel={
	},
	missiontilemap={
	},
	missiontimertypes={
	},
	missiontransitiontiles={
	},
	mobileactoneatlasquestprogression={
	},
	mobileascendancythresholds={
	},
	mobileatlaseldermemories={
	},
	mobileatlasinventorylayout={
	},
	mobilecharactercreation={
	},
	mobilequestaudio={
	},
	mobileskillgemlayout={
	},
	mobileskillgemlayoutpages={
	},
	mobiletutorial={
	},
	mobiletutorialgroup={
	},
	moddomains={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=220
		}
	},
	modeffectstats={
		[1]={
			list=false,
			name="Stats",
			refTo="Stats",
			type="Key",
			width=400
		},
		[2]={
			list=true,
			name="Tags",
			refTo="Tags",
			type="Key",
			width=220
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		}
	},
	modequivalencies={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="ModsKey0",
			refTo="Mods",
			type="Key",
			width=350
		},
		[3]={
			list=false,
			name="ModsKey1",
			refTo="Mods",
			type="Key",
			width=350
		},
		[4]={
			list=false,
			name="ModsKey2",
			refTo="Mods",
			type="Key",
			width=350
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		}
	},
	modfamily={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=200
		}
	},
	modgenerationtypes={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		}
	},
	modgrantedskills={
		[1]={
			list=false,
			name="Mod",
			refTo="Mods",
			type="Key",
			width=300
		},
		[2]={
			list=false,
			name="Skill Gem",
			refTo="skillgems",
			type="Key",
			width=400
		}
	},
	mods={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=450
		},
		[2]={
			list=false,
			name="Hash",
			refTo="",
			type="UInt16",
			width=60
		},
		[3]={
			list=false,
			name="Type",
			refTo="ModType",
			type="Key",
			width=280
		},
		[4]={
			list=false,
			name="Level",
			refTo="",
			type="Int",
			width=60
		},
		[5]={
			list=false,
			name="Stat1",
			refTo="Stats",
			type="Key",
			width=400
		},
		[6]={
			list=false,
			name="Stat2",
			refTo="Stats",
			type="Key",
			width=200
		},
		[7]={
			list=false,
			name="Stat3",
			refTo="Stats",
			type="Key",
			width=200
		},
		[8]={
			list=false,
			name="Stat4",
			refTo="Stats",
			type="Key",
			width=200
		},
		[9]={
			list=false,
			name="Domain",
			refTo="modDomains",
			type="Enum",
			width=140
		},
		[10]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=350
		},
		[11]={
			list=false,
			name="GenerationType",
			refTo="modGenerationTypes",
			type="Enum",
			width=120
		},
		[12]={
			list=true,
			name="Family",
			refTo="ModFamily",
			type="Key",
			width=300
		},
		[13]={
			list=false,
			name="Stat1Value",
			refTo="",
			type="Interval",
			width=70
		},
		[14]={
			list=false,
			name="Stat2Value",
			refTo="",
			type="Interval",
			width=70
		},
		[15]={
			list=false,
			name="Stat3Value",
			refTo="",
			type="Interval",
			width=70
		},
		[16]={
			list=false,
			name="Stat4Value",
			refTo="",
			type="Interval",
			width=70
		},
		[17]={
			list=true,
			name="SpawnTags",
			refTo="Tags",
			type="Key",
			width=150
		},
		[18]={
			list=true,
			name="SpawnWeights",
			refTo="Tags",
			type="Int",
			width=150
		},
		[19]={
			list=true,
			name="Tags",
			refTo="Tags",
			type="Key",
			width=150
		},
		[20]={
			list=true,
			name="GrantedEffect",
			refTo="GrantedEffectsPerLevel",
			type="Key",
			width=150
		},
		[21]={
			list=true,
			name="AuraFlags",
			refTo="ModAuraFlags",
			type="Enum",
			width=80
		},
		[22]={
			list=false,
			name="Daemon",
			refTo="",
			type="String",
			width=150
		},
		[23]={
			list=true,
			name="MonsterKillAchievements",
			refTo="AchievementItems",
			type="Key",
			width=150
		},
		[24]={
			list=true,
			name="ArchnemesisType",
			refTo="ModType",
			type="Key",
			width=240
		},
		[25]={
			list=false,
			name="Stat5Value",
			refTo="",
			type="Interval",
			width=60
		},
		[26]={
			list=false,
			name="Stat5",
			refTo="Stats",
			type="Key",
			width=150
		},
		[27]={
			list=true,
			name="FullClear",
			refTo="AchievementItems",
			type="Key",
			width=100
		},
		[28]={
			list=true,
			name="AchievementItemsKey",
			refTo="AchievementItems",
			type="Key",
			width=130
		},
		[29]={
			list=true,
			name="GenerationWeightTags",
			refTo="Tags",
			type="Key",
			width=130
		},
		[30]={
			list=true,
			name="GenerationWeightValues",
			refTo="Tags",
			type="Int",
			width=130
		},
		[31]={
			list=true,
			name="ModifyMapsAchievements",
			refTo="AchievementItems",
			type="Key",
			width=150
		},
		[32]={
			list=false,
			name="Stat6Value",
			refTo="",
			type="Interval",
			width=130
		},
		[33]={
			list=false,
			name="Stat6",
			refTo="Stats",
			type="Key",
			width=150
		},
		[34]={
			list=false,
			name="MaxLevel",
			refTo="",
			type="Int",
			width=50
		},
		[35]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=40
		},
		[36]={
			list=true,
			name="CraftingClassRestrictions",
			refTo="ItemClasses",
			type="Key",
			width=150
		},
		[37]={
			list=false,
			name="MonsterOnDeath",
			refTo="",
			type="String",
			width=100
		},
		[38]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=200
		},
		[39]={
			list=true,
			name="HeistAchievements",
			refTo="AchievementItems",
			type="Key",
			width=230
		},
		[40]={
			list=false,
			name="Heist_SubStatValue1",
			refTo="Heist_SubStatValue1",
			type="Int",
			width=150
		},
		[41]={
			list=false,
			name="Heist_SubStatValue2",
			refTo="GrantedEffectsPerLevel",
			type="Int",
			width=150
		},
		[42]={
			list=false,
			name="Heist_StatsKey0",
			refTo="Stats",
			type="Key",
			width=150
		},
		[43]={
			list=false,
			name="Heist_StatsKey1",
			refTo="Stats",
			type="Key",
			width=150
		},
		[44]={
			list=false,
			name="Heist_AddStatValue1",
			refTo="",
			type="Int",
			width=240
		},
		[45]={
			list=false,
			name="Heist_AddStatValue2",
			refTo="",
			type="Int",
			width=150
		},
		[46]={
			list=false,
			name="InfluenceTypes",
			refTo="",
			type="Int",
			width=150
		},
		[47]={
			list=true,
			name="ImplicitTags",
			refTo="Tags",
			type="Key",
			width=150
		},
		[48]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[49]={
			list=false,
			name="UnknownStat1",
			refTo="",
			type="Interval",
			width=150
		},
		[50]={
			list=false,
			name="UnknownStat2",
			refTo="",
			type="Interval",
			width=150
		},
		[51]={
			list=false,
			name="UnknownStat3",
			refTo="",
			type="Interval",
			width=150
		},
		[52]={
			list=false,
			name="UnknownStat4",
			refTo="",
			type="Interval",
			width=150
		},
		[53]={
			list=false,
			name="UnknownStat5",
			refTo="",
			type="Interval",
			width=150
		},
		[54]={
			list=false,
			name="UnknownStat6",
			refTo="",
			type="Interval",
			width=150
		},
		[55]={
			list=false,
			name="UnknownStat7",
			refTo="",
			type="Interval",
			width=150
		},
		[56]={
			list=false,
			name="UnknownStat8",
			refTo="",
			type="Interval",
			width=150
		},
		[57]={
			list=false,
			name="BuffTemplate",
			refTo="BuffTemplates",
			type="Key",
			width=150
		},
		[58]={
			list=false,
			name="ArchnemesisMinionMod",
			refTo="Mods",
			type="ShortKey",
			width=290
		},
		[59]={
			list=false,
			name="HASH32",
			refTo="",
			type="UInt",
			width=150
		},
		[60]={
			list=true,
			name="BuffTemplate2",
			refTo="BuffTemplates",
			type="Key",
			width=150
		},
		[61]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[62]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=90
		},
		[63]={
			list=false,
			name="NodeType",
			refTo="passiveNodeTypes",
			type="Enum",
			width=70
		},
		[64]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		}
	},
	modsellpricetypes={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=250
		}
	},
	modsetnames={
	},
	modsets={
	},
	modtype={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=400
		},
		[2]={
			list=true,
			name="ModSellPriceTypesKeys",
			refTo="ModSellPriceTypes",
			type="Key",
			width=190
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		}
	},
	monsteradditionalmonsterdrops={
	},
	monsterarmours={
	},
	monsterbehavior={
	},
	monsterbonuses={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=230
		},
		[2]={
			list=true,
			name="Mods",
			refTo="Mods",
			type="Key",
			width=300
		},
		[3]={
			list=false,
			name="BuffDefinition",
			refTo="buffdefinitions",
			type="Key",
			width=300
		},
		[4]={
			list=true,
			name="BuffValues",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=true,
			name="Stats",
			refTo="Stats",
			type="Key",
			width=150
		},
		[6]={
			list=true,
			name="StatValues",
			refTo="",
			type="Int",
			width=150
		}
	},
	monstercategories={
		[1]={
			list=false,
			name="Tag",
			refTo="Tags",
			type="Key",
			width=150
		},
		[2]={
			list=false,
			name="Type",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="HudImage",
			refTo="",
			type="String",
			width=400
		}
	},
	monsterchancetodropitemtemplate={
	},
	monsterconditionaleffectpacks={
	},
	monsterconditions={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=200
		},
		[2]={
			list=false,
			name="Rarity",
			refTo="Rarity",
			type="Key",
			width=150
		},
		[3]={
			list=false,
			name="Stat",
			refTo="Stats",
			type="Key",
			width=400
		},
		[4]={
			list=false,
			name="NotRarity",
			refTo="Rarity",
			type="Key",
			width=150
		},
		[5]={
			list=true,
			name="NotStat",
			refTo="Stats",
			type="Key",
			width=300
		},
		[6]={
			list=false,
			name="MapBoss",
			refTo="",
			type="Bool",
			width=150
		},
		[7]={
			list=false,
			name="NotMapBoss",
			refTo="",
			type="Bool",
			width=150
		},
		[8]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=400
		},
		[9]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[13]={
			list=false,
			name="HASH32",
			refTo="",
			type="Int",
			width=150
		}
	},
	monsterdeathachievements={
	},
	monsterdeathconditions={
	},
	monsterencounterskillgroups={
	},
	monsterfleeconditions={
	},
	monstergroupentries={
	},
	monstergroupnames={
	},
	monsterheightbrackets={
	},
	monsterheights={
	},
	monstermapbossdifficulty={
		[1]={
			list=false,
			name="AreaLevel",
			refTo="",
			type="Int",
			width=150
		},
		[2]={
			list=false,
			name="BossLifePercentIncrease",
			refTo="",
			type="Int",
			width=170
		},
		[3]={
			list=false,
			name="BossDamagePercentIncrease",
			refTo="",
			type="Int",
			width=170
		},
		[4]={
			list=false,
			name="Stat1",
			refTo="Stats",
			type="Key",
			width=240
		},
		[5]={
			list=false,
			name="Stat2",
			refTo="Stats",
			type="Key",
			width=270
		},
		[6]={
			list=false,
			name="Stat3",
			refTo="Stats",
			type="Key",
			width=240
		},
		[7]={
			list=false,
			name="BossIncItemQuantity",
			refTo="",
			type="Int",
			width=150
		},
		[8]={
			list=false,
			name="Stat4",
			refTo="Stats",
			type="Key",
			width=220
		},
		[9]={
			list=false,
			name="BossIncItemRarity",
			refTo="",
			type="Int",
			width=150
		},
		[10]={
			list=false,
			name="Stat5",
			refTo="Stats",
			type="Key",
			width=330
		},
		[11]={
			list=false,
			name="BossAilmentPercentDecrease",
			refTo="",
			type="Int",
			width=150
		}
	},
	monstermapdifficulty={
		[1]={
			list=false,
			name="AreaLevel",
			refTo="",
			type="Int",
			width=80
		},
		[2]={
			list=false,
			name="LifePercentIncrease",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=false,
			name="DamagePercentIncrease",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=false,
			name="Stat1",
			refTo="Stats",
			type="Key",
			width=230
		},
		[5]={
			list=false,
			name="Stat2",
			refTo="Stats",
			type="Key",
			width=250
		},
		[6]={
			list=false,
			name="Stat3",
			refTo="Stats",
			type="Key",
			width=150
		},
		[7]={
			list=false,
			name="Stat3Value",
			refTo="",
			type="Int",
			width=150
		},
		[8]={
			list=false,
			name="Stat4",
			refTo="Stats",
			type="Key",
			width=150
		},
		[9]={
			list=false,
			name="Stat4Value",
			refTo="",
			type="Int",
			width=150
		}
	},
	monstermortar={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="Int",
			width=150
		},
		[2]={
			list=false,
			name="Projectile",
			refTo="Projectiles",
			type="Key",
			width=370
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[4]={
			list=false,
			name="Animation",
			refTo="MiscAnimated",
			type="Key",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[14]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[15]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[16]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[17]={
			list=false,
			name="BounceAnimation",
			refTo="MiscAnimated",
			type="Key",
			width=150
		},
		[18]={
			list=false,
			name="Bounces",
			refTo="",
			type="Int",
			width=80
		},
		[19]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=80
		},
		[20]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=80
		},
		[21]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=80
		},
		[22]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[23]={
			list=false,
			name="Animation2",
			refTo="MiscAnimated",
			type="Key",
			width=150
		},
		[24]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=100
		}
	},
	monsterpackcounts={
	},
	monsterpackentries={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=80
		},
		[2]={
			list=false,
			name="MonsterPacksKey",
			refTo="MonsterPacks",
			type="Key",
			width=240
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[4]={
			list=false,
			name="Weight",
			refTo="",
			type="Int",
			width=90
		},
		[5]={
			list=false,
			name="MonsterVarietiesKey",
			refTo="MonsterVarieties",
			type="Key",
			width=530
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		}
	},
	monsterpacks={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=240
		},
		[2]={
			list=true,
			name="WorldAreas",
			refTo="WorldAreas",
			type="Key",
			width=150
		},
		[3]={
			list=false,
			name="MinCount",
			refTo="",
			type="Int",
			width=90
		},
		[4]={
			list=false,
			name="MaxCount",
			refTo="",
			type="Int",
			width=90
		},
		[5]={
			list=false,
			name="BossMonsterChance",
			refTo="",
			type="Int",
			width=120
		},
		[6]={
			list=false,
			name="BossCount",
			refTo="",
			type="Int",
			width=80
		},
		[7]={
			list=true,
			name="BossMonsters",
			refTo="MonsterVarieties",
			type="Key",
			width=490
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[9]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[10]={
			list=true,
			name="Grounds",
			refTo="",
			type="String",
			width=150
		},
		[11]={
			list=true,
			name="Tags",
			refTo="Tags",
			type="Key",
			width=150
		},
		[12]={
			list=false,
			name="MinLevel",
			refTo="",
			type="Int",
			width=80
		},
		[13]={
			list=false,
			name="MaxLevel",
			refTo="",
			type="Int",
			width=80
		},
		[14]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[15]={
			list=false,
			name="Formation",
			refTo="PackFormation",
			type="Key",
			width=150
		},
		[16]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[17]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[18]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[19]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[20]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[21]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[22]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[23]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[24]={
			list=true,
			name="AdditionalMonsters",
			refTo="MonsterVarieties",
			type="Key",
			width=480
		},
		[25]={
			list=true,
			name="AdditionalCounts",
			refTo="",
			type="Int",
			width=110
		}
	},
	monsterprojectileattack={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="Int",
			width=80
		},
		[2]={
			list=false,
			name="Projectile",
			refTo="Projectiles",
			type="Key",
			width=390
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		}
	},
	monsterprojectilespell={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="Int",
			width=80
		},
		[2]={
			list=false,
			name="Projectile",
			refTo="Projectiles",
			type="Key",
			width=390
		},
		[3]={
			list=false,
			name="Animation",
			refTo="",
			type="Key",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		}
	},
	monsterpushtypes={
	},
	monsterresistances={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=true,
			name="Fire1",
			refTo="",
			type="Int",
			width=50
		},
		[3]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[4]={
			list=true,
			name="Fire2",
			refTo="",
			type="Int",
			width=50
		},
		[5]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[6]={
			list=true,
			name="Fire3",
			refTo="",
			type="Int",
			width=50
		},
		[7]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[8]={
			list=true,
			name="Fire4",
			refTo="",
			type="Int",
			width=100
		},
		[9]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=100
		},
		[10]={
			list=true,
			name="Cold1",
			refTo="",
			type="Int",
			width=50
		},
		[11]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[12]={
			list=true,
			name="Cold2",
			refTo="",
			type="Int",
			width=50
		},
		[13]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[14]={
			list=true,
			name="Cold3",
			refTo="",
			type="Int",
			width=50
		},
		[15]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[16]={
			list=true,
			name="Cold4",
			refTo="",
			type="Int",
			width=100
		},
		[17]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=100
		},
		[18]={
			list=true,
			name="Lightning1",
			refTo="",
			type="Int",
			width=60
		},
		[19]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[20]={
			list=true,
			name="Lightning2",
			refTo="",
			type="Int",
			width=60
		},
		[21]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[22]={
			list=true,
			name="Lightning3",
			refTo="",
			type="Int",
			width=60
		},
		[23]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[24]={
			list=true,
			name="Lightning4",
			refTo="",
			type="Int",
			width=100
		},
		[25]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=90
		},
		[26]={
			list=true,
			name="Chaos1",
			refTo="",
			type="Int",
			width=50
		},
		[27]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[28]={
			list=true,
			name="Chaos2",
			refTo="",
			type="Int",
			width=50
		},
		[29]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[30]={
			list=true,
			name="Chaos3",
			refTo="",
			type="Int",
			width=50
		},
		[31]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=30
		},
		[32]={
			list=true,
			name="Chaos4",
			refTo="",
			type="Int",
			width=100
		},
		[33]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=80
		}
	},
	monsterscalingbylevel={
	},
	monstersegments={
	},
	monstershapeshift={
	},
	monstersize={
	},
	monsterskillsalivedead={
	},
	monsterskillsattackspell={
	},
	monsterskillsclientinstance={
	},
	monsterskillshull={
	},
	monsterskillsorientation={
	},
	monsterskillsplacement={
	},
	monsterskillsreference={
	},
	monsterskillssequencemode={
	},
	monsterskillsshape={
	},
	monsterskillstargets={
	},
	monsterskillswavedirection={
	},
	monsterspawnergroups={
	},
	monsterspawnergroupsperlevel={
	},
	monsterspawneroverrides={
	},
	monsterstatsfrommapstats={
	},
	monstertypes={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=300
		},
		[2]={
			list=false,
			name="OTFile",
			refTo="",
			type="String",
			width=550
		},
		[3]={
			list=false,
			name="Accuracy",
			refTo="",
			type="Int",
			width=70
		},
		[4]={
			list=false,
			name="IsPlayerMinion",
			refTo="",
			type="Bool",
			width=80
		},
		[5]={
			list=false,
			name="Armour",
			refTo="",
			type="Int",
			width=60
		},
		[6]={
			list=false,
			name="Evasion",
			refTo="",
			type="Int",
			width=60
		},
		[7]={
			list=false,
			name="EnergyShield",
			refTo="",
			type="Int",
			width=70
		},
		[8]={
			list=false,
			name="DamageSpread",
			refTo="",
			type="Int",
			width=100
		},
		[9]={
			list=true,
			name="Resistances",
			refTo="MonsterResistances",
			type="Key",
			width=250
		},
		[10]={
			list=false,
			name="BaseDamageIgnoresAttackSpeed",
			refTo="",
			type="Bool",
			width=180
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		}
	},
	monstervarieties={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=510
		},
		[2]={
			list=false,
			name="Type",
			refTo="MonsterTypes",
			type="Key",
			width=260
		},
		[3]={
			list=false,
			name="MovementSpeed",
			refTo="",
			type="Int",
			width=100
		},
		[4]={
			list=false,
			name="ObjectSize",
			refTo="",
			type="Enum",
			width=60
		},
		[5]={
			list=false,
			name="MinimumAttackRange",
			refTo="",
			type="Int",
			width=110
		},
		[6]={
			list=false,
			name="MaximumAttackRange",
			refTo="",
			type="Int",
			width=110
		},
		[7]={
			list=true,
			name="Actor",
			refTo="",
			type="String",
			width=360
		},
		[8]={
			list=true,
			name="AnimatedObject",
			refTo="",
			type="String",
			width=390
		},
		[9]={
			list=false,
			name="ObjectType",
			refTo="",
			type="String",
			width=370
		},
		[10]={
			list=true,
			name="Mods",
			refTo="Mods",
			type="Key",
			width=500
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=50
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=50
		},
		[14]={
			list=false,
			name="ModelSizeMultiplier",
			refTo="",
			type="Int",
			width=120
		},
		[15]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[16]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[17]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[18]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[19]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[20]={
			list=true,
			name="Tags",
			refTo="Tags",
			type="Key",
			width=150
		},
		[21]={
			list=false,
			name="ExperienceMultiplier",
			refTo="",
			type="Int",
			width=120
		},
		[22]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[23]={
			list=false,
			name="MinAgroRange",
			refTo="",
			type="Int",
			width=100
		},
		[24]={
			list=false,
			name="MaxAgroRange",
			refTo="",
			type="Int",
			width=100
		},
		[25]={
			list=false,
			name="SpotlightColour1",
			refTo="",
			type="Int",
			width=100
		},
		[26]={
			list=false,
			name="SpotlightColour2",
			refTo="",
			type="Int",
			width=100
		},
		[27]={
			list=false,
			name="SpotlightColour3",
			refTo="",
			type="Int",
			width=100
		},
		[28]={
			list=true,
			name="GrantedEffects",
			refTo="GrantedEffects",
			type="Key",
			width=500
		},
		[29]={
			list=false,
			name="AIScript",
			refTo="",
			type="String",
			width=150
		},
		[30]={
			list=true,
			name="ModsKeys2",
			refTo="Mods",
			type="Key",
			width=200
		},
		[31]={
			list=false,
			name="Stance",
			refTo="",
			type="String",
			width=100
		},
		[32]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=120
		},
		[33]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=200
		},
		[34]={
			list=false,
			name="DamageMultiplier",
			refTo="",
			type="Int",
			width=100
		},
		[35]={
			list=false,
			name="LifeMultiplier",
			refTo="",
			type="Int",
			width=100
		},
		[36]={
			list=false,
			name="AttackDuration",
			refTo="",
			type="Int",
			width=100
		},
		[37]={
			list=true,
			name="MainHandItem",
			refTo="ItemVisualIdentity",
			type="Key",
			width=150
		},
		[38]={
			list=true,
			name="OffHandItem",
			refTo="ItemVisualIdentity",
			type="Key",
			width=150
		},
		[39]={
			list=false,
			name="BackItem",
			refTo="ItemVisualIdentity",
			type="Key",
			width=150
		},
		[40]={
			list=false,
			name="MainHandItemClass",
			refTo="ItemClasses",
			type="Key",
			width=150
		},
		[41]={
			list=false,
			name="OffHandItemClass",
			refTo="ItemClasses",
			type="Key",
			width=150
		},
		[42]={
			list=false,
			name="HelmetItem",
			refTo="ItemVisualIdentity",
			type="Key",
			width=150
		},
		[43]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[44]={
			list=true,
			name="KillSpecificMonsterCount",
			refTo="AchievementItems",
			type="Key",
			width=150
		},
		[45]={
			list=true,
			name="SpecialMods",
			refTo="Mods",
			type="Key",
			width=150
		},
		[46]={
			list=true,
			name="KillRare",
			refTo="AchievementItems",
			type="Key",
			width=150
		},
		[47]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[48]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[49]={
			list=false,
			name="",
			refTo="AchievementItems",
			type="Int",
			width=50
		},
		[50]={
			list=false,
			name="",
			refTo="AchievementItems",
			type="Int",
			width=50
		},
		[51]={
			list=false,
			name="",
			refTo="AchievementItems",
			type="Int",
			width=50
		},
		[52]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[53]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[54]={
			list=false,
			name="Hash",
			refTo="",
			type="UInt16",
			width=50
		},
		[55]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=40
		},
		[56]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=50
		},
		[57]={
			list=false,
			name="AchievementFlashback",
			refTo="AchievementItems",
			type="Key",
			width=150
		},
		[58]={
			list=false,
			name="MonsterSegments",
			refTo="MonsterSegments",
			type="Key",
			width=120
		},
		[59]={
			list=false,
			name="MonsterArmours",
			refTo="MonsterArmours",
			type="Key",
			width=120
		},
		[60]={
			list=false,
			name="AchievementTalisman",
			refTo="AchievementItems",
			type="Key",
			width=150
		},
		[61]={
			list=true,
			name="ModsPart1",
			refTo="Mods",
			type="Key",
			width=150
		},
		[62]={
			list=true,
			name="ModsPart2",
			refTo="Mods",
			type="Key",
			width=150
		},
		[63]={
			list=true,
			name="ModsEndgame",
			refTo="Mods",
			type="Key",
			width=150
		},
		[64]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[65]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[66]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[67]={
			list=true,
			name="",
			refTo="AchievementItems",
			type="Key",
			width=50
		},
		[68]={
			list=true,
			name="MultiPartAchievements",
			refTo="MultiPartAchievements",
			type="Key",
			width=150
		},
		[69]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[70]={
			list=false,
			name="SinkAnimation",
			refTo="",
			type="String",
			width=100
		},
		[71]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[72]={
			list=true,
			name="",
			refTo="MultiPartAchievements",
			type="Key",
			width=50
		},
		[73]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[74]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[75]={
			list=false,
			name="NotSpectre",
			refTo="",
			type="Bool",
			width=80
		},
		[76]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[77]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[78]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=50
		},
		[79]={
			list=false,
			name="SinkEffect",
			refTo="",
			type="String",
			width=100
		},
		[80]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[81]={
			list=false,
			name="MonsterConditionalEffectPack",
			refTo="MonsterConditionalEffectPacks",
			type="Key",
			width=150
		},
		[82]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[83]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[84]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[85]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[86]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[87]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[88]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[89]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[90]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[91]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[92]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[93]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[94]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[95]={
			list=false,
			name="BossHealthBar",
			refTo="",
			type="Bool",
			width=100
		},
		[96]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=200
		},
		[97]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[98]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[99]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[100]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[101]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[102]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[103]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[104]={
			list=false,
			name="QuestFlag",
			refTo="QuestFlags",
			type="Key",
			width=150
		},
		[105]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=80
		},
		[106]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=80
		},
		[107]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[108]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[109]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[110]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[111]={
			list=false,
			name="PoiseThreshold",
			refTo="",
			type="Int",
			width=100
		},
		[112]={
			list=false,
			name="AttackCrit",
			refTo="",
			type="Float",
			width=150
		},
		[113]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[114]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[115]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[116]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[117]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[118]={
			list=false,
			name="MonsterCategory",
			refTo="MonsterCategories",
			type="Key",
			width=150
		}
	},
	monstervarietiesartvariations={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=200
		},
		[2]={
			list=false,
			name="Variant",
			refTo="",
			type="Int",
			width=100
		},
		[3]={
			list=true,
			name="Monster",
			refTo="MonsterVarieties",
			type="Key",
			width=550
		}
	},
	mousecursorsizesettings={
	},
	movedaemon={
	},
	mtxsetbonus={
	},
	mtxtypegamespecific={
	},
	mtxtypes={
	},
	multipartachievementareas={
	},
	multipartachievementconditions={
	},
	multipartachievements={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=350
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[3]={
			list=false,
			name="Achievement",
			refTo="AchievementItems",
			type="Key",
			width=210
		},
		[4]={
			list=false,
			name="Threshold",
			refTo="",
			type="Int",
			width=50
		}
	},
	music={
	},
	musiccategories={
	},
	mysteryboxes={
	},
	nearbymonsterconditions={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=260
		}
	},
	nettiers={
	},
	notifications={
	},
	npcadditionalvendoritems={
	},
	npcaudio={
	},
	npcconversations={
	},
	npcdialoguecutscene={
	},
	npcdialoguecutscenesequences={
	},
	npcdialoguestyles={
	},
	npcfollowervariations={
	},
	npcmaster={
	},
	npcmasterlevels={
	},
	npcportraitaooverrides={
	},
	npcportraits={
	},
	npcs={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=360
		},
		[2]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="MetaData",
			refTo="",
			type="String",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="NPCMaster",
			type="Key",
			width=150
		},
		[6]={
			list=false,
			name="ShortName",
			refTo="",
			type="String",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[8]={
			list=true,
			name="NPCAudios1",
			refTo="NPCAudio",
			type="Key",
			width=150
		},
		[9]={
			list=true,
			name="NPCAudios2",
			refTo="NPCAudio",
			type="Key",
			width=150
		},
		[10]={
			list=false,
			name="HASH16",
			refTo="",
			type="UInt16",
			width=150
		},
		[11]={
			list=false,
			name="Model?",
			refTo="npcs",
			type="ShortKey",
			width=230
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=100
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=100
		},
		[14]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=100
		},
		[15]={
			list=false,
			name="Gender",
			refTo="",
			type="String",
			width=150
		},
		[16]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=100
		},
		[17]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[18]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		}
	},
	npcshop={
	},
	npcshopadditionalitems={
	},
	npcshopgamblervisualidentity={
	},
	npcshops={
	},
	npcshopsellpricetype={
	},
	npctalk={
	},
	npctalkcategory={
	},
	npctalkconsolequickactions={
	},
	npctalkmobilegroup={
	},
	npctextaudio={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=true,
			name="Characters",
			refTo="Characters",
			type="Key",
			width=230
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=400
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		}
	},
	npctextaudiointerruptrules={
	},
	npctype={
	},
	npcvendordialogue={
	},
	npcvendordialogueconditions={
	},
	oldmapstashtablayout={
	},
	ongoingbuffvariations={
	},
	ongoingtriggervariations={
	},
	onhiteffecttarget={
	},
	onkillachievements={
	},
	orientations={
	},
	packformation={
	},
	pantheonpanellayout={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="X",
			refTo="",
			type="Int",
			width=50
		},
		[3]={
			list=false,
			name="Y",
			refTo="",
			type="Int",
			width=50
		},
		[4]={
			list=false,
			name="IsMajorGod",
			refTo="",
			type="Bool",
			width=80
		},
		[5]={
			list=false,
			name="CoverImage",
			refTo="",
			type="String",
			width=380
		},
		[6]={
			list=false,
			name="GodName2",
			refTo="",
			type="String",
			width=220
		},
		[7]={
			list=false,
			name="SelectionFrame",
			refTo="",
			type="String",
			width=390
		},
		[8]={
			list=true,
			name="Effect1StatsKey",
			refTo="Stats",
			type="Key",
			width=650
		},
		[9]={
			list=true,
			name="Effect1Values",
			refTo="",
			type="Int",
			width=150
		},
		[10]={
			list=true,
			name="Effect2StatsKey",
			refTo="Stats",
			type="Key",
			width=530
		},
		[11]={
			list=false,
			name="GodName3",
			refTo="",
			type="String",
			width=170
		},
		[12]={
			list=true,
			name="Effect3Values",
			refTo="",
			type="Int",
			width=150
		},
		[13]={
			list=true,
			name="Effect3StatsKey",
			refTo="Stats",
			type="Key",
			width=470
		},
		[14]={
			list=false,
			name="GodName4",
			refTo="",
			type="String",
			width=170
		},
		[15]={
			list=true,
			name="Effect4StatsKey",
			refTo="Stats",
			type="Key",
			width=370
		},
		[16]={
			list=true,
			name="Effect4Values",
			refTo="",
			type="Int",
			width=150
		},
		[17]={
			list=false,
			name="GodName1",
			refTo="",
			type="String",
			width=150
		},
		[18]={
			list=true,
			name="Effect2Values",
			refTo="",
			type="Int",
			width=100
		},
		[19]={
			list=false,
			name="QuestFlags",
			refTo="QuestFlags",
			type="Key",
			width=100
		},
		[20]={
			list=false,
			name="AchievementItems",
			refTo="AchievementItems",
			type="Key",
			width=150
		},
		[21]={
			list=false,
			name="QuestState1",
			refTo="QuestStates",
			type="Key",
			width=70
		},
		[22]={
			list=false,
			name="QuestState2",
			refTo="QuestStates",
			type="Key",
			width=70
		},
		[23]={
			list=false,
			name="IsDisabled",
			refTo="",
			type="Bool",
			width=150
		},
		[24]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=100
		}
	},
	pantheonsouls={
	},
	passivejewelart={
		[1]={
			list=false,
			name="Item",
			refTo="BaseItemTypes",
			type="Key",
			width=300
		},
		[2]={
			list=false,
			name="JewelArt",
			refTo="",
			type="String",
			width=550
		},
		[3]={
			list=false,
			name="JewelBlueArt",
			refTo="",
			type="String",
			width=480
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		}
	},
	passivejewelnodemodifyingstats={
		[1]={
			list=false,
			name="Stat",
			refTo="Stats",
			type="Key",
			width=500
		},
		[2]={
			list=false,
			name="ReplaceStat",
			refTo="Stats",
			type="Key",
			width=250
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		}
	},
	passivejewelradii={
		[1]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=100
		},
		[2]={
			list=false,
			name="RingOuter",
			refTo="",
			type="Int",
			width=100
		},
		[3]={
			list=false,
			name="RingInner",
			refTo="",
			type="Int",
			width=100
		},
		[4]={
			list=false,
			name="Radius",
			refTo="",
			type="Int",
			width=100
		}
	},
	passivejewelslots={
		[1]={
			list=false,
			name="Passive",
			refTo="PassiveSkills",
			type="Key",
			width=180
		},
		[2]={
			list=false,
			name="ClusterSize",
			refTo="PassiveTreeExpansionJewelSizes",
			type="Key",
			width=80
		},
		[3]={
			list=false,
			name="ClusterIndex",
			refTo="",
			type="Int",
			width=80
		},
		[4]={
			list=false,
			name="Parent",
			refTo="PassiveJewelSlots",
			type="Enum",
			width=170
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Enum",
			width=150
		},
		[6]={
			list=false,
			name="Proxy",
			refTo="PassiveSkills",
			type="Key",
			width=210
		},
		[7]={
			list=true,
			name="StartIndices",
			refTo="",
			type="Int",
			width=150
		},
		[8]={
			list=false,
			name="UIArtOverride",
			refTo="PassiveNodeUIArtOverride",
			type="Key",
			width=200
		}
	},
	passivejeweluniqueart={
		[1]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[4]={
			list=false,
			name="JewelAsset",
			refTo="",
			type="String",
			width=560
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		}
	},
	passivenodetypes={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		}
	},
	passivenodeuiartoverride={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=200
		},
		[2]={
			list=false,
			name="SocketNormal",
			refTo="",
			type="String",
			width=420
		},
		[3]={
			list=false,
			name="SocketCanAllocate",
			refTo="",
			type="String",
			width=450
		},
		[4]={
			list=false,
			name="SocketActive",
			refTo="",
			type="String",
			width=420
		},
		[5]={
			list=false,
			name="SocketNormal1",
			refTo="",
			type="String",
			width=150
		},
		[6]={
			list=false,
			name="SocketCanAllocate1",
			refTo="",
			type="String",
			width=150
		},
		[7]={
			list=false,
			name="SocketActive1",
			refTo="",
			type="String",
			width=150
		},
		[8]={
			list=false,
			name="SocketNormal2",
			refTo="",
			type="String",
			width=150
		},
		[9]={
			list=false,
			name="SocketCanAllocate2",
			refTo="",
			type="String",
			width=150
		},
		[10]={
			list=false,
			name="SocketActive2",
			refTo="",
			type="String",
			width=150
		},
		[11]={
			list=false,
			name="SocketMask",
			refTo="",
			type="String",
			width=510
		}
	},
	passiveoverridelimits={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Description",
			refTo="",
			type="String",
			width=200
		}
	},
	passiveskillbuffs={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=350
		},
		[2]={
			list=true,
			name="",
			refTo="",
			type="Float",
			width=150
		},
		[3]={
			list=false,
			name="Buff",
			refTo="BuffDefinitions",
			type="Key",
			width=350
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	passiveskillfiltercatagories={
	},
	passiveskillfilteroptions={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	passiveskillmasteryeffects={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Hash",
			refTo="",
			type="UInt16",
			width=80
		},
		[3]={
			list=true,
			name="Stats",
			refTo="Stats",
			type="Key",
			width=450
		},
		[4]={
			list=false,
			name="Stat1",
			refTo="",
			type="Int",
			width=80
		},
		[5]={
			list=false,
			name="Stat2",
			refTo="",
			type="Int",
			width=80
		},
		[6]={
			list=false,
			name="Stat3",
			refTo="",
			type="Int",
			width=80
		},
		[7]={
			list=false,
			name="AchievementItem",
			refTo="AchievementItems",
			type="Key",
			width=150
		}
	},
	passiveskillmasterygroups={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=true,
			name="MasteryEffects",
			refTo="PassiveSkillMasteryEffects",
			type="Key",
			width=150
		},
		[3]={
			list=false,
			name="IconInactive",
			refTo="",
			type="String",
			width=150
		},
		[4]={
			list=false,
			name="IconActive",
			refTo="",
			type="String",
			width=150
		},
		[5]={
			list=false,
			name="Background",
			refTo="",
			type="String",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[7]={
			list=false,
			name="SoundEffect",
			refTo="SoundEffects",
			type="Key",
			width=150
		},
		[8]={
			list=false,
			name="MasteryCount",
			refTo="Stats",
			type="Key",
			width=150
		}
	},
	passiveskilloverrides={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=220
		},
		[3]={
			list=false,
			name="Icon",
			refTo="",
			type="String",
			width=330
		},
		[4]={
			list=true,
			name="StatsKeys",
			refTo="Stats",
			type="Key",
			width=580
		},
		[5]={
			list=true,
			name="StatValues",
			refTo="",
			type="Int",
			width=100
		},
		[6]={
			list=false,
			name="Hash",
			refTo="",
			type="Int",
			width=100
		},
		[7]={
			list=false,
			name="Background",
			refTo="",
			type="String",
			width=530
		},
		[8]={
			list=false,
			name="GrantedEffect",
			refTo="GrantedEffectsPerLevel",
			type="Key",
			width=250
		},
		[9]={
			list=false,
			name="TattooType",
			refTo="passiveskilloverridetypes",
			type="Key",
			width=150
		},
		[10]={
			list=false,
			name="Limit",
			refTo="passiveoverridelimits",
			type="Key",
			width=150
		},
		[11]={
			list=false,
			name="MinimumConnected",
			refTo="",
			type="Int",
			width=150
		},
		[12]={
			list=false,
			name="MaximumConnected",
			refTo="",
			type="Int",
			width=150
		},
		[13]={
			list=false,
			name="PassiveSkill",
			refTo="PassiveSkills",
			type="Key",
			width=150
		}
	},
	passiveskilloverridetypes={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Stat",
			refTo="Stats",
			type="Key",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		}
	},
	passiveskills={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=350
		},
		[2]={
			list=false,
			name="Icon",
			refTo="",
			type="String",
			width=330
		},
		[3]={
			list=true,
			name="Stats",
			refTo="Stats",
			type="Key",
			width=640
		},
		[4]={
			list=false,
			name="Stat1",
			refTo="",
			type="Int",
			width=50
		},
		[5]={
			list=false,
			name="Stat2",
			refTo="",
			type="Int",
			width=50
		},
		[6]={
			list=false,
			name="Stat3",
			refTo="",
			type="Int",
			width=50
		},
		[7]={
			list=false,
			name="Stat4",
			refTo="",
			type="Int",
			width=50
		},
		[8]={
			list=false,
			name="PassiveSkillNodeId",
			refTo="",
			type="UInt16",
			width=100
		},
		[9]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[10]={
			list=true,
			name="ClassStart",
			refTo="Characters",
			type="Key",
			width=530
		},
		[11]={
			list=false,
			name="Keystone",
			refTo="",
			type="Bool",
			width=60
		},
		[12]={
			list=false,
			name="Notable",
			refTo="",
			type="Bool",
			width=60
		},
		[13]={
			list=false,
			name="FlavourText",
			refTo="",
			type="String",
			width=300
		},
		[14]={
			list=false,
			name="IsOnlyImage",
			refTo="",
			type="Bool",
			width=60
		},
		[15]={
			list=false,
			name="Achievement",
			refTo="AchievementItems",
			type="Key",
			width=150
		},
		[16]={
			list=false,
			name="JewelSocket",
			refTo="",
			type="Bool",
			width=70
		},
		[17]={
			list=false,
			name="Ascendancy",
			refTo="Ascendancy",
			type="Key",
			width=90
		},
		[18]={
			list=false,
			name="AscendancyStart",
			refTo="",
			type="Bool",
			width=100
		},
		[19]={
			list=true,
			name="ReminderTexts",
			refTo="ClientStrings",
			type="Key",
			width=150
		},
		[20]={
			list=false,
			name="PassivePointsGranted",
			refTo="",
			type="Int",
			width=120
		},
		[21]={
			list=false,
			name="MultipleChoice",
			refTo="",
			type="Bool",
			width=80
		},
		[22]={
			list=false,
			name="MultipleChoiceOption",
			refTo="",
			type="Bool",
			width=110
		},
		[23]={
			list=false,
			name="Stat5",
			refTo="",
			type="Int",
			width=50
		},
		[24]={
			list=true,
			name="PassiveSkillBuffs",
			refTo="BuffTemplates",
			type="Key",
			width=150
		},
		[25]={
			list=false,
			name="AnnointOnly",
			refTo="",
			type="Bool",
			width=50
		},
		[26]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[27]={
			list=false,
			name="ClusterNode",
			refTo="",
			type="Bool",
			width=70
		},
		[28]={
			list=false,
			name="Proxy",
			refTo="",
			type="Bool",
			width=60
		},
		[29]={
			enumBase=1,
			list=false,
			name="Type",
			refTo="PassiveSkillTypes",
			type="Enum",
			width=80
		},
		[30]={
			list=false,
			name="MasteryGroup",
			refTo="PassiveSkillMasteryGroups",
			type="Key",
			width=150
		},
		[31]={
			list=false,
			name="AtlasMastery_rid",
			refTo="AtlasPassiveSkillTreeGroupType",
			type="Key",
			width=150
		},
		[32]={
			list=false,
			name="SoundEffect",
			refTo="SoundEffects",
			type="Key",
			width=150
		},
		[33]={
			list=false,
			name="AtlasnodeGroup",
			refTo="",
			type="String",
			width=190
		},
		[34]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[35]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[36]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[37]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[38]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[39]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[40]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[41]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[42]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[43]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[44]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[45]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[46]={
			list=false,
			name="Attribute",
			refTo="",
			type="Bool",
			width=150
		},
		[47]={
			list=false,
			name="AtlasSubTree",
			refTo="AtlasPassiveSkillSubTrees",
			type="Key",
			width=150
		},
		[48]={
			list=false,
			name="IsRootOfAtlas",
			refTo="",
			type="Bool",
			width=150
		},
		[49]={
			list=false,
			name="GrantedSkill",
			refTo="SkillGems",
			type="Key",
			width=340
		},
		[50]={
			list=false,
			name="WeaponPointsGranted",
			refTo="",
			type="Int",
			width=150
		},
		[51]={
			list=false,
			name="FreeAllocate",
			refTo="",
			type="Bool",
			width=150
		},
		[52]={
			list=false,
			name="ApplyToArmour?",
			refTo="",
			type="Bool",
			width=150
		}
	},
	passiveskillstatcategories={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		}
	},
	passiveskilltattoos={
		[1]={
			list=false,
			name="BaseItem",
			refTo="BaseItemTypes",
			type="Key",
			width=380
		},
		[2]={
			list=false,
			name="Override",
			refTo="passiveskilloverrides",
			type="Key",
			width=150
		},
		[3]={
			list=false,
			name="NodeTarget",
			refTo="passiveskilltattootargetsets",
			type="Key",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		}
	},
	passiveskilltattootargets={
	},
	passiveskilltattootargetsets={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=false,
			name="Type",
			refTo="",
			type="String",
			width=150
		},
		[4]={
			list=false,
			name="Value",
			refTo="",
			type="String",
			width=150
		}
	},
	passiveskilltrees={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="PassiveSkillGraph",
			refTo="",
			type="String",
			width=390
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[14]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[15]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[16]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[17]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[18]={
			list=false,
			name="ClientStrings",
			refTo="ClientStrings",
			type="Key",
			width=600
		},
		[19]={
			list=false,
			name="UIArt",
			refTo="passiveskilltreeuiart",
			type="Key",
			width=150
		}
	},
	passiveskilltreetutorial={
	},
	passiveskilltreeuiart={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="GroupBackgroundSmall",
			refTo="",
			type="String",
			width=550
		},
		[3]={
			list=false,
			name="GroupBackgroundMedium",
			refTo="",
			type="String",
			width=550
		},
		[4]={
			list=false,
			name="GroupBackgroundLarge",
			refTo="",
			type="String",
			width=550
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[6]={
			list=false,
			name="PassiveFrameNormal",
			refTo="",
			type="String",
			width=550
		},
		[7]={
			list=false,
			name="NotableFrameNormal",
			refTo="",
			type="String",
			width=550
		},
		[8]={
			list=false,
			name="KeystoneFrameNormal",
			refTo="",
			type="String",
			width=550
		},
		[9]={
			list=false,
			name="PassiveFrameActive",
			refTo="",
			type="String",
			width=550
		},
		[10]={
			list=false,
			name="NotableFrameActive",
			refTo="",
			type="String",
			width=550
		},
		[11]={
			list=false,
			name="KeystoneFrameActive",
			refTo="",
			type="String",
			width=550
		},
		[12]={
			list=false,
			name="PassiveFrameCanAllocate",
			refTo="",
			type="String",
			width=550
		},
		[13]={
			list=false,
			name="NotableFrameCanAllocate",
			refTo="",
			type="String",
			width=550
		},
		[14]={
			list=false,
			name="KeystoneFrameCanAllocate",
			refTo="",
			type="String",
			width=550
		},
		[15]={
			list=false,
			name="Ornament",
			refTo="",
			type="String",
			width=550
		},
		[16]={
			list=false,
			name="GroupBackgroundSmallBlank",
			refTo="",
			type="String",
			width=550
		},
		[17]={
			list=false,
			name="GroupBackgroundMediumBlank",
			refTo="",
			type="String",
			width=550
		},
		[18]={
			list=false,
			name="GroupBackgroundLargeBlank",
			refTo="",
			type="String",
			width=550
		}
	},
	passiveskilltypes={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		}
	},
	passivetreeexpansionjewels={
		[1]={
			list=false,
			name="BaseItemType",
			refTo="BaseItemTypes",
			type="Key",
			width=360
		},
		[2]={
			list=false,
			name="Size",
			refTo="PassiveTreeExpansionJewelSizes",
			type="Key",
			width=70
		},
		[3]={
			list=false,
			name="MinNodes",
			refTo="",
			type="Int",
			width=70
		},
		[4]={
			list=false,
			name="MaxNodes",
			refTo="",
			type="Int",
			width=70
		},
		[5]={
			list=true,
			name="SmallIndicies",
			refTo="",
			type="Int",
			width=200
		},
		[6]={
			list=true,
			name="NotableIndicies",
			refTo="",
			type="Int",
			width=130
		},
		[7]={
			list=true,
			name="SocketIndicies",
			refTo="",
			type="Int",
			width=90
		},
		[8]={
			list=false,
			name="TotalIndicies",
			refTo="",
			type="Int",
			width=150
		}
	},
	passivetreeexpansionjewelsizes={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		}
	},
	passivetreeexpansionskills={
		[1]={
			list=false,
			name="Node",
			refTo="PassiveSkills",
			type="Key",
			width=320
		},
		[2]={
			list=false,
			name="Mastery",
			refTo="PassiveSkills",
			type="Key",
			width=410
		},
		[3]={
			list=false,
			name="Tag",
			refTo="Tags",
			type="Key",
			width=350
		},
		[4]={
			list=false,
			name="JewelSize",
			refTo="PassiveTreeExpansionJewelSizes",
			type="Key",
			width=70
		}
	},
	passivetreeexpansionspecialskills={
		[1]={
			list=false,
			name="Node",
			refTo="PassiveSkills",
			type="Key",
			width=270
		},
		[2]={
			list=false,
			name="Stat",
			refTo="Stats",
			type="Key",
			width=350
		}
	},
	pathofendurance={
	},
	pcbangrewardmicros={
	},
	perandusbosses={
	},
	peranduschests={
	},
	perandusdaemons={
	},
	perandusguards={
	},
	perlevelvalues={
	},
	pet={
	},
	playerconditions={
	},
	playerminionintrinsicstats={
		[1]={
			list=false,
			name="Id",
			refTo="Stats",
			type="Key",
			width=350
		},
		[2]={
			list=false,
			name="Value",
			refTo="",
			type="Int",
			width=150
		}
	},
	playertradewhisperformats={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=170
		},
		[2]={
			list=false,
			name="Whisper",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="InStash",
			refTo="",
			type="Bool",
			width=150
		},
		[4]={
			list=false,
			name="IsPriced",
			refTo="",
			type="Bool",
			width=150
		}
	},
	portalaudio={
	},
	portalaudioevents={
	},
	preloadfromstats={
	},
	preloadgroups={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		}
	},
	preloadpriorities={
	},
	primordialbosslifescalingperlevel={
	},
	projectilecollisiontypes={
	},
	projectileoverrides={
	},
	projectiles={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=370
		},
		[2]={
			list=true,
			name="AOFiles",
			refTo="",
			type="String",
			width=420
		},
		[3]={
			list=true,
			name="LoopAnimationIds",
			refTo="",
			type="String",
			width=200
		},
		[4]={
			list=true,
			name="ImpactAnimationIds",
			refTo="",
			type="String",
			width=200
		},
		[5]={
			list=false,
			name="ProjectileSpeed",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[10]={
			list=false,
			name="InheritsFrom",
			refTo="",
			type="String",
			width=250
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=70
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=70
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[14]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[15]={
			list=true,
			name="Stuck_AOFile",
			refTo="",
			type="String",
			width=150
		},
		[16]={
			list=false,
			name="Bounce_AOFile",
			refTo="",
			type="String",
			width=150
		},
		[17]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[18]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[19]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[20]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[21]={
			list=false,
			name="Animation1",
			refTo="miscanimated",
			type="Key",
			width=150
		},
		[22]={
			list=false,
			name="Animation2",
			refTo="miscanimated",
			type="Key",
			width=200
		},
		[23]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[24]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[25]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[26]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[27]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[28]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=70
		},
		[29]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[30]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[31]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[32]={
			list=false,
			name="BounceAnimation",
			refTo="MiscAnimated",
			type="Key",
			width=260
		},
		[33]={
			list=false,
			name="TerrainImpactAnimation",
			refTo="MiscAnimated",
			type="Key",
			width=250
		},
		[34]={
			list=false,
			name="DamageEffect",
			refTo="DamageEffectVariations",
			type="Key",
			width=250
		},
		[35]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[36]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[37]={
			list=false,
			name="DamageEffect",
			refTo="DamageEffectVariations",
			type="Key",
			width=200
		},
		[38]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[39]={
			list=false,
			name="",
			refTo="BallisticBounceOverride",
			type="Key",
			width=150
		},
		[40]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[41]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[42]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		}
	},
	projectilesartvariations={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Variant",
			refTo="",
			type="Int",
			width=100
		},
		[3]={
			list=true,
			name="Projectile",
			refTo="Projectiles",
			type="Key",
			width=380
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=100
		}
	},
	projectilevariations={
	},
	prophecies={
	},
	prophecychain={
	},
	prophecysetnames={
	},
	prophecysets={
	},
	prophecytype={
	},
	pvptypes={
	},
	quest={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=230
		},
		[2]={
			list=false,
			name="Act",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=180
		},
		[4]={
			list=false,
			name="Icon",
			refTo="",
			type="String",
			width=150
		},
		[5]={
			list=false,
			name="QuestId",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[7]={
			list=false,
			name="Type",
			refTo="QuestType",
			type="Key",
			width=150
		},
		[8]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=40
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[13]={
			list=true,
			name="NormalReward",
			refTo="QuestRewardType",
			type="Key",
			width=150
		},
		[14]={
			list=true,
			name="CruelReward",
			refTo="QuestRewardType",
			type="Key",
			width=150
		}
	},
	questachievements={
	},
	questflags={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=250
		},
		[2]={
			list=false,
			name="Hash32",
			refTo="",
			type="UInt",
			width=150
		}
	},
	questitemnpcaudio={
	},
	questitems={
	},
	questrewardoffers={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=200
		},
		[2]={
			list=false,
			name="Quest",
			refTo="Quest",
			type="Key",
			width=200
		},
		[3]={
			list=false,
			name="QuestFlags",
			refTo="QuestFlags",
			type="Key",
			width=200
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Interval",
			width=150
		}
	},
	questrewards={
		[1]={
			list=false,
			name="Id",
			refTo="QuestRewardOffers",
			type="Key",
			width=150
		},
		[2]={
			list=false,
			name="QuestPart",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=false,
			name="ActDifficulty",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[5]={
			list=false,
			name="BaseItemType",
			refTo="BaseItemTypes",
			type="Key",
			width=460
		},
		[6]={
			list=false,
			name="RewardItemLevel",
			refTo="",
			type="Int",
			width=150
		},
		[7]={
			list=false,
			name="QuestType",
			refTo="QuestType",
			type="Key",
			width=150
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=50
		},
		[10]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[14]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[15]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=50
		},
		[16]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[17]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[18]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[19]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[20]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=50
		},
		[21]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[22]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		}
	},
	questrewardtype={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Icon",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[4]={
			list=false,
			name="Description",
			refTo="",
			type="String",
			width=150
		},
		[5]={
			list=false,
			name="BaseItemType",
			refTo="BaseItemTypes",
			type="Key",
			width=150
		}
	},
	queststatecalculation={
	},
	queststates={
	},
	queststaticrewards={
		[1]={
			list=false,
			name="QuestFlag",
			refTo="questflags",
			type="Key",
			width=250
		},
		[2]={
			list=false,
			name="Acts",
			refTo="",
			type="Int",
			width=50
		},
		[3]={
			list=false,
			name="WeaponPassives",
			refTo="",
			type="Int",
			width=100
		},
		[4]={
			list=true,
			name="Stats",
			refTo="Stats",
			type="Key",
			width=400
		},
		[5]={
			list=true,
			name="StatValues",
			refTo="",
			type="Int",
			width=100
		},
		[6]={
			list=false,
			name="Quest",
			refTo="Quest",
			type="Key",
			width=150
		},
		[7]={
			list=false,
			name="AscendancyPointsRewarded",
			refTo="",
			type="Int",
			width=150
		},
		[8]={
			list=false,
			name="ClientString",
			refTo="clientstrings",
			type="Key",
			width=250
		},
		[9]={
			list=false,
			name="WeaponSetPointsRewarded",
			refTo="",
			type="Int",
			width=150
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		}
	},
	questtrackergroup={
	},
	questtype={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=true,
			name="Colour",
			refTo="",
			type="Int",
			width=150
		}
	},
	questvendorrewards={
	},
	raceareas={
	},
	racerewardtomicro={
	},
	races={
	},
	racetimes={
	},
	randomuniquemonsters={
	},
	raremonsterlifescalingperlevel={
	},
	rarity={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="MinMods",
			refTo="",
			type="Int",
			width=80
		},
		[3]={
			list=false,
			name="MaxMods",
			refTo="",
			type="Int",
			width=80
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[5]={
			list=false,
			name="MaxPrefix",
			refTo="",
			type="Int",
			width=80
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[7]={
			list=false,
			name="MaxSuffix",
			refTo="",
			type="Int",
			width=80
		},
		[8]={
			list=false,
			name="Colour",
			refTo="",
			type="String",
			width=80
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=80
		}
	},
	raritymask={
	},
	realms={
	},
	recipeunlockdisplay={
		[1]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=320
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		}
	},
	recipeunlockobjects={
	},
	relativeimportanceconstants={
	},
	relicinventorylayout={
	},
	relicitemeffectvariations={
	},
	remindertext={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=350
		},
		[2]={
			list=false,
			name="Text",
			refTo="",
			type="String",
			width=1000
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		}
	},
	reservationskillsaudio={
	},
	resistancepenaltyperarealevel={
		[1]={
			list=false,
			name="Level",
			refTo="",
			type="Int",
			width=80
		},
		[2]={
			list=false,
			name="Penalty",
			refTo="",
			type="Int",
			width=80
		}
	},
	ritualbalanceperlevel={
	},
	ritualconstants={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=300
		}
	},
	ritualrunetypes={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=170
		}
	},
	ritualsetkillachievements={
	},
	ritualspawnpatterns={
	},
	rogueexilelifescalingperlevel={
	},
	rogueexilemodbonuses={
		[1]={
			list=false,
			name="Mod",
			refTo="Mods",
			type="Key",
			width=200
		},
		[2]={
			list=true,
			name="Stats",
			refTo="Stats",
			type="Key",
			width=400
		},
		[3]={
			list=true,
			name="StatValues",
			refTo="",
			type="Int",
			width=150
		}
	},
	rogueexiles={
	},
	rulesets={
	},
	runiccircles={
	},
	safehousebyocrafting={
	},
	safehousecraftingspree={
	},
	safehousecraftingspreecurrencies={
	},
	safehousecraftingspreetype={
	},
	salvageboxes={
	},
	sanctumairlocks={
	},
	sanctumbalanceperlevel={
	},
	sanctumdefenceicons={
	},
	sanctumdeferredrewarddisplaycategories={
	},
	sanctumeffecttriggers={
	},
	sanctumfloors={
	},
	sanctumfodderlifescalingperlevel={
	},
	sanctumimmediateeffecttype={
	},
	sanctumlifescalingperlevel={
	},
	sanctumpersistenteffectcategories={
	},
	sanctumpersistenteffectfamily={
	},
	sanctumpersistenteffects={
	},
	sanctumrewardobjects={
	},
	sanctumrooms={
	},
	sanctumroomtypes={
	},
	sanctumselectiondisplayoverride={
	},
	scarabs={
	},
	scarabtypes={
	},
	scoutingreports={
	},
	sentinelcraftingcurrency={
	},
	sentineldroneinventorylayout={
	},
	sentinelpassives={
	},
	sentinelpassivestats={
	},
	sentinelpassivetypes={
	},
	sentinelpowerexplevels={
	},
	sentinelstoragelayout={
	},
	sentineltaggedmonsterstats={
	},
	sessionquestflags={
	},
	shaperguardians={
	},
	shapermemoryfragments={
	},
	shaperorbs={
	},
	shapeshiftformclones={
	},
	shapeshiftforms={
		[1]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="AnimatedObject",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="Actor",
			refTo="",
			type="String",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[6]={
			list=true,
			name="Stat",
			refTo="Stats",
			type="Key",
			width=700
		},
		[7]={
			list=true,
			name="StatValues",
			refTo="",
			type="Int",
			width=150
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=100
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=100
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=80
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=100
		},
		[14]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=50
		},
		[15]={
			list=false,
			name="Stat2",
			refTo="Stats",
			type="Key",
			width=150
		},
		[16]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=100
		},
		[17]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=100
		},
		[18]={
			list=false,
			name="TransformedState",
			refTo="",
			type="String",
			width=150
		},
		[19]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		}
	},
	shapeshifttransformdata={
	},
	shieldtypes={
		[1]={
			list=false,
			name="BaseItemType",
			refTo="BaseItemTypes",
			type="Key",
			width=350
		},
		[2]={
			list=false,
			name="Block",
			refTo="",
			type="Int",
			width=80
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	shopcategory={
	},
	shopcountry={
	},
	shopcurrency={
	},
	shopforumbadge={
	},
	shoppackageplatform={
	},
	shoppaymentpackage={
	},
	shoppaymentpackageitems={
	},
	shoppaymentpackageprice={
	},
	shoppaymentpackageproxy={
	},
	shopregion={
	},
	shoptag={
	},
	shoptoken={
	},
	shrinebuffs={
	},
	shrines={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Timeout",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=false,
			name="Shared",
			refTo="",
			type="Bool",
			width=150
		},
		[4]={
			list=false,
			name="PlayerBuff",
			refTo="BuffTemplates",
			type="Key",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[7]={
			list=false,
			name="MonsterBuff",
			refTo="BuffTemplates",
			type="Key",
			width=150
		},
		[8]={
			list=false,
			name="MonsterVarieties",
			refTo="MonsterVarieties",
			type="Key",
			width=150
		},
		[9]={
			list=false,
			name="MonsterVarietiesPlayer",
			refTo="MonsterVarieties",
			type="Key",
			width=150
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[11]={
			list=false,
			name="Duration",
			refTo="",
			type="Int",
			width=150
		},
		[12]={
			list=false,
			name="ShrineSounds",
			refTo="ShrineSounds",
			type="Key",
			width=150
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[14]={
			list=true,
			name="Achievement",
			refTo="AchievementItems",
			type="Key",
			width=150
		},
		[15]={
			list=false,
			name="PVPOnly",
			refTo="",
			type="Bool",
			width=150
		},
		[16]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[17]={
			list=false,
			name="LesserShrine",
			refTo="",
			type="Bool",
			width=150
		},
		[18]={
			list=false,
			name="Description",
			refTo="ClientStrings",
			type="Key",
			width=150
		},
		[19]={
			list=false,
			name="Name",
			refTo="ClientStrings",
			type="Key",
			width=150
		},
		[20]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[21]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=70
		},
		[22]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=70
		},
		[23]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=70
		},
		[24]={
			list=false,
			name="",
			refTo="Stats",
			type="Key",
			width=300
		}
	},
	shrinesounds={
	},
	shrinevisualartvariations={
	},
	sigildisplay={
	},
	singlegroundlaser={
	},
	skillartvariations={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=200
		},
		[2]={
			list=true,
			name="AnimatedArt",
			refTo="MiscAnimatedArtVariations",
			type="Key",
			width=150
		},
		[3]={
			list=true,
			name="BeamArt",
			refTo="MiscBeamsArtVariations",
			type="Key",
			width=150
		},
		[4]={
			list=true,
			name="EffectPacksArt",
			refTo="MiscEffectPacksArtVariations",
			type="Key",
			width=290
		},
		[5]={
			list=true,
			name="ObjectArt",
			refTo="MiscObjectsArtVariations",
			type="Key",
			width=150
		},
		[6]={
			list=true,
			name="Variant",
			refTo="",
			type="String",
			width=400
		},
		[7]={
			list=true,
			name="BuffVisuals",
			refTo="BuffVisualsArtVariations",
			type="Key",
			width=150
		},
		[8]={
			list=true,
			name="VariantId?",
			refTo="",
			type="Int",
			width=100
		},
		[9]={
			list=true,
			name="ProjectilesArt",
			refTo="ProjectilesArtVariations",
			type="Key",
			width=350
		},
		[10]={
			list=false,
			name="Stat",
			refTo="Stats",
			type="Key",
			width=150
		},
		[11]={
			list=true,
			name="MonsterVarietiesArt",
			refTo="MonsterVarietiesArtVariations",
			type="Key",
			width=150
		},
		[12]={
			list=true,
			name="ActiveSkill",
			refTo="ActiveSkills",
			type="Key",
			width=300
		},
		[13]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[14]={
			list=true,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[15]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[16]={
			list=true,
			name="ProjectileModArt",
			refTo="MiscProjectileModArtVariations",
			type="Key",
			width=150
		},
		[17]={
			list=true,
			name="ParticleArt",
			refTo="MiscParticlesArtVariations",
			type="Key",
			width=150
		}
	},
	skillcraftingdata={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	skillevents={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=190
		}
	},
	skillgeminfo={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=300
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=870
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=430
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=520
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=240
		}
	},
	skillgemlevelupeffects={
	},
	skillgems={
		[1]={
			list=false,
			name="BaseItemType",
			refTo="BaseItemTypes",
			type="Key",
			width=400
		},
		[2]={
			list=false,
			name="Str",
			refTo="",
			type="Int",
			width=40
		},
		[3]={
			list=false,
			name="Dex",
			refTo="",
			type="Int",
			width=40
		},
		[4]={
			list=false,
			name="Int",
			refTo="",
			type="Int",
			width=40
		},
		[5]={
			list=false,
			name="VaalGem",
			refTo="BaseItemTypes",
			type="Key",
			width=290
		},
		[6]={
			list=false,
			name="IsVaalGem",
			refTo="",
			type="Bool",
			width=70
		},
		[7]={
			list=false,
			name="MinionGlobalSkillLevelStat",
			refTo="Stats",
			type="Key",
			width=150
		},
		[8]={
			list=false,
			name="IsSupport",
			refTo="",
			type="Bool",
			width=70
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[14]={
			list=false,
			name="Awakened",
			refTo="SkillGems",
			type="Key",
			width=70
		},
		[15]={
			list=false,
			name="GemColour",
			refTo="",
			type="Int",
			width=100
		},
		[16]={
			list=false,
			name="MinLevelReq",
			refTo="",
			type="Int",
			width=70
		},
		[17]={
			list=false,
			name="GemLevelProgression",
			refTo="ItemExperienceTypes",
			type="Key",
			width=200
		},
		[18]={
			list=true,
			name="MtxSlotType",
			refTo="MtxSlotTypes: [MicrotransactionSkillGemEffectSlotTypes]",
			type="Key",
			width=70
		},
		[19]={
			list=true,
			name="",
			refTo="",
			type="ShortKey",
			width=70
		},
		[20]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[21]={
			list=true,
			name="GemEffects",
			refTo="GemEffects",
			type="Key",
			width=150
		},
		[22]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[23]={
			list=false,
			name="TutorialVideo",
			refTo="",
			type="String",
			width=100
		},
		[24]={
			list=false,
			name="HoverImage",
			refTo="",
			type="String",
			width=100
		},
		[25]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=70
		},
		[26]={
			list=false,
			name="Tier",
			refTo="",
			type="Int",
			width=70
		},
		[27]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[28]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=70
		},
		[29]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[30]={
			list=true,
			name="SearchTerms",
			refTo="SkillGemSearchTerms",
			type="Key",
			width=220
		}
	},
	skillgemsearchterms={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		}
	},
	skillgemsforuniquestat={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="Int",
			width=50
		},
		[2]={
			list=true,
			name="SkillGem",
			refTo="Skillgems",
			type="Key",
			width=300
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		}
	},
	skillgemsupports={
		[1]={
			list=false,
			name="ActiveGem",
			refTo="SkillGems",
			type="Key",
			width=350
		},
		[2]={
			list=true,
			name="SuggestedSupport",
			refTo="SkillGems",
			type="Key",
			width=500
		}
	},
	skillminevariations={
	},
	skillmorphdisplay={
	},
	skillmorphdisplayoverlaycondition={
	},
	skillmorphdisplayoverlaystyle={
	},
	skillsurgeeffects={
	},
	skilltotemvariations={
		[1]={
			list=false,
			name="SkillTotem",
			refTo="",
			type="Int",
			width=60
		},
		[2]={
			list=false,
			name="Variation",
			refTo="",
			type="Int",
			width=60
		},
		[3]={
			list=false,
			name="MonsterVariety",
			refTo="MonsterVarieties",
			type="Key",
			width=390
		}
	},
	skilltrapvariations={
	},
	skillweaponeffects={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=190
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	socketaudioevents={
	},
	socketnotches={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=370
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=390
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=390
		}
	},
	soulcores={
		[1]={
			list=false,
			name="BaseItemTypes",
			refTo="BaseItemTypes",
			type="Key",
			width=350
		},
		[2]={
			list=true,
			name="StatsKeysWeapon",
			refTo="Stats",
			type="Key",
			width=550
		},
		[3]={
			list=true,
			name="StatsValuesWeapon",
			refTo="",
			type="Int",
			width=150
		},
		[4]={
			list=true,
			name="StatsKeysArmour",
			refTo="Stats",
			type="Key",
			width=550
		},
		[5]={
			list=true,
			name="StatsValuesArmour",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=false,
			name="Rank",
			refTo="",
			type="Int",
			width=150
		},
		[7]={
			list=true,
			name="StatsKeysCaster",
			refTo="Stats",
			type="Key",
			width=350
		},
		[8]={
			list=true,
			name="StatsValuesCaster",
			refTo="",
			type="Int",
			width=150
		},
		[9]={
			list=true,
			name="StatsKeysAttributes",
			refTo="Stats",
			type="Key",
			width=150
		},
		[10]={
			list=true,
			name="StatsValuesAttributes",
			refTo="",
			type="Int",
			width=150
		}
	},
	soulcoresperclass={
		[1]={
			list=false,
			name="BaseItemType",
			refTo="BaseItemTypes",
			type="Key",
			width=300
		},
		[2]={
			list=false,
			name="ItemClass",
			refTo="ItemClasses",
			type="Key",
			width=150
		},
		[3]={
			list=true,
			name="Stats",
			refTo="Stats",
			type="Key",
			width=800
		},
		[4]={
			list=true,
			name="StatsValues",
			refTo="",
			type="Int",
			width=150
		}
	},
	soundeffects={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=480
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=360
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		}
	},
	spawnadditionalchestsorclusters={
	},
	spawnobject={
	},
	specialrooms={
	},
	specialtiles={
	},
	spectreoverrides={
		[1]={
			list=false,
			name="Monster",
			refTo="MonsterVarieties",
			type="Key",
			width=500
		},
		[2]={
			list=false,
			name="Spectre",
			refTo="MonsterVarieties",
			type="Key",
			width=500
		},
		[3]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=150
		}
	},
	stampchoice={
	},
	stampfamily={
	},
	startingpassiveskills={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=true,
			name="PassiveSkills",
			refTo="PassiveSkills",
			type="Key",
			width=300
		}
	},
	stashid={
	},
	stashtabaffinities={
	},
	stashtype={
	},
	statconvertaltattackcontainer={
	},
	statdescriptionfunctions={
		[1]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=280
		},
		[2]={
			list=false,
			name="Stat",
			refTo="",
			type="String",
			width=310
		}
	},
	statistictrackingmicrotransactions={
	},
	statistictrackingmicrotransactionsstatistics={
	},
	stats={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=310
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[3]={
			list=false,
			name="Local",
			refTo="",
			type="Bool",
			width=50
		},
		[4]={
			list=false,
			name="WeaponLocal",
			refTo="",
			type="Bool",
			width=80
		},
		[5]={
			list=false,
			name="Semantic",
			refTo="StatSemantics",
			type="Enum",
			width=70
		},
		[6]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=300
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[8]={
			list=false,
			name="Virtual",
			refTo="",
			type="Bool",
			width=50
		},
		[9]={
			list=false,
			name="Main Hand Stat",
			refTo="Stats",
			type="ShortKey",
			width=350
		},
		[10]={
			list=false,
			name="Off Hand Stat",
			refTo="Stats",
			type="ShortKey",
			width=350
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[12]={
			list=false,
			name="Hash",
			refTo="",
			type="UInt",
			width=80
		},
		[13]={
			list=true,
			name="Skills",
			refTo="",
			type="String",
			width=150
		},
		[14]={
			list=false,
			name="PassiveCategory",
			refTo="PassiveSkillStatCategories",
			type="Key",
			width=110
		},
		[15]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[16]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=50
		},
		[17]={
			list=false,
			name="IsScalable",
			refTo="",
			type="Bool",
			width=70
		},
		[18]={
			list=true,
			name="ContextFlags",
			refTo="VirtualStatContextFlags",
			type="Key",
			width=270
		},
		[19]={
			list=true,
			name="DotFlag",
			refTo="VirtualStatContextFlags",
			type="Key",
			width=150
		},
		[20]={
			list=false,
			name="WeaponHandCheck",
			refTo="",
			type="Bool",
			width=150
		}
	},
	statsaffectinggeneration={
		[1]={
			list=false,
			name="",
			refTo="Stats",
			type="Key",
			width=440
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	statsets={
	},
	statsfromskillstats={
		[1]={
			list=false,
			name="SkillCondition",
			refTo="Stats",
			type="Key",
			width=280
		},
		[2]={
			list=false,
			name="GrantedFlag",
			refTo="Stats",
			type="Key",
			width=270
		},
		[3]={
			list=false,
			name="FlagValue",
			refTo="",
			type="Bool",
			width=150
		}
	},
	statvisuals={
	},
	strdexintmissionextrarequirement={
	},
	strdexintmissions={
	},
	strongboxes={
	},
	strongboxpacks={
		[1]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[3]={
			list=false,
			name="MonsterPackKey",
			refTo="MonsterPacks",
			type="Key",
			width=270
		}
	},
	suicideexplosion={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="Int",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="MonsterVarieties",
			type="Key",
			width=470
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		}
	},
	summonedspecificbarrels={
	},
	summonedspecificmonsters={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="Int",
			width=80
		},
		[2]={
			list=false,
			name="MonsterVarietiesKey",
			refTo="MonsterVarieties",
			type="Key",
			width=550
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=80
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=80
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=80
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[14]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[15]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=80
		},
		[16]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[17]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[18]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		}
	},
	summonedspecificmonstersondeath={
	},
	supershaperinfluence={
	},
	supporterpacksets={
	},
	supportgems={
		[1]={
			list=false,
			name="SkillGem",
			refTo="SkillGems",
			type="Key",
			width=340
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=50
		},
		[3]={
			list=false,
			name="DDS",
			refTo="",
			type="String",
			width=150
		}
	},
	surgecategory={
	},
	surgeeffectpackartvariations={
	},
	surgeeffects={
	},
	surgetypes={
	},
	synthesis={
	},
	synthesisareas={
	},
	synthesisareasize={
	},
	synthesisbonuses={
	},
	synthesisbrackets={
	},
	synthesisfragmentdialogue={
	},
	synthesisglobalmods={
	},
	synthesismonsterexperienceperlevel={
	},
	synthesisrewardcategories={
	},
	synthesisrewardtypes={
	},
	tablecharge={
		[1]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=80
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=250
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[7]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=320
		},
		[8]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=80
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[11]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[12]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[13]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[14]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[15]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=80
		},
		[16]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=80
		},
		[17]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[18]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[19]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[20]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[21]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[22]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[23]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[24]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[25]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[26]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[27]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[28]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		}
	},
	tablemonsterspawners={
	},
	tags={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Hash32",
			refTo="",
			type="UInt",
			width=150
		},
		[3]={
			list=false,
			name="DisplayText",
			refTo="",
			type="String",
			width=210
		},
		[4]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[5]={
			list=false,
			name="Text",
			refTo="",
			type="String",
			width=150
		}
	},
	talismanmonstermods={
	},
	talismanpacks={
	},
	talismans={
	},
	talkingpetaudioevents={
	},
	talkingpetnpcaudio={
	},
	talkingpets={
	},
	tencentautolootpetcurrencies={
	},
	tencentautolootpetcurrenciesexcludable={
	},
	terrainplugins={
	},
	threetoonerecipes={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="Mods",
			type="Key",
			width=260
		},
		[3]={
			list=false,
			name="",
			refTo="Mods",
			type="Key",
			width=280
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		}
	},
	tieredmicrotransactions={
	},
	tieredmicrotransactionsvisuals={
	},
	tips={
	},
	topologies={
	},
	tormentspirits={
	},
	totemdefendervarieties={
	},
	touchinteractiontype={
	},
	trademarketcategory={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=200
		},
		[2]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=300
		},
		[3]={
			list=false,
			name="StyleFlag",
			refTo="",
			type="Enum",
			width=150
		},
		[4]={
			list=false,
			name="Group",
			refTo="TradeMarketCategoryGroups",
			type="Key",
			width=200
		},
		[5]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=70
		},
		[7]={
			list=false,
			name="IsDisabled",
			refTo="",
			type="Bool",
			width=150
		}
	},
	trademarketcategorygroups={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=200
		},
		[2]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=200
		}
	},
	trademarketcategorylistallclass={
		[1]={
			list=false,
			name="TradeCategory",
			refTo="TradeMarketCategory",
			type="Key",
			width=200
		},
		[2]={
			list=false,
			name="ItemClass",
			refTo="ItemClasses",
			type="Key",
			width=200
		}
	},
	trademarketcategorystyleflag={
	},
	trademarketimplicitmoddisplay={
		[1]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=200
		}
	},
	trademarketindexitemas={
		[1]={
			list=false,
			name="Item",
			refTo="",
			type="Key",
			width=150
		},
		[2]={
			list=false,
			name="IndexAs",
			refTo="",
			type="Key",
			width=150
		}
	},
	traptools={
		[1]={
			list=false,
			name="BaseItemType",
			refTo="BaseItemTypes",
			type="Key",
			width=400
		},
		[2]={
			list=false,
			name="DetonationType",
			refTo="",
			type="Int",
			width=100
		},
		[3]={
			list=false,
			name="ThrowTime",
			refTo="",
			type="Int",
			width=100
		}
	},
	treasurehuntermissions={
	},
	triggerbeam={
	},
	triggeredaudioeventvolumeoverrides={
	},
	triggerspawners={
	},
	trythenewleagueversions={
	},
	tutorial={
	},
	typetags={
	},
	uitalkcategories={
	},
	uitalktext={
	},
	ultimatumencounters={
	},
	ultimatumencountertypes={
	},
	ultimatumencountertypesserver={
	},
	ultimatumitemisedrewards={
	},
	ultimatummapmodifiers={
	},
	ultimatummodifiers={
	},
	ultimatummodifiertypes={
	},
	ultimatummonsterpackfamily={
	},
	ultimatumrooms={
	},
	ultimatumtriallength={
	},
	ultimatumtrialmasteraudio={
	},
	ultimatumwagertypes={
	},
	uncutgemadditionaltiers={
		[1]={
			list=false,
			name="BaseItemType",
			refTo="BaseItemTypes",
			type="Key",
			width=270
		},
		[2]={
			list=false,
			name="PlayerLevel",
			refTo="",
			type="Int",
			width=100
		},
		[3]={
			list=false,
			name="GemLevel",
			refTo="",
			type="Int",
			width=100
		},
		[4]={
			list=false,
			name="Weight",
			refTo="",
			type="Int",
			width=100
		}
	},
	uncutgemtiers={
		[1]={
			list=false,
			name="BaseItemType",
			refTo="BaseItemTypes",
			type="Key",
			width=280
		},
		[2]={
			list=false,
			name="GemLevel",
			refTo="",
			type="Int",
			width=100
		},
		[3]={
			list=false,
			name="PlayerLevel",
			refTo="",
			type="Int",
			width=100
		}
	},
	uniquechests={
		[1]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=220
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		}
	},
	uniquefragments={
	},
	uniquejewellimits={
		[1]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	uniquemapinfo={
	},
	uniquemaps={
	},
	uniquesetnames={
	},
	uniquestashlayout={
		[1]={
			list=false,
			name="WordsKey",
			refTo="Words",
			type="Key",
			width=150
		},
		[2]={
			list=false,
			name="ItemVisualIdentity",
			refTo="ItemVisualIdentity",
			type="Key",
			width=150
		},
		[3]={
			list=false,
			name="UniqueStashTypes",
			refTo="UniqueStashTypes",
			type="Key",
			width=150
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=false,
			name="OverrideWidth",
			refTo="",
			type="Int",
			width=150
		},
		[7]={
			list=false,
			name="OverrideHeight",
			refTo="",
			type="Int",
			width=150
		},
		[8]={
			list=false,
			name="ShowIfEmptyChallengeLeague",
			refTo="",
			type="Bool",
			width=150
		},
		[9]={
			list=false,
			name="ShowIfEmptyStandard",
			refTo="",
			type="Bool",
			width=150
		},
		[10]={
			list=false,
			name="RenamedVersion",
			refTo="UniqueStashLayout",
			type="ShortKey",
			width=150
		},
		[11]={
			list=false,
			name="BaseVersion",
			refTo="UniqueStashLayout",
			type="ShortKey",
			width=150
		},
		[12]={
			list=false,
			name="IsAlternateArt",
			refTo="",
			type="Bool",
			width=150
		}
	},
	uniquestashtypes={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=100
		},
		[2]={
			list=false,
			name="Order",
			refTo="",
			type="Int",
			width=80
		},
		[3]={
			list=false,
			name="Width",
			refTo="",
			type="Int",
			width=80
		},
		[4]={
			list=false,
			name="Height",
			refTo="",
			type="Int",
			width=80
		},
		[5]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[6]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[7]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=100
		},
		[8]={
			list=false,
			name="StandardCount",
			refTo="",
			type="Int",
			width=120
		},
		[9]={
			list=false,
			name="Image",
			refTo="",
			type="String",
			width=150
		},
		[10]={
			list=false,
			name="ChallengeLeagueCount",
			refTo="",
			type="Int",
			width=120
		},
		[11]={
			list=false,
			name="Disabled",
			refTo="",
			type="Bool",
			width=80
		}
	},
	userinterfacemodecondition={
	},
	utilityflaskbuffs={
		[1]={
			list=false,
			name="BuffDefinitionsKey",
			refTo="BuffDefinitions",
			type="Key",
			width=250
		},
		[2]={
			list=true,
			name="StatValues",
			refTo="",
			type="Int",
			width=240
		},
		[3]={
			list=true,
			name="StatValues2",
			refTo="",
			type="Int",
			width=250
		},
		[4]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=250
		}
	},
	villageuniquedisenchantvalues={
		[1]={
			list=false,
			name="Unique",
			refTo="Words.Text",
			type="Key",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Float",
			width=150
		}
	},
	virtualstatcontextflags={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		}
	},
	visualpinproperties={
	},
	votestate={
	},
	votetype={
	},
	warbandsgraph={
	},
	warbandsmapgraph={
	},
	warbandspackmonsters={
	},
	warbandspacknumbers={
	},
	weaponarmourcommon={
	},
	weaponclasses={
		[1]={
			list=false,
			name="ItemClass",
			refTo="ItemClasses",
			type="Key",
			width=150
		},
		[2]={
			list=false,
			name="MaxRange",
			refTo="",
			type="Int",
			width=150
		},
		[3]={
			list=false,
			name="DisplayName",
			refTo="ClientStrings",
			type="Key",
			width=460
		}
	},
	weapondamagescaling={
	},
	weaponimpactsounddata={
	},
	weaponpassiveskills={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=230
		}
	},
	weaponpassiveskilltypes={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=250
		}
	},
	weaponpassivetreebalanceperitemlevel={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=230
		}
	},
	weaponpassivetreeuniquebasetypes={
		[1]={
			list=false,
			name="UniqueBase",
			refTo="BaseItemTypes",
			type="String",
			width=500
		}
	},
	weaponsoundtypes={
	},
	weapontypes={
		[1]={
			list=false,
			name="BaseItemType",
			refTo="BaseItemTypes",
			type="Key",
			width=490
		},
		[2]={
			list=false,
			name="CritChance",
			refTo="",
			type="Int",
			width=70
		},
		[3]={
			list=false,
			name="WeaponClass",
			refTo="WeaponClasses",
			type="Key",
			width=150
		},
		[4]={
			list=false,
			name="Speed",
			refTo="",
			type="Int",
			width=50
		},
		[5]={
			list=false,
			name="DamageMin",
			refTo="",
			type="Int",
			width=70
		},
		[6]={
			list=false,
			name="DamageMax",
			refTo="",
			type="Int",
			width=80
		},
		[7]={
			list=false,
			name="Range",
			refTo="",
			type="Int",
			width=50
		},
		[8]={
			list=false,
			name="ReloadTime",
			refTo="",
			type="Int",
			width=80
		}
	},
	wieldableclasses={
		[1]={
			list=false,
			name="ItemClass",
			refTo="ItemClasses",
			type="Key",
			width=150
		},
		[2]={
			list=false,
			name="2WeaponSlots",
			refTo="",
			type="Bool",
			width=150
		},
		[3]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[4]={
			list=false,
			name="Damage",
			refTo="Stats",
			type="Key",
			width=150
		},
		[5]={
			list=false,
			name="CritChance",
			refTo="Stats",
			type="Key",
			width=150
		},
		[6]={
			list=false,
			name="MinPhys",
			refTo="Stats",
			type="Key",
			width=150
		},
		[7]={
			list=false,
			name="MaxPhys",
			refTo="Stats",
			type="Key",
			width=150
		},
		[8]={
			list=false,
			name="MinFire",
			refTo="Stats",
			type="Key",
			width=150
		},
		[9]={
			list=false,
			name="MaxFire",
			refTo="Stats",
			type="Key",
			width=150
		},
		[10]={
			list=false,
			name="MinCold",
			refTo="Stats",
			type="Key",
			width=150
		},
		[11]={
			list=false,
			name="MaxCold",
			refTo="Stats",
			type="Key",
			width=150
		},
		[12]={
			list=false,
			name="MinLightning",
			refTo="Stats",
			type="Key",
			width=150
		},
		[13]={
			list=false,
			name="MaxLightning",
			refTo="Stats",
			type="Key",
			width=150
		},
		[14]={
			list=false,
			name="MinChaos",
			refTo="Stats",
			type="Key",
			width=150
		},
		[15]={
			list=false,
			name="MaxChaos",
			refTo="Stats",
			type="Key",
			width=150
		},
		[16]={
			list=false,
			name="CritMulti",
			refTo="Stats",
			type="Key",
			width=150
		},
		[17]={
			list=false,
			name="PhysDamage",
			refTo="Stats",
			type="Key",
			width=150
		},
		[18]={
			list=false,
			name="FireDamage",
			refTo="Stats",
			type="Key",
			width=150
		},
		[19]={
			list=false,
			name="ColdDamage",
			refTo="Stats",
			type="Key",
			width=150
		},
		[20]={
			list=false,
			name="Knockback",
			refTo="Stats",
			type="Key",
			width=150
		},
		[21]={
			list=false,
			name="CritKnockback",
			refTo="Stats",
			type="Key",
			width=150
		},
		[22]={
			list=false,
			name="Accuracy",
			refTo="Stats",
			type="Key",
			width=150
		},
		[23]={
			list=false,
			name="AccuracyInc",
			refTo="Stats",
			type="Key",
			width=150
		},
		[24]={
			list=false,
			name="AttackSpeed",
			refTo="Stats",
			type="Key",
			width=150
		},
		[25]={
			list=false,
			name="MeleeRange",
			refTo="Stats",
			type="Key",
			width=150
		},
		[26]={
			list=false,
			name="ElementalDamage",
			refTo="Stats",
			type="Key",
			width=150
		},
		[27]={
			list=false,
			name="Tag",
			refTo="Tags",
			type="Key",
			width=150
		},
		[28]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		}
	},
	windowcursors={
	},
	wordlists={
	},
	words={
		[1]={
			list=false,
			name="Wordlist",
			refTo="",
			type="Int",
			width=150
		},
		[2]={
			list=false,
			name="Text",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=true,
			name="SpawnWeight_Tags",
			refTo="Tags",
			type="Key",
			width=150
		},
		[4]={
			list=true,
			name="SpawnWeight_Values",
			refTo="",
			type="Int",
			width=150
		},
		[5]={
			list=false,
			name="HASH32",
			refTo="",
			type="Int",
			width=150
		},
		[6]={
			list=false,
			name="Text2",
			refTo="",
			type="String",
			width=150
		},
		[7]={
			list=false,
			name="",
			refTo="",
			type="String",
			width=150
		}
	},
	worldarealeaguechances={
	},
	worldareas={
		[1]={
			list=false,
			name="Id",
			refTo="",
			type="String",
			width=150
		},
		[2]={
			list=false,
			name="Name",
			refTo="",
			type="String",
			width=150
		},
		[3]={
			list=false,
			name="Act",
			refTo="",
			type="Int",
			width=40
		},
		[4]={
			list=false,
			name="IsTown",
			refTo="",
			type="Bool",
			width=60
		},
		[5]={
			list=false,
			name="HasWaypoint",
			refTo="",
			type="Bool",
			width=80
		},
		[6]={
			list=true,
			name="Connections",
			refTo="WorldAreas",
			type="ShortKey",
			width=150
		},
		[7]={
			list=false,
			name="AreaLevel",
			refTo="",
			type="Int",
			width=90
		},
		[8]={
			list=false,
			name="HASH16",
			refTo="",
			type="UInt16",
			width=150
		},
		[9]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=60
		},
		[10]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=60
		},
		[11]={
			list=true,
			name="LoadingScreens",
			refTo="",
			type="String",
			width=390
		},
		[12]={
			list=true,
			name="FlagOnEntered",
			refTo="QuestFlags",
			type="Key",
			width=150
		},
		[13]={
			list=true,
			name="RequiredFlags",
			refTo="QuestFlags",
			type="Key",
			width=150
		},
		[14]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=70
		},
		[15]={
			list=true,
			name="Topologies",
			refTo="",
			type="Key",
			width=150
		},
		[16]={
			list=false,
			name="ParentTown",
			refTo="WorldAreas",
			type="ShortKey",
			width=150
		},
		[17]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[18]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[19]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[20]={
			list=true,
			name="Bosses",
			refTo="MonsterVarieties",
			type="Key",
			width=250
		},
		[21]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[22]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[23]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[24]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[25]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[26]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[27]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=80
		},
		[28]={
			list=true,
			name="AreaMods",
			refTo="",
			type="Key",
			width=150
		},
		[29]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[30]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=80
		},
		[31]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[32]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[33]={
			list=false,
			name="IsHideout",
			refTo="",
			type="Bool",
			width=80
		},
		[34]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=80
		},
		[35]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[36]={
			list=true,
			name="Tags",
			refTo="Tags",
			type="Key",
			width=150
		},
		[37]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[38]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[39]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[40]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[41]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[42]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[43]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=150
		},
		[44]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[45]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[46]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[47]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[48]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[49]={
			list=false,
			name="Environment",
			refTo="Environments",
			type="Key",
			width=150
		},
		[50]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[51]={
			list=false,
			name="TerrainPlugins",
			refTo="TerrainPlugins",
			type="Key",
			width=150
		},
		[52]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[53]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[54]={
			list=false,
			name="IsEndGameArea",
			refTo="",
			type="Bool",
			width=110
		},
		[55]={
			list=false,
			name="",
			refTo="",
			type="Bool",
			width=100
		},
		[56]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=100
		},
		[57]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=100
		},
		[58]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=100
		},
		[59]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=100
		},
		[60]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=100
		},
		[61]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[62]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[63]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[64]={
			list=false,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[65]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[66]={
			list=true,
			name="",
			refTo="",
			type="Key",
			width=150
		},
		[67]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[68]={
			list=true,
			name="",
			refTo="",
			type="Int",
			width=150
		},
		[69]={
			list=true,
			name="QuestFlags",
			refTo="QuestFlags",
			type="Key",
			width=150
		},
		[70]={
			list=false,
			name="",
			refTo="",
			type="Int",
			width=90
		},
		[71]={
			list=false,
			name="Description",
			refTo="",
			type="String",
			width=200
		}
	},
	worldmaplegends={
	},
	worldpopupicontypes={
	},
	worldscreenmappindialogue={
	},
	zanalevels={
	},
	zanaquests={
	}
}