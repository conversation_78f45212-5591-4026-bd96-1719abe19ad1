-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} second duration between gaining Ghost Shrouds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[2]={
		[1]={
		},
		stats={
			[1]="display_skill_ghost_dance_grants_energy_shield_when_hit"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Maximum {0} Ghost Shrouds"
			}
		},
		stats={
			[1]="ghost_dance_max_stacks"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="Ghost Shrouds recover Energy Shield equal to {0:+d}% of your Evasion"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="When Hit, Consume a Ghost Shroud to\nrecover Energy Shield equal to {0}% of your Evasion"
			}
		},
		stats={
			[1]="skill_base_ghost_dance_grants_%_evasion_as_energy_shield_when_hit",
			[2]="quality_display_ghost_dance_is_gem"
		}
	},
	[5]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	[6]={
		[1]={
		},
		stats={
			[1]="virtual_ghost_dance_max_stacks"
		}
	},
	["base_skill_effect_duration"]=1,
	["display_skill_ghost_dance_grants_energy_shield_when_hit"]=2,
	["ghost_dance_max_stacks"]=3,
	parent="skill_stat_descriptions",
	["quality_display_ghost_dance_is_gem"]=4,
	["skill_base_ghost_dance_grants_%_evasion_as_energy_shield_when_hit"]=4,
	["skill_effect_duration"]=5,
	["virtual_ghost_dance_max_stacks"]=6
}