name: Behaviour bug report
description: Create a bug report to help us fix incorrect behaviour or logic in Path of Building for PoE2.
labels: ["bug:behaviour"]
body:
  - type: markdown
    attributes:
      value: |
        Thank you for reporting an issue with PoB-**PoE2**.
        [Issues with PoB-PoE1 go here.](https://github.com/PathOfBuildingCommunity/PathOfBuilding/issues).
        Please try to fill in as much of the form below as you're able to. Fields marked with an asterisk (*) are required.
  - type: checkboxes
    id: version
    attributes:
      label: Check version
      options:
        - label: I'm running the latest version of Path of Building and I've verified this by checking the [changelog](https://github.com/PathOfBuildingCommunity/PathOfBuilding-PoE2/blob/master/CHANGELOG.md)
          required: true
  - type: checkboxes
    id: duplicates
    attributes:
      label: Check for duplicates
      options:
        - label: I've checked for duplicate open **and closed** issues by using the search function of the [issue tracker](https://github.com/PathOfBuildingCommunity/PathOfBuilding-PoE2/issues?q=is%3Aissue)
          required: true
  - type: checkboxes
    id: validity
    attributes:
      label: Check for support
      options: 
        - label: I've checked that the behaviour is supposed to be supported. If it isn't please open a feature request instead (Red text is a feature request).
          required: true
  - type: dropdown
    id: platform
    attributes:
      label: What platform are you running Path of Building on?
      options: 
        - Windows
        - Linux - Wine
        - Linux - PoB Frontend
        - MacOS
      default: 0
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: What is the behaviour in-game?
      description: Please write a clear and concise description of what should happen.
      placeholder: E.g. In-game something behaves in a certain way. e.g. curse priority, auras disabled by mod, skills triggered in a certain way, etc.
    validations:
      required: true
  - type: textarea
    id: actual
    attributes:
      label: What is the behaviour in Path of Building?
      description: Please write a clear and concise description of what actually happens.
      placeholder: E.g. In Path of Building something behaves incorrectly e.g. incorrect curse priority, auras not disabled by mod, skill not triggered correctly, etc.
    validations:
      required: true
  - type: textarea
    id: steps
    attributes:
      label: How to reproduce the issue
      description: Please provide step-by-step instructions.
      placeholder: |
        E.g.
        1. Add [...] skill.
        2. Switch to the calcs tab.
        3. See incorrect [...] applied.
    validations:
      required: false
  - type: textarea
    id: build_code
    attributes:
      label: PoB for PoE2 build code
      description: Always provide a build code that exhibits the bug you want to report, even if it is not specific to a particular build. This helps us greatly to reproduce bugs faster.
      placeholder: This can be either a code copied from the "Import/Export Build" tab or a link to a PoB build.
      render: shell
    validations:
      required: true
  - type: textarea
    id: context
    attributes:
      label: Screenshots
      description: Always provide screenshots of the problematic behaviours if possible. GIFs or short videos are also okay, if the bug cannot be displayed in a still image.
      placeholder: In this text area, you can attach files/images (copy paste) directly, or link to them if they're hosted elsewhere instead.
    validations:
      required: false
