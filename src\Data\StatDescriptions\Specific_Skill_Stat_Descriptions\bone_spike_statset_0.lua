-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="base_secondary_skill_effect_duration"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Can fire up to {0} Projectiles"
			}
		},
		stats={
			[1]="base_number_of_allowed_bone_storm_projectiles"
		}
	},
	[3]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Debuff duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Debuff duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[4]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	[5]={
		[1]={
		},
		stats={
			[1]="total_number_of_projectiles_to_fire"
		}
	},
	["base_number_of_allowed_bone_storm_projectiles"]=2,
	["base_secondary_skill_effect_duration"]=1,
	["base_skill_effect_duration"]=3,
	parent="skill_stat_descriptions",
	["skill_effect_duration"]=4,
	["total_number_of_projectiles_to_fire"]=5
}