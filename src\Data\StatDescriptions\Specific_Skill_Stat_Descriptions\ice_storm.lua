-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="cold_damage_cannot_chill"
		}
	},
	[2]={
		stats={
			[1]="never_freeze"
		}
	},
	[3]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[4]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d} metre to impact radius"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d} metres to impact radius"
			},
			[3]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Impact radius is {0} metre"
			},
			[4]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Impact radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius",
			[2]="quality_display_active_skill_base_area_of_effect_radius_is_gem"
		}
	},
	[5]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Storm radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Storm radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_secondary_area_of_effect_radius"
		}
	},
	[6]={
		[1]={
		},
		stats={
			[1]="active_skill_secondary_area_of_effect_radius"
		}
	},
	[7]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Storm duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Storm duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[8]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp",
					v=1
				},
				limit={
					[1]={
						[1]=1,
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="One impact every {0} seconds"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]=1,
						[2]="#"
					}
				},
				text="{1} Bolts"
			}
		},
		stats={
			[1]="fire_storm_fireball_delay_ms",
			[2]="vaal_firestorm_number_of_meteors"
		}
	},
	[9]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Improves {0} Bolt per Chill Consumed"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Improves {0} Bolts per Chill Consumed"
			}
		},
		stats={
			[1]="firestorm_improved_bolts_per_chill_consumed"
		}
	},
	[10]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Improves {0} Bolt per Power of enemies from\nwhich Freeze is Consumed"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Improves {0} Bolts per Power of enemies from\nwhich Freeze is Consumed"
			}
		},
		stats={
			[1]="firestorm_improved_bolts_per_monster_power_of_freeze_consumed"
		}
	},
	[11]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Limit {0} Icestorm"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Limit {0} Icestorms"
			}
		},
		stats={
			[1]="firestorm_max_number_of_storms"
		}
	},
	[12]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	[13]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Icestorm Limit@{0}"
			}
		},
		stats={
			[1]="virtual_firestorm_max_number_of_storms"
		}
	},
	["active_skill_area_of_effect_radius"]=3,
	["active_skill_base_area_of_effect_radius"]=4,
	["active_skill_base_secondary_area_of_effect_radius"]=5,
	["active_skill_secondary_area_of_effect_radius"]=6,
	["base_skill_effect_duration"]=7,
	["cold_damage_cannot_chill"]=1,
	["fire_storm_fireball_delay_ms"]=8,
	["firestorm_improved_bolts_per_chill_consumed"]=9,
	["firestorm_improved_bolts_per_monster_power_of_freeze_consumed"]=10,
	["firestorm_max_number_of_storms"]=11,
	["never_freeze"]=2,
	parent="skill_stat_descriptions",
	["quality_display_active_skill_base_area_of_effect_radius_is_gem"]=4,
	["skill_effect_duration"]=12,
	["vaal_firestorm_number_of_meteors"]=8,
	["virtual_firestorm_max_number_of_storms"]=13
}