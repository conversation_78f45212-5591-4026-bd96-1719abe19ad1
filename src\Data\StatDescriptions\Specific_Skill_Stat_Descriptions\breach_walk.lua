-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Flames of Chayula fade after {0} second if not picked up"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Flames of Chayula fade after {0} seconds if not picked up"
			}
		},
		stats={
			[1]="base_remnant_duration_ms"
		}
	},
	[2]={
		[1]={
		},
		stats={
			[1]="virtual_remnant_duration_ms"
		}
	},
	["base_remnant_duration_ms"]=1,
	parent="skill_stat_descriptions",
	["virtual_remnant_duration_ms"]=2
}