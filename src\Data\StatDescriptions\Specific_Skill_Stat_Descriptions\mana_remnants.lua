-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=99
					}
				},
				text="{0}% chance to spawn a Remnant on killing an enemy affected by an Elemental Ailment"
			},
			[2]={
				limit={
					[1]={
						[1]=100,
						[2]="#"
					}
				},
				text="Spawn a Remnant on killing an enemy affected by an Elemental Ailment"
			}
		},
		stats={
			[1]="mana_remnants_chance_to_spawn_orb_on_killing_enemy_with_elemental_ailment_%"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Spawn a Remnant on Critically Hitting target affected by an Elemental Ailment, no more than once per second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Spawn a Remnant on Critically Hitting a target affected by an Elemental Ailment, no more than once every {0} seconds"
			}
		},
		stats={
			[1]="mana_remnants_spawn_remnant_on_crit_vs_elemental_ailment_enemy_every_X_ms"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Each Remnant grants {0} Mana"
			}
		},
		stats={
			[1]="mana_remnants_mana_gain_per_globe"
		}
	},
	[4]={
		[1]={
		},
		stats={
			[1]="virtual_mana_remnants_mana_gain_per_globe"
		}
	},
	["mana_remnants_chance_to_spawn_orb_on_killing_enemy_with_elemental_ailment_%"]=1,
	["mana_remnants_mana_gain_per_globe"]=3,
	["mana_remnants_spawn_remnant_on_crit_vs_elemental_ailment_enemy_every_X_ms"]=2,
	parent="skill_stat_descriptions",
	["virtual_mana_remnants_mana_gain_per_globe"]=4
}