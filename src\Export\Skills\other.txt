-- Path of Building
--
-- Other active skills
-- Skill data (c) Grinding Gear Games
--
local skills, mod, flag, skill = ...

#from tree
#minionList An<PERSON><PERSON>SpiritTurtle AncestralSpiritHulk AncestralSpiritCaster AncestralSpiritWarhorn
#skill AncestralSpiritsPlayer
#set AncestralSpiritsPlayer
#flags spell minion
#mods
#skillEnd

#from tree
#skill BleedingConcoctionPlayer
#set BleedingConcoctionPlayer
#flags attack projectile unarmed area
statMap = {
	["flask_throw_bleed_effect_+%_final"] = {
		mod("AilmentMagnitude", "MORE", nil, 0, KeywordFlag.Bleed),
	},
},
#mods
#skillEnd

#from tree
#skill BloodBoilPlayer
#set BloodBoilPlayer
#flags
#mods
#skillEnd

#from item
#skill MetaCastOnCharmUsePlayer
#set MetaCastOnCharmUsePlayer
#flags
#mods
#set SupportMetaCastOnCharmUsePlayer
#flags
#mods
#skillEnd

#from item
#skill MeleeBowPlayer
#set MeleeBowPlayer
#flags attack projectile
#mods
#skillEnd

#from item
#skill MeleeCrossbowPlayer
#set MeleeCrossbowPlayer
#flags attack area projectile
#mods
#skillEnd

#from item
#skill UnloadAmmoPlayer
#set UnloadAmmoPlayer
#flags
#mods
#skillEnd

#from tree
#skill DemonFormPlayer
#set DemonFormPlayer
#flags
#mods
#skillEnd

#from tree
#skill ElementalExpressionTriggeredPlayer
#set ElementalExpressionTriggeredPlayer
#flags
#mods
#set ElementalExpressionFirePlayer
#flags spell area
#mods
#set ElementalExpressionColdPlayer
#flags spell projectile
#mods
#set ElementalExpressionLightningPlayer
#flags spell chaining
#mods
#skillEnd

#from tree
#skill ElementalStormPlayer
#set ElementalStormPlayer
#flags area duration
statMap = {
	["tornado_base_damage_interval_ms"] = {
		skill("hitTimeOverride", nil),
		div = 1000,
	},
},
#mods
#set ElementalStormFirePlayer
#flags spell area duration
statMap = {
	["tornado_base_damage_interval_ms"] = {
		skill("hitTimeOverride", nil),
		div = 1000,
	},
},
#mods
#set ElementalStormLightningPlayer
#flags spell area duration
statMap = {
	["tornado_base_damage_interval_ms"] = {
		skill("hitTimeOverride", nil),
		div = 1000,
	},
},
#mods
#set ElementalStormColdPlayer
#flags spell area duration
statMap = {
	["tornado_base_damage_interval_ms"] = {
		skill("hitTimeOverride", nil),
		div = 1000,
	},
},
#mods
#skillEnd

#from tree
#skill EncaseInJadePlayer
#set EncaseInJadePlayer
#flags duration
#mods
#skillEnd

#from tree
#skill MetaCastFireSpellOnHitPlayer
#set MetaCastFireSpellOnHitPlayer
#flags
#mods
#skillEnd

#from tree
#skill SupportMetaCastFireSpellOnHitPlayer
#set SupportMetaCastFireSpellOnHitPlayer
#flags
#mods
#skillEnd

#from tree
#skill ExplosiveConcoctionPlayer
#set ExplosiveConcoctionPlayer
#flags attack projectile duration unarmed area
statMap = {
	["flask_throw_fire_exposure_ms"] = {
		mod("FireExposureChance", "BASE", nil),
		value = 100,
	},
},
#mods
#skillEnd

#from tree
#skill FulminatingConcoctionPlayer
#set FulminatingConcoctionPlayer
#flags attack projectile duration unarmed area
statMap = {
	["flask_throw_lightning_exposure_ms"] = {
		mod("LightningExposureChance", "BASE", nil),
		value = 100,
	},
},
#mods
#skillEnd

#from tree
#skill AmazonTriggerElementalInfusionPlayer
#set AmazonTriggerColdInfusionPlayer
#flags attack area
#mods
#set AmazonTriggerFireInfusionPlayer
#flags attack area
#mods
#set AmazonTriggerLightningInfusionPlayer
#flags attack area
#mods
#skillEnd

#from tree
#skill IntoTheBreachPlayer
#set IntoTheBreachPlayer
#flags
#mods
#skillEnd

#from tree
#skill LifeRemnantsPlayer
#set LifeRemnantsPlayer
#flags
#mods
#skillEnd

#from item
#skill Melee1HMacePlayer
#set Melee1HMacePlayer
#flags attack area melee
#mods
#skillEnd

#from item
#skill Melee2HMacePlayer
#set Melee2HMacePlayer
#flags attack area melee
#mods
#skillEnd

#from item
#skill MeleeMaceMacePlayer
#set MeleeMaceMacePlayer
#flags attack area melee
#mods
#skillEnd

#from item
#skill ChaosSpearTriggerChaosInfusionPlayer
#set ChaosSpearTriggerChaosInfusionPlayer
#flags attack area
#mods
#skillEnd

#from tree
#minionList ManifestWeapon
#skill ManifestWeaponPlayer
minionHasItemSet = true,
minionUses = {
	["Weapon 1"] = true,
},
#set ManifestWeaponPlayer
#flags spell minion duration
#mods
#skillEnd

#from tree
#skill MeditatePlayer
#set MeditatePlayer
#flags
#mods
#skillEnd

#from tree
#skill ParryPlayer
#set ParryPlayer
#flags attack melee duration shieldAttack
statMap = {
	["base_parry_buff_damage_taken_+%_final_to_apply"] = {
		mod("DamageTaken", "MORE", nil, ModFlag.Attack, 0, { type = "GlobalEffect", effectType = "Debuff", effectName = "Parry" }, { type = "Condition", var = "ParryActive" }),
	},
},
#mods
#skillEnd

#from tree
#skill PoisonousConcoctionPlayer
#set PoisonousConcoctionPlayer
#flags attack projectile unarmed area
statMap = {
	["flask_throw_poison_effect_+%_final"] = {
		mod("AilmentMagnitude", "MORE", nil, 0, KeywordFlag.Poison),
	},
},
#mods
#skillEnd

#noGem
#skill MeleeUnarmedPlayer
#set MeleeUnarmedPlayer
#flags attack area melee
#mods
#skillEnd

#from item
#skill MeleeQuarterstaffPlayer
#set MeleeQuarterstaffPlayer
#flags attack melee area
#mods
#skillEnd

#from item
#skill ShieldBlockPlayer
#set ShieldBlockPlayer
#flags attack area shieldAttack melee
#mods
#skillEnd

#from tree
#skill RitualSacrificePlayer
#set RitualSacrificePlayer
#flags attack area
#mods
#skillEnd

#from tree
#skill ShatteringConcoctionPlayer
#set ShatteringConcoctionPlayer
#flags attack projectile duration unarmed area
statMap = {
	["flask_throw_cold_exposure_ms"] = {
		mod("ColdExposureChance", "BASE", nil),
		value = 100,
	},
},
#mods
#skillEnd

#from tree
#skill SorceryWardPlayer
#set SorceryWardPlayer
#flags
#mods
#skillEnd

#from item
#skill MeleeSpearOffHandPlayer
#set MeleeSpearOffHandPlayer
#flags attack melee area
#mods
#skillEnd

#from item
#skill MeleeSpearPlayer
#set MeleeSpearPlayer
#flags attack melee area
#mods
#skillEnd

#from item
#skill SpearThrowPlayer
#set SpearThrowPlayer
#flags attack projectile
#mods
#set SpearThrowFrenzyChargePlayer
#flags attack projectile area
#mods
#skillEnd

#from tree
#minionList SummonedHellhound
#skill SummonInfernalHoundPlayer
#set SummonInfernalHoundPlayer
#flags spell minion permanentMinion
#mods
#skillEnd

#from tree
#minionList
#skill SupportingFirePlayer
#set SupportingFirePlayer
#flags minion permanentMinion
#mods
#set CommandDeathFromAbovePlayer
#flags minion permanentMinion
#mods
#skillEnd

#from tree
#skill TemperWeaponPlayer
#set TemperWeaponPlayer
#flags
#mods
#skillEnd

#from tree
#skill TemperWeaponCombustionPlayer
#set TemperWeaponCombustionPlayer
#flags attack area
#mods
#skillEnd

#from tree
#skill TemporalRiftPlayer
#set TemporalRiftPlayer
#flags
#mods
#skillEnd

#from tree
#skill TimeFreezePlayer
#set TimeFreezePlayer
#flags area duration
#mods
#skillEnd

#from tree
#skill TimeSnapPlayer
#set TimeSnapPlayer
#flags
#mods
#skillEnd

#from tree
#skill UnboundAvatarPlayer
#set UnboundAvatarPlayer
#flags
#mods
#skillEnd

#from item
#skill UniqueBreachLightningBoltPlayer
#set UniqueBreachLightningBoltPlayer
#flags spell area
#mods
#skillEnd

#from item
#skill CrossbowRequiemAmmoPlayer
#set CrossbowRequiemAmmoPlayer
#flags
#mods
#skillEnd

#from item
#skill CrossbowRequiemPlayer
#set CrossbowRequiemPlayer
#flags projectile area
#mods
#skillEnd

#from item
#skill ExplodingPoisonToadPlayer
#set ExplodingPoisonToadPlayer
#flags area attack
#mods
#skillEnd

#from item
#skill PinnacleOfPowerPlayer
#set PinnacleOfPowerPlayer
#flags buff duration
#mods
#skillEnd

#from item
#skill ImpurityPlayer
#set ImpurityPlayer
#flags aura
statMap = {
	["base_skill_buff_chaos_damage_resistance_%_to_apply"] = {
		mod("ChaosResist", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Aura" }),
	},
},
#mods
#skillEnd

#from item
#skill HeartOfIcePlayer
#set HeartOfIcePlayer
#flags buff aura
#mods
#skillEnd

#from item
#skill IcestormPlayer
#set IcestormPlayer
#flags spell area duration
#mods
#skillEnd