-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0} maximum stages"
			}
		},
		stats={
			[1]="wind_dancer_maximum_number_of_stages"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Gain a stage every {0} seconds"
			}
		},
		stats={
			[1]="wind_dancer_stages_gained_every_x_ms"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Evasion Rating per stage\nOn being Hit by an Enemy, consume all\nstages to trigger Gale Force"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Evasion Rating per stage\nOn being Hit by an Enemy, consume all\nstages to trigger Gale Force"
			}
		},
		stats={
			[1]="wind_dancer_evasion_rating_+%_final_per_stage"
		}
	},
	parent="skill_stat_descriptions",
	["wind_dancer_evasion_rating_+%_final_per_stage"]=3,
	["wind_dancer_maximum_number_of_stages"]=1,
	["wind_dancer_stages_gained_every_x_ms"]=2
}