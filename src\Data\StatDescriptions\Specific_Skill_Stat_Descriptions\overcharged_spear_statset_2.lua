-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fires bolts at enemies within a {0} metre radius"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Limit {0} lodged spear"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Limit {0} lodged spears"
			}
		},
		stats={
			[1]="base_number_of_overcharged_spears_allowed"
		}
	},
	[4]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Lodged spear duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Lodged spear duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Bolts Chain to {0} enemy"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Bolts Chain to {0} enemies"
			}
		},
		stats={
			[1]="number_of_chains"
		}
	},
	[6]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Lodged spear limit@{0}"
			}
		},
		stats={
			[1]="number_of_overcharged_spears_allowed"
		}
	},
	[7]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Fires a bolt every second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fires a bolt every {0} seconds"
			}
		},
		stats={
			[1]="overcharged_spear_base_frequency_ms"
		}
	},
	[8]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	["active_skill_area_of_effect_radius"]=1,
	["active_skill_base_area_of_effect_radius"]=2,
	["base_number_of_overcharged_spears_allowed"]=3,
	["base_skill_effect_duration"]=4,
	["number_of_chains"]=5,
	["number_of_overcharged_spears_allowed"]=6,
	["overcharged_spear_base_frequency_ms"]=7,
	parent="skill_stat_descriptions",
	["skill_effect_duration"]=8
}