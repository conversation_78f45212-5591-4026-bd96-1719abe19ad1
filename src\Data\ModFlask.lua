-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	["FlaskChargesAddedIncreasePercent1"] = { type = "Suffix", affix = "of the Constant", "(23-30)% increased Charges gained", statOrder = { 1001 }, level = 1, group = "FlaskIncreasedChargesAdded", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3196823591, },
	["FlaskChargesAddedIncreasePercent2_"] = { type = "Suffix", affix = "of the Continuous", "(31-38)% increased Charges gained", statOrder = { 1001 }, level = 13, group = "FlaskIncreasedChargesAdded", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3196823591, },
	["FlaskChargesAddedIncreasePercent3_"] = { type = "Suffix", affix = "of the Endless", "(39-46)% increased Charges gained", statOrder = { 1001 }, level = 33, group = "FlaskIncreasedChargesAdded", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3196823591, },
	["FlaskChargesAddedIncreasePercent4_"] = { type = "Suffix", affix = "of the Bottomless", "(47-54)% increased Charges gained", statOrder = { 1001 }, level = 48, group = "FlaskIncreasedChargesAdded", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3196823591, },
	["FlaskChargesAddedIncreasePercent5__"] = { type = "Suffix", affix = "of the Perpetual", "(55-62)% increased Charges gained", statOrder = { 1001 }, level = 63, group = "FlaskIncreasedChargesAdded", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3196823591, },
	["FlaskChargesAddedIncreasePercent6"] = { type = "Suffix", affix = "of the Eternal", "(63-70)% increased Charges gained", statOrder = { 1001 }, level = 82, group = "FlaskIncreasedChargesAdded", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3196823591, },
	["FlaskExtraCharges1"] = { type = "Suffix", affix = "of the Wide", "(23-30)% increased Charges", statOrder = { 1004 }, level = 1, group = "FlaskIncreasedMaxCharges", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 1366840608, },
	["FlaskExtraCharges2__"] = { type = "Suffix", affix = "of the Copious", "(31-38)% increased Charges", statOrder = { 1004 }, level = 12, group = "FlaskIncreasedMaxCharges", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 1366840608, },
	["FlaskExtraCharges3_"] = { type = "Suffix", affix = "of the Plentiful", "(39-46)% increased Charges", statOrder = { 1004 }, level = 32, group = "FlaskIncreasedMaxCharges", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 1366840608, },
	["FlaskExtraCharges4__"] = { type = "Suffix", affix = "of the Bountiful", "(47-54)% increased Charges", statOrder = { 1004 }, level = 47, group = "FlaskIncreasedMaxCharges", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 1366840608, },
	["FlaskExtraCharges5"] = { type = "Suffix", affix = "of the Abundant", "(55-62)% increased Charges", statOrder = { 1004 }, level = 62, group = "FlaskIncreasedMaxCharges", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 1366840608, },
	["FlaskExtraCharges6"] = { type = "Suffix", affix = "of the Ample", "(63-70)% increased Charges", statOrder = { 1004 }, level = 81, group = "FlaskIncreasedMaxCharges", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 1366840608, },
	["FlaskChargesUsed1"] = { type = "Suffix", affix = "of the Apprentice", "(15-17)% reduced Charges per use", statOrder = { 1002 }, level = 1, group = "FlaskChargesUsed", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3139816101, },
	["FlaskChargesUsed2"] = { type = "Suffix", affix = "of the Practitioner", "(18-20)% reduced Charges per use", statOrder = { 1002 }, level = 14, group = "FlaskChargesUsed", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3139816101, },
	["FlaskChargesUsed3__"] = { type = "Suffix", affix = "of the Mixologist", "(21-23)% reduced Charges per use", statOrder = { 1002 }, level = 34, group = "FlaskChargesUsed", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3139816101, },
	["FlaskChargesUsed4__"] = { type = "Suffix", affix = "of the Distiller", "(24-26)% reduced Charges per use", statOrder = { 1002 }, level = 49, group = "FlaskChargesUsed", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3139816101, },
	["FlaskChargesUsed5"] = { type = "Suffix", affix = "of the Brewer", "(27-29)% reduced Charges per use", statOrder = { 1002 }, level = 64, group = "FlaskChargesUsed", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3139816101, },
	["FlaskChargesUsed6"] = { type = "Suffix", affix = "of the Chemist", "(30-32)% reduced Charges per use", statOrder = { 1002 }, level = 83, group = "FlaskChargesUsed", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3139816101, },
	["FlaskChanceRechargeOnKill1"] = { type = "Suffix", affix = "of the Medic", "(21-25)% Chance to gain a Charge when you Kill an Enemy", statOrder = { 1000 }, level = 8, group = "FlaskChanceRechargeOnKill", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 828533480, },
	["FlaskChanceRechargeOnKill2"] = { type = "Suffix", affix = "of the Doctor", "(26-30)% Chance to gain a Charge when you Kill an Enemy", statOrder = { 1000 }, level = 26, group = "FlaskChanceRechargeOnKill", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 828533480, },
	["FlaskChanceRechargeOnKill3"] = { type = "Suffix", affix = "of the Surgeon", "(31-35)% Chance to gain a Charge when you Kill an Enemy", statOrder = { 1000 }, level = 45, group = "FlaskChanceRechargeOnKill", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 828533480, },
	["FlaskFillChargesPerMinute1"] = { type = "Suffix", affix = "of the Foliage", "Gains 0.15 Charges per Second", statOrder = { 6985 }, level = 8, group = "FlaskGainChargePerMinute", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1873752457, },
	["FlaskFillChargesPerMinute2"] = { type = "Suffix", affix = "of the Verdant", "Gains 0.2 Charges per Second", statOrder = { 6985 }, level = 26, group = "FlaskGainChargePerMinute", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1873752457, },
	["FlaskFillChargesPerMinute3"] = { type = "Suffix", affix = "of the Sylvan", "Gains 0.25 Charges per Second", statOrder = { 6985 }, level = 45, group = "FlaskGainChargePerMinute", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = {  }, tradeHash = 1873752457, },
	["FlaskIncreasedRecoverySpeed1"] = { type = "Prefix", affix = "Dense", "(41-45)% increased Recovery rate", statOrder = { 913 }, level = 1, group = "FlaskIncreasedRecoverySpeed", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 173226756, },
	["FlaskIncreasedRecoverySpeed2"] = { type = "Prefix", affix = "Undiluted", "(46-50)% increased Recovery rate", statOrder = { 913 }, level = 15, group = "FlaskIncreasedRecoverySpeed", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 173226756, },
	["FlaskIncreasedRecoverySpeed3_"] = { type = "Prefix", affix = "Hearty", "(51-55)% increased Recovery rate", statOrder = { 913 }, level = 31, group = "FlaskIncreasedRecoverySpeed", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 173226756, },
	["FlaskIncreasedRecoverySpeed4"] = { type = "Prefix", affix = "Viscous", "(56-60)% increased Recovery rate", statOrder = { 913 }, level = 46, group = "FlaskIncreasedRecoverySpeed", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 173226756, },
	["FlaskIncreasedRecoverySpeed5"] = { type = "Prefix", affix = "Condensed", "(61-65)% increased Recovery rate", statOrder = { 913 }, level = 61, group = "FlaskIncreasedRecoverySpeed", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 173226756, },
	["FlaskIncreasedRecoverySpeed6"] = { type = "Prefix", affix = "Catalysed", "(66-70)% increased Recovery rate", statOrder = { 913 }, level = 81, group = "FlaskIncreasedRecoverySpeed", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 173226756, },
	["FlaskIncreasedRecoveryAmount1"] = { type = "Prefix", affix = "Opaque", "(41-45)% increased Amount Recovered", statOrder = { 905 }, level = 1, group = "FlaskIncreasedRecoveryAmount", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 700317374, },
	["FlaskIncreasedRecoveryAmount2_"] = { type = "Prefix", affix = "Compact", "(46-50)% increased Amount Recovered", statOrder = { 905 }, level = 11, group = "FlaskIncreasedRecoveryAmount", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 700317374, },
	["FlaskIncreasedRecoveryAmount3"] = { type = "Prefix", affix = "Full-bodied", "(51-55)% increased Amount Recovered", statOrder = { 905 }, level = 23, group = "FlaskIncreasedRecoveryAmount", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 700317374, },
	["FlaskIncreasedRecoveryAmount4"] = { type = "Prefix", affix = "Abundant", "(56-60)% increased Amount Recovered", statOrder = { 905 }, level = 34, group = "FlaskIncreasedRecoveryAmount", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 700317374, },
	["FlaskIncreasedRecoveryAmount5"] = { type = "Prefix", affix = "Substantial", "(61-65)% increased Amount Recovered", statOrder = { 905 }, level = 45, group = "FlaskIncreasedRecoveryAmount", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 700317374, },
	["FlaskIncreasedRecoveryAmount6"] = { type = "Prefix", affix = "Concentrated", "(66-70)% increased Amount Recovered", statOrder = { 905 }, level = 56, group = "FlaskIncreasedRecoveryAmount", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 700317374, },
	["FlaskIncreasedRecoveryAmount7"] = { type = "Prefix", affix = "Potent", "(71-75)% increased Amount Recovered", statOrder = { 905 }, level = 67, group = "FlaskIncreasedRecoveryAmount", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 700317374, },
	["FlaskIncreasedRecoveryAmount8"] = { type = "Prefix", affix = "Saturated", "(76-80)% increased Amount Recovered", statOrder = { 905 }, level = 83, group = "FlaskIncreasedRecoveryAmount", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 700317374, },
	["FlaskIncreasedRecoveryOnLowLife1"] = { type = "Prefix", affix = "Prudent", "(51-60)% more Recovery if used while on Low Life", statOrder = { 906 }, level = 2, group = "FlaskIncreasedRecoveryOnLowLife", weightKey = { "life_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 886931978, },
	["FlaskIncreasedRecoveryOnLowLife2_"] = { type = "Prefix", affix = "Prepared", "(61-70)% more Recovery if used while on Low Life", statOrder = { 906 }, level = 25, group = "FlaskIncreasedRecoveryOnLowLife", weightKey = { "life_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 886931978, },
	["FlaskIncreasedRecoveryOnLowLife3"] = { type = "Prefix", affix = "Wary", "(71-80)% more Recovery if used while on Low Life", statOrder = { 906 }, level = 44, group = "FlaskIncreasedRecoveryOnLowLife", weightKey = { "life_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 886931978, },
	["FlaskIncreasedRecoveryOnLowLife4"] = { type = "Prefix", affix = "Careful", "(81-90)% more Recovery if used while on Low Life", statOrder = { 906 }, level = 63, group = "FlaskIncreasedRecoveryOnLowLife", weightKey = { "life_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 886931978, },
	["FlaskIncreasedRecoveryOnLowLife5"] = { type = "Prefix", affix = "Cautious", "(91-100)% more Recovery if used while on Low Life", statOrder = { 906 }, level = 82, group = "FlaskIncreasedRecoveryOnLowLife", weightKey = { "life_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 886931978, },
	["FlaskIncreasedRecoveryOnLowMana1"] = { type = "Prefix", affix = "Sustained", "(51-60)% more Recovery if used while on Low Mana", statOrder = { 904 }, level = 2, group = "FlaskIncreasedRecoveryOnLowMana", weightKey = { "mana_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3276224428, },
	["FlaskIncreasedRecoveryOnLowMana2"] = { type = "Prefix", affix = "Tenacious", "(61-70)% more Recovery if used while on Low Mana", statOrder = { 904 }, level = 25, group = "FlaskIncreasedRecoveryOnLowMana", weightKey = { "mana_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3276224428, },
	["FlaskIncreasedRecoveryOnLowMana3"] = { type = "Prefix", affix = "Persistent", "(71-80)% more Recovery if used while on Low Mana", statOrder = { 904 }, level = 44, group = "FlaskIncreasedRecoveryOnLowMana", weightKey = { "mana_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3276224428, },
	["FlaskIncreasedRecoveryOnLowMana4"] = { type = "Prefix", affix = "Persevering", "(81-90)% more Recovery if used while on Low Mana", statOrder = { 904 }, level = 63, group = "FlaskIncreasedRecoveryOnLowMana", weightKey = { "mana_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3276224428, },
	["FlaskIncreasedRecoveryOnLowMana5"] = { type = "Prefix", affix = "Prolonged", "(91-100)% more Recovery if used while on Low Mana", statOrder = { 904 }, level = 82, group = "FlaskIncreasedRecoveryOnLowMana", weightKey = { "mana_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 3276224428, },
	["FlaskExtraLifeCostsMana1"] = { type = "Prefix", affix = "Impairing", "(61-68)% increased Life Recovered", "Removes 15% of Life Recovered from Mana when used", statOrder = { 908, 914 }, level = 13, group = "FlaskExtraLifeCostsMana", weightKey = { "life_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "life", "mana" }, tradeHash = 2639206668, },
	["FlaskExtraLifeCostsMana2"] = { type = "Prefix", affix = "Dizzying", "(69-76)% increased Life Recovered", "Removes 15% of Life Recovered from Mana when used", statOrder = { 908, 914 }, level = 30, group = "FlaskExtraLifeCostsMana", weightKey = { "life_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "life", "mana" }, tradeHash = 2639206668, },
	["FlaskExtraLifeCostsMana3"] = { type = "Prefix", affix = "Depleting", "(77-84)% increased Life Recovered", "Removes 15% of Life Recovered from Mana when used", statOrder = { 908, 914 }, level = 47, group = "FlaskExtraLifeCostsMana", weightKey = { "life_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "life", "mana" }, tradeHash = 2639206668, },
	["FlaskExtraLifeCostsMana4"] = { type = "Prefix", affix = "Vitiating", "(85-92)% increased Life Recovered", "Removes 15% of Life Recovered from Mana when used", statOrder = { 908, 914 }, level = 64, group = "FlaskExtraLifeCostsMana", weightKey = { "life_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "life", "mana" }, tradeHash = 2639206668, },
	["FlaskExtraLifeCostsMana5_"] = { type = "Prefix", affix = "Sapping", "(93-100)% increased Life Recovered", "Removes 15% of Life Recovered from Mana when used", statOrder = { 908, 914 }, level = 81, group = "FlaskExtraLifeCostsMana", weightKey = { "life_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "life", "mana" }, tradeHash = 2639206668, },
	["FlaskExtraManaCostsLife1"] = { type = "Prefix", affix = "Aged", "(61-68)% increased Mana Recovered", "Removes 15% of Mana Recovered from Life when used", statOrder = { 909, 915 }, level = 13, group = "FlaskExtraManaCostsLife", weightKey = { "mana_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "life", "mana" }, tradeHash = 1058254507, },
	["FlaskExtraManaCostsLife2"] = { type = "Prefix", affix = "Fermented", "(69-76)% increased Mana Recovered", "Removes 15% of Mana Recovered from Life when used", statOrder = { 909, 915 }, level = 30, group = "FlaskExtraManaCostsLife", weightKey = { "mana_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "life", "mana" }, tradeHash = 1058254507, },
	["FlaskExtraManaCostsLife3_"] = { type = "Prefix", affix = "Congealed", "(77-84)% increased Mana Recovered", "Removes 15% of Mana Recovered from Life when used", statOrder = { 909, 915 }, level = 47, group = "FlaskExtraManaCostsLife", weightKey = { "mana_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "life", "mana" }, tradeHash = 1058254507, },
	["FlaskExtraManaCostsLife4"] = { type = "Prefix", affix = "Turbid", "(85-92)% increased Mana Recovered", "Removes 15% of Mana Recovered from Life when used", statOrder = { 909, 915 }, level = 64, group = "FlaskExtraManaCostsLife", weightKey = { "mana_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "life", "mana" }, tradeHash = 1058254507, },
	["FlaskExtraManaCostsLife5_"] = { type = "Prefix", affix = "Caustic", "(93-100)% increased Mana Recovered", "Removes 15% of Mana Recovered from Life when used", statOrder = { 909, 915 }, level = 81, group = "FlaskExtraManaCostsLife", weightKey = { "mana_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "life", "mana" }, tradeHash = 1058254507, },
	["FlaskPartialInstantRecovery1"] = { type = "Prefix", affix = "Simmering", "(20-23)% of Recovery applied Instantly", statOrder = { 912 }, level = 3, group = "FlaskPartialInstantRecovery", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 2503377690, },
	["FlaskPartialInstantRecovery2"] = { type = "Prefix", affix = "Effervescent", "(24-27)% of Recovery applied Instantly", statOrder = { 912 }, level = 27, group = "FlaskPartialInstantRecovery", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 2503377690, },
	["FlaskPartialInstantRecovery3"] = { type = "Prefix", affix = "Bubbling", "(28-30)% of Recovery applied Instantly", statOrder = { 912 }, level = 46, group = "FlaskPartialInstantRecovery", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 2503377690, },
	["FlaskFullInstantRecovery1"] = { type = "Prefix", affix = "Seething", "50% reduced Amount Recovered", "Instant Recovery", statOrder = { 905, 911 }, level = 42, group = "FlaskFullInstantRecovery", weightKey = { "flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask" }, tradeHash = 4131977470, },
	["FlaskHealsMinions1"] = { type = "Prefix", affix = "Novice's", "Grants (51-56)% of Life Recovery to Minions", statOrder = { 910 }, level = 10, group = "FlaskHealsMinions", weightKey = { "life_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "life", "minion" }, tradeHash = 2416869319, },
	["FlaskHealsMinions2"] = { type = "Prefix", affix = "Acolyte's", "Grants (57-62)% of Life Recovery to Minions", statOrder = { 910 }, level = 28, group = "FlaskHealsMinions", weightKey = { "life_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "life", "minion" }, tradeHash = 2416869319, },
	["FlaskHealsMinions3"] = { type = "Prefix", affix = "Summoner's", "Grants (63-68)% of Life Recovery to Minions", statOrder = { 910 }, level = 46, group = "FlaskHealsMinions", weightKey = { "life_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "life", "minion" }, tradeHash = 2416869319, },
	["FlaskHealsMinions4____"] = { type = "Prefix", affix = "Conjurer's", "Grants (69-74)% of Life Recovery to Minions", statOrder = { 910 }, level = 64, group = "FlaskHealsMinions", weightKey = { "life_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "life", "minion" }, tradeHash = 2416869319, },
	["FlaskHealsMinions5"] = { type = "Prefix", affix = "Necromancer's", "Grants (75-80)% of Life Recovery to Minions", statOrder = { 910 }, level = 82, group = "FlaskHealsMinions", weightKey = { "life_flask", "default" }, weightVal = { 1, 0 }, modTags = { "flask", "resource", "life", "minion" }, tradeHash = 2416869319, },
	["FlaskCurseImmunity1"] = { type = "Suffix", affix = "of Warding", "Removes Curses on use", statOrder = { 658 }, level = 18, group = "FlaskCurseImmunity", weightKey = { }, weightVal = {  }, modTags = { "flask", "caster", "curse" }, tradeHash = 3895393544, },
	["FlaskBleedCorruptingBloodImmunity1"] = { type = "Suffix", affix = "of Sealing", "Grants Immunity to Bleeding for (6-8) seconds if used while Bleeding", "Grants Immunity to Corrupted Blood for (6-8) seconds if used while affected by Corrupted Blood", statOrder = { 661, 661.1 }, level = 8, group = "FlaskBleedCorruptingBloodImmunity", weightKey = { }, weightVal = {  }, modTags = { "flask" }, tradeHash = 182714578, },
	["FlaskShockImmunity1"] = { type = "Suffix", affix = "of Earthing", "Grants Immunity to Shock for (6-8) seconds if used while Shocked", statOrder = { 671 }, level = 6, group = "FlaskShockImmunity", weightKey = { }, weightVal = {  }, modTags = { "flask" }, tradeHash = 3854439683, },
	["FlaskChillFreezeImmunity1"] = { type = "Suffix", affix = "of Convection", "Grants Immunity to Chill for (6-8) seconds if used while Chilled", "Grants Immunity to Freeze for (6-8) seconds if used while Frozen", statOrder = { 663, 663.1 }, level = 4, group = "FlaskChillFreezeImmunity", weightKey = { }, weightVal = {  }, modTags = { "flask" }, tradeHash = 3869628136, },
	["FlaskIgniteImmunity1"] = { type = "Suffix", affix = "of Damping", "Grants Immunity to Ignite for (6-8) seconds if used while Ignited", "Removes all Burning when used", statOrder = { 665, 665.1 }, level = 6, group = "FlaskIgniteImmunity", weightKey = { }, weightVal = {  }, modTags = { "flask" }, tradeHash = 2361218755, },
	["FlaskPoisonImmunity1__"] = { type = "Suffix", affix = "of the Antitoxin", "Grants Immunity to Poison for (6-8) seconds if used while Poisoned", statOrder = { 669 }, level = 16, group = "FlaskPoisonImmunity", weightKey = { }, weightVal = {  }, modTags = { "flask" }, tradeHash = 542375676, },
	["FlaskPoisonImmunityDuringEffect"] = { type = "Suffix", affix = "of the Skunk", "(45-49)% less Duration", "Immunity to Poison during Effect", statOrder = { 626, 745 }, level = 16, group = "FlaskPoisonImmunityDuringEffect", weightKey = { }, weightVal = {  }, modTags = { "flask" }, tradeHash = 188046746, },
	["FlaskShockImmunityDuringEffect"] = { type = "Suffix", affix = "of the Conger", "(45-49)% less Duration", "Immunity to Shock during Effect", statOrder = { 626, 746 }, level = 6, group = "FlaskShockImmunityDuringEffect", weightKey = { }, weightVal = {  }, modTags = { "flask" }, tradeHash = 4195389357, },
	["FlaskFreezeAndChillImmunityDuringEffect"] = { type = "Suffix", affix = "of the Deer", "(45-49)% less Duration", "Immunity to Freeze and Chill during Effect", statOrder = { 626, 744 }, level = 4, group = "FlaskFreezeAndChillImmunityDuringEffect", weightKey = { }, weightVal = {  }, modTags = { "flask" }, tradeHash = 650731684, },
	["FlaskIgniteImmunityDuringEffect_"] = { type = "Suffix", affix = "of the Urchin", "(45-49)% less Duration", "Immunity to Ignite during Effect", "Removes Burning on use", statOrder = { 626, 676, 676.1 }, level = 6, group = "FlaskIgniteImmunityDuringEffect", weightKey = { }, weightVal = {  }, modTags = { "flask" }, tradeHash = 3238382707, },
	["FlaskBleedingAndCorruptedBloodImmunityDuringEffect_1"] = { type = "Suffix", affix = "of the Lizard", "(45-49)% less Duration", "Immunity to Bleeding and Corrupted Blood during Effect", statOrder = { 626, 742 }, level = 8, group = "FlaskBleedingAndCorruptedBloodImmunityDuringEffect", weightKey = { }, weightVal = {  }, modTags = { "flask" }, tradeHash = 3588165839, },
}