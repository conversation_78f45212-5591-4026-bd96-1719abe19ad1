--
-- Dexterity support gems
-- Skill data (c) Grinding Gear Games
--
local skills, mod, flag, skill = ...

#skill SupportFasterProjectilesPlayer
#set SupportFasterProjectilesPlayer
statMap = {
	["support_faster_projectiles_projectile_speed_+%_final"] = {
		mod("ProjectileSpeed", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportAdherePlayer
#set SupportAdherePlayer
statMap = {
	["support_sticky_grenade_damage_+%_final"] = {
		mod("Damage", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportAlignmentPlayer
#set SupportAlignmentPlayer
#mods
#skillEnd

#skill SupportAmmoConservationPlayer
#set SupportAmmoConservationPlayer
statMap = {
	["crossbow_attack_%_chance_to_not_consume_ammo"] = {
		mod("ChanceToNotConsumeAmmo", "BASE", nil),
	},
},
#mods
#skillEnd

#skill SupportBlindPlayer
#set SupportBlindPlayer
#mods
#skillEnd

#skill SupportBlindsidePlayer
#set SupportBlindsidePlayer
statMap = {
	["support_unseen_critical_damage_multiplier_+%_final_vs_blinded_enemies"] = {
		mod("CritMultiplier", "MORE", nil, 0, 0, { type = "ActorCondition", actor = "enemy", var = "Blinded" } ),
	},
	["support_unseen_critical_strike_chance_+%_final_vs_blinded_enemies"] = {
		mod("CritChance", "MORE", nil, 0, 0, { type = "ActorCondition", actor = "enemy", var = "Blinded" } ),
	},
},
#mods
#skillEnd

#skill SupportBloodInTheEyesPlayer
#set SupportBloodInTheEyesPlayer
#mods
#skillEnd

#skill SupportDazedBreakPlayer
#set SupportDazedBreakPlayer
#mods
#skillEnd

#skill SupportAdditionalAccuracyPlayer
#set SupportAdditionalAccuracyPlayer
statMap = {
	["support_additional_accurary_rating_+%_final"] = {
		mod("Accuracy", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportBurstingPlaguePlayer
#set SupportBurstingPlaguePlayer
#mods
#skillEnd
#skill PlagueBurstPlayer
#set PlagueBurstPlayer
#mods
#skillEnd

#skill SupportCadencePlayer
#set SupportCadencePlayer
#mods
#skillEnd

#skill SupportCaltropsPlayer
#set SupportCaltropsPlayer
#mods
#skillEnd

#skill TriggeredCaltropsPlayer
#set TriggeredCaltropsPlayer
#flags projectile duration area
#mods
#skillEnd

#skill SupportChainPlayer
#set SupportChainPlayer
statMap = {
	["support_chain_hit_damage_+%_final"] = {
		mod("Damage", "MORE", nil, ModFlag.Hit),
	},
},
#mods
#skillEnd

#skill SupportChargedShotsPlayer
#set SupportChargedShotsPlayer
#mods
#skillEnd

#skill SupportCharmBountyPlayer
#set SupportCharmBountyPlayer
#mods
#skillEnd

#skill SupportCloseCombatPlayer
#set SupportCloseCombatPlayer
statMap = {
	["support_close_combat_attack_damage_+%_final_from_distance"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "DistanceRamp", ramp = {{10,1},{35,0}} }),
	},
},
#mods
#skillEnd

#skill SupportComboFinisherPlayer
#set SupportComboFinisherPlayer
#mods
#skillEnd

#skill SupportCommiseratePlayer
#set SupportCommiseratePlayer
#mods
#skillEnd

#skill SupportMultiplePoisonPlayer
#set SupportMultiplePoisonPlayer
statMap = {
	["support_multi_poison_poison_duration_+%_final"] = {
		mod("EnemyPoisonDuration", "MORE", nil),
	},
	["number_of_additional_poison_stacks"] = {
		mod("PoisonStacks", "BASE", nil),
		flag("PoisonCanStack"),
	},
},
#mods
#skillEnd

#skill SupportChanceToShockPlayer
#set SupportChanceToShockPlayer
statMap = {
	["support_conduction_chance_to_shock_+%_final"] = {
		mod("EnemyShockChance", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportCorrosionPlayer
#set SupportCorrosionPlayer
#mods
#skillEnd

#skill SupportCrescendoPlayer
#set SupportCrescendoPlayer
#mods
#skillEnd

#skill SupportCullingStrikePlayer
#set SupportCullingStrikePlayer
statMap = {
	["support_culling_strike_vs_rare_or_unique_enemy"] = {
		mod("CullPercent", "MAX", nil, 0, 0, { type = "ActorCondition", actor = "enemy", var = "RareOrUnique" }),
		value = 10
	},
},
#mods
#skillEnd

#skill SupportCulminationPlayer
#set SupportCulminationPlayer
#mods
#skillEnd

#skill SupportDazzlePlayer
#set SupportDazzlePlayer
#mods
#skillEnd

#skill SupportDeadlyHeraldsPlayer
#set SupportDeadlyHeraldsPlayer
statMap = {
	["support_deadly_heralds_damage_+%_final"] = {
		mod("Damage", "MORE", nil),
	}
},
#mods
#skillEnd

#skill SupportDeadlyPoisonPlayer
#set SupportDeadlyPoisonPlayer
statMap = {
	["support_deadly_poison_hit_damage_+%_final"] = {
		mod("Damage", "MORE", nil, ModFlag.Hit),
	},
	["support_deadly_poison_poison_effect_+%_final"] = {
		mod("AilmentMagnitude", "MORE", nil, 0, KeywordFlag.Poison),
	},
},
#mods
#skillEnd

#skill SupportSlowerProjectilesPlayer
#set SupportSlowerProjectilesPlayer
statMap = {
	["support_slower_projectiles_projectile_speed_+%_final"] = {
		mod("ProjectileSpeed", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportDelayedGratificationPlayer
#set SupportDelayedGratificationPlayer
#mods
#skillEnd

#skill SupportDelayedReactionPlayer
#set SupportDelayedReactionPlayer
statMap = {
	["support_sunblast_hazard_hazard_damage_+%_final"] = {
		mod("Damage", "MORE", nil),
	},
	["support_sunblast_hazard_hazard_duration_+%_final"] = {
		mod("Duration", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportDeliberationPlayer
#set SupportDeliberationPlayer
statMap = {
	["support_deliberation_damage_+%_final"] = {
		mod("Damage", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportDazingPlayer
#set SupportDazingPlayer
#mods
#skillEnd

#skill SupportElectrocutePlayer
#set SupportElectrocutePlayer
#mods
#skillEnd

#skill SupportEncumberancePlayer
#set SupportEncumberancePlayer
#mods
#skillEnd

#skill SupportChanceToPoisonPlayer
#set SupportChanceToPoisonPlayer
#mods
#skillEnd

#skill SupportExcoriatePlayer
#set SupportExcoriatePlayer
statMap = {
	["support_elemental_assault_melee_damage_+%_final_per_elemental_ailment_on_target"] = {
		mod("Damage", "MORE", nil, ModFlag.Melee, 0, { type = "ActorCondition", actor = "enemy", var = "Ignited" }),
		mod("Damage", "MORE", nil, ModFlag.Melee, 0, { type = "ActorCondition", actor = "enemy", var = "Chilled" }),
		mod("Damage", "MORE", nil, ModFlag.Melee, 0, { type = "ActorCondition", actor = "enemy", var = "Frozen" }),
		mod("Damage", "MORE", nil, ModFlag.Melee, 0, { type = "ActorCondition", actor = "enemy", var = "Shocked" }),
		mod("Damage", "MORE", nil, ModFlag.Melee, 0, { type = "ActorCondition", actor = "enemy", var = "Electrocuted" }),
	},
},
#mods
#skillEnd

#skill SupportFerocityPlayer
#set SupportFerocityPlayer
statMap = {
	["skill_consume_frenzy_charge_to_gain_skill_speed_+%_final"] = {
		mod("Speed", "MORE", nil, 0, 0, { type = "MultiplierThreshold", var = "RemovableFrenzyCharge", threshold = 1 }),
	},
},
#mods
#skillEnd

#skill SupportFlowPlayer
#set SupportFlowPlayer
#mods
#skillEnd

#skill SupportForkPlayer
#set SupportForkPlayer
statMap = {
	["support_fork_forked_projectile_damage_+%_final"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "StatThreshold", stat = "ForkedCount", threshold = 1 }),
	},
},
#mods
#skillEnd

#skill SupportGambleshotPlayer
#set SupportGambleshotPlayer
statMap = {
	[ "support_gambleshot_projectile_damage_+%_final"] = {
		mod("Damage", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportHitAndRunPlayer
#set SupportHitAndRunPlayer
#mods
#skillEnd

#skill SupportImpalePlayer
#set SupportImpalePlayer
#mods
#skillEnd

#skill SupportCooldownReductionPlayer
#set SupportCooldownReductionPlayer
statMap = {
	["support_cooldown_reduction_cooldown_recovery_+%"] = {
		mod("CooldownRecovery", "INC", nil),
	},
},
#mods
#skillEnd

#skill SupportInhibitorPlayer
#set SupportInhibitorPlayer
statMap = {
	["support_inhibitor_damage_+%_final_per_charge_type"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "MultiplierThreshold", var = "FrenzyCharge", threshold = 1 }),
		mod("Damage", "MORE", nil, 0, 0, { type = "MultiplierThreshold", var = "PowerCharge", threshold = 1 }),
		mod("Damage", "MORE", nil, 0, 0, { type = "MultiplierThreshold", var = "EnduranceCharge", threshold = 1 }),
	},
},
#mods
#skillEnd

#skill SupportInnervatePlayer
#set SupportInnervatePlayer
statMap = {
	["support_innervate_buff_grant_%_added_lightning_attack_damage"] = {
		mod("DamageGainAsLightning", "BASE", nil, ModFlag.Attack, 0, { type = "Condition", var = "KilledShockedLast3Seconds" }, { type = "GlobalEffect", effectType = "Buff", effectName = "Innervate" }),
	},
	["support_innervate_base_buff_duration"] = {
		mod("Duration", "BASE", nil, 0, 0, { type = "Condition", var = "KilledShockedLast3Seconds" }, { type = "GlobalEffect", effectType = "Buff" }),
		div = 1000,
	},
},
#mods
#skillEnd

#skill SupportLastingShockPlayer
#set SupportLastingShockPlayer
statMap = {
	["support_lasting_shock_chance_to_shock_+%_final"] = {
		mod("EnemyShockChance", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportLeveragePlayer
#set SupportLeveragePlayer
#mods
#skillEnd

#skill SupportLifeFlaskPlayer
#set SupportLifeFlaskPlayer
#mods
#skillEnd

#skill SupportLifeOnCullPlayer
#set SupportLifeOnCullPlayer
#mods
#skillEnd

#skill SupportLightningExposurePlayer
#set SupportLightningExposurePlayer
#mods
#skillEnd

#skill SupportAddedLightningDamagePlayer
#set SupportAddedLightningDamagePlayer
statMap = {
	["support_cold_and_fire_damage_+%_final"] = {
		mod("ColdDamage", "MORE", nil),
		mod("FireDamage", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportLightningPenetrationPlayer
#set SupportLightningPenetrationPlayer
#mods
#skillEnd

#skill SupportLockdownPlayer
#set SupportLockdownPlayer
#mods
#skillEnd

#skill SupportFarCombatPlayer
#set SupportFarCombatPlayer
statMap = {
	["support_far_combat_attack_damage_+%_final_from_distance"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "DistanceRamp", ramp = {{35,0},{70,1}} }),
	},
},
#mods
#skillEnd

#skill SupportMaimPlayer
#set SupportMaimPlayer
#mods
#skillEnd

#skill SupportMaladyPlayer
#set SupportMaladyPlayer
#mods
#skillEnd

#skill SupportManaFlaskPlayer
#set SupportManaFlaskPlayer
#mods
#skillEnd

#skill SupportFasterAttackPlayer
#set SupportFasterAttackPlayer
statMap = {
	["support_faster_attacks_attack_speed_+%_final"] = {
		mod("Speed", "MORE", nil, ModFlag.Attack),
	},
},
#mods
#skillEnd

#skill SupportMobilityPlayer
#set SupportMobilityPlayer
#mods
#skillEnd

#skill SupportMomentumPlayer
#set SupportMomentumPlayer
statMap = {
	["support_momnetum_damage_+%_final_with_momentum"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "Condition", var = "Moved2m" }),
	},
},
#mods
#skillEnd

#skill SupportEmpoweredCullPlayer
#set SupportEmpoweredCullPlayer
statMap = {
	["support_empowered_culling_strike"] = {
		mod("ExtraEmpowerMod", "LIST", { mod = mod("CullPercent", "MAX", nil), unscalable = true }),
		value = 10,
	}
},
#mods
#skillEnd

#skill SupportNeuralOverloadPlayer
#set SupportNeuralOverloadPlayer
#mods
#skillEnd

#skill SupportNimbleReloadPlayer
#set SupportNimbleReloadPlayer
#mods
#skillEnd

#skill SupportOutmaneuverPlayer
#set SupportOutmaneuverPlayer
#mods
#skillEnd

#skill SupportIncreaseLimitPlayer
#set SupportIncreaseLimitPlayer
statMap = {
	["support_limit_skill_effect_duration_+%_final"] = {
		mod("Duration", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportOverchargePlayer
#set SupportOverchargePlayer
#mods
#skillEnd

#skill SupportOverextendPlayer
#set SupportOverextendPlayer
statMap = {
	["support_overextend_critical_strike_multiplier_+%_final"] = {
		mod("CritMultiplier", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportPayloadPlayer
#set SupportPayloadPlayer
#mods
#skillEnd

#skill SupportPerfectionPlayer
#set SupportPerfectionPlayer
#mods
#skillEnd

#skill SupportPerpetualChargePlayer
#set SupportPerpetualChargePlayer
#mods
#skillEnd

#skill SupportPiercePlayer
#set SupportPiercePlayer
#mods
#skillEnd

#skill SupportPinPlayer
#set SupportPinPlayer
#mods
#skillEnd

#skill SupportPracticedComboPlayer
#set SupportPracticedComboPlayer
#mods
#skillEnd

#skill SupportPrecisionPlayer
#set SupportPrecisionPlayer
statMap = {
	["support_precision_accuracy_rating_+%"] = {
		mod("Accuracy", "INC", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Precision" } ),
	},
},
#mods
#skillEnd

#skill SupportMultipleChargesPlayer
#set SupportMultipleChargesPlayer
#mods
#skillEnd

#skill SupportPunchThroughPlayer
#set SupportPunchThroughPlayer
#mods
#skillEnd

#skill SupportPursuitPlayer
#set SupportPursuitPlayer
statMap = {
	["support_advancing_assault_melee_damage_+%_final_if_projectile_attack_damage_hit_in_past_8_seconds"] = {
		mod("Damage", "MORE", nil, ModFlag.Melee, 0, { type = "Condition", var = "HitProjectileRecently" } ),
	},
	["support_advancing_assault_projectile_damage_+%_final"] = {
		mod("Damage", "MORE", nil, ModFlag.Projectile),
	},
},
#mods
#skillEnd

#skill SupportReachPlayer
#set SupportReachPlayer
statMap = {
	["support_reach_accuracy_within_2m_+%_final"] = {
		mod("Accuracy", "MORE", nil, 0, 0, { type = "MultiplierThreshold", var = "enemyDistance", threshold = 20, upper = true } ),
	},
	["support_reach_area_of_effect_+%_final"] = {
		mod("AreaOfEffect", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportRearmPlayer
#set SupportRearmPlayer
#mods
#skillEnd

#skill SupportRetortPlayer
#set SupportRetortPlayer
#mods
#skillEnd

#skill SupportRetreatPlayer
#set SupportRetreatPlayer
statMap = {
	["support_retreating_assault_projectile_damage_+%_final_if_melee_hit_in_past_8_seconds"] = {
		mod("Damage", "MORE", nil, ModFlag.Projectile, 0, { type = "Condition", var = "HitMeleeRecently" } ),
	},
	["support_retreating_assault_melee_damage_+%_final"] = {
		mod("Damage", "MORE", nil, ModFlag.Melee),
	},
},
#mods
#skillEnd

#skill SupportRicochetPlayer
#set SupportRicochetPlayer
#mods
#skillEnd

#skill SupportSalvoPlayer
#set SupportSalvoPlayer
#mods
#skillEnd

#skill SupportMultipleProjectilesPlayer
#set SupportMultipleProjectilesPlayer
statMap = {
	["support_multiple_damage_+%_final"] = {
		mod("Damage", "MORE", nil),
	},
	["support_multiple_attack_and_cast_speed_+%_final"] = {
		mod("Speed", "MORE", nil),
	},
},
#mods
#skillEnd

#skill SupportSecondWindPlayer
#set SupportSecondWindPlayer
#mods
#skillEnd

#skill SupportShockingLeapPlayer
#set SupportShockingLeapPlayer
#mods
#skillEnd

#skill SupportSingleOutPlayer
#set SupportSingleOutPlayer
#mods
#skillEnd

#skill SupportManaOnCullPlayer
#set SupportManaOnCullPlayer
#mods
#skillEnd

#skill SupportSpectralVolleyPlayer
#set SupportSpectralVolleyPlayer
#mods
#skillEnd

#skill SupportStormchainPlayer
#set SupportStormchainPlayer
#mods
#skillEnd

#skill SupportSwiftAfflictionPlayer
#set SupportSwiftAfflictionPlayer
statMap = {
	["support_rapid_decay_damage_over_time_+%_final"] = {
		mod("Damage", "MORE", nil, ModFlag.Dot),
	},
},
#mods
#skillEnd

#skill SupportTumultPlayer
#set SupportTumultPlayer
#mods
#skillEnd

#skill SupportTwofoldPlayer
#set SupportTwofoldPlayer
#mods
#skillEnd

#skill SupportUnerringPowerPlayer
#set SupportUnerringPowerPlayer
statMap = {
	["support_discount_skill_cost_+%_final_if_empowered"] = {
		mod("Cost", "MORE", nil, 0, 0, { type = "Condition", var = "Empowered" } )
	},
	["support_discount_accuracy_rating_+%_final_if_empowered"] = {
		mod("Accuracy", "MORE", nil, 0, 0, { type = "Condition", var = "Empowered" } )
	},
},
#mods
#skillEnd

#skill SupportUntouchablePlayer
#set SupportUntouchablePlayer
#mods
#skillEnd

#skill SupportVoltPlayer
#set SupportVoltPlayer
#mods
#skillEnd

#skill SupportWarmbloodedPlayer
#set SupportWarmbloodedPlayer
statMap = {
	["support_freeze_protection_spirit_cost_freeze_duration_on_self_+%_final"] = {
		mod("SelfFreezeDuration", "MORE", nil, 0, 0, { type = "GlobalEffect", effectType = "Buff", effectName = "Warm Blooded" }),
	},
},
#mods
#skillEnd

#skill SupportKnockbackWavePlayer
#set SupportKnockbackWavePlayer
#mods
#skillEnd
#skill KnockbackWavePlayer
#set KnockbackWavePlayer
#mods
#skillEnd

#skill SupportWindowOfOpportunityPlayer
#set SupportWindowOfOpportunityPlayer
statMap = {
	["support_window_of_opportunity_perfect_timing_damage_+%_final"] = {
		mod("Damage", "MORE", nil, 0, 0, { type = "Condition", var = "PerfectTiming" }),
	},
},
#mods
#skillEnd
