-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="action_speed_-%"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Nearby Allies deal {0}% increased Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Nearby Enemies deal {0}% reduced Damage"
			}
		},
		stats={
			[1]="damage_+%"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="You and nearby Allies have {0}% increased Movement Speed"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="You and nearby Allies have {0}% reduced Movement Speed"
			}
		},
		stats={
			[1]="base_movement_velocity_+%"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Nearby Enemies have {0:+d}% chance to be Ignited when Hit"
			}
		},
		stats={
			[1]="chance_to_be_ignited_%"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Nearby Enemies have {0:+d}% chance to be Frozen when Hit"
			}
		},
		stats={
			[1]="chance_to_be_frozen_%"
		}
	},
	[6]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Nearby Enemies have {0:+d}% chance to be Shocked when Hit"
			}
		},
		stats={
			[1]="chance_to_be_shocked_%"
		}
	},
	[7]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Nearby Enemies deal {0}% more Elemental Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Nearby Enemies deal {0}% less Elemental Damage"
			}
		},
		stats={
			[1]="inquisitor_aura_elemental_damage_+%_final"
		}
	},
	[8]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Nearby Enemies take {0}% increased Elemental Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Nearby Enemies take {0}% reduced Elemental Damage"
			}
		},
		stats={
			[1]="elemental_damage_taken_+%"
		}
	},
	[9]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Nearby Enemies have {0:+d}% to Cold Resistance"
			}
		},
		stats={
			[1]="base_cold_damage_resistance_%"
		}
	},
	[10]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Nearby Enemies have {0:+d}% to Chaos Resistance"
			}
		},
		stats={
			[1]="base_chaos_damage_resistance_%"
		}
	},
	[11]={
		[1]={
			[1]={
				[1]={
					k="per_minute_to_per_second_2dp",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="You and nearby Allies Regenerate {0}% of maximum Life per second"
			}
		},
		stats={
			[1]="life_regeneration_rate_per_minute_%"
		}
	},
	[12]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Nearby Enemies have {0}% increased Life Regeneration rate"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Nearby Enemies have {0}% reduced Life Regeneration rate"
			}
		},
		stats={
			[1]="life_regeneration_rate_+%"
		}
	},
	[13]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Nearby Chilled Enemies deal {0}% increased Damage with Hits"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Nearby Chilled Enemies deal {0}% reduced Damage with Hits"
			}
		},
		stats={
			[1]="hit_damage_+%_while_chilled"
		}
	},
	[14]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Nearby Hindered Enemies deal {0}% increased Damage over Time"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Nearby Hindered Enemies deal {0}% reduced Damage over Time"
			}
		},
		stats={
			[1]="damage_over_time_+%_while_hindered"
		}
	},
	[15]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="You and nearby Allies deal {0} to {1} added Physical Damage for\neach Impale on Enemy"
			}
		},
		stats={
			[1]="minimum_added_physical_damage_per_impaled_on_enemy",
			[2]="maximum_added_physical_damage_per_impaled_on_enemy"
		}
	},
	[16]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Nearby Enemies have {0}% increased Critical Damage Bonus"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Nearby Enemies have {0}% reduced Critical Damage Bonus"
			}
		},
		stats={
			[1]="base_critical_strike_multiplier_+"
		}
	},
	[17]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Nearby Allies have {0}% increased Attack, Cast and Movement Speed"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Nearby Allies have {0}% reduced Attack, Cast and Movement Speed"
			}
		},
		stats={
			[1]="attack_cast_movement_speed_+%"
		}
	},
	[18]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Nearby Allies have {0}% increased Area of Effect"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Nearby Allies have {0}% reduced Area of Effect"
			}
		},
		stats={
			[1]="base_skill_area_of_effect_+%"
		}
	},
	[19]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]=99
					}
				},
				text="Nearby Allies have {0}% chance to Intimidate Enemies for 4 seconds on Hit"
			},
			[2]={
				limit={
					[1]={
						[1]=100,
						[2]="#"
					}
				},
				text="Nearby Allies Intimidate Enemies for 4 seconds on Hit"
			}
		},
		stats={
			[1]="chance_to_intimidate_on_hit_%"
		}
	},
	[20]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]=99
					}
				},
				text="Nearby Allies have {0}% chance to Unnerve Enemies for 4 seconds on Hit"
			},
			[2]={
				limit={
					[1]={
						[1]=100,
						[2]="#"
					}
				},
				text="Nearby Allies Unnerve Enemies for 4 seconds on Hit"
			}
		},
		stats={
			[1]="chance_to_unnerve_on_hit_%"
		}
	},
	[21]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="You and nearby Allies have Tailwind"
			}
		},
		stats={
			[1]="has_tailwind"
		}
	},
	[22]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Nearby Enemies have {0}% increased Cooldown Recovery Rate"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Nearby Enemies have {0}% reduced Cooldown Recovery Rate"
			}
		},
		stats={
			[1]="base_cooldown_speed_+%"
		}
	},
	[23]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Nearby Enemies are Blinded"
			}
		},
		stats={
			[1]="blinded"
		}
	},
	[24]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Nearby Enemies cannot deal Critical Hits"
			}
		},
		stats={
			[1]="global_cannot_crit"
		}
	},
	["action_speed_-%"]=1,
	["attack_cast_movement_speed_+%"]=17,
	["base_chaos_damage_resistance_%"]=10,
	["base_cold_damage_resistance_%"]=9,
	["base_cooldown_speed_+%"]=22,
	["base_critical_strike_multiplier_+"]=16,
	["base_movement_velocity_+%"]=3,
	["base_skill_area_of_effect_+%"]=18,
	blinded=23,
	["chance_to_be_frozen_%"]=5,
	["chance_to_be_ignited_%"]=4,
	["chance_to_be_shocked_%"]=6,
	["chance_to_intimidate_on_hit_%"]=19,
	["chance_to_unnerve_on_hit_%"]=20,
	["damage_+%"]=2,
	["damage_over_time_+%_while_hindered"]=14,
	["elemental_damage_taken_+%"]=8,
	["global_cannot_crit"]=24,
	["has_tailwind"]=21,
	["hit_damage_+%_while_chilled"]=13,
	["inquisitor_aura_elemental_damage_+%_final"]=7,
	["life_regeneration_rate_+%"]=12,
	["life_regeneration_rate_per_minute_%"]=11,
	["maximum_added_physical_damage_per_impaled_on_enemy"]=15,
	["minimum_added_physical_damage_per_impaled_on_enemy"]=15,
	parent="passive_skill_stat_descriptions"
}