-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Chills enemies in your Presence as though dealing {0} to {1} Cold damage"
			}
		},
		stats={
			[1]="spell_minimum_base_cold_damage",
			[2]="spell_maximum_base_cold_damage"
		}
	},
	parent="skill_stat_descriptions",
	["spell_maximum_base_cold_damage"]=1,
	["spell_minimum_base_cold_damage"]=1
}