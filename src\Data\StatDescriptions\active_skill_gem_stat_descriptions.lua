-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} Base Off Hand Physical Damage"
			}
		},
		stats={
			[1]="off_hand_weapon_minimum_physical_damage",
			[2]="off_hand_weapon_maximum_physical_damage"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} Added Fire Damage per 15 Armour on Shield"
			}
		},
		stats={
			[1]="off_hand_minimum_added_fire_damage_per_15_shield_armour",
			[2]="off_hand_maximum_added_fire_damage_per_15_shield_armour"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} Added Physical Damage per 15 Armour or Evasion Rating on Shield"
			}
		},
		stats={
			[1]="off_hand_minimum_added_physical_damage_per_15_shield_armour_and_evasion_rating",
			[2]="off_hand_maximum_added_physical_damage_per_15_shield_armour_and_evasion_rating"
		}
	},
	[4]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Base Off Hand Attack time is {0} seconds"
			}
		},
		stats={
			[1]="off_hand_base_weapon_attack_duration_ms"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Deals {0} to {1} Physical Damage"
			}
		},
		stats={
			[1]="global_minimum_added_physical_damage",
			[2]="global_maximum_added_physical_damage"
		}
	},
	[6]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Deals {0} to {1} Fire Damage"
			}
		},
		stats={
			[1]="global_minimum_added_fire_damage",
			[2]="global_maximum_added_fire_damage"
		}
	},
	[7]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Deals {0} to {1} Cold Damage"
			}
		},
		stats={
			[1]="global_minimum_added_cold_damage",
			[2]="global_maximum_added_cold_damage"
		}
	},
	[8]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Deals {0} to {1} Lightning Damage"
			}
		},
		stats={
			[1]="global_minimum_added_lightning_damage",
			[2]="global_maximum_added_lightning_damage"
		}
	},
	[9]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Deals {0} to {1} Chaos Damage"
			}
		},
		stats={
			[1]="global_minimum_added_chaos_damage",
			[2]="global_maximum_added_chaos_damage"
		}
	},
	[10]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Deals {0}% more Attack Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Deals {0}% less Attack Damage"
			}
		},
		stats={
			[1]="active_skill_attack_damage_+%_final"
		}
	},
	[11]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Attack Speed"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Attack Speed"
			}
		},
		stats={
			[1]="supplementary_stat_container_attack_speed_+%_final"
		}
	},
	[12]={
		[1]={
			[1]={
				[1]={
					k="divide_by_one_hundred",
					v=1
				},
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Deals {0}% more Attack Damage"
			},
			[2]={
				[1]={
					k="divide_by_one_hundred_and_negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Deals {0}% less Attack Damage"
			}
		},
		stats={
			[1]="active_skill_attack_damage_final_permyriad"
		}
	},
	[13]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Deals {0}% more Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Deals {0}% less Damage"
			}
		},
		stats={
			[1]="active_skill_damage_+%_final"
		}
	},
	[14]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Minions deal {0}% more Damage"
			}
		},
		stats={
			[1]="active_skill_minion_damage_+%_final"
		}
	},
	[15]={
		[1]={
			[1]={
				[1]={
					k="multiplicative_damage_modifier",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Minions deal {0}% of Physical Damage"
			}
		},
		stats={
			[1]="active_skill_minion_physical_damage_+%_final"
		}
	},
	[16]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Deals {0}% more Physical Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Deals {0}% less Physical Damage"
			}
		},
		stats={
			[1]="active_skill_physical_damage_+%_final"
		}
	},
	[17]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Physical Damage per Frenzy Charge"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Physical Damage per Frenzy Charge"
			}
		},
		stats={
			[1]="physical_damage_+%_per_frenzy_charge"
		}
	},
	[18]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Limit {0} Raised Zombie"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Limit {0} Raised Zombies"
			}
		},
		stats={
			[1]="base_number_of_zombies_allowed"
		}
	},
	[19]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Maximum {0} Raised Spectre"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Maximum {0} Raised Spectres"
			}
		},
		stats={
			[1]="base_number_of_spectres_allowed"
		}
	},
	[20]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Maximum {0} Summoned Skeleton"
			},
			[2]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Maximum {0} Summoned Skeletons"
			}
		},
		stats={
			[1]="base_number_of_skeletons_allowed"
		}
	},
	[21]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Limit {0} Summoned Raging Spirit"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Limit {0} Summoned Raging Spirits"
			}
		},
		stats={
			[1]="base_number_of_raging_spirits_allowed"
		}
	},
	[22]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Area of Effect"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Area of Effect"
			}
		},
		stats={
			[1]="base_aura_area_of_effect_+%"
		}
	},
	[23]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Aura Magnitudes"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Aura Magnitudes"
			}
		},
		stats={
			[1]="aura_effect_+%"
		}
	},
	[24]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="During initial Duration, can be hit by your Projectiles up to {0} times"
			}
		},
		stats={
			[1]="tornado_maximum_number_of_hits"
		}
	},
	[25]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Additional {0} seconds Base Duration per extra corpse Consumed"
			}
		},
		stats={
			[1]="offering_skill_effect_duration_per_corpse"
		}
	},
	[26]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0:+d} seconds to Buff Duration per Endurance Charge removed"
			}
		},
		stats={
			[1]="base_buff_duration_ms_+_per_removable_endurance_charge"
		}
	},
	[27]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Buff and Debuff Duration"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Buff and Debuff Duration"
			}
		},
		stats={
			[1]="buff_duration_+%"
		}
	},
	[28]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Skill Effect Duration"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Skill Effect Duration"
			}
		},
		stats={
			[1]="skill_effect_duration_+%"
		}
	},
	[29]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Minions have {0} base maximum Life"
			}
		},
		stats={
			[1]="display_minion_base_maximum_life"
		}
	},
	[30]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Area Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Area Damage"
			}
		},
		stats={
			[1]="active_skill_area_damage_+%_final"
		}
	},
	[31]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Spell Repeats once"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Spell Repeats {0} times"
			}
		},
		stats={
			[1]="base_spell_repeat_count"
		}
	},
	[32]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Maximum {0} Summoned Golem"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Maximum {0} Summoned Golems"
			}
		},
		stats={
			[1]="base_number_of_golems_allowed"
		}
	},
	[33]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0:+d} to maximum number of Summoned Ballista Totems"
			}
		},
		stats={
			[1]="attack_skills_additional_ballista_totems_allowed"
		}
	},
	[34]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Limit {0} Totem"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Limit {0} Totems"
			}
		},
		stats={
			[1]="base_number_of_totems_allowed"
		}
	},
	[35]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fires {0} Shard Projectiles in a Spiral when Projectile ends\nModifiers to number of Projectiles only apply\nto the final Spiral"
			}
		},
		stats={
			[1]="eye_of_winter_base_explosion_shards"
		}
	},
	[36]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Activates every {0} second while Attached"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Activates every {0} seconds while Attached"
			}
		},
		stats={
			[1]="base_sigil_repeat_frequency_ms"
		}
	},
	[37]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Activation frequency"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Activation frequency"
			}
		},
		stats={
			[1]="sigil_repeat_frequency_+%"
		}
	},
	[38]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Increases and Reductions to Cast Speed also apply to this Skill's Activation frequency"
			}
		},
		stats={
			[1]="additive_cast_speed_modifiers_apply_to_sigil_repeat_frequency"
		}
	},
	[39]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="This Attack Repeats {0} additional time"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="This Attack Repeats {0} additional times"
			}
		},
		stats={
			[1]="base_melee_attack_repeat_count"
		}
	},
	[40]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=0,
						[2]=0
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Can use Items requiring up to Level {1}"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Can use Items requiring up to Level {1}\nAnimated Weapons are transformed into random Unique Weapons with Level Requirement between {0} and {1}"
			}
		},
		stats={
			[1]="vaal_animate_weapon_minimum_level_requirement",
			[2]="animate_item_maximum_level_requirement"
		}
	},
	[41]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0:+d} to Accuracy Rating"
			}
		},
		stats={
			[1]="accuracy_rating"
		}
	},
	[42]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Accuracy Rating"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Accuracy Rating"
			}
		},
		stats={
			[1]="accuracy_rating_+%"
		}
	},
	[43]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Attack Damage with Two Handed Weapons"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Attack Damage with Two Handed Weapons"
			}
		},
		stats={
			[1]="active_skill_attack_damage_+%_final_with_two_handed_weapon"
		}
	},
	[44]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Attack Speed with Two Handed Weapons"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Attack Speed with Two Handed Weapons"
			}
		},
		stats={
			[1]="active_skill_attack_speed_+%_final_with_two_handed_weapon"
		}
	},
	[45]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Converts {0}% of Cold Damage to Fire Damage"
			}
		},
		stats={
			[1]="active_skill_base_cold_damage_%_to_convert_to_fire"
		}
	},
	[46]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Converts {0}% of Fire damage to Chaos damage"
			}
		},
		stats={
			[1]="active_skill_base_fire_damage_%_to_convert_to_chaos"
		}
	},
	[47]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Converts {0}% of Lightning damage to Chaos damage"
			}
		},
		stats={
			[1]="active_skill_base_lightning_damage_%_to_convert_to_chaos"
		}
	},
	[48]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Gains {0}% of Physical damage as Cold damage"
			}
		},
		stats={
			[1]="active_skill_base_physical_damage_%_to_gain_as_cold"
		}
	},
	[49]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Converts {0}% of Physical Damage to Chaos Damage"
			}
		},
		stats={
			[1]="active_skill_base_physical_damage_%_to_convert_to_chaos"
		}
	},
	[50]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="Converts {0}% of Physical Damage to Cold Damage"
			}
		},
		stats={
			[1]="active_skill_base_physical_damage_%_to_convert_to_cold",
			[2]="active_skill_display_suppress_physical_to_cold_damage_conversion"
		}
	},
	[51]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Converts {0}% of Physical Damage to Fire Damage"
			}
		},
		stats={
			[1]="active_skill_base_physical_damage_%_to_convert_to_fire"
		}
	},
	[52]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Converts {0}% of Physical Damage to Lightning Damage"
			}
		},
		stats={
			[1]="active_skill_base_physical_damage_%_to_convert_to_lightning"
		}
	},
	[53]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Requires {0} stored Corpse per Zombie raised"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Requires {0} stored Corpses per Zombie raised"
			}
		},
		stats={
			[1]="active_skill_base_stored_corpse_cost"
		}
	},
	[54]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Can have an additional Brand Attached to an Enemy"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Can have {0} additional Brands Attached to an Enemy"
			}
		},
		stats={
			[1]="active_skill_brands_allowed_on_enemy_+"
		}
	},
	[55]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=100,
						[2]=100
					}
				},
				text="Increases and Reductions to Cast Speed apply to this Skill's Attack Speed"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Increases and Reductions to Cast Speed apply to this Skill's Attack Speed at {0}% of their value"
			}
		},
		stats={
			[1]="active_skill_cast_speed_+%_applies_to_attack_speed_at_%_of_original_value"
		}
	},
	[56]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Cast Speed"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Cast Speed"
			}
		},
		stats={
			[1]="active_skill_cast_speed_+%_final"
		}
	},
	[57]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Critical Hit Chance"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Critical Hit Chance"
			}
		},
		stats={
			[1]="active_skill_critical_strike_chance_+%_final"
		}
	},
	[58]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Poison Duration"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				[2]={
					k="canonical_line",
					v=true
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Poison Duration"
			}
		},
		stats={
			[1]="active_skill_poison_duration_+%_final"
		}
	},
	[59]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{}% more Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{}% less Damage"
			}
		},
		stats={
			[1]="active_skill_quality_damage_+%_final"
		}
	},
	[60]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Skill Effect Duration"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Skill Effect Duration"
			}
		},
		stats={
			[1]="active_skill_quality_duration_+%_final"
		}
	},
	[61]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% chance to gain a Power Charge on Kill"
			}
		},
		stats={
			[1]="add_power_charge_on_kill_%_chance"
		}
	},
	[62]={
		[1]={
			[1]={
				[1]={
					k="divide_by_one_hundred",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0:+d}% to Critical Hit Chance"
			}
		},
		stats={
			[1]="additional_base_critical_strike_chance"
		}
	},
	[63]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Increases and Reductions to Mine Duration also apply to this Skill's Buff Duration"
			}
		},
		stats={
			[1]="additive_mine_duration_modifiers_apply_to_buff_effect_duration"
		}
	},
	[64]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Totem base attack time is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Totem base attack time is {0} seconds"
			}
		},
		stats={
			[1]="alt_attack_container_main_hand_base_weapon_attack_duration_ms"
		}
	},
	[65]={
		[1]={
			[1]={
				[1]={
					k="divide_by_one_hundred_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Totem base Critical Hit chance is {0}%"
			}
		},
		stats={
			[1]="alt_attack_container_main_hand_weapon_critical_strike_chance"
		}
	},
	[66]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} base Cold Damage"
			}
		},
		stats={
			[1]="alt_attack_container_main_hand_weapon_minimum_cold_damage",
			[2]="alt_attack_container_main_hand_weapon_maximum_cold_damage"
		}
	},
	[67]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} base Lightning Damage"
			}
		},
		stats={
			[1]="alt_attack_container_main_hand_weapon_minimum_lightning_damage",
			[2]="alt_attack_container_main_hand_weapon_maximum_lightning_damage"
		}
	},
	[68]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Totem uses its own weapon, dealing\n{0} to {1} base Physical damage"
			}
		},
		stats={
			[1]="alt_attack_container_main_hand_weapon_minimum_physical_damage",
			[2]="alt_attack_container_main_hand_weapon_maximum_physical_damage"
		}
	},
	[69]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Effect of Ancestor Totem Buff"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Effect of Ancestor Totem Buff"
			}
		},
		stats={
			[1]="ancestor_totem_buff_effect_+%"
		}
	},
	[70]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Activation range"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Activation range"
			}
		},
		stats={
			[1]="ancestor_totem_parent_activation_range_+%"
		}
	},
	[71]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Hit damage when Chaining"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Hit damage when Chaining"
			}
		},
		stats={
			[1]="arc_chain_hit_damage_+%_final"
		}
	},
	[72]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Area Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Area Damage"
			}
		},
		stats={
			[1]="area_damage_+%"
		}
	},
	[73]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Area of Effect while Dead"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Area of Effect while Dead"
			}
		},
		stats={
			[1]="area_of_effect_+%_while_dead"
		}
	},
	[74]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="Deals additional Fire Damage equal to {0:+d}% of Minion's maximum Life"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Deals additional Fire Damage equal to {0}% of Minion's maximum Life"
			}
		},
		stats={
			[1]="arsonist_destructive_link_%_of_life_as_fire_damage",
			[2]="quality_display_arsonist_is_gem"
		}
	},
	[75]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Attack and Cast Speed"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Attack and Cast Speed"
			}
		},
		stats={
			[1]="attack_and_cast_speed_+%"
		}
	},
	[76]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Attack and Cast Speed during Onslaught"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Attack and Cast Speed during Onslaught"
			}
		},
		stats={
			[1]="attack_and_cast_speed_+%_during_onslaught"
		}
	},
	[77]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} Added Chaos Damage"
			}
		},
		stats={
			[1]="attack_minimum_added_chaos_damage",
			[2]="attack_maximum_added_chaos_damage"
		}
	},
	[78]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} Added Cold Damage"
			}
		},
		stats={
			[1]="attack_minimum_added_cold_damage",
			[2]="attack_maximum_added_cold_damage"
		}
	},
	[79]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} Added Fire Damage"
			}
		},
		stats={
			[1]="attack_minimum_added_fire_damage",
			[2]="attack_maximum_added_fire_damage"
		}
	},
	[80]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} Added Lightning Damage"
			}
		},
		stats={
			[1]="attack_minimum_added_lightning_damage",
			[2]="attack_maximum_added_lightning_damage"
		}
	},
	[81]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} Added Attack Physical Damage"
			}
		},
		stats={
			[1]="attack_minimum_added_physical_damage",
			[2]="attack_maximum_added_physical_damage"
		}
	},
	[82]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Attack Speed"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Attack Speed"
			}
		},
		stats={
			[1]="attack_speed_+%"
		}
	},
	[83]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Attack Speed"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Attack Speed"
			}
		},
		stats={
			[1]="attack_speed_+%_granted_from_skill"
		}
	},
	[84]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=99
					}
				},
				text="{0}% chance to Blind Enemies"
			},
			[2]={
				limit={
					[1]={
						[1]=100,
						[2]="#"
					}
				},
				text="Blinds Enemies"
			}
		},
		stats={
			[1]="attacks_chance_to_blind_on_hit_%"
		}
	},
	[85]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=99
					}
				},
				text="{0}% chance to Ignore Stuns While Using this Skill"
			},
			[2]={
				limit={
					[1]={
						[1]=100,
						[2]="#"
					}
				},
				text="Ignore Stuns While Using this Skill"
			}
		},
		stats={
			[1]="avoid_interruption_while_using_this_skill_%"
		}
	},
	[86]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Hits enemies in range every {0} seconds"
			}
		},
		stats={
			[1]="ball_lightning_base_hit_frequency_ms"
		}
	},
	[87]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=-1,
						[2]=1
					}
				},
				text="{0:+d} Cooldown Use"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0:+d} Cooldown Uses"
			}
		},
		stats={
			[1]="base_added_cooldown_count"
		}
	},
	[88]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Aura Area of Effect"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Aura Area of Effect"
			}
		},
		stats={
			[1]="base_aura_area_of_effect_+%"
		}
	},
	[89]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Pulses every {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Pulses every {0} seconds"
			}
		},
		stats={
			[1]="base_blackhole_tick_rate_ms"
		}
	},
	[90]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp",
					v=1
				},
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Hits Enemies every {0} Seconds"
			}
		},
		stats={
			[1]="base_blade_vortex_hit_rate_ms"
		}
	},
	[91]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Bleeding Duration"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Bleeding Duration"
			}
		},
		stats={
			[1]="base_bleed_duration_+%"
		}
	},
	[92]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Cast Speed"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Cast Speed"
			}
		},
		stats={
			[1]="base_cast_speed_+%"
		}
	},
	[93]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]=1,
						[2]="#"
					}
				},
				text="Always Freezes Enemies on Hit"
			},
			[2]={
				limit={
					[1]={
						[1]=100,
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="Always Freeze"
			},
			[3]={
				limit={
					[1]={
						[1]=1,
						[2]=99
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0}% chance to Freeze"
			}
		},
		stats={
			[1]="base_chance_to_freeze_%",
			[2]="always_freeze"
		}
	},
	[94]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=99
					}
				},
				text="{0}% chance to Ignite"
			},
			[2]={
				limit={
					[1]={
						[1]=100,
						[2]="#"
					}
				},
				text="Always Ignite"
			}
		},
		stats={
			[1]="base_chance_to_ignite_%"
		}
	},
	[95]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=99
					}
				},
				text="{0}% chance to Shock"
			},
			[2]={
				limit={
					[1]={
						[1]=100,
						[2]="#"
					}
				},
				text="Always Shock"
			}
		},
		stats={
			[1]="base_chance_to_shock_%"
		}
	},
	[96]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Cost"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Cost"
			}
		},
		stats={
			[1]="base_cost_+%"
		}
	},
	[97]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Critical Damage Bonus"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Critical Damage Bonus"
			}
		},
		stats={
			[1]="base_critical_strike_multiplier_+"
		}
	},
	[98]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Curse Duration"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Curse Duration"
			}
		},
		stats={
			[1]="base_curse_duration_+%"
		}
	},
	[99]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Fires a beam every {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fires a beam every {0} seconds"
			}
		},
		stats={
			[1]="base_galvanic_field_beam_delay_ms"
		}
	},
	[100]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% chance to Knock Enemies Back on hit"
			}
		},
		stats={
			[1]="base_global_chance_to_knockback_%"
		}
	},
	[101]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Rarity of Items Dropped by Slain Enemies"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Rarity of Items Dropped by Slain Enemies"
			}
		},
		stats={
			[1]="base_killed_monster_dropped_item_rarity_+%"
		}
	},
	[102]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Knocks Back Enemies"
			}
		},
		stats={
			[1]="base_knockback_distance"
		}
	},
	[103]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Life Cost"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Life Cost"
			}
		},
		stats={
			[1]="base_life_cost_+%"
		}
	},
	[104]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Gain {0} Life per Enemy Hit"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Lose {0} Life per Enemy Hit"
			}
		},
		stats={
			[1]="base_life_gain_per_target"
		}
	},
	[105]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Life Reservation"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Life Reservation"
			}
		},
		stats={
			[1]="base_life_reservation_+%"
		}
	},
	[106]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% reduced Mana Cost"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% increased Mana Cost"
			}
		},
		stats={
			[1]="base_mana_cost_-%"
		}
	},
	[107]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Mana Reservation"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Mana Reservation"
			}
		},
		stats={
			[1]="base_mana_reservation_+%"
		}
	},
	[108]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Base Mine Detonation Time is {0} seconds"
			}
		},
		stats={
			[1]="base_mine_detonation_time_ms"
		}
	},
	[109]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Fires {0} Arrows"
			}
		},
		stats={
			[1]="base_number_of_arrows"
		}
	},
	[110]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Limit {0} Bone Offering Spike"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Limit {0} Bone Offering Spikes"
			}
		},
		stats={
			[1]="base_number_of_bone_offerings_allowed"
		}
	},
	[111]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Maximum {0} Summoned Sentinel of Purity"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Maximum {0} Summoned Sentinels of Purity"
			}
		},
		stats={
			[1]="base_number_of_champions_of_light_allowed"
		}
	},
	[112]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Limit {0} Pain Offering Spike"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Limit {0} Pain Offering Spikes"
			}
		},
		stats={
			[1]="base_number_of_pain_offerings_allowed"
		}
	},
	[113]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Limit {0} Soul Offering Spike"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Limit {0} Soul Offering Spikes"
			}
		},
		stats={
			[1]="base_number_of_power_offerings_allowed"
		}
	},
	[114]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Maximum {0} Summoned Holy Relic"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Maximum {0} Summoned Holy Relics"
			}
		},
		stats={
			[1]="base_number_of_relics_allowed"
		}
	},
	[115]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Limit {0} Bone Construct"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Limit {0} Bone Constructs"
			}
		},
		stats={
			[1]="base_number_of_skeletal_constructs_allowed"
		}
	},
	[116]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Poison Duration"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Poison Duration"
			}
		},
		stats={
			[1]="base_poison_duration_+%"
		}
	},
	[117]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Projectile Speed"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Projectile Speed"
			}
		},
		stats={
			[1]="base_projectile_speed_+%"
		}
	},
	[118]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Penetrates {0}% Cold Resistance"
			}
		},
		stats={
			[1]="base_reduce_enemy_cold_resistance_%"
		}
	},
	[119]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Penetrates {0}% Fire Resistance"
			}
		},
		stats={
			[1]="base_reduce_enemy_fire_resistance_%"
		}
	},
	[120]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Penetrates {0}% Lightning Resistance"
			}
		},
		stats={
			[1]="base_reduce_enemy_lightning_resistance_%"
		}
	},
	[121]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Reservation Efficiency"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Reservation Efficiency"
			}
		},
		stats={
			[1]="base_reservation_efficiency_+%"
		}
	},
	[122]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Reservation"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Reservation"
			}
		},
		stats={
			[1]="base_reservation_+%"
		}
	},
	[123]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Area of Effect"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Area of Effect"
			}
		},
		stats={
			[1]="base_skill_area_of_effect_+%"
		}
	},
	[124]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Stun Duration on Enemies"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Stun Duration on Enemies"
			}
		},
		stats={
			[1]="base_stun_duration_+%"
		}
	},
	[125]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Spend Life instead of Mana for Effects of this Skill"
			}
		},
		stats={
			[1]="base_use_life_in_place_of_mana"
		}
	},
	[126]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased rotation speed"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced rotation speed"
			}
		},
		stats={
			[1]="base_weapon_trap_rotation_speed_+%"
		}
	},
	[127]={
		[1]={
			[1]={
				[1]={
					k="divide_by_one_hundred_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Rotates {0} times"
			}
		},
		stats={
			[1]="base_weapon_trap_total_rotation_%"
		}
	},
	[128]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Lose {0} Rage per second"
			}
		},
		stats={
			[1]="berserk_base_rage_loss_per_second"
		}
	},
	[129]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Leaves a Lingering Blade in the ground for every Volley"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Leaves a Lingering Blade in the ground for every {0} Volleys"
			}
		},
		stats={
			[1]="bladefall_blade_left_in_ground_for_every_X_volleys"
		}
	},
	[130]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="1 Volley"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="{0} Volleys"
			}
		},
		stats={
			[1]="bladefall_number_of_volleys"
		}
	},
	[131]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Leaves {0}% more Lingering Blades in the ground if you don't Cast this Spell yourself"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Leaves {0}% fewer Lingering Blades in the ground if you don't Cast this Spell yourself"
			}
		},
		stats={
			[1]="blades_left_in_ground_+%_final_if_not_hand_cast"
		}
	},
	[132]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Reserves {0} Spirit per socketed Curse"
			}
		},
		stats={
			[1]="blasphemy_base_spirit_reservation_per_socketed_curse"
		}
	},
	[133]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Blinding duration"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Blinding duration"
			}
		},
		stats={
			[1]="blind_duration_+%"
		}
	},
	[134]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Creates {0} additional Spikes if you've changed Stance Recently"
			}
		},
		stats={
			[1]="blood_spears_additional_number_of_spears_if_changed_stance_recently"
		}
	},
	[135]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Creates {0} Spikes"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Creates {0} fewer Spikes"
			}
		},
		stats={
			[1]="blood_spears_base_number_of_spears"
		}
	},
	[136]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Damage while in Blood Stance"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Damage while in Blood Stance"
			}
		},
		stats={
			[1]="blood_spears_damage_+%_final_in_blood_stance"
		}
	},
	[137]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Debuffed enemies take {0} to {1} additional\nPhysical damage from next Attack"
			}
		},
		stats={
			[1]="bone_spear_minimum_added_attack_physical_damage_taken",
			[2]="bone_spear_maximum_added_attack_physical_damage_taken"
		}
	},
	[138]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Debuff can deal a maximum of {0} to {1} damage"
			}
		},
		stats={
			[1]="bone_spear_minimum_damage_threshold",
			[2]="bone_spear_maximum_damage_threshold"
		}
	},
	[139]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Burning Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Burning Damage"
			}
		},
		stats={
			[1]="burn_damage_+%"
		}
	},
	[140]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=99
					}
				},
				text="{0}% chance to also Poison a nearby Enemy when you inflict Poison"
			},
			[2]={
				limit={
					[1]={
						[1]=100,
						[2]="#"
					}
				},
				text="Poison a nearby Enemy when you inflict Poison"
			}
		},
		stats={
			[1]="chance_%_when_poison_to_also_poison_another_enemy"
		}
	},
	[141]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0}% chance to double Stun Duration"
			}
		},
		stats={
			[1]="chance_to_double_stun_duration_%"
		}
	},
	[142]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Projectiles have {0}% chance for an additional Projectile when Forking"
			}
		},
		stats={
			[1]="chance_to_fork_extra_projectile_%"
		}
	},
	[143]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=100,
						[2]="#"
					}
				},
				text="Melee Hits Fortify"
			},
			[2]={
				limit={
					[1]={
						[1]=1,
						[2]=99
					}
				},
				text="Melee Hits have {0}% chance to Fortify"
			}
		},
		stats={
			[1]="chance_to_fortify_on_melee_hit_+%"
		}
	},
	[144]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=99
					}
				},
				text="{0}% chance to gain a Frenzy Charge on Killing a Frozen Enemy"
			},
			[2]={
				limit={
					[1]={
						[1]=100,
						[2]="#"
					}
				},
				text="Gain a Frenzy Charge on Killing a Frozen Enemy"
			}
		},
		stats={
			[1]="chance_to_gain_frenzy_charge_on_killing_frozen_enemy_%"
		}
	},
	[145]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0}% chance when throwing Mines to throw up to 1 additional Mine"
			}
		},
		stats={
			[1]="chance_to_place_an_additional_mine_%"
		}
	},
	[146]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=99
					}
				},
				text="{0}% chance to Scorch"
			},
			[2]={
				limit={
					[1]={
						[1]=100,
						[2]="#"
					}
				},
				text="Always Scorch"
			}
		},
		stats={
			[1]="chance_to_scorch_%"
		}
	},
	[147]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Chaos Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Chaos Damage"
			}
		},
		stats={
			[1]="chaos_damage_+%"
		}
	},
	[148]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Chill Duration on Enemies"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Chill Duration on Enemies"
			}
		},
		stats={
			[1]="chill_duration_+%"
		}
	},
	[149]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Magnitude of Chills you inflict"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Magnitude of Chills you inflict"
			}
		},
		stats={
			[1]="chill_effect_+%"
		}
	},
	[150]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Mana Cost of your Skills while in Area"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Mana Cost of your Skills while in Area"
			}
		},
		stats={
			[1]="circle_of_power_skill_cost_mana_cost_+%"
		}
	},
	[151]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Magnitude of Cold Ailments you inflict"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Magnitude of Cold Ailments you inflict"
			}
		},
		stats={
			[1]="cold_ailment_effect_+%"
		}
	},
	[152]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Cold Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Cold Damage"
			}
		},
		stats={
			[1]="cold_damage_+%"
		}
	},
	[153]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0}% increased Effect of Consecrated Ground"
			}
		},
		stats={
			[1]="consecrated_ground_effect_+%"
		}
	},
	[154]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Consecrated Ground applies {0}% increased Damage taken to Enemies"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Consecrated Ground applies {0}% reduced Damage taken to Enemies"
			}
		},
		stats={
			[1]="consecrated_ground_enemy_damage_taken_+%"
		}
	},
	[155]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Consecrated Ground Area"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Consecrated Ground Area"
			}
		},
		stats={
			[1]="consecrated_ground_area_+%"
		}
	},
	[156]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% chance to Spread when affected Enemy is Hit"
			},
			[2]={
				limit={
					[1]={
						[1]=100,
						[2]="#"
					}
				},
				text="Spread when affected Enemy is Hit"
			}
		},
		stats={
			[1]="contagion_spread_on_hit_affected_enemy_%"
		}
	},
	[157]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Converted Enemies deal {0}% increased Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Converted Enemies deal {0}% reduced Damage"
			}
		},
		stats={
			[1]="conversation_trap_converted_enemy_damage_+%"
		}
	},
	[158]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Converted Enemies have {0}% chance to Taunt on Hit"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]=100,
						[2]="#"
					}
				},
				text="Converted Enemies Taunt on Hit"
			}
		},
		stats={
			[1]="conversion_trap_converted_enemies_chance_to_taunt_on_hit_%"
		}
	},
	[159]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Maximum of {0} Geysers at a time"
			}
		},
		stats={
			[1]="corpse_erruption_base_maximum_number_of_geyers"
		}
	},
	[160]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Fires Projectiles {0}% faster"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Fires Projectiles {0}% slower"
			}
		},
		stats={
			[1]="cremation_fires_projectiles_faster_+%_final"
		}
	},
	[161]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Magnitude of Damaging Ailments you inflict with Critical Hits"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Magnitude of Damaging Ailments you inflict with Critical Hits"
			}
		},
		stats={
			[1]="critical_hit_damaging_ailment_effect_+%"
		}
	},
	[162]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{:+d}% to Critical Damage Bonus per 100 Maximum Energy Shield on Shield"
			}
		},
		stats={
			[1]="critical_multiplier_+%_per_100_max_es_on_shield"
		}
	},
	[163]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Critical Hit Chance"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Critical Hit Chance"
			}
		},
		stats={
			[1]="critical_strike_chance_+%"
		}
	},
	[164]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Movement Speed while at maximum Stages"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Movement Speed while at maximum Stages"
			}
		},
		stats={
			[1]="cyclone_max_stages_movement_speed_+%"
		}
	},
	[165]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Damage over Time"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Damage over Time"
			}
		},
		stats={
			[1]="damage_over_time_+%"
		}
	},
	[166]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Damage"
			}
		},
		stats={
			[1]="damage_+%"
		}
	},
	[167]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Damage per Endurance Charge"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Damage per Endurance Charge"
			}
		},
		stats={
			[1]="damage_+%_per_endurance_charge"
		}
	},
	[168]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Damage per Frenzy Charge"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Damage per Frenzy Charge"
			}
		},
		stats={
			[1]="damage_+%_per_frenzy_charge"
		}
	},
	[169]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Damage per Power Charge"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Damage per Power Charge"
			}
		},
		stats={
			[1]="damage_+%_per_power_charge"
		}
	},
	[170]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Damage with Hits against Enemies that are on Full Life"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Damage with Hits against Enemies that are on Full Life"
			}
		},
		stats={
			[1]="damage_+%_vs_enemies_on_full_life"
		}
	},
	[171]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Damage with Hits per Freeze, Shock or Ignite on Enemy"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Damage with Hits per Freeze, Shock or Ignite on Enemy"
			}
		},
		stats={
			[1]="damage_+%_vs_enemies_per_freeze_shock_ignite"
		}
	},
	[172]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Damage with Hits against Frozen Enemies"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Damage with Hits against Frozen Enemies"
			}
		},
		stats={
			[1]="damage_+%_vs_frozen_enemies"
		}
	},
	[173]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Damage while on Full Energy Shield"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Damage while on Full Energy Shield"
			}
		},
		stats={
			[1]="damage_+%_on_full_energy_shield"
		}
	},
	[174]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Damage when on Full Life"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Damage when on Full Life"
			}
		},
		stats={
			[1]="damage_+%_when_on_full_life"
		}
	},
	[175]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Damage when on Low Life"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Damage when on Low Life"
			}
		},
		stats={
			[1]="damage_+%_when_on_low_life"
		}
	},
	[176]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Damage with Hits per Curse on Enemy"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Damage with Hits per Curse on Enemy"
			}
		},
		stats={
			[1]="damage_vs_cursed_enemies_per_enemy_curse_+%"
		}
	},
	[177]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Damage with Hits against Enemies that are on Low Life"
			}
		},
		stats={
			[1]="damage_vs_enemies_on_low_life_+%"
		}
	},
	[178]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_1dp",
					v=1
				},
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Grants Phasing for {0} seconds"
			}
		},
		stats={
			[1]="dash_grants_phasing_after_use_ms"
		}
	},
	[179]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="This Spell loses 1 Intensity every 0.25 seconds while moving, or immediately if you teleport"
			}
		},
		stats={
			[1]="display_base_intensity_loss"
		}
	},
	[180]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Can store up to 10 Corpses"
			}
		},
		stats={
			[1]="display_consume_corpse_storage_limit"
		}
	},
	[181]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Increases and Reductions to Cast Speed also apply to Projectile Frequency"
			}
		},
		stats={
			[1]="display_frost_fury_additive_cast_speed_modifiers_apply_to_fire_speed"
		}
	},
	[182]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Damages {0} nearby Enemies when you gain Stages"
			}
		},
		stats={
			[1]="divine_tempest_base_number_of_nearby_enemies_to_zap"
		}
	},
	[183]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0:+d}% to Damage over Time Multiplier"
			}
		},
		stats={
			[1]="dot_multiplier_+"
		}
	},
	[184]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Elemental Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Elemental Damage"
			}
		},
		stats={
			[1]="elemental_damage_+%"
		}
	},
	[185]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="Empowered Attacks Repeat {0:+d} time"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="Empowered Attacks Repeat {0:+d} times"
			},
			[3]={
				limit={
					[1]={
						[1]=0,
						[2]=0
					},
					[2]={
						[1]=1,
						[2]=1
					}
				},
				text="Empowered Attacks Repeat {1:+d} time per Frenzy Charge consumed"
			},
			[4]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					},
					[2]={
						[1]=1,
						[2]=1
					}
				},
				text="Empowered Attacks Repeat {1:+d} time per Frenzy Charge consumed"
			},
			[5]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					},
					[2]={
						[1]=1,
						[2]=1
					}
				},
				text="Empowered Attacks Repeat {0:+d} times, and a further {1:+d} time per Frenzy Charge consumed"
			},
			[6]={
				limit={
					[1]={
						[1]=0,
						[2]=0
					},
					[2]={
						[1]=2,
						[2]="#"
					}
				},
				text="Empowered Attacks Repeat {0:+d} time, and a further {1:+d} times per Frenzy Charge consumed"
			},
			[7]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					},
					[2]={
						[1]=2,
						[2]="#"
					}
				},
				text="Empowered Attacks Repeat {0:+d} time, and a further {1:+d} times per Frenzy Charge consumed"
			},
			[8]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					},
					[2]={
						[1]=2,
						[2]="#"
					}
				},
				text="Empowered Attacks Repeat {0:+d} times, and a further {1:+d} times per Frenzy Charge consumed"
			}
		},
		stats={
			[1]="empower_barrage_base_number_of_barrage_repeats",
			[2]="empower_barrage_number_of_barrage_repeats_per_frenzy_charge"
		}
	},
	[186]={
		[1]={
			[1]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="Repeats deal {0:+d}% more Damage"
			},
			[2]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Repeats deal {0}% less Damage"
			},
			[3]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Repeats deal {0}% more Damage"
			}
		},
		stats={
			[1]="empower_barrage_damage_-%_final_with_repeated_projectiles",
			[2]="quality_display_barrage_is_gem"
		}
	},
	[187]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Overwhelm {0}% Physical Damage Reduction"
			}
		},
		stats={
			[1]="enemy_phys_reduction_%_penalty_vs_hit"
		}
	},
	[188]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Leaves a Lingering Blade in the ground for every Projectile fired"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Leaves a Lingering Blade in the ground for every {0} Projectiles fired"
			}
		},
		stats={
			[1]="ethereal_knives_blade_left_in_ground_for_every_X_projectiles"
		}
	},
	[189]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Bleeding you inflict deals Damage {0}% faster"
			}
		},
		stats={
			[1]="faster_bleed_%"
		}
	},
	[190]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Ignites you inflict deal Damage {0}% faster"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Ignites you inflict deal Damage {0}% slower"
			}
		},
		stats={
			[1]="faster_burn_%"
		}
	},
	[191]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Poisons you inflict deal Damage {0}% faster"
			}
		},
		stats={
			[1]="faster_poison_%"
		}
	},
	[192]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Fire Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Fire Damage"
			}
		},
		stats={
			[1]="fire_damage_+%"
		}
	},
	[193]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased explosion Area of Effect"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced explosion Area of Effect"
			}
		},
		stats={
			[1]="firestorm_explosion_area_of_effect_+%"
		}
	},
	[194]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Linked target gains {0} to {1} Added Fire Damage"
			}
		},
		stats={
			[1]="flame_link_minimum_fire_damage",
			[2]="flame_link_maximum_fire_damage"
		}
	},
	[195]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Linked target gains Added Fire Damage equal to {0}% of your Maximum Life"
			}
		},
		stats={
			[1]="flame_link_added_fire_damage_from_life_%"
		}
	},
	[196]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Fortification Duration"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Fortification Duration"
			}
		},
		stats={
			[1]="fortify_duration_+%"
		}
	},
	[197]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Freeze Duration on Enemies"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Freeze Duration on Enemies"
			}
		},
		stats={
			[1]="freeze_duration_+%"
		}
	},
	[198]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Chill can Slow by up to {0}%"
			}
		},
		stats={
			[1]="freezing_bolt_chill_maximum_magnitude_override"
		}
	},
	[199]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Fires Beams with {0}% increased Frequency"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Fires Beams with {0}% reduced Frequency"
			}
		},
		stats={
			[1]="galvanic_field_beam_frequency_+%"
		}
	},
	[200]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Limit {0} Orb"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Limit {0} Orbs"
			}
		},
		stats={
			[1]="galvanic_field_maximum_number_of_spheres"
		}
	},
	[201]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0}% chance to Blind enemies on hit"
			}
		},
		stats={
			[1]="global_chance_to_blind_on_hit_%"
		}
	},
	[202]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Adds {0} to {1} Chaos Damage"
			}
		},
		stats={
			[1]="global_minimum_added_chaos_damage",
			[2]="global_maximum_added_chaos_damage"
		}
	},
	[203]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Adds {0} to {1} Cold Damage"
			}
		},
		stats={
			[1]="global_minimum_added_cold_damage",
			[2]="global_maximum_added_cold_damage"
		}
	},
	[204]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Adds {0} to {1} Fire Damage"
			}
		},
		stats={
			[1]="global_minimum_added_fire_damage",
			[2]="global_maximum_added_fire_damage"
		}
	},
	[205]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Adds {0} to {1} Lightning Damage"
			}
		},
		stats={
			[1]="global_minimum_added_lightning_damage",
			[2]="global_maximum_added_lightning_damage"
		}
	},
	[206]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Adds {0} to {1} Physical Damage"
			}
		},
		stats={
			[1]="global_minimum_added_physical_damage",
			[2]="global_maximum_added_physical_damage"
		}
	},
	[207]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% reduced Enemy Block Chance"
			}
		},
		stats={
			[1]="global_reduce_enemy_block_%"
		}
	},
	[208]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Buff Effect"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Buff Effect"
			}
		},
		stats={
			[1]="golem_buff_effect_+%"
		}
	},
	[209]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Storm Hits an Enemy every {0} Seconds"
			}
		},
		stats={
			[1]="herald_of_thunder_bolt_base_frequency"
		}
	},
	[210]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Damage with Hits"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Damage with Hits"
			}
		},
		stats={
			[1]="hit_damage_+%"
		}
	},
	[211]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds",
					v=1
				},
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Pulses every {0} seconds while Frozen, Shocked, Brittle or Sapped"
			}
		},
		stats={
			[1]="hydro_sphere_base_pulse_frequency_ms"
		}
	},
	[212]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Pulse Frequency"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Pulse Frequency"
			}
		},
		stats={
			[1]="hydro_sphere_pulse_frequency_+%"
		}
	},
	[213]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="Explosion deals {0}% more damage per Stage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="Explosion deals {0}% less damage per Stage"
			}
		},
		stats={
			[1]="ice_ambusher_damage_+%_final_per_stack",
			[2]="frozen_locus_stat_suppression"
		}
	},
	[214]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Crystal has {0} initial stages"
			}
		},
		stats={
			[1]="ice_ambusher_initial_stack_count"
		}
	},
	[215]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Crystal loses a stage every {0} seconds"
			}
		},
		stats={
			[1]="ice_ambusher_stack_decay_rate_ms"
		}
	},
	[216]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Ignite Duration on Enemies"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Ignite Duration on Enemies"
			}
		},
		stats={
			[1]="ignite_duration_+%"
		}
	},
	[217]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Loses Intensity with {0}% increased frequency while moving"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Loses Intensity with {0}% reduced frequency while moving"
			}
		},
		stats={
			[1]="intensity_loss_frequency_while_moving_+%"
		}
	},
	[218]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Projectile changes direction {0} times"
			}
		},
		stats={
			[1]="kinetic_wand_base_number_of_zig_zags"
		}
	},
	[219]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Knockback Distance"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Knockback Distance"
			}
		},
		stats={
			[1]="knockback_distance_+%"
		}
	},
	[220]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Magnitude of Lightning Ailments you inflict"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Magnitude of Lightning Ailments you inflict"
			}
		},
		stats={
			[1]="lightning_ailment_effect_+%"
		}
	},
	[221]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Lightning Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Lightning Damage"
			}
		},
		stats={
			[1]="lightning_damage_+%"
		}
	},
	[222]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Limit {0} Lightning Storm"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Limit {0} Lightning Storms"
			}
		},
		stats={
			[1]="lightning_storm_max_number_of_storms"
		}
	},
	[223]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Strikes every {0} seconds"
			}
		},
		stats={
			[1]="lightning_tower_trap_base_interval_duration_ms"
		}
	},
	[224]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Illusions have {0}% of your maximum Life"
			}
		},
		stats={
			[1]="lingering_illusion_clone_base_maximum_life_%_of_owner_maximum_life"
		}
	},
	[225]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0}% chance to Detonate for Double Damage instead of Chaining"
			}
		},
		stats={
			[1]="magma_orb_%_chance_to_big_explode_instead_of_chaining"
		}
	},
	[226]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Effect of Maim"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Effect of Maim"
			}
		},
		stats={
			[1]="maim_effect_+%"
		}
	},
	[227]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} base Chaos Damage"
			}
		},
		stats={
			[1]="main_hand_weapon_minimum_chaos_damage",
			[2]="main_hand_weapon_maximum_chaos_damage"
		}
	},
	[228]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} base Cold Damage"
			}
		},
		stats={
			[1]="main_hand_weapon_minimum_cold_damage",
			[2]="main_hand_weapon_maximum_cold_damage"
		}
	},
	[229]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} base Fire Damage"
			}
		},
		stats={
			[1]="main_hand_weapon_minimum_fire_damage",
			[2]="main_hand_weapon_maximum_fire_damage"
		}
	},
	[230]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} base Lightning Damage"
			}
		},
		stats={
			[1]="main_hand_weapon_minimum_lightning_damage",
			[2]="main_hand_weapon_maximum_lightning_damage"
		}
	},
	[231]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} base Physical Damage"
			}
		},
		stats={
			[1]="main_hand_weapon_minimum_physical_damage",
			[2]="main_hand_weapon_maximum_physical_damage"
		}
	},
	[232]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Gain {0} Mana per Enemy Hit"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Lose {0} Mana per Enemy Hit"
			}
		},
		stats={
			[1]="mana_gain_per_target"
		}
	},
	[233]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Corpses Spawned have {0}% increased Maximum Life"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Corpses Spawned have {0}% reduced Maximum Life"
			}
		},
		stats={
			[1]="maximum_life_+%_for_corpses_you_create"
		}
	},
	[234]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Melee Strikes target {0} additional nearby Enemy"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Melee Strikes target {0} additional nearby Enemies"
			}
		},
		stats={
			[1]="melee_attack_number_of_spirit_strikes"
		}
	},
	[235]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Melee Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Melee Damage"
			}
		},
		stats={
			[1]="melee_damage_+%"
		}
	},
	[236]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Melee Damage against Bleeding Enemies"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Melee Damage against Bleeding Enemies"
			}
		},
		stats={
			[1]="melee_damage_vs_bleeding_enemies_+%"
		}
	},
	[237]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Melee Physical Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Melee Physical Damage"
			}
		},
		stats={
			[1]="melee_physical_damage_+%"
		}
	},
	[238]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Mine Detonation Area of Effect"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Mine Detonation Area of Effect"
			}
		},
		stats={
			[1]="mine_detonation_radius_+%"
		}
	},
	[239]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Mines have {0}% increased Detonation Speed"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Mines have {0}% reduced Detonation Speed"
			}
		},
		stats={
			[1]="mine_detonation_speed_+%"
		}
	},
	[240]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Mine duration"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Mine duration"
			}
		},
		stats={
			[1]="mine_duration_+%"
		}
	},
	[241]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Mine Throwing Speed"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Mine Throwing Speed"
			}
		},
		stats={
			[1]="mine_laying_speed_+%"
		}
	},
	[242]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Minions have {0}% chance to deal Double Damage"
			}
		},
		stats={
			[1]="minion_chance_to_deal_double_damage_%"
		}
	},
	[243]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Minions from this Skill have {0:+d}% to all Elemental Resistances"
			}
		},
		stats={
			[1]="minion_elemental_resistance_%"
		}
	},
	[244]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Modifiers to number of Projectiles instead apply to\nthe number of targets Projectile Splits towards"
			}
		},
		stats={
			[1]="modifiers_to_number_of_projectiles_instead_apply_to_splitting"
		}
	},
	[245]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Projectile count cannot be modified"
			}
		},
		stats={
			[1]="modifiers_to_projectile_count_do_not_apply"
		}
	},
	[246]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Aura Magnitudes"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Aura Magnitudes"
			}
		},
		stats={
			[1]="non_curse_aura_effect_+%"
		}
	},
	[247]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Fires an additional Arrow"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Fires {0} additional Arrows"
			}
		},
		stats={
			[1]="number_of_additional_arrows"
		}
	},
	[248]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=0,
						[2]=0
					},
					[2]={
						[1]=1,
						[2]=1
					}
				},
				text="Fires an additional Projectile"
			},
			[2]={
				limit={
					[1]={
						[1]=0,
						[2]=0
					},
					[2]={
						[1]=2,
						[2]="#"
					}
				},
				text="Fires {1} additional Projectiles"
			}
		},
		stats={
			[1]="active_skill_additional_projectiles_description_mode",
			[2]="number_of_additional_projectiles"
		}
	},
	[249]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Chains {0} Times"
			}
		},
		stats={
			[1]="number_of_chains"
		}
	},
	[250]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Can have up to {0} additional Remote Mine placed at a time"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Can have up to {0} additional Remote Mines placed at a time"
			}
		},
		stats={
			[1]="number_of_additional_remote_mines_allowed"
		}
	},
	[251]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Can have up to {0} additional Trap placed at a time"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Can have up to {0} additional Traps placed at a time"
			}
		},
		stats={
			[1]="number_of_additional_traps_allowed"
		}
	},
	[252]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Throw up to 1 additional Trap"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Throw up to {0} additional Traps"
			}
		},
		stats={
			[1]="number_of_additional_traps_to_throw"
		}
	},
	[253]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Maximum {0} Animated Weapon"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Maximum {0} Animated Weapons"
			}
		},
		stats={
			[1]="number_of_animated_weapons_allowed"
		}
	},
	[254]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Fires {0}% more Projectiles per Steel Shard consumed"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Fires {0}% less Projectiles per Steel Shard consumed"
			}
		},
		stats={
			[1]="number_of_projectiles_to_fire_+%_final_per_steel_ammo_consumed"
		}
	},
	[255]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Maximum {0} Summoned Reaper"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Maximum {0} Summoned Reapers"
			}
		},
		stats={
			[1]="base_number_of_reapers_allowed"
		}
	},
	[256]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} Added Physical Damage per 15 Armour on Shield"
			}
		},
		stats={
			[1]="off_hand_minimum_added_physical_damage_per_15_shield_armour",
			[2]="off_hand_maximum_added_physical_damage_per_15_shield_armour"
		}
	},
	[257]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} Added Physical Damage per 5 Armour on Shield"
			}
		},
		stats={
			[1]="off_hand_minimum_added_physical_damage_per_5_shield_armour",
			[2]="off_hand_maximum_added_physical_damage_per_5_shield_armour"
		}
	},
	[258]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} to {1} Base Off Hand Fire Damage"
			}
		},
		stats={
			[1]="off_hand_weapon_minimum_fire_damage",
			[2]="off_hand_weapon_maximum_fire_damage"
		}
	},
	[259]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Fires a bolt every second, and when you use a Lightning Skill near the Orb"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fires a bolt every {0} seconds, and when you use a Lightning Skill near the Orb"
			}
		},
		stats={
			[1]="orb_of_storms_base_bolt_frequency_ms"
		}
	},
	[260]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Fires every second while Channelling a Lightning Skill near the Orb"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fires every {0} seconds while Channelling a Lightning Skill near the Orb"
			}
		},
		stats={
			[1]="orb_of_storms_base_channelling_bolt_frequency_ms"
		}
	},
	[261]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Limit {0} Orb"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Limit {0} Orbs"
			}
		},
		stats={
			[1]="orb_of_storms_base_maximum_number_of_orbs"
		}
	},
	[262]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Releases waves every {0} seconds"
			}
		},
		stats={
			[1]="phys_cascade_trap_base_interval_duration_ms"
		}
	},
	[263]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Physical Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Physical Damage"
			}
		},
		stats={
			[1]="physical_damage_+%"
		}
	},
	[264]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Cooldown Recovery Rate for throwing Traps"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Cooldown Recovery Rate for throwing Traps"
			}
		},
		stats={
			[1]="placing_traps_cooldown_recovery_+%"
		}
	},
	[265]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Poison inflicted by this Skill is affected by Modifiers to Skill Effect Duration"
			}
		},
		stats={
			[1]="poison_duration_is_skill_duration"
		}
	},
	[266]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="You and nearby Allies have {0}% more Area of Effect"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="You and nearby Allies have {0}% less Area of Effect"
			}
		},
		stats={
			[1]="precision_grants_area_of_effect_+%_final"
		}
	},
	[267]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Primary Projectile Chains {0:+d} Time"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Primary Projectile Chains {0:+d} Times"
			}
		},
		stats={
			[1]="primary_projectile_chains_+"
		}
	},
	[268]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Arrows fire Beams every {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Arrows fire Beams every {0} seconds"
			}
		},
		stats={
			[1]="prismatic_rain_beam_base_frequency_ms"
		}
	},
	[269]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Pierces the first target Hit"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Pierces the first {0} targets Hit"
			}
		},
		stats={
			[1]="projectile_base_number_of_targets_to_pierce"
		}
	},
	[270]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Projectile Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Projectile Damage"
			}
		},
		stats={
			[1]="projectile_damage_+%"
		}
	},
	[271]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Projectiles Split towards {0} targets"
			}
		},
		stats={
			[1]="projectile_number_to_split"
		}
	},
	[272]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Projectiles Fork"
			}
		},
		stats={
			[1]="projectiles_fork"
		}
	},
	[273]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=0,
						[2]=0
					},
					[2]={
						[1]=1,
						[2]=99
					}
				},
				text="Projectiles have {1}% chance to Return to you"
			},
			[2]={
				limit={
					[1]={
						[1]=0,
						[2]=0
					},
					[2]={
						[1]=100,
						[2]="#"
					}
				},
				text="Projectiles Return to you"
			},
			[3]={
				limit={
					[1]={
						[1]="!",
						[2]=0
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Projectiles Return to you"
			}
		},
		stats={
			[1]="projectiles_return",
			[2]="projectile_return_%_chance"
		}
	},
	[274]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0}% of Damage from Hits is taken from the Buff before your Life or Energy Shield\nBuff can take {1} Damage"
			}
		},
		stats={
			[1]="quick_guard_damage_absorbed_%",
			[2]="quick_guard_damage_absorb_limit"
		}
	},
	[275]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Fires an additional sequence of arrows"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Fires {0} additional sequences of arrows"
			}
		},
		stats={
			[1]="rain_of_arrows_additional_sequences"
		}
	},
	[276]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="+{0} to maximum Stages"
			}
		},
		stats={
			[1]="reave_additional_max_stacks"
		}
	},
	[277]={
		[1]={
			[1]={
				[1]={
					k="per_minute_to_per_second_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Lose {0}% of maximum Life per second\n{1}% more Life loss each second"
			}
		},
		stats={
			[1]="reaver_enrage_base_life_%_degen_per_minute",
			[2]="reaver_enrage_decay_rate_increase_+%_final"
		}
	},
	[278]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Penetrates {0}% Chaos Resistance"
			}
		},
		stats={
			[1]="reduce_enemy_chaos_resistance_%"
		}
	},
	[279]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% reduced Enemy chance to Dodge"
			}
		},
		stats={
			[1]="reduce_enemy_dodge_%"
		}
	},
	[280]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Regenerate {0} Life over 1 second"
			}
		},
		stats={
			[1]="regenerate_x_life_over_1_second_on_skill_use_or_trigger"
		}
	},
	[281]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Limit {0} active rituals"
			}
		},
		stats={
			[1]="ritual_of_power_maximum_number_of_rituals"
		}
	},
	[282]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased wave frequency"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced wave frequency"
			}
		},
		stats={
			[1]="seismic_trap_frequency_+%"
		}
	},
	[283]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="{0:+d} second to Total Attack Time if not already a Bear"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0:+d} seconds to Total Attack Time if not already a Bear"
			}
		},
		stats={
			[1]="shapeshift_to_bear_added_attack_time_ms"
		}
	},
	[284]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="{0:+d} second to Total Use Time if not already a Bear"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0:+d} seconds to Total Use Time if not already a Bear"
			}
		},
		stats={
			[1]="shapeshift_to_bear_added_cast_time_ms"
		}
	},
	[285]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Shock Duration"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Shock Duration"
			}
		},
		stats={
			[1]="shock_duration_+%"
		}
	},
	[286]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Deals {0}% of your maximum Energy Shield as base Chaos Damage"
			}
		},
		stats={
			[1]="skill_base_chaos_damage_%_maximum_energy_shield"
		}
	},
	[287]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Deals {0}% of your maximum Life as base Chaos Damage"
			}
		},
		stats={
			[1]="skill_base_chaos_damage_%_maximum_life"
		}
	},
	[288]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fire Area Spells summon a Spirit per {0} metres of radius\nProjectiles from Fire Spells summon a Spirit on hit"
			}
		},
		stats={
			[1]="skill_raging_spirit_per_x_radius"
		}
	},
	[289]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Maximum {0} Spirit summoned per Spell cast"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Maximum {0} Spirits summoned per Spell cast"
			}
		},
		stats={
			[1]="skill_raging_spirits_max_per_action"
		}
	},
	[290]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{}% chance to Fire a caught Projectile when Hit"
			}
		},
		stats={
			[1]="snapping_adder_chance_to_release_projectile_when_hit_%"
		}
	},
	[291]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Take {0}% of maximum Life and {1}% of maximum Energy Shield as Chaos Damage"
			}
		},
		stats={
			[1]="soulfeast_take_%_maximum_life_as_chaos_damage",
			[2]="soulfeast_take_%_maximum_energy_shield_as_chaos_damage"
		}
	},
	[292]={
		[1]={
			[1]={
				[1]={
					k="divide_by_one_hundred_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Creates 1 Spear per {0} metre of Radius"
			},
			[2]={
				[1]={
					k="divide_by_one_hundred_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Creates 1 Spear per {0} metres of Radius"
			}
		},
		stats={
			[1]="spear_wall_cm_per_spear"
		}
	},
	[293]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Bounces up to {0} time\nModifiers to number of Projectiles instead apply to the number of Bounces"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Bounces up to {0} times\nModifiers to number of Projectiles instead apply to the number of Bounces"
			}
		},
		stats={
			[1]="spectral_spiral_weapon_base_number_of_bounces"
		}
	},
	[294]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="This Spell's Cast Time is added to its Cooldown if Triggered"
			}
		},
		stats={
			[1]="spell_cast_time_added_to_cooldown_if_triggered"
		}
	},
	[295]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Spell Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Spell Damage"
			}
		},
		stats={
			[1]="spell_damage_+%"
		}
	},
	[296]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0} maximum Beam Targets"
			}
		},
		stats={
			[1]="static_strike_number_of_beam_targets"
		}
	},
	[297]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]="#",
						[2]="#"
					},
					[4]={
						[1]="#",
						[2]="#"
					}
				},
				text="Energy Blades have minimum Lightning Damage equal to {2}% of Energy Shield, plus {0}\nEnergy Blades have maximum Lightning Damage equal to {3}% of Energy Shield, plus {1}"
			}
		},
		stats={
			[1]="storm_blade_minimum_lightning_damage",
			[2]="storm_blade_maximum_lightning_damage",
			[3]="storm_blade_minimum_lightning_damage_from_es_%",
			[4]="storm_blade_maximum_lightning_damage_from_es_%"
		}
	},
	[298]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Two Handed Energy Blades have {0}% more Lightning Damage"
			}
		},
		stats={
			[1]="storm_blade_damage_+%_final_with_two_hand_weapon"
		}
	},
	[299]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="This Spell gains Intensity when you Cast it, to a maximum of 3"
			}
		},
		stats={
			[1]="active_skill_display_does_intensity_stuff"
		}
	},
	[300]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Trap Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Trap Damage"
			}
		},
		stats={
			[1]="support_trap_damage_+%_final"
		}
	},
	[301]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% more Area of Effect per 0.1 metres of\nKnockback the triggering Hit would have caused"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% less Area of Effect per 0.1 metres of\nKnockback the triggering Hit would have caused"
			}
		},
		stats={
			[1]="tempest_bell_area_of_effect_+%_final_per_1_unit_of_knockback"
		}
	},
	[302]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Gains {0}% of Physical Damage as Extra Damage of each Type matching an Elemental Ailment on the Bell"
			}
		},
		stats={
			[1]="tempest_bell_physical_damage_%_as_elemental_per_ailment"
		}
	},
	[303]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased initial Duration"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced initial Duration"
			}
		},
		stats={
			[1]="tornado_only_primary_duration_+%"
		}
	},
	[304]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Fires {0} secondary Projectile"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Fires {0} secondary Projectiles"
			}
		},
		stats={
			[1]="tornado_shot_num_of_secondary_projectiles"
		}
	},
	[305]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d} second to Total Attack Time"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d} seconds to Total Attack Time"
			},
			[3]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				[2]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=2
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0:+d} second to Total Attack Time, plus up to\n{1:+d} seconds based on distance travelled"
			},
			[4]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				[2]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=2
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0:+d} seconds to Total Attack Time, plus up to\n{1:+d} seconds based on distance travelled"
			}
		},
		stats={
			[1]="total_attack_time_+_ms",
			[2]="leap_slam_added_attack_time_by_distance_ms"
		}
	},
	[306]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="{0:+d} second to Total Cast Time"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="{0:+d} seconds to Total Cast Time"
			}
		},
		stats={
			[1]="total_cast_time_+_ms"
		}
	},
	[307]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Totems Explode on Death, dealing {0}% of their Life as Physical Damage"
			}
		},
		stats={
			[1]="totems_explode_on_death_for_%_life_as_physical"
		}
	},
	[308]={
		[1]={
			[1]={
				[1]={
					k="per_minute_to_per_second",
					v=1
				},
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Totems Regenerate {0}% of maximum Life per second"
			}
		},
		stats={
			[1]="totems_regenerate_%_life_per_minute"
		}
	},
	[309]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Trap Damage"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Trap Damage"
			}
		},
		stats={
			[1]="trap_damage_+%"
		}
	},
	[310]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Trap duration"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Trap duration"
			}
		},
		stats={
			[1]="trap_duration_+%"
		}
	},
	[311]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Trap Throwing Speed"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Trap Throwing Speed"
			}
		},
		stats={
			[1]="trap_throwing_speed_+%"
		}
	},
	[312]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Trap Trigger Area of Effect"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Trap Trigger Area of Effect"
			}
		},
		stats={
			[1]="trap_trigger_radius_+%"
		}
	},
	[313]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Spawns corpses with Level {0}"
			}
		},
		stats={
			[1]="unearth_base_corpse_level"
		}
	},
	[314]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Projectiles change direction {0} time\nProjectiles can Hit targets each time they change direction"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Projectiles change direction {0} times\nProjectiles can Hit targets each time they change direction"
			}
		},
		stats={
			[1]="vaal_lightning_arrow_number_of_redirects"
		}
	},
	[315]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Modifiers which would cause Projectiles to Fork or\nChain instead cause them to change direction additional times"
			}
		},
		stats={
			[1]="vaal_lightning_arrow_fork_and_chain_modifiers_apply_to_number_of_redirects"
		}
	},
	[316]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Strikes an Enemy every {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Strikes an Enemy every {0} seconds"
			}
		},
		stats={
			[1]="vaal_storm_call_base_delay_ms"
		}
	},
	[317]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Consumes up to {0} corpses"
			}
		},
		stats={
			[1]="volatile_dead_base_number_of_corpses_to_consume"
		}
	},
	[318]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Maximum of {0} Orbs at a time"
			}
		},
		stats={
			[1]="volatile_dead_max_cores_allowed"
		}
	},
	[319]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Limit {0} Fissure"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Limit {0} Fissures"
			}
		},
		stats={
			[1]="volcanic_fissure_base_maximum_number_of_fissures"
		}
	},
	[320]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Warcry Speed"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Warcry Speed"
			}
		},
		stats={
			[1]="warcry_speed_+%"
		}
	},
	[321]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased Elemental Damage with Weapons"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced Elemental Damage with Weapons"
			}
		},
		stats={
			[1]="weapon_elemental_damage_+%"
		}
	},
	[322]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="{0}% increased rotation speed if Dual Wielding"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="{0}% reduced rotation speed if Dual Wielding"
			}
		},
		stats={
			[1]="weapon_trap_rotation_speed_+%_if_dual_wielding"
		}
	},
	[323]={
		[1]={
			[1]={
				[1]={
					k="divide_by_one_hundred_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Rotates {0:+d} times if Dual Wielding"
			}
		},
		stats={
			[1]="weapon_trap_total_rotation_%_if_dual_wielding"
		}
	},
	["accuracy_rating"]=41,
	["accuracy_rating_+%"]=42,
	["active_skill_additional_projectiles_description_mode"]=248,
	["active_skill_area_damage_+%_final"]=30,
	["active_skill_attack_damage_+%_final"]=10,
	["active_skill_attack_damage_+%_final_with_two_handed_weapon"]=43,
	["active_skill_attack_damage_final_permyriad"]=12,
	["active_skill_attack_speed_+%_final_with_two_handed_weapon"]=44,
	["active_skill_base_cold_damage_%_to_convert_to_fire"]=45,
	["active_skill_base_fire_damage_%_to_convert_to_chaos"]=46,
	["active_skill_base_lightning_damage_%_to_convert_to_chaos"]=47,
	["active_skill_base_physical_damage_%_to_convert_to_chaos"]=49,
	["active_skill_base_physical_damage_%_to_convert_to_cold"]=50,
	["active_skill_base_physical_damage_%_to_convert_to_fire"]=51,
	["active_skill_base_physical_damage_%_to_convert_to_lightning"]=52,
	["active_skill_base_physical_damage_%_to_gain_as_cold"]=48,
	["active_skill_base_stored_corpse_cost"]=53,
	["active_skill_brands_allowed_on_enemy_+"]=54,
	["active_skill_cast_speed_+%_applies_to_attack_speed_at_%_of_original_value"]=55,
	["active_skill_cast_speed_+%_final"]=56,
	["active_skill_critical_strike_chance_+%_final"]=57,
	["active_skill_damage_+%_final"]=13,
	["active_skill_display_does_intensity_stuff"]=299,
	["active_skill_display_suppress_physical_to_cold_damage_conversion"]=50,
	["active_skill_minion_damage_+%_final"]=14,
	["active_skill_minion_physical_damage_+%_final"]=15,
	["active_skill_physical_damage_+%_final"]=16,
	["active_skill_poison_duration_+%_final"]=58,
	["active_skill_quality_damage_+%_final"]=59,
	["active_skill_quality_duration_+%_final"]=60,
	["add_power_charge_on_kill_%_chance"]=61,
	["additional_base_critical_strike_chance"]=62,
	["additive_cast_speed_modifiers_apply_to_sigil_repeat_frequency"]=38,
	["additive_mine_duration_modifiers_apply_to_buff_effect_duration"]=63,
	["alt_attack_container_main_hand_base_weapon_attack_duration_ms"]=64,
	["alt_attack_container_main_hand_weapon_critical_strike_chance"]=65,
	["alt_attack_container_main_hand_weapon_maximum_cold_damage"]=66,
	["alt_attack_container_main_hand_weapon_maximum_lightning_damage"]=67,
	["alt_attack_container_main_hand_weapon_maximum_physical_damage"]=68,
	["alt_attack_container_main_hand_weapon_minimum_cold_damage"]=66,
	["alt_attack_container_main_hand_weapon_minimum_lightning_damage"]=67,
	["alt_attack_container_main_hand_weapon_minimum_physical_damage"]=68,
	["always_freeze"]=93,
	["ancestor_totem_buff_effect_+%"]=69,
	["ancestor_totem_parent_activation_range_+%"]=70,
	["animate_item_maximum_level_requirement"]=40,
	["arc_chain_hit_damage_+%_final"]=71,
	["area_damage_+%"]=72,
	["area_of_effect_+%_while_dead"]=73,
	["arsonist_destructive_link_%_of_life_as_fire_damage"]=74,
	["attack_and_cast_speed_+%"]=75,
	["attack_and_cast_speed_+%_during_onslaught"]=76,
	["attack_maximum_added_chaos_damage"]=77,
	["attack_maximum_added_cold_damage"]=78,
	["attack_maximum_added_fire_damage"]=79,
	["attack_maximum_added_lightning_damage"]=80,
	["attack_maximum_added_physical_damage"]=81,
	["attack_minimum_added_chaos_damage"]=77,
	["attack_minimum_added_cold_damage"]=78,
	["attack_minimum_added_fire_damage"]=79,
	["attack_minimum_added_lightning_damage"]=80,
	["attack_minimum_added_physical_damage"]=81,
	["attack_skills_additional_ballista_totems_allowed"]=33,
	["attack_speed_+%"]=82,
	["attack_speed_+%_granted_from_skill"]=83,
	["attacks_chance_to_blind_on_hit_%"]=84,
	["aura_effect_+%"]=23,
	["avoid_interruption_while_using_this_skill_%"]=85,
	["ball_lightning_base_hit_frequency_ms"]=86,
	["base_added_cooldown_count"]=87,
	["base_aura_area_of_effect_+%"]=88,
	["base_blackhole_tick_rate_ms"]=89,
	["base_blade_vortex_hit_rate_ms"]=90,
	["base_bleed_duration_+%"]=91,
	["base_buff_duration_ms_+_per_removable_endurance_charge"]=26,
	["base_cast_speed_+%"]=92,
	["base_chance_to_freeze_%"]=93,
	["base_chance_to_ignite_%"]=94,
	["base_chance_to_shock_%"]=95,
	["base_cost_+%"]=96,
	["base_critical_strike_multiplier_+"]=97,
	["base_curse_duration_+%"]=98,
	["base_galvanic_field_beam_delay_ms"]=99,
	["base_global_chance_to_knockback_%"]=100,
	["base_killed_monster_dropped_item_rarity_+%"]=101,
	["base_knockback_distance"]=102,
	["base_life_cost_+%"]=103,
	["base_life_gain_per_target"]=104,
	["base_life_reservation_+%"]=105,
	["base_mana_cost_-%"]=106,
	["base_mana_reservation_+%"]=107,
	["base_melee_attack_repeat_count"]=39,
	["base_mine_detonation_time_ms"]=108,
	["base_number_of_arrows"]=109,
	["base_number_of_bone_offerings_allowed"]=110,
	["base_number_of_champions_of_light_allowed"]=111,
	["base_number_of_golems_allowed"]=32,
	["base_number_of_pain_offerings_allowed"]=112,
	["base_number_of_power_offerings_allowed"]=113,
	["base_number_of_raging_spirits_allowed"]=21,
	["base_number_of_reapers_allowed"]=255,
	["base_number_of_relics_allowed"]=114,
	["base_number_of_skeletal_constructs_allowed"]=115,
	["base_number_of_skeletons_allowed"]=20,
	["base_number_of_spectres_allowed"]=19,
	["base_number_of_totems_allowed"]=34,
	["base_number_of_zombies_allowed"]=18,
	["base_poison_duration_+%"]=116,
	["base_projectile_speed_+%"]=117,
	["base_reduce_enemy_cold_resistance_%"]=118,
	["base_reduce_enemy_fire_resistance_%"]=119,
	["base_reduce_enemy_lightning_resistance_%"]=120,
	["base_reservation_+%"]=122,
	["base_reservation_efficiency_+%"]=121,
	["base_sigil_repeat_frequency_ms"]=36,
	["base_skill_area_of_effect_+%"]=123,
	["base_spell_repeat_count"]=31,
	["base_stun_duration_+%"]=124,
	["base_use_life_in_place_of_mana"]=125,
	["base_weapon_trap_rotation_speed_+%"]=126,
	["base_weapon_trap_total_rotation_%"]=127,
	["berserk_base_rage_loss_per_second"]=128,
	["bladefall_blade_left_in_ground_for_every_X_volleys"]=129,
	["bladefall_number_of_volleys"]=130,
	["blades_left_in_ground_+%_final_if_not_hand_cast"]=131,
	["blasphemy_base_spirit_reservation_per_socketed_curse"]=132,
	["blind_duration_+%"]=133,
	["blood_spears_additional_number_of_spears_if_changed_stance_recently"]=134,
	["blood_spears_base_number_of_spears"]=135,
	["blood_spears_damage_+%_final_in_blood_stance"]=136,
	["bone_spear_maximum_added_attack_physical_damage_taken"]=137,
	["bone_spear_maximum_damage_threshold"]=138,
	["bone_spear_minimum_added_attack_physical_damage_taken"]=137,
	["bone_spear_minimum_damage_threshold"]=138,
	["buff_duration_+%"]=27,
	["burn_damage_+%"]=139,
	["chance_%_when_poison_to_also_poison_another_enemy"]=140,
	["chance_to_double_stun_duration_%"]=141,
	["chance_to_fork_extra_projectile_%"]=142,
	["chance_to_fortify_on_melee_hit_+%"]=143,
	["chance_to_gain_frenzy_charge_on_killing_frozen_enemy_%"]=144,
	["chance_to_place_an_additional_mine_%"]=145,
	["chance_to_scorch_%"]=146,
	["chaos_damage_+%"]=147,
	["chill_duration_+%"]=148,
	["chill_effect_+%"]=149,
	["circle_of_power_skill_cost_mana_cost_+%"]=150,
	["cold_ailment_effect_+%"]=151,
	["cold_damage_+%"]=152,
	["consecrated_ground_area_+%"]=155,
	["consecrated_ground_effect_+%"]=153,
	["consecrated_ground_enemy_damage_taken_+%"]=154,
	["contagion_spread_on_hit_affected_enemy_%"]=156,
	["conversation_trap_converted_enemy_damage_+%"]=157,
	["conversion_trap_converted_enemies_chance_to_taunt_on_hit_%"]=158,
	["corpse_erruption_base_maximum_number_of_geyers"]=159,
	["cremation_fires_projectiles_faster_+%_final"]=160,
	["critical_hit_damaging_ailment_effect_+%"]=161,
	["critical_multiplier_+%_per_100_max_es_on_shield"]=162,
	["critical_strike_chance_+%"]=163,
	["cyclone_max_stages_movement_speed_+%"]=164,
	["damage_+%"]=166,
	["damage_+%_on_full_energy_shield"]=173,
	["damage_+%_per_endurance_charge"]=167,
	["damage_+%_per_frenzy_charge"]=168,
	["damage_+%_per_power_charge"]=169,
	["damage_+%_vs_enemies_on_full_life"]=170,
	["damage_+%_vs_enemies_per_freeze_shock_ignite"]=171,
	["damage_+%_vs_frozen_enemies"]=172,
	["damage_+%_when_on_full_life"]=174,
	["damage_+%_when_on_low_life"]=175,
	["damage_over_time_+%"]=165,
	["damage_vs_cursed_enemies_per_enemy_curse_+%"]=176,
	["damage_vs_enemies_on_low_life_+%"]=177,
	["dash_grants_phasing_after_use_ms"]=178,
	["display_base_intensity_loss"]=179,
	["display_consume_corpse_storage_limit"]=180,
	["display_frost_fury_additive_cast_speed_modifiers_apply_to_fire_speed"]=181,
	["display_minion_base_maximum_life"]=29,
	["divine_tempest_base_number_of_nearby_enemies_to_zap"]=182,
	["dot_multiplier_+"]=183,
	["elemental_damage_+%"]=184,
	["empower_barrage_base_number_of_barrage_repeats"]=185,
	["empower_barrage_damage_-%_final_with_repeated_projectiles"]=186,
	["empower_barrage_number_of_barrage_repeats_per_frenzy_charge"]=185,
	["enemy_phys_reduction_%_penalty_vs_hit"]=187,
	["ethereal_knives_blade_left_in_ground_for_every_X_projectiles"]=188,
	["eye_of_winter_base_explosion_shards"]=35,
	["faster_bleed_%"]=189,
	["faster_burn_%"]=190,
	["faster_poison_%"]=191,
	["fire_damage_+%"]=192,
	["firestorm_explosion_area_of_effect_+%"]=193,
	["flame_link_added_fire_damage_from_life_%"]=195,
	["flame_link_maximum_fire_damage"]=194,
	["flame_link_minimum_fire_damage"]=194,
	["fortify_duration_+%"]=196,
	["freeze_duration_+%"]=197,
	["freezing_bolt_chill_maximum_magnitude_override"]=198,
	["frozen_locus_stat_suppression"]=213,
	["galvanic_field_beam_frequency_+%"]=199,
	["galvanic_field_maximum_number_of_spheres"]=200,
	["global_chance_to_blind_on_hit_%"]=201,
	["global_maximum_added_chaos_damage"]=202,
	["global_maximum_added_cold_damage"]=203,
	["global_maximum_added_fire_damage"]=204,
	["global_maximum_added_lightning_damage"]=205,
	["global_maximum_added_physical_damage"]=206,
	["global_minimum_added_chaos_damage"]=202,
	["global_minimum_added_cold_damage"]=203,
	["global_minimum_added_fire_damage"]=204,
	["global_minimum_added_lightning_damage"]=205,
	["global_minimum_added_physical_damage"]=206,
	["global_reduce_enemy_block_%"]=207,
	["golem_buff_effect_+%"]=208,
	["herald_of_thunder_bolt_base_frequency"]=209,
	["hit_damage_+%"]=210,
	["hydro_sphere_base_pulse_frequency_ms"]=211,
	["hydro_sphere_pulse_frequency_+%"]=212,
	["ice_ambusher_damage_+%_final_per_stack"]=213,
	["ice_ambusher_initial_stack_count"]=214,
	["ice_ambusher_stack_decay_rate_ms"]=215,
	["ignite_duration_+%"]=216,
	["intensity_loss_frequency_while_moving_+%"]=217,
	["kinetic_wand_base_number_of_zig_zags"]=218,
	["knockback_distance_+%"]=219,
	["leap_slam_added_attack_time_by_distance_ms"]=305,
	["lightning_ailment_effect_+%"]=220,
	["lightning_damage_+%"]=221,
	["lightning_storm_max_number_of_storms"]=222,
	["lightning_tower_trap_base_interval_duration_ms"]=223,
	["lingering_illusion_clone_base_maximum_life_%_of_owner_maximum_life"]=224,
	["magma_orb_%_chance_to_big_explode_instead_of_chaining"]=225,
	["maim_effect_+%"]=226,
	["main_hand_weapon_maximum_chaos_damage"]=227,
	["main_hand_weapon_maximum_cold_damage"]=228,
	["main_hand_weapon_maximum_fire_damage"]=229,
	["main_hand_weapon_maximum_lightning_damage"]=230,
	["main_hand_weapon_maximum_physical_damage"]=231,
	["main_hand_weapon_minimum_chaos_damage"]=227,
	["main_hand_weapon_minimum_cold_damage"]=228,
	["main_hand_weapon_minimum_fire_damage"]=229,
	["main_hand_weapon_minimum_lightning_damage"]=230,
	["main_hand_weapon_minimum_physical_damage"]=231,
	["mana_gain_per_target"]=232,
	["maximum_life_+%_for_corpses_you_create"]=233,
	["melee_attack_number_of_spirit_strikes"]=234,
	["melee_damage_+%"]=235,
	["melee_damage_vs_bleeding_enemies_+%"]=236,
	["melee_physical_damage_+%"]=237,
	["mine_detonation_radius_+%"]=238,
	["mine_detonation_speed_+%"]=239,
	["mine_duration_+%"]=240,
	["mine_laying_speed_+%"]=241,
	["minion_chance_to_deal_double_damage_%"]=242,
	["minion_elemental_resistance_%"]=243,
	["modifiers_to_number_of_projectiles_instead_apply_to_splitting"]=244,
	["modifiers_to_projectile_count_do_not_apply"]=245,
	["non_curse_aura_effect_+%"]=246,
	["number_of_additional_arrows"]=247,
	["number_of_additional_projectiles"]=248,
	["number_of_additional_remote_mines_allowed"]=250,
	["number_of_additional_traps_allowed"]=251,
	["number_of_additional_traps_to_throw"]=252,
	["number_of_animated_weapons_allowed"]=253,
	["number_of_chains"]=249,
	["number_of_projectiles_to_fire_+%_final_per_steel_ammo_consumed"]=254,
	["off_hand_base_weapon_attack_duration_ms"]=4,
	["off_hand_maximum_added_fire_damage_per_15_shield_armour"]=2,
	["off_hand_maximum_added_physical_damage_per_15_shield_armour"]=256,
	["off_hand_maximum_added_physical_damage_per_15_shield_armour_and_evasion_rating"]=3,
	["off_hand_maximum_added_physical_damage_per_5_shield_armour"]=257,
	["off_hand_minimum_added_fire_damage_per_15_shield_armour"]=2,
	["off_hand_minimum_added_physical_damage_per_15_shield_armour"]=256,
	["off_hand_minimum_added_physical_damage_per_15_shield_armour_and_evasion_rating"]=3,
	["off_hand_minimum_added_physical_damage_per_5_shield_armour"]=257,
	["off_hand_weapon_maximum_fire_damage"]=258,
	["off_hand_weapon_maximum_physical_damage"]=1,
	["off_hand_weapon_minimum_fire_damage"]=258,
	["off_hand_weapon_minimum_physical_damage"]=1,
	["offering_skill_effect_duration_per_corpse"]=25,
	["orb_of_storms_base_bolt_frequency_ms"]=259,
	["orb_of_storms_base_channelling_bolt_frequency_ms"]=260,
	["orb_of_storms_base_maximum_number_of_orbs"]=261,
	parent="gem_stat_descriptions",
	["phys_cascade_trap_base_interval_duration_ms"]=262,
	["physical_damage_+%"]=263,
	["physical_damage_+%_per_frenzy_charge"]=17,
	["placing_traps_cooldown_recovery_+%"]=264,
	["poison_duration_is_skill_duration"]=265,
	["precision_grants_area_of_effect_+%_final"]=266,
	["primary_projectile_chains_+"]=267,
	["prismatic_rain_beam_base_frequency_ms"]=268,
	["projectile_base_number_of_targets_to_pierce"]=269,
	["projectile_damage_+%"]=270,
	["projectile_number_to_split"]=271,
	["projectile_return_%_chance"]=273,
	["projectiles_fork"]=272,
	["projectiles_return"]=273,
	["quality_display_arsonist_is_gem"]=74,
	["quality_display_barrage_is_gem"]=186,
	["quick_guard_damage_absorb_limit"]=274,
	["quick_guard_damage_absorbed_%"]=274,
	["rain_of_arrows_additional_sequences"]=275,
	["reave_additional_max_stacks"]=276,
	["reaver_enrage_base_life_%_degen_per_minute"]=277,
	["reaver_enrage_decay_rate_increase_+%_final"]=277,
	["reduce_enemy_chaos_resistance_%"]=278,
	["reduce_enemy_dodge_%"]=279,
	["regenerate_x_life_over_1_second_on_skill_use_or_trigger"]=280,
	["ritual_of_power_maximum_number_of_rituals"]=281,
	["seismic_trap_frequency_+%"]=282,
	["shapeshift_to_bear_added_attack_time_ms"]=283,
	["shapeshift_to_bear_added_cast_time_ms"]=284,
	["shock_duration_+%"]=285,
	["sigil_repeat_frequency_+%"]=37,
	["skill_base_chaos_damage_%_maximum_energy_shield"]=286,
	["skill_base_chaos_damage_%_maximum_life"]=287,
	["skill_effect_duration_+%"]=28,
	["skill_raging_spirit_per_x_radius"]=288,
	["skill_raging_spirits_max_per_action"]=289,
	["snapping_adder_chance_to_release_projectile_when_hit_%"]=290,
	["soulfeast_take_%_maximum_energy_shield_as_chaos_damage"]=291,
	["soulfeast_take_%_maximum_life_as_chaos_damage"]=291,
	["spear_wall_cm_per_spear"]=292,
	["spectral_spiral_weapon_base_number_of_bounces"]=293,
	["spell_cast_time_added_to_cooldown_if_triggered"]=294,
	["spell_damage_+%"]=295,
	["static_strike_number_of_beam_targets"]=296,
	["storm_blade_damage_+%_final_with_two_hand_weapon"]=298,
	["storm_blade_maximum_lightning_damage"]=297,
	["storm_blade_maximum_lightning_damage_from_es_%"]=297,
	["storm_blade_minimum_lightning_damage"]=297,
	["storm_blade_minimum_lightning_damage_from_es_%"]=297,
	["supplementary_stat_container_attack_speed_+%_final"]=11,
	["support_trap_damage_+%_final"]=300,
	["tempest_bell_area_of_effect_+%_final_per_1_unit_of_knockback"]=301,
	["tempest_bell_physical_damage_%_as_elemental_per_ailment"]=302,
	["tornado_maximum_number_of_hits"]=24,
	["tornado_only_primary_duration_+%"]=303,
	["tornado_shot_num_of_secondary_projectiles"]=304,
	["total_attack_time_+_ms"]=305,
	["total_cast_time_+_ms"]=306,
	["totems_explode_on_death_for_%_life_as_physical"]=307,
	["totems_regenerate_%_life_per_minute"]=308,
	["trap_damage_+%"]=309,
	["trap_duration_+%"]=310,
	["trap_throwing_speed_+%"]=311,
	["trap_trigger_radius_+%"]=312,
	["unearth_base_corpse_level"]=313,
	["vaal_animate_weapon_minimum_level_requirement"]=40,
	["vaal_lightning_arrow_fork_and_chain_modifiers_apply_to_number_of_redirects"]=315,
	["vaal_lightning_arrow_number_of_redirects"]=314,
	["vaal_storm_call_base_delay_ms"]=316,
	["volatile_dead_base_number_of_corpses_to_consume"]=317,
	["volatile_dead_max_cores_allowed"]=318,
	["volcanic_fissure_base_maximum_number_of_fissures"]=319,
	["warcry_speed_+%"]=320,
	["weapon_elemental_damage_+%"]=321,
	["weapon_trap_rotation_speed_+%_if_dual_wielding"]=322,
	["weapon_trap_total_rotation_%_if_dual_wielding"]=323
}