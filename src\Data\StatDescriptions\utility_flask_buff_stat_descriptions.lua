-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Grants Immunity to Chill if used while Chilled"
			}
		},
		stats={
			[1]="base_immune_to_chill"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Grants Immunity to Freeze"
			}
		},
		stats={
			[1]="base_immune_to_freeze"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Grants Immunity to Ignite"
			}
		},
		stats={
			[1]="base_immune_to_ignite"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Grants Immunity to Shock"
			}
		},
		stats={
			[1]="base_immune_to_shock"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Grants Immunity to Bleeding"
			}
		},
		stats={
			[1]="immune_to_bleeding"
		}
	},
	[6]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Grants Immunity to Burning if used while Burning"
			}
		},
		stats={
			[1]="immune_to_burning"
		}
	},
	[7]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Grants Immunity to Poison"
			}
		},
		stats={
			[1]="immune_to_poison"
		}
	},
	["base_immune_to_chill"]=1,
	["base_immune_to_freeze"]=2,
	["base_immune_to_ignite"]=3,
	["base_immune_to_shock"]=4,
	["immune_to_bleeding"]=5,
	["immune_to_burning"]=6,
	["immune_to_poison"]=7,
	parent="stat_descriptions"
}