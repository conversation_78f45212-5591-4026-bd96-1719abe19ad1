-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games
local itemBases = ...

itemBases["Ruby"] = {
	type = "Jewel",
	tags = { strjewel = true, jewel = true, default = true, },
	implicitModTypes = { },
	req = { },
}
itemBases["Emerald"] = {
	type = "Jewel",
	tags = { dexjewel = true, jewel = true, default = true, },
	implicitModTypes = { },
	req = { },
}
itemBases["Sapphire"] = {
	type = "Jewel",
	tags = { jewel = true, intjewel = true, default = true, },
	implicitModTypes = { },
	req = { },
}
itemBases["Diamond"] = {
	type = "Jewel",
	tags = { jewel = true, not_for_sale = true, default = true, },
	implicitModTypes = { },
	req = { },
}

itemBases["Time-Lost Ruby"] = {
	type = "Jewel",
	subType = "Radius",
	tags = { str_radius_jewel = true, not_for_sale = true, jewel = true, radius_jewel = true, default = true, },
	implicitModTypes = { },
	req = { },
}
itemBases["Time-Lost Emerald"] = {
	type = "Jewel",
	subType = "Radius",
	tags = { not_for_sale = true, jewel = true, dex_radius_jewel = true, radius_jewel = true, default = true, },
	implicitModTypes = { },
	req = { },
}
itemBases["Time-Lost Sapphire"] = {
	type = "Jewel",
	subType = "Radius",
	tags = { not_for_sale = true, radius_jewel = true, jewel = true, int_radius_jewel = true, default = true, },
	implicitModTypes = { },
	req = { },
}
itemBases["Time-Lost Diamond"] = {
	type = "Jewel",
	subType = "Radius",
	tags = { not_for_sale = true, jewel = true, radius_jewel = true, default = true, },
	implicitModTypes = { },
	req = { },
}

itemBases["Timeless Jewel"] = {
	type = "Jewel",
	subType = "Timeless",
	tags = { dexjewel = true, not_for_sale = true, strjewel = true, jewel = true, default = true, intjewel = true, },
	implicitModTypes = { },
	req = { },
}
