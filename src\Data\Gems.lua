-- This file is automatically generated, do not edit!
-- Gem data (c) Grinding Gear Games

return {
	["Metadata/Items/Gems/SkillGemIceNova"] = {
		name = "Ice Nova",
		baseTypeName = "Ice Nova",
		gameId = "Metadata/Items/Gems/SkillGemIceNova",
		variantId = "IceNova",
		grantedEffectId = "IceNovaPlayer",
		additionalStatSet1 = "IceNovaPlayerOnFrostbolt",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			cold = true,
			nova = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Cold, Nova",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemLeapSlam"] = {
		name = "Leap Slam",
		baseTypeName = "Leap Slam",
		gameId = "Metadata/Items/Gems/SkillGemLeapSlam",
		variantId = "LeapSlam",
		grantedEffectId = "LeapSlamPlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			slam = true,
			travel = true,
			payoff = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Slam, Travel, Payoff",
		weaponRequirements = "One Hand Mace, Two Hand Mace",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemShieldCharge"] = {
		name = "Shield Charge",
		baseTypeName = "Shield Charge",
		gameId = "Metadata/Items/Gems/SkillGemShieldCharge",
		variantId = "ShieldCharge",
		grantedEffectId = "ShieldChargePlayer",
		additionalStatSet1 = "ShieldChargeFinalConePlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			physical = true,
			channelling = true,
			travel = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Physical, Channelling, Travel",
		weaponRequirements = "Armoured Shield",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemDetonateDead"] = {
		name = "Detonate Dead",
		baseTypeName = "Detonate Dead",
		gameId = "Metadata/Items/Gems/SkillGemDetonateDead",
		variantId = "DetonateDead",
		grantedEffectId = "DetonateDeadPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			physical = true,
			fire = true,
			detonator = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Physical, Fire, Detonator",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemVolatileDead"] = {
		name = "Volatile Dead",
		baseTypeName = "Volatile Dead",
		gameId = "Metadata/Items/Gems/SkillGemVolatileDead",
		variantId = "VolatileDead",
		grantedEffectId = "VolatileDeadPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			fire = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Fire",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemAddedFireDamageSupport"] = {
		name = "Fire Infusion",
		gameId = "Metadata/Items/Gems/SupportGemFireInfusion",
		variantId = "AddedFireDamageSupport",
		grantedEffectId = "SupportAddedFireDamagePlayer",
		tags = {
			support = true,
			attack = true,
			fire = true,
		},
		gemType = "Support",
		tagString = "Attack, Fire",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFasterAttackSupport"] = {
		name = "Martial Tempo",
		gameId = "Metadata/Items/Gems/SupportGemMartialTempo",
		variantId = "FasterAttackSupport",
		grantedEffectId = "SupportFasterAttackPlayer",
		tags = {
			support = true,
			attack = true,
		},
		gemType = "Support",
		tagString = "Attack",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemMultipleProjectilesSupport"] = {
		name = "Scattershot",
		gameId = "Metadata/Items/Gems/SupportGemScattershot",
		variantId = "MultipleProjectilesSupport",
		grantedEffectId = "SupportMultipleProjectilesPlayer",
		tags = {
			support = true,
			projectile = true,
		},
		gemType = "Support",
		tagString = "Projectile",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFasterProjectilesSupport"] = {
		name = "Acceleration",
		gameId = "Metadata/Items/Gems/SupportGemAcceleration",
		variantId = "FasterProjectilesSupport",
		grantedEffectId = "SupportFasterProjectilesPlayer",
		tags = {
			support = true,
			projectile = true,
		},
		gemType = "Support",
		tagString = "Projectile",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemAddedColdDamageSupport"] = {
		name = "Cold Infusion",
		gameId = "Metadata/Items/Gems/SupportGemColdInfusion",
		variantId = "AddedColdDamageSupport",
		grantedEffectId = "SupportAddedColdDamagePlayer",
		tags = {
			support = true,
			attack = true,
			cold = true,
		},
		gemType = "Support",
		tagString = "Attack, Cold",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemAdditionalAccuracySupport"] = {
		name = "Bullseye",
		gameId = "Metadata/Items/Gems/SupportGemBullseye",
		variantId = "AdditionalAccuracySupport",
		grantedEffectId = "SupportAdditionalAccuracyPlayer",
		tags = {
			support = true,
			attack = true,
		},
		gemType = "Support",
		tagString = "Attack",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemIncreasedAreaOfEffectSupport"] = {
		name = "Magnified Effect",
		gameId = "Metadata/Items/Gems/SupportGemMagnifiedEffect",
		variantId = "IncreasedAreaOfEffectSupport",
		grantedEffectId = "SupportIncreasedAreaOfEffectPlayer",
		tags = {
			support = true,
			area = true,
		},
		gemType = "Support",
		tagString = "AoE",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemAddedLightningDamageSupport"] = {
		name = "Lightning Infusion",
		gameId = "Metadata/Items/Gems/SupportGemLightningInfusion",
		variantId = "AddedLightningDamageSupport",
		grantedEffectId = "SupportAddedLightningDamagePlayer",
		tags = {
			support = true,
			attack = true,
			lightning = true,
		},
		gemType = "Support",
		tagString = "Attack, Lightning",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemRaiseZombie"] = {
		name = "Raise Zombie",
		baseTypeName = "Raise Zombie",
		gameId = "Metadata/Items/Gems/SkillGemRaiseZombie",
		variantId = "RaiseZombie",
		grantedEffectId = "RaiseZombiePlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			minion = true,
			physical = true,
			duration = true,
		},
		gemType = "Minion",
		tagString = "Physical, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemInspirationSupport"] = {
		name = "Inspiration",
		gameId = "Metadata/Items/Gems/SupportGemInspiration",
		variantId = "InspirationSupport",
		grantedEffectId = "SupportInspirationPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemIncreasedCriticalDamageSupport"] = {
		name = "Supercritical",
		gameId = "Metadata/Items/Gems/SupportGemSupercritical",
		variantId = "IncreasedCriticalDamageSupport",
		grantedEffectId = "SupportIncreasedCriticalDamagePlayer",
		tags = {
			support = true,
			critical = true,
		},
		gemType = "Support",
		tagString = "Critical",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemKnockbackSupport"] = {
		name = "Bludgeon",
		gameId = "Metadata/Items/Gems/SupportGemBludgeon",
		variantId = "KnockbackSupport",
		grantedEffectId = "SupportKnockbackPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemLifeLeechSupport"] = {
		name = "Life Thief",
		gameId = "Metadata/Items/Gems/SupportGemLifeThief",
		variantId = "LifeLeechSupport",
		grantedEffectId = "SupportLifeLeechPlayer",
		tags = {
			support = true,
			attack = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Attack, Physical",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemManaLeechSupport"] = {
		name = "Soul Thief",
		gameId = "Metadata/Items/Gems/SupportGemSoulThief",
		variantId = "ManaLeechSupport",
		grantedEffectId = "SupportManaLeechPlayer",
		tags = {
			support = true,
			attack = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Attack, Physical",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemAddedChaosDamageSupport"] = {
		name = "Chaos Infusion",
		gameId = "Metadata/Items/Gems/SupportGemChaosInfusion",
		variantId = "AddedChaosDamageSupport",
		grantedEffectId = "SupportAddedChaosDamagePlayer",
		tags = {
			support = true,
			attack = true,
			chaos = true,
		},
		gemType = "Support",
		tagString = "Attack, Chaos",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFlickerStrike"] = {
		name = "Flicker Strike",
		baseTypeName = "Flicker Strike",
		gameId = "Metadata/Items/Gems/SkillGemFlickerStrike",
		variantId = "FlickerStrike",
		grantedEffectId = "FlickerStrikePlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike",
		weaponRequirements = "Quarterstaff",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 13,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSpark"] = {
		name = "Spark",
		baseTypeName = "Spark",
		gameId = "Metadata/Items/Gems/SkillGemSpark",
		variantId = "Spark",
		grantedEffectId = "SparkPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			projectile = true,
			lightning = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, Projectile, Lightning, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPierceSupport"] = {
		name = "Pierce",
		gameId = "Metadata/Items/Gems/SupportGemPierce",
		variantId = "PierceSupport",
		grantedEffectId = "SupportPiercePlayer",
		tags = {
			support = true,
			projectile = true,
		},
		gemType = "Support",
		tagString = "Projectile",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemSummonSpectre"] = {
		name = "Spectre: {0}",
		baseTypeName = "Spectre: {0} ",
		gameId = "Metadata/Items/Gems/SkillGemSummonSpectre",
		variantId = "SummonSpectre",
		grantedEffectId = "SummonSpectrePlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			minion = true,
			persistent = true,
		},
		gemType = "Barrageable",
		tagString = "Minion, Persistent",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemFrostWall"] = {
		name = "Frost Wall",
		baseTypeName = "Frost Wall",
		gameId = "Metadata/Items/Gems/SkillGemFrostWall",
		variantId = "FrostWall",
		grantedEffectId = "FrostWallPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			area = true,
			cold = true,
			duration = true,
		},
		gemType = "Spell",
		tagString = "AoE, Cold, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemShockNova"] = {
		name = "Shock Nova",
		baseTypeName = "Shock Nova",
		gameId = "Metadata/Items/Gems/SkillGemShockNova",
		variantId = "ShockNova",
		grantedEffectId = "ShockNovaPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			lightning = true,
			nova = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Lightning, Nova",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemMeleePhysicalDamageSupport"] = {
		name = "Heavy Swing",
		gameId = "Metadata/Items/Gems/SupportGemHeavySwing",
		variantId = "MeleePhysicalDamageSupport",
		grantedEffectId = "SupportMeleePhysicalDamagePlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee, Physical",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFasterCastSupport"] = {
		name = "Arcane Tempo",
		gameId = "Metadata/Items/Gems/SupportGemArcaneTempo",
		variantId = "FasterCastSupport",
		grantedEffectId = "SupportFasterCastPlayer",
		tags = {
			support = true,
			spell = true,
		},
		gemType = "Support",
		tagString = "Spell",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemTemporalChains"] = {
		name = "Temporal Chains",
		baseTypeName = "Temporal Chains",
		gameId = "Metadata/Items/Gems/SkillGemTemporalChains",
		variantId = "TemporalChains",
		grantedEffectId = "TemporalChainsPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			duration = true,
			curse = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Duration, Curse",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 13,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemEnfeeble"] = {
		name = "Enfeeble",
		baseTypeName = "Enfeeble",
		gameId = "Metadata/Items/Gems/SkillGemEnfeeble",
		variantId = "Enfeeble",
		grantedEffectId = "EnfeeblePlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			duration = true,
			curse = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Duration, Curse",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSnipersMark"] = {
		name = "Sniper's Mark",
		baseTypeName = "Sniper's Mark",
		gameId = "Metadata/Items/Gems/SkillGemSnipersMark",
		variantId = "SnipersMark",
		grantedEffectId = "SnipersMarkPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			mark = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, Mark, Duration",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemDespair"] = {
		name = "Despair",
		baseTypeName = "Despair",
		gameId = "Metadata/Items/Gems/SkillGemDespair",
		variantId = "Despair",
		grantedEffectId = "DespairPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			chaos = true,
			duration = true,
			curse = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Chaos, Duration, Curse",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemLightningWarp"] = {
		name = "Lightning Warp",
		baseTypeName = "Lightning Warp",
		gameId = "Metadata/Items/Gems/SkillGemLightningWarp",
		variantId = "LightningWarp",
		grantedEffectId = "LightningWarpPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			lightning = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Lightning, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemArmourBreaker"] = {
		name = "Armour Breaker",
		baseTypeName = "Armour Breaker",
		gameId = "Metadata/Items/Gems/SkillGemArmourBreaker",
		variantId = "ArmourBreaker",
		grantedEffectId = "ArmourBreakerPlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
			physical = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike, Physical",
		weaponRequirements = "One Hand Mace, Two Hand Mace",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemRainOfArrows"] = {
		name = "Rain of Arrows",
		baseTypeName = "Rain of Arrows",
		gameId = "Metadata/Items/Gems/SkillGemRainOfArrows",
		variantId = "RainOfArrows",
		grantedEffectId = "RainOfArrowsPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			sustained = true,
		},
		gemType = "Attack",
		tagString = "AoE, Projectile, Sustained",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemConcentratedEffectSupport"] = {
		name = "Concentrated Effect",
		gameId = "Metadata/Items/Gems/SupportGemConcentratedEffect",
		variantId = "ConcentratedEffectSupport",
		grantedEffectId = "SupportConcentratedEffectPlayer",
		tags = {
			support = true,
			area = true,
		},
		gemType = "Support",
		tagString = "AoE",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemBloodlustSupport"] = {
		name = "Bloodlust",
		gameId = "Metadata/Items/Gems/SupportGemBloodlust",
		variantId = "BloodlustSupport",
		grantedEffectId = "SupportBloodlustPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee, Physical",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFirestorm"] = {
		name = "Firestorm",
		baseTypeName = "Firestorm",
		gameId = "Metadata/Items/Gems/SkillGemFirestorm",
		variantId = "Firestorm",
		grantedEffectId = "FirestormPlayer",
		additionalStatSet1 = "FirestormEmpoweredPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			sustained = true,
			fire = true,
			duration = true,
			payoff = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Sustained, Fire, Duration, Payoff",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 11,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemFallingThunder"] = {
		name = "Falling Thunder",
		baseTypeName = "Falling Thunder",
		gameId = "Metadata/Items/Gems/SkillGemFallingThunder",
		variantId = "FallingThunder",
		grantedEffectId = "FallingThunderPlayer",
		additionalStatSet1 = "FallingThunderProjectilePlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			slam = true,
			projectile = true,
			lightning = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Slam, Projectile, Lightning",
		weaponRequirements = "Quarterstaff",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPowerSiphon"] = {
		name = "Power Siphon",
		baseTypeName = "Power Siphon",
		gameId = "Metadata/Items/Gems/SkillGemPowerSiphon",
		variantId = "PowerSiphon",
		grantedEffectId = "PowerSiphonPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			physical = true,
		},
		gemType = "Spell",
		tagString = "Physical",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemLightningArrow"] = {
		name = "Lightning Arrow",
		baseTypeName = "Lightning Arrow",
		gameId = "Metadata/Items/Gems/SkillGemLightningArrow",
		variantId = "LightningArrow",
		grantedEffectId = "LightningArrowPlayer",
		additionalStatSet1 = "LightningArrowArcPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			area = true,
			projectile = true,
			lightning = true,
			chaining = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, AoE, Projectile, Lightning, Chaining",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemDiscipline"] = {
		name = "Discipline",
		baseTypeName = "Discipline",
		gameId = "Metadata/Items/Gems/SkillGemDiscipline",
		variantId = "Discipline",
		grantedEffectId = "DisciplinePlayer",
		tags = {
			strength = true,
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			aura = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Aura",
		reqStr = 25,
		reqDex = 0,
		reqInt = 75,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemShockwaveTotem"] = {
		name = "Shockwave Totem",
		baseTypeName = "Shockwave Totem",
		gameId = "Metadata/Items/Gems/SkillGemShockwaveTotem",
		variantId = "ShockwaveTotem",
		grantedEffectId = "ShockwaveTotemPlayer",
		additionalGrantedEffectId1 = "ShockwaveTotemQuakePlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			attack = true,
			totem = true,
			area = true,
			melee = true,
			slam = true,
			physical = true,
			duration = true,
			nova = true,
		},
		gemType = "Attack",
		tagString = "Totem, AoE, Melee, Slam, Physical, Duration, Nova",
		weaponRequirements = "Any Martial Weapon",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemBlindSupport"] = {
		name = "Blind",
		gameId = "Metadata/Items/Gems/SupportGemBlind",
		variantId = "BlindSupport",
		grantedEffectId = "SupportBlindPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemArc"] = {
		name = "Arc",
		baseTypeName = "Arc",
		gameId = "Metadata/Items/Gems/SkillGemArc",
		variantId = "Arc",
		grantedEffectId = "ArcPlayer",
		additionalStatSet1 = "ArcExplosionPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			lightning = true,
			chaining = true,
			payoff = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Lightning, Chaining, Payoff",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemArcticArmour"] = {
		name = "Arctic Armour",
		baseTypeName = "Arctic Armour",
		gameId = "Metadata/Items/Gems/SkillGemArcticArmour",
		variantId = "ArcticArmour",
		grantedEffectId = "ArcticArmourPlayer",
		tags = {
			buff = true,
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			persistent = true,
			sustained = true,
			cold = true,
			stages = true,
		},
		gemType = "Buff",
		tagString = "Spell, Persistent, Sustained, Cold, Staged",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 4,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemFirePenetrationSupport"] = {
		name = "Fire Penetration",
		gameId = "Metadata/Items/Gems/SupportGemFirePenetration",
		variantId = "FirePenetrationSupport",
		grantedEffectId = "SupportFirePenetrationPlayer",
		tags = {
			support = true,
			fire = true,
		},
		gemType = "Support",
		tagString = "Fire",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemColdPenetrationSupport"] = {
		name = "Cold Penetration",
		gameId = "Metadata/Items/Gems/SupportGemColdPenetration",
		variantId = "ColdPenetrationSupport",
		grantedEffectId = "SupportColdPenetrationPlayer",
		tags = {
			support = true,
			cold = true,
		},
		gemType = "Support",
		tagString = "Cold",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemLightningPenetrationSupport"] = {
		name = "Lightning Penetration",
		gameId = "Metadata/Items/Gems/SupportGemLightningPenetration",
		variantId = "LightningPenetrationSupport",
		grantedEffectId = "SupportLightningPenetrationPlayer",
		tags = {
			support = true,
			lightning = true,
		},
		gemType = "Support",
		tagString = "Lightning",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemChainSupport"] = {
		name = "Chain",
		gameId = "Metadata/Items/Gems/SupportGemChain",
		variantId = "ChainSupport",
		grantedEffectId = "SupportChainPlayer",
		tags = {
			support = true,
			projectile = true,
			chaining = true,
		},
		gemType = "Support",
		tagString = "Projectile, Chaining",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemForkSupport"] = {
		name = "Fork",
		gameId = "Metadata/Items/Gems/SupportGemFork",
		variantId = "ForkSupport",
		grantedEffectId = "SupportForkPlayer",
		tags = {
			support = true,
			projectile = true,
		},
		gemType = "Support",
		tagString = "Projectile",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFlammability"] = {
		name = "Flammability",
		baseTypeName = "Flammability",
		gameId = "Metadata/Items/Gems/SkillGemFlammability",
		variantId = "Flammability",
		grantedEffectId = "FlammabilityPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			fire = true,
			duration = true,
			curse = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Fire, Duration, Curse",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemHypothermia"] = {
		name = "Hypothermia",
		baseTypeName = "Hypothermia",
		gameId = "Metadata/Items/Gems/SkillGemHypothermia",
		variantId = "Hypothermia",
		grantedEffectId = "HypothermiaPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			cold = true,
			duration = true,
			curse = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Cold, Duration, Curse",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemConductivity"] = {
		name = "Conductivity",
		baseTypeName = "Conductivity",
		gameId = "Metadata/Items/Gems/SkillGemConductivity",
		variantId = "Conductivity",
		grantedEffectId = "ConductivityPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			lightning = true,
			duration = true,
			curse = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Lightning, Duration, Curse",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemIncinerate"] = {
		name = "Incinerate",
		baseTypeName = "Incinerate",
		gameId = "Metadata/Items/Gems/SkillGemIncinerate",
		variantId = "Incinerate",
		grantedEffectId = "IncineratePlayer",
		additionalStatSet1 = "IncinerateGroundPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			area = true,
			sustained = true,
			fire = true,
			duration = true,
			channelling = true,
			stages = true,
		},
		gemType = "Spell",
		tagString = "AoE, Sustained, Fire, Duration, Channelling, Staged",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSpellEchoSupport"] = {
		name = "Spell Echo",
		gameId = "Metadata/Items/Gems/SupportGemSpellEcho",
		variantId = "SpellEchoSupport",
		grantedEffectId = "SupportSpellEchoPlayer",
		tags = {
			support = true,
			spell = true,
			area = true,
		},
		gemType = "Support",
		tagString = "Spell, AoE",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemElementalArmySupport"] = {
		name = "Elemental Army",
		gameId = "Metadata/Items/Gems/SupportGemElementalArmy",
		variantId = "ElementalArmySupport",
		grantedEffectId = "SupportElementalArmyPlayer",
		tags = {
			support = true,
			minion = true,
		},
		gemType = "Support",
		tagString = "Minion",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemSlowerProjectilesSupport"] = {
		name = "Deceleration",
		gameId = "Metadata/Items/Gems/SupportGemDeceleration",
		variantId = "SlowerProjectilesSupport",
		grantedEffectId = "SupportSlowerProjectilesPlayer",
		tags = {
			support = true,
			projectile = true,
		},
		gemType = "Support",
		tagString = "Projectile",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemPurityOfFire"] = {
		name = "Purity of Fire",
		baseTypeName = "Purity of Fire",
		gameId = "Metadata/Items/Gems/SkillGemPurityOfFire",
		variantId = "PurityOfFire",
		grantedEffectId = "PurityOfFirePlayer",
		tags = {
			strength = true,
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			aura = true,
			fire = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Aura, Fire",
		reqStr = 25,
		reqDex = 0,
		reqInt = 75,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPurityOfIce"] = {
		name = "Purity of Ice",
		baseTypeName = "Purity of Ice",
		gameId = "Metadata/Items/Gems/SkillGemPurityOfIce",
		variantId = "PurityOfIce",
		grantedEffectId = "PurityOfIcePlayer",
		tags = {
			strength = true,
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			aura = true,
			cold = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Aura, Cold",
		reqStr = 25,
		reqDex = 0,
		reqInt = 75,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPurityOfLightning"] = {
		name = "Purity of Lightning",
		baseTypeName = "Purity of Lightning",
		gameId = "Metadata/Items/Gems/SkillGemPurityOfLightning",
		variantId = "PurityOfLightning",
		grantedEffectId = "PurityOfLightningPlayer",
		tags = {
			strength = true,
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			aura = true,
			lightning = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Aura, Lightning",
		reqStr = 25,
		reqDex = 0,
		reqInt = 75,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemImpurity"] = {
		name = "Impurity",
		baseTypeName = "Impurity",
		gameId = "Metadata/Items/Gems/SkillGemImpurity",
		variantId = "Impurity",
		grantedEffectId = "ImpurityPlayer",
		tags = {
			strength = true,
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			aura = true,
			chaos = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Aura, Chaos",
		reqStr = 25,
		reqDex = 0,
		reqInt = 75,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemFlameblast"] = {
		name = "Flameblast",
		baseTypeName = "Flameblast",
		gameId = "Metadata/Items/Gems/SkillGemFlameblast",
		variantId = "Flameblast",
		grantedEffectId = "FlameblastPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			area = true,
			fire = true,
			channelling = true,
			nova = true,
			stages = true,
		},
		gemType = "Spell",
		tagString = "AoE, Fire, Channelling, Nova, Staged",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 13,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemBarrage"] = {
		name = "Barrage",
		baseTypeName = "Barrage",
		gameId = "Metadata/Items/Gems/SkillGemBarrage",
		variantId = "Barrage",
		grantedEffectId = "BarragePlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			spell = true,
			buff = true,
			duration = true,
		},
		gemType = "Spell",
		tagString = "Buff, Duration",
		weaponRequirements = "Spear, Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemBallLightning"] = {
		name = "Ball Lightning",
		baseTypeName = "Ball Lightning",
		gameId = "Metadata/Items/Gems/SkillGemBallLightning",
		variantId = "BallLightning",
		grantedEffectId = "BallLightningPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			projectile = true,
			sustained = true,
			lightning = true,
			chaining = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Projectile, Sustained, Lightning, Chaining",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 11,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemBoneOffering"] = {
		name = "Bone Offering",
		baseTypeName = "Bone Offering",
		gameId = "Metadata/Items/Gems/SkillGemBoneOffering",
		variantId = "BoneOffering",
		grantedEffectId = "BoneOfferingPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			minion = true,
			buff = true,
			area = true,
			physical = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Minion, Buff, AoE, Physical, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 11,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemHeraldOfAsh"] = {
		name = "Herald of Ash",
		baseTypeName = "Herald of Ash",
		gameId = "Metadata/Items/Gems/SkillGemHeraldOfAsh",
		variantId = "HeraldOfAsh",
		grantedEffectId = "HeraldOfAshPlayer",
		additionalStatSet1 = "HeraldOfAshOnKillPlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			area = true,
			fire = true,
			duration = true,
			herald = true,
		},
		gemType = "Buff",
		tagString = "Persistent, AoE, Fire, Duration, Herald",
		weaponRequirements = "Any Martial Weapon",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 4,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemHeraldOfIce"] = {
		name = "Herald of Ice",
		baseTypeName = "Herald of Ice",
		gameId = "Metadata/Items/Gems/SkillGemHeraldOfIce",
		variantId = "HeraldOfIce",
		grantedEffectId = "HeraldOfIcePlayer",
		additionalStatSet1 = "HeraldOfIceOnKillPlayer",
		tags = {
			buff = true,
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			persistent = true,
			area = true,
			cold = true,
			herald = true,
			payoff = true,
		},
		gemType = "Buff",
		tagString = "Attack, Persistent, AoE, Cold, Herald, Payoff",
		weaponRequirements = "Any Martial Weapon",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 4,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemHeraldOfThunder"] = {
		name = "Herald of Thunder",
		baseTypeName = "Herald of Thunder",
		gameId = "Metadata/Items/Gems/SkillGemHeraldOfThunder",
		variantId = "HeraldOfThunder",
		grantedEffectId = "HeraldOfThunderPlayer",
		additionalStatSet1 = "HeraldOfThunderOnKillPlayer",
		tags = {
			buff = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			persistent = true,
			area = true,
			lightning = true,
			herald = true,
			payoff = true,
		},
		gemType = "Buff",
		tagString = "Attack, Persistent, AoE, Lightning, Herald, Payoff",
		weaponRequirements = "Any Martial Weapon",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 4,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemBlasphemy"] = {
		name = "Blasphemy",
		baseTypeName = "Blasphemy",
		gameId = "Metadata/Items/Gems/SkillGemBlasphemy",
		variantId = "Blasphemy",
		grantedEffectId = "BlasphemyPlayer",
		additionalGrantedEffectId1 = "SupportBlasphemyPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			area = true,
			aura = true,
			meta = true,
		},
		gemType = "Buff",
		tagString = "Persistent, AoE, Aura, Meta",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 8,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemInfernallCry"] = {
		name = "Infernal Cry",
		baseTypeName = "Infernal Cry",
		gameId = "Metadata/Items/Gems/SkillGemInfernalCry",
		variantId = "InfernallCry",
		grantedEffectId = "InfernalCryPlayer",
		additionalGrantedEffectId1 = "InfernalCryCorpseExplosionPlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			warcry = true,
			area = true,
			trigger = true,
			fire = true,
			duration = true,
		},
		gemType = "Warcry",
		tagString = "AoE, Trigger, Fire, Duration",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemIceBiteSupport"] = {
		name = "Ice Bite",
		gameId = "Metadata/Items/Gems/SupportGemIceBite",
		variantId = "IceBiteSupport",
		grantedEffectId = "SupportIceBitePlayer",
		tags = {
			support = true,
			attack = true,
			cold = true,
			duration = true,
		},
		gemType = "Support",
		tagString = "Attack, Cold, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemGlaciationSupport"] = {
		name = "Glaciation",
		gameId = "Metadata/Items/Gems/SupportGemGlaciation",
		variantId = "GlaciationSupport",
		grantedEffectId = "SupportGlaciationPlayer",
		tags = {
			support = true,
			cold = true,
		},
		gemType = "Support",
		tagString = "Cold",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemChanceToShockSupport"] = {
		name = "Conduction",
		gameId = "Metadata/Items/Gems/SupportGemConduction",
		variantId = "ChanceToShockSupport",
		grantedEffectId = "SupportChanceToShockPlayer",
		tags = {
			support = true,
			lightning = true,
		},
		gemType = "Support",
		tagString = "Lightning",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFrostBomb"] = {
		name = "Frost Bomb",
		baseTypeName = "Frost Bomb",
		gameId = "Metadata/Items/Gems/SkillGemFrostBomb",
		variantId = "FrostBomb",
		grantedEffectId = "FrostBombPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			area = true,
			cold = true,
			duration = true,
			orb = true,
		},
		gemType = "Spell",
		tagString = "AoE, Cold, Duration, Orb",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemOrbOfStorms"] = {
		name = "Orb of Storms",
		baseTypeName = "Orb of Storms",
		gameId = "Metadata/Items/Gems/SkillGemOrbOfStorms",
		variantId = "OrbOfStorms",
		grantedEffectId = "OrbOfStormsPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			area = true,
			sustained = true,
			lightning = true,
			duration = true,
			chaining = true,
			orb = true,
		},
		gemType = "Spell",
		tagString = "AoE, Sustained, Lightning, Duration, Chaining, Orb",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemEarthquake"] = {
		name = "Earthquake",
		baseTypeName = "Earthquake",
		gameId = "Metadata/Items/Gems/SkillGemEarthquake",
		variantId = "Earthquake",
		grantedEffectId = "EarthquakePlayer",
		additionalStatSet1 = "EarthquakeAftershockPlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			slam = true,
			duration = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Slam, Duration",
		weaponRequirements = "One Hand Mace, Two Hand Mace",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemContagion"] = {
		name = "Contagion",
		baseTypeName = "Contagion",
		gameId = "Metadata/Items/Gems/SkillGemContagion",
		variantId = "Contagion",
		grantedEffectId = "ContagionPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			chaos = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Chaos, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemWither"] = {
		name = "Wither",
		baseTypeName = "Wither",
		gameId = "Metadata/Items/Gems/SkillGemWither",
		variantId = "Wither",
		grantedEffectId = "WitherPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			area = true,
			chaos = true,
			duration = true,
			channelling = true,
		},
		gemType = "Spell",
		tagString = "AoE, Chaos, Duration, Channelling",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemEssenceDrain"] = {
		name = "Essence Drain",
		baseTypeName = "Essence Drain",
		gameId = "Metadata/Items/Gems/SkillGemEssenceDrain",
		variantId = "EssenceDrain",
		grantedEffectId = "EssenceDrainPlayer",
		additionalStatSet1 = "EssenceDrainDotPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			projectile = true,
			chaos = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, Projectile, Chaos, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemControlledDestructionSupport"] = {
		name = "Controlled Destruction",
		gameId = "Metadata/Items/Gems/SupportGemControlledDestruction",
		variantId = "ControlledDestructionSupport",
		grantedEffectId = "SupportControlledDestructionPlayer",
		tags = {
			support = true,
			spell = true,
			critical = true,
		},
		gemType = "Support",
		tagString = "Spell, Critical",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemSunder"] = {
		name = "Sunder",
		baseTypeName = "Sunder",
		gameId = "Metadata/Items/Gems/SkillGemSunder",
		variantId = "Sunder",
		grantedEffectId = "SunderPlayer",
		additionalStatSet1 = "SunderShockwavePlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			slam = true,
			payoff = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Slam, Payoff",
		weaponRequirements = "One Hand Mace, Two Hand Mace",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemFrostBolt"] = {
		name = "Frostbolt",
		baseTypeName = "Frostbolt",
		gameId = "Metadata/Items/Gems/SkillGemFrostbolt",
		variantId = "FrostBolt",
		grantedEffectId = "FrostboltPlayer",
		additionalStatSet1 = "FrostboltExplosionPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			projectile = true,
			cold = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Projectile, Cold, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemElementalFocusSupport"] = {
		name = "Elemental Focus",
		gameId = "Metadata/Items/Gems/SupportGemElementalFocus",
		variantId = "ElementalFocusSupport",
		grantedEffectId = "SupportElementalFocusPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemSkeletalSniper"] = {
		name = "Skeletal Sniper",
		baseTypeName = "Skeletal Sniper Minion",
		gameId = "Metadata/Items/Gems/SkillGemSkeletalWarrior",
		variantId = "SkeletalSniper",
		grantedEffectId = "SummonSkeletalSnipersPlayer",
		additionalGrantedEffectId1 = "CommandSkeletalSniperPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			minion = true,
			persistent = true,
			physical = true,
			fire = true,
			chaos = true,
		},
		gemType = "Minion",
		tagString = "Persistent, Physical, Fire, Chaos",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSkeletalReaver"] = {
		name = "Skeletal Reaver",
		baseTypeName = "Skeletal Reaver Minion",
		gameId = "Metadata/Items/Gems/SkillGemSkeletalReaver",
		variantId = "SkeletalReaver",
		grantedEffectId = "SummonSkeletalReaversPlayer",
		additionalGrantedEffectId1 = "CommandSkeletalReaversPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			minion = true,
			persistent = true,
			physical = true,
		},
		gemType = "Minion",
		tagString = "Persistent, Physical",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSkeletalBrute"] = {
		name = "Skeletal Brute",
		baseTypeName = "Skeletal Brute Minion",
		gameId = "Metadata/Items/Gems/SkillGemSkeletalBrute",
		variantId = "SkeletalBrute",
		grantedEffectId = "SummonSkeletalBrutesPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			minion = true,
			persistent = true,
			physical = true,
		},
		gemType = "Minion",
		tagString = "Persistent, Physical",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 13,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSkeletalSniper"] = {
		name = "Skeletal Sniper",
		baseTypeName = "Skeletal Sniper Minion",
		gameId = "Metadata/Items/Gems/SkillGemSkeletalSniper",
		variantId = "SkeletalSniper",
		grantedEffectId = "SummonSkeletalSnipersPlayer",
		additionalGrantedEffectId1 = "CommandSkeletalSniperPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			minion = true,
			persistent = true,
			physical = true,
			fire = true,
			chaos = true,
		},
		gemType = "Minion",
		tagString = "Persistent, Physical, Fire, Chaos",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSkeletalFrostMage"] = {
		name = "Skeletal Frost Mage",
		baseTypeName = "Skeletal Frost Mage Minion",
		gameId = "Metadata/Items/Gems/SkillGemSkeletalFrostMage",
		variantId = "SkeletalFrostMage",
		grantedEffectId = "SummonSkeletalFrostMagesPlayer",
		additionalGrantedEffectId1 = "CommandSkeletalFrostMagePlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			minion = true,
			persistent = true,
			cold = true,
		},
		gemType = "Minion",
		tagString = "Persistent, Cold",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSkeletalStormMage"] = {
		name = "Skeletal Storm Mage",
		baseTypeName = "Skeletal Storm Mage Minion",
		gameId = "Metadata/Items/Gems/SkillGemSkeletalStormMage",
		variantId = "SkeletalStormMage",
		grantedEffectId = "SummonSkeletalStormMagesPlayer",
		additionalGrantedEffectId1 = "CommandSkeletalStormMagePlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			minion = true,
			persistent = true,
			lightning = true,
		},
		gemType = "Minion",
		tagString = "Persistent, Lightning",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 11,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSkeletalArsonist"] = {
		name = "Skeletal Arsonist",
		baseTypeName = "Skeletal Arsonist Minion",
		gameId = "Metadata/Items/Gems/SkillGemSkeletalArsonist",
		variantId = "SkeletalArsonist",
		grantedEffectId = "SummonSkeletalArsonistsPlayer",
		additionalGrantedEffectId1 = "CommandSkeletalArsonistPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			minion = true,
			persistent = true,
			fire = true,
			detonator = true,
		},
		gemType = "Minion",
		tagString = "Persistent, Fire, Detonator",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSkeletalCleric"] = {
		name = "Skeletal Cleric",
		baseTypeName = "Skeletal Cleric Minion",
		gameId = "Metadata/Items/Gems/SkillGemSkeletalCleric",
		variantId = "SkeletalCleric",
		grantedEffectId = "SummonSkeletalClericsPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			minion = true,
			persistent = true,
		},
		gemType = "Minion",
		tagString = "Persistent",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 13,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemWildfireSupport"] = {
		name = "Wildfire",
		gameId = "Metadata/Items/Gems/SupportGemWildfire",
		variantId = "WildfireSupport",
		grantedEffectId = "SupportWildfirePlayer",
		tags = {
			support = true,
			area = true,
			fire = true,
		},
		gemType = "Support",
		tagString = "AoE, Fire",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemChanceToBleedSupport"] = {
		name = "Lacerate",
		gameId = "Metadata/Items/Gems/SupportGemLacerate",
		variantId = "ChanceToBleedSupport",
		grantedEffectId = "SupportChanceToBleedPlayer",
		tags = {
			support = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Physical",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemChanceToPoisonSupport"] = {
		name = "Envenom",
		gameId = "Metadata/Items/Gems/SupportGemEnvenom",
		variantId = "ChanceToPoisonSupport",
		grantedEffectId = "SupportChanceToPoisonPlayer",
		tags = {
			support = true,
			chaos = true,
		},
		gemType = "Support",
		tagString = "Chaos",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemMaimSupport"] = {
		name = "Maim",
		gameId = "Metadata/Items/Gems/SupportGemMaim",
		variantId = "MaimSupport",
		grantedEffectId = "SupportMaimPlayer",
		tags = {
			support = true,
			attack = true,
		},
		gemType = "Support",
		tagString = "Attack",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemImmolateSupport"] = {
		name = "Immolate",
		gameId = "Metadata/Items/Gems/SupportGemImmolate",
		variantId = "ImmolateSupport",
		grantedEffectId = "SupportImmolatePlayer",
		tags = {
			support = true,
			fire = true,
			payoff = true,
		},
		gemType = "Support",
		tagString = "Fire, Payoff",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemLastingShockSupport"] = {
		name = "Lasting Shock",
		gameId = "Metadata/Items/Gems/SupportGemLastingShock",
		variantId = "LastingShockSupport",
		grantedEffectId = "SupportLastingShockPlayer",
		tags = {
			support = true,
			lightning = true,
		},
		gemType = "Support",
		tagString = "Lightning",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemBrutalitySupport"] = {
		name = "Brutality",
		gameId = "Metadata/Items/Gems/SupportGemBrutality",
		variantId = "BrutalitySupport",
		grantedEffectId = "SupportBrutalityPlayer",
		tags = {
			support = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Physical",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemMomentumSupport"] = {
		name = "Momentum",
		gameId = "Metadata/Items/Gems/SupportGemMomentum",
		variantId = "MomentumSupport",
		grantedEffectId = "SupportMomentumPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemArcaneSurgeSupport"] = {
		name = "Arcane Surge",
		gameId = "Metadata/Items/Gems/SupportGemArcaneSurge",
		variantId = "ArcaneSurgeSupport",
		grantedEffectId = "SupportArcaneSurgePlayer",
		tags = {
			support = true,
			spell = true,
			duration = true,
		},
		gemType = "Support",
		tagString = "Spell, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemVulnerability"] = {
		name = "Vulnerability",
		baseTypeName = "Vulnerability",
		gameId = "Metadata/Items/Gems/SkillGemVulnerability",
		variantId = "Vulnerability",
		grantedEffectId = "VulnerabilityPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			physical = true,
			duration = true,
			curse = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Physical, Duration, Curse",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemWitheringTouchSupport"] = {
		name = "Withering Touch",
		gameId = "Metadata/Items/Gems/SupportGemWitheringTouch",
		variantId = "WitheringTouchSupport",
		grantedEffectId = "SupportWitheringTouchPlayer",
		tags = {
			support = true,
			chaos = true,
			duration = true,
		},
		gemType = "Support",
		tagString = "Chaos, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemUnleashSupport"] = {
		name = "Unleash",
		gameId = "Metadata/Items/Gems/SupportGemUnleash",
		variantId = "UnleashSupport",
		grantedEffectId = "SupportUnleashPlayer",
		tags = {
			support = true,
			spell = true,
		},
		gemType = "Support",
		tagString = "Spell",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCloseCombatSupport"] = {
		name = "Close Combat",
		gameId = "Metadata/Items/Gems/SupportGemCloseCombat",
		variantId = "CloseCombatSupport",
		grantedEffectId = "SupportCloseCombatPlayer",
		tags = {
			support = true,
			attack = true,
		},
		gemType = "Support",
		tagString = "Attack",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemRageSupport"] = {
		name = "Rage",
		gameId = "Metadata/Items/Gems/SupportGemRage",
		variantId = "RageSupport",
		grantedEffectId = "SupportRagePlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemPlagueBearer"] = {
		name = "Plague Bearer",
		baseTypeName = "Plague Bearer",
		gameId = "Metadata/Items/Gems/SkillGemPlagueBearer",
		variantId = "PlagueBearer",
		grantedEffectId = "PlagueBearerPlayer",
		additionalGrantedEffectId1 = "PlagueBearerNovaPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			area = true,
			physical = true,
			chaos = true,
			nova = true,
		},
		gemType = "Buff",
		tagString = "Persistent, AoE, Physical, Chaos, Nova",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 4,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemFeedingFrenzySupport"] = {
		name = "Feeding Frenzy",
		gameId = "Metadata/Items/Gems/SupportGemFeedingFrenzy",
		variantId = "FeedingFrenzySupport",
		grantedEffectId = "SupportFeedingFrenzyPlayer",
		tags = {
			support = true,
			minion = true,
		},
		gemType = "Support",
		tagString = "Minion",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemMeatShieldSupport"] = {
		name = "Meat Shield",
		gameId = "Metadata/Items/Gems/SupportGemMeatShield",
		variantId = "MeatShieldSupport",
		grantedEffectId = "SupportMeatShieldPlayer",
		tags = {
			support = true,
			minion = true,
		},
		gemType = "Support",
		tagString = "Minion",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemInfernalLegionSupport"] = {
		name = "Infernal Legion",
		gameId = "Metadata/Items/Gems/SupportGemInfernalLegion",
		variantId = "InfernalLegionSupport",
		grantedEffectId = "SupportInfernalLegionPlayer",
		tags = {
			support = true,
			minion = true,
			area = true,
			fire = true,
		},
		gemType = "Support",
		tagString = "Minion, AoE, Fire",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemArtilleryBallista"] = {
		name = "Artillery Ballista",
		baseTypeName = "Artillery Ballista",
		gameId = "Metadata/Items/Gems/SkillGemArtilleryBallista",
		variantId = "ArtilleryBallista",
		grantedEffectId = "ArtilleryBallistaPlayer",
		additionalGrantedEffectId1 = "ArtilleryBallistaProjectilePlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			totem = true,
			area = true,
			projectile = true,
			fire = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, Totem, AoE, Projectile, Fire, Duration",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSecondWindSupport"] = {
		name = "Second Wind",
		gameId = "Metadata/Items/Gems/SupportGemSecondWind",
		variantId = "SecondWindSupport",
		grantedEffectId = "SupportSecondWindPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemSeismicCry"] = {
		name = "Seismic Cry",
		baseTypeName = "Seismic Cry",
		gameId = "Metadata/Items/Gems/SkillGemSeismicCry",
		variantId = "SeismicCry",
		grantedEffectId = "SeismicCryPlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			warcry = true,
			area = true,
			physical = true,
			duration = true,
		},
		gemType = "Warcry",
		tagString = "AoE, Physical, Duration",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 11,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemEarthshatter"] = {
		name = "Earthshatter",
		baseTypeName = "Earthshatter",
		gameId = "Metadata/Items/Gems/SkillGemEarthshatter",
		variantId = "Earthshatter",
		grantedEffectId = "EarthshatterPlayer",
		additionalStatSet1 = "EarthshatterSpikePlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			slam = true,
			duration = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Slam, Duration",
		weaponRequirements = "One Hand Mace, Two Hand Mace",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSigilOfPower"] = {
		name = "Sigil of Power",
		baseTypeName = "Sigil of Power",
		gameId = "Metadata/Items/Gems/SkillGemSigilOfPower",
		variantId = "SigilOfPower",
		grantedEffectId = "SigilOfPowerPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			area = true,
			duration = true,
			stages = true,
		},
		gemType = "Spell",
		tagString = "AoE, Duration, Staged",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemFlameWall"] = {
		name = "Flame Wall",
		baseTypeName = "Flame Wall",
		gameId = "Metadata/Items/Gems/SkillGemFlameWall",
		variantId = "FlameWall",
		grantedEffectId = "FlameWallPlayer",
		additionalStatSet1 = "FlameWallProjectileBuffPlayer",
		additionalStatSet2 = "FlameWallLingeringIgnitePlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			fire = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Fire, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemImpendingDoomSupport"] = {
		name = "Impending Doom",
		gameId = "Metadata/Items/Gems/SupportGemImpendingDoom",
		variantId = "ImpendingDoomSupport",
		grantedEffectId = "ViciousHexSupportPlayer",
		additionalGrantedEffectId1 = "DoomBlastPlayer",
		tags = {
			support = true,
			grants_active_skill = true,
			spell = true,
			area = true,
			trigger = true,
			chaos = true,
		},
		gemType = "Support",
		tagString = "Spell, AoE, Trigger, Chaos",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemHexblast"] = {
		name = "Hexblast",
		baseTypeName = "Hexblast",
		gameId = "Metadata/Items/Gems/SkillGemHexblast",
		variantId = "Hexblast",
		grantedEffectId = "HexblastPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			chaos = true,
			payoff = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Chaos, Payoff",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 11,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemExsanguinate"] = {
		name = "Exsanguinate",
		baseTypeName = "Exsanguinate",
		gameId = "Metadata/Items/Gems/SkillGemExsanguinate",
		variantId = "Exsanguinate",
		grantedEffectId = "ExsanguinatePlayer",
		additionalStatSet1 = "ExsanguinateDotPlayer",
		tags = {
			strength = true,
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			physical = true,
			duration = true,
			chaining = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, Physical, Duration, Chaining",
		reqStr = 50,
		reqDex = 0,
		reqInt = 50,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemReap"] = {
		name = "Reap",
		baseTypeName = "Reap",
		gameId = "Metadata/Items/Gems/SkillGemReap",
		variantId = "Reap",
		grantedEffectId = "ReapPlayer",
		tags = {
			strength = true,
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			physical = true,
			duration = true,
			critical = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Physical, Duration, Critical",
		reqStr = 50,
		reqDex = 0,
		reqInt = 50,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemBloodMagicSupport"] = {
		name = "Lifetap",
		gameId = "Metadata/Items/Gems/SupportGemLifetap",
		variantId = "BloodMagicSupport",
		grantedEffectId = "SupportBloodMagicPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemBeheadSupport"] = {
		name = "Behead",
		gameId = "Metadata/Items/Gems/SupportGemBehead",
		variantId = "BeheadSupport",
		grantedEffectId = "SupportBeheadPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
			strike = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee, Strike",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemExecuteSupport"] = {
		name = "Execute",
		gameId = "Metadata/Items/Gems/SupportGemExecute",
		variantId = "ExecuteSupport",
		grantedEffectId = "SupportExecutePlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemBoneshatter"] = {
		name = "Boneshatter",
		baseTypeName = "Boneshatter",
		gameId = "Metadata/Items/Gems/SkillGemBoneshatter",
		variantId = "Boneshatter",
		grantedEffectId = "BoneshatterPlayer",
		additionalStatSet1 = "BoneshatterShockwavePlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike",
		weaponRequirements = "One Hand Mace, Two Hand Mace",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemEyeOfWinter"] = {
		name = "Eye of Winter",
		baseTypeName = "Eye of Winter",
		gameId = "Metadata/Items/Gems/SkillGemEyeOfWinter",
		variantId = "EyeOfWinter",
		grantedEffectId = "EyeOfWinterPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			projectile = true,
			cold = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, Projectile, Cold",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 13,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemOverchargeSupport"] = {
		name = "Overcharge",
		gameId = "Metadata/Items/Gems/SupportGemOvercharge",
		variantId = "OverchargeSupport",
		grantedEffectId = "SupportOverchargePlayer",
		tags = {
			support = true,
			lightning = true,
		},
		gemType = "Support",
		tagString = "Lightning",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemLightningConduit"] = {
		name = "Lightning Conduit",
		baseTypeName = "Lightning Conduit",
		gameId = "Metadata/Items/Gems/SkillGemLightningConduit",
		variantId = "LightningConduit",
		grantedEffectId = "LightningConduitPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			lightning = true,
			payoff = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Lightning, Payoff",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 13,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemGalvanicField"] = {
		name = "Galvanic Field",
		baseTypeName = "Galvanic Field",
		gameId = "Metadata/Items/Gems/SkillGemGalvanicField",
		variantId = "GalvanicField",
		grantedEffectId = "GalvanicFieldPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			buff = true,
			area = true,
			lightning = true,
			duration = true,
			chaining = true,
			orb = true,
		},
		gemType = "Spell",
		tagString = "Buff, AoE, Lightning, Duration, Chaining, Orb",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemVolcanicFissure"] = {
		name = "Volcanic Fissure",
		baseTypeName = "Volcanic Fissure",
		gameId = "Metadata/Items/Gems/SkillGemVolcanicFissure",
		variantId = "VolcanicFissure",
		grantedEffectId = "VolcanicFissurePlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			slam = true,
			sustained = true,
			fire = true,
			duration = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Slam, Sustained, Fire, Duration",
		weaponRequirements = "One Hand Mace, Two Hand Mace",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemRapidAssault"] = {
		name = "Rapid Assault",
		baseTypeName = "Rapid Assault",
		gameId = "Metadata/Items/Gems/SkillGemRapidAssault",
		variantId = "RapidAssault",
		grantedEffectId = "RapidAssaultPlayer",
		additionalStatSet1 = "RapidAssaultFinalHitPlayer",
		additionalStatSet2 = "RapidAssaultDetonation",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
			physical = true,
			duration = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike, Physical, Duration",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemDisengage"] = {
		name = "Disengage",
		baseTypeName = "Disengage",
		gameId = "Metadata/Items/Gems/SkillGemDisengage",
		variantId = "Disengage",
		grantedEffectId = "DisengagePlayer",
		additionalStatSet1 = "DisengageShockwavePlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
			travel = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike, Travel",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemBloodHunt"] = {
		name = "Blood Hunt",
		baseTypeName = "Blood Hunt",
		gameId = "Metadata/Items/Gems/SkillGemBloodHunt",
		variantId = "BloodHunt",
		grantedEffectId = "BloodHuntPlayer",
		additionalStatSet1 = "BloodHuntExplosionPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
			physical = true,
			payoff = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike, Physical, Payoff",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemWhirlingSlash"] = {
		name = "Whirling Slash",
		baseTypeName = "Whirling Slash",
		gameId = "Metadata/Items/Gems/SkillGemWhirlingSlash",
		variantId = "WhirlingSlash",
		grantedEffectId = "WhirlingSlashPlayer",
		additionalStatSet1 = "WhirlingSlashSandstormPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
			stages = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike, Staged",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSpearfield"] = {
		name = "Spearfield",
		baseTypeName = "Spearfield",
		gameId = "Metadata/Items/Gems/SkillGemSpearfield",
		variantId = "Spearfield",
		grantedEffectId = "SpearfieldPlayer",
		additionalStatSet1 = "SpearfieldHazardPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			sustained = true,
			duration = true,
			hazard = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Sustained, Duration, Hazard",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemLightningSpear"] = {
		name = "Lightning Spear",
		baseTypeName = "Lightning Spear",
		gameId = "Metadata/Items/Gems/SkillGemLightningSpear",
		variantId = "LightningSpear",
		grantedEffectId = "LightningSpearPlayer",
		additionalStatSet1 = "LightningSpearSecondaryProjectilePlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			area = true,
			projectile = true,
			lightning = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, AoE, Projectile, Lightning",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemGlacialLance"] = {
		name = "Glacial Lance",
		baseTypeName = "Glacial Lance",
		gameId = "Metadata/Items/Gems/SkillGemGlacialLance",
		variantId = "GlacialLance",
		grantedEffectId = "GlacialLancePlayer",
		additionalStatSet1 = "GlacialLanceWallsPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			area = true,
			projectile = true,
			cold = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, AoE, Projectile, Cold, Duration",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemWhirlingAssault"] = {
		name = "Whirling Assault",
		baseTypeName = "Whirling Assault",
		gameId = "Metadata/Items/Gems/SkillGemWhirlingAssault",
		variantId = "WhirlingAssault",
		grantedEffectId = "WhirlingAssaultPlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike",
		weaponRequirements = "Quarterstaff",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 11,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemRollingSlam"] = {
		name = "Rolling Slam",
		baseTypeName = "Rolling Slam",
		gameId = "Metadata/Items/Gems/SkillGemRollingSlam",
		variantId = "RollingSlam",
		grantedEffectId = "RollingSlamPlayer",
		additionalStatSet1 = "RollingSlamFirstSlamPlayer",
		additionalStatSet2 = "RollingSlamSecondSlamPlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			slam = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Slam",
		weaponRequirements = "One Hand Mace, Two Hand Mace",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemEscapeShot"] = {
		name = "Escape Shot",
		baseTypeName = "Escape Shot",
		gameId = "Metadata/Items/Gems/SkillGemEscapeShot",
		variantId = "EscapeShot",
		grantedEffectId = "EscapeShotPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			cold = true,
		},
		gemType = "Attack",
		tagString = "AoE, Projectile, Cold",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSuperchargedSlam"] = {
		name = "Supercharged Slam",
		baseTypeName = "Supercharged Slam",
		gameId = "Metadata/Items/Gems/SkillGemSuperchargedSlam",
		variantId = "SuperchargedSlam",
		grantedEffectId = "SuperchargedSlamPlayer",
		additionalStatSet1 = "SuperchargedSlamAftershockPlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			slam = true,
			channelling = true,
			stages = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Slam, Channelling, Staged",
		weaponRequirements = "Two Hand Mace",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 11,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemWindBlast"] = {
		name = "Wind Blast",
		baseTypeName = "Wind Blast",
		gameId = "Metadata/Items/Gems/SkillGemWindBlast",
		variantId = "WindBlast",
		grantedEffectId = "GaleStrikePlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee",
		weaponRequirements = "Quarterstaff",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemWaveOfFrost"] = {
		name = "Wave of Frost",
		baseTypeName = "Wave of Frost",
		gameId = "Metadata/Items/Gems/SkillGemWaveOfFrost",
		variantId = "WaveOfFrost",
		grantedEffectId = "WaveOfFrostPlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			cold = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Cold",
		weaponRequirements = "Quarterstaff",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemIceStrike"] = {
		name = "Ice Strike",
		baseTypeName = "Ice Strike",
		gameId = "Metadata/Items/Gems/SkillGemIceStrike",
		variantId = "IceStrike",
		grantedEffectId = "IceStrikePlayer",
		additionalStatSet1 = "IceStrikeThirdAttackPlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
			cold = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike, Cold",
		weaponRequirements = "Quarterstaff",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemJaggedGroundSupport"] = {
		name = "Jagged Ground",
		gameId = "Metadata/Items/Gems/SupportGemJaggedGround",
		variantId = "JaggedGroundSupport",
		grantedEffectId = "SupportJaggedGroundPlayer",
		tags = {
			support = true,
			attack = true,
			area = true,
			melee = true,
			slam = true,
			duration = true,
		},
		gemType = "Support",
		tagString = "Attack, AoE, Melee, Slam, Duration",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemInevitableCriticalsSupport"] = {
		name = "Inevitable Critical",
		gameId = "Metadata/Items/Gems/SupportGemInevitableCritical",
		variantId = "InevitableCriticalsSupport",
		grantedEffectId = "SupportInevitableCriticalsPlayer",
		tags = {
			support = true,
			critical = true,
		},
		gemType = "Support",
		tagString = "Critical",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemRuthlessSupport"] = {
		name = "Ruthless",
		gameId = "Metadata/Items/Gems/SupportGemRuthless",
		variantId = "RuthlessSupport",
		grantedEffectId = "RuthlessSupportPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemLessDurationSupport"] = {
		name = "Fast Forward",
		gameId = "Metadata/Items/Gems/SupportGemFastForward",
		variantId = "LessDurationSupport",
		grantedEffectId = "LessDurationSupportPlayer",
		tags = {
			support = true,
			duration = true,
		},
		gemType = "Support",
		tagString = "Duration",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFistOfWarSupport"] = {
		name = "Fist of War",
		gameId = "Metadata/Items/Gems/SupportGemFistOfWar",
		variantId = "FistOfWarSupport",
		grantedEffectId = "FistOfWarSupportPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
			slam = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee, Slam",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemUnbreakableSupport"] = {
		name = "Unbreakable",
		gameId = "Metadata/Items/Gems/SupportGemUnbreakable",
		variantId = "UnbreakableSupport",
		grantedEffectId = "UnbreakableSupportPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemMoreDurationSupport"] = {
		name = "Persistence",
		gameId = "Metadata/Items/Gems/SupportGemPersistence",
		variantId = "MoreDurationSupport",
		grantedEffectId = "MoreDurationSupportPlayer",
		tags = {
			support = true,
			duration = true,
		},
		gemType = "Support",
		tagString = "Duration",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemImpactShockwaveSupport"] = {
		name = "Impact Shockwave",
		gameId = "Metadata/Items/Gems/SupportGemImpactShockwave",
		variantId = "ImpactShockwaveSupport",
		grantedEffectId = "ImpactShockwaveSupportPlayer",
		tags = {
			support = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
		},
		gemType = "Support",
		tagString = "Attack, AoE, Melee, Strike",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemHexBloomSupport"] = {
		name = "Hex Bloom",
		gameId = "Metadata/Items/Gems/SupportGemHexBloom",
		variantId = "HexBloomSupport",
		grantedEffectId = "SupportHexBloomPlayer",
		tags = {
			support = true,
			area = true,
			curse = true,
		},
		gemType = "Support",
		tagString = "AoE, Curse",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemGlacialCascade"] = {
		name = "Glacial Cascade",
		baseTypeName = "Glacial Cascade",
		gameId = "Metadata/Items/Gems/SkillGemGlacialCascade",
		variantId = "GlacialCascade",
		grantedEffectId = "GlacialCascadePlayer",
		additionalStatSet1 = "GlacialCascadeLastSpikePlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			cold = true,
			payoff = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Cold, Payoff",
		weaponRequirements = "Quarterstaff",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemComet"] = {
		name = "Comet",
		baseTypeName = "Comet",
		gameId = "Metadata/Items/Gems/SkillGemComet",
		variantId = "Comet",
		grantedEffectId = "CometPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			cold = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Cold",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 11,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemStaffConsecrate"] = {
		name = "Consecrate",
		baseTypeName = "Consecrate",
		gameId = "Metadata/items/Gems/SkillGemStaffConsecrate",
		variantId = "StaffConsecrate",
		grantedEffectId = "StaffConsecratePlayer",
		tags = {
			strength = true,
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			area = true,
			duration = true,
			nova = true,
		},
		gemType = "Spell",
		tagString = "AoE, Duration, Nova",
		reqStr = 50,
		reqDex = 0,
		reqInt = 50,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemRipwireBallista"] = {
		name = "Ripwire Ballista",
		baseTypeName = "Ripwire Ballista",
		gameId = "Metadata/Items/Gems/SkillGemRipwireBallista",
		variantId = "RipwireBallista",
		grantedEffectId = "RipwireBallistaPlayer",
		additionalGrantedEffectId1 = "RipwireBallistaProjectilePlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			totem = true,
			projectile = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, Totem, Projectile, Duration",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemManaTempest"] = {
		name = "Mana Tempest",
		baseTypeName = "Mana Tempest",
		gameId = "Metadata/Items/Gems/SkillGemManaTempest",
		variantId = "ManaTempest",
		grantedEffectId = "ManaTempestPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			buff = true,
			lightning = true,
		},
		gemType = "Spell",
		tagString = "Buff, Lightning",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemKillingPalm"] = {
		name = "Killing Palm",
		baseTypeName = "Killing Palm",
		gameId = "Metadata/Items/Gems/SkillGemKillingPalm",
		variantId = "KillingPalm",
		grantedEffectId = "KillingPalmPlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
			physical = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike, Physical",
		weaponRequirements = "Quarterstaff, Unarmed",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemShatteringPalm"] = {
		name = "Shattering Palm",
		baseTypeName = "Shattering Palm",
		gameId = "Metadata/Items/Gems/SkillGemShatteringPalm",
		variantId = "ShatteringPalm",
		grantedEffectId = "ShatteringPalmPlayer",
		additionalGrantedEffectId1 = "ShatteringPalmExplosionPlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
			cold = true,
			nova = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike, Cold, Nova",
		weaponRequirements = "Quarterstaff, Unarmed",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 11,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemChillingIceSupport"] = {
		name = "Frost Nexus",
		gameId = "Metadata/Items/Gems/SupportGemFrostNexus",
		variantId = "ChillingIceSupport",
		grantedEffectId = "SupportChillingIcePlayer",
		tags = {
			support = true,
			area = true,
			cold = true,
		},
		gemType = "Support",
		tagString = "AoE, Cold",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemSpiralVolley"] = {
		name = "Spiral Volley",
		baseTypeName = "Spiral Volley",
		gameId = "Metadata/Items/Gems/SkillGemSpiralVolley",
		variantId = "SpiralVolley",
		grantedEffectId = "SpiralVolleyPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			projectile = true,
			sustained = true,
		},
		gemType = "Attack",
		tagString = "Projectile, Sustained",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 13,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemLightningBolt"] = {
		name = "Lightning Bolt",
		baseTypeName = "Lightning Bolt",
		gameId = "Metadata/Items/Gems/SkillGemLightningBolt",
		variantId = "LightningBolt",
		grantedEffectId = "LightningBoltPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			lightning = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Lightning",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemEnergyShieldOnShockKillSupport"] = {
		name = "Shock Siphon",
		gameId = "Metadata/Items/Gems/SupportGemShockSiphon",
		variantId = "EnergyShieldOnShockKillSupport",
		grantedEffectId = "SupportEnergyShieldOnShockKillPlayer",
		tags = {
			support = true,
			lightning = true,
		},
		gemType = "Support",
		tagString = "Lightning",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemUnleash"] = {
		name = "Unleash",
		baseTypeName = "Unleash",
		gameId = "Metadata/items/Gems/SkillGemStaffUnleash",
		variantId = "Unleash",
		grantedEffectId = "UnleashPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			buff = true,
			duration = true,
		},
		gemType = "Spell",
		tagString = "Buff, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCoursingCurrentSupport"] = {
		name = "Coursing Current",
		gameId = "Metadata/Items/Gems/SupportGemCoursingCurrent",
		variantId = "CoursingCurrentSupport",
		grantedEffectId = "SupportCoursingCurrentPlayer",
		tags = {
			support = true,
			area = true,
			lightning = true,
		},
		gemType = "Support",
		tagString = "AoE, Lightning",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemLastingFrostSupport"] = {
		name = "Deep Freeze",
		gameId = "Metadata/Items/Gems/SupportGemDeepFreeze",
		variantId = "LastingFrostSupport",
		grantedEffectId = "SupportLastingFrostPlayer",
		tags = {
			support = true,
			cold = true,
		},
		gemType = "Support",
		tagString = "Cold",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemChanceToIgniteSupport"] = {
		name = "Ignition",
		gameId = "Metadata/Items/Gems/SupportGemIgnition",
		variantId = "ChanceToIgniteSupport",
		grantedEffectId = "SupportChanceToIgnitePlayer",
		tags = {
			support = true,
			fire = true,
		},
		gemType = "Support",
		tagString = "Fire",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDeadlyIgnitesSupport"] = {
		name = "Searing Flame",
		gameId = "Metadata/Items/Gems/SupportGemSearingFlame",
		variantId = "DeadlyIgnitesSupport",
		grantedEffectId = "SupportDeadlyIgnitesPlayer",
		tags = {
			support = true,
			fire = true,
		},
		gemType = "Support",
		tagString = "Fire",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemIgniteDurationSupport"] = {
		name = "Eternal Flame",
		gameId = "Metadata/Items/Gems/SupportGemEternalFlame",
		variantId = "IgniteDurationSupport",
		grantedEffectId = "SupportIgniteDurationPlayer",
		tags = {
			support = true,
			fire = true,
		},
		gemType = "Support",
		tagString = "Fire",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemEssenceHarvestSupport"] = {
		name = "Essence Harvest",
		gameId = "Metadata/Items/Gems/SupportGemEssenceHarvest",
		variantId = "EssenceHarvestSupport",
		grantedEffectId = "SupportEssenceHarvestPlayer",
		tags = {
			support = true,
			minion = true,
			persistent = true,
		},
		gemType = "Support",
		tagString = "Minion, Persistent",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCastOnShock"] = {
		name = "Cast on Shock",
		baseTypeName = "Cast on Shock",
		gameId = "Metadata/Items/Gems/SkillGemCastOnShock",
		variantId = "CastOnShock",
		grantedEffectId = "MetaCastOnShockPlayer",
		additionalGrantedEffectId1 = "SupportMetaCastOnShockPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			trigger = true,
			lightning = true,
			meta = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Trigger, Lightning, Meta",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 8,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemCastOnFreeze"] = {
		name = "Cast on Freeze",
		baseTypeName = "Cast on Freeze",
		gameId = "Metadata/Items/Gems/SkillGemCastOnFreeze",
		variantId = "CastOnFreeze",
		grantedEffectId = "MetaCastOnFreezePlayer",
		additionalGrantedEffectId1 = "SupportMetaCastOnFreezePlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			trigger = true,
			cold = true,
			meta = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Trigger, Cold, Meta",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 8,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemCastOnIgnite"] = {
		name = "Cast on Ignite",
		baseTypeName = "Cast on Ignite",
		gameId = "Metadata/Items/Gems/SkillGemCastOnIgnite",
		variantId = "CastOnIgnite",
		grantedEffectId = "MetaCastOnIgnitePlayer",
		additionalGrantedEffectId1 = "SupportMetaCastOnIgnitePlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			trigger = true,
			fire = true,
			meta = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Trigger, Fire, Meta",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 8,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemCastOnMeleeKill"] = {
		name = "Cast on Melee Kill",
		baseTypeName = "Cast on Melee Kill",
		gameId = "Metadata/Items/Gems/SkillGemCastOnMeleeKill",
		variantId = "CastOnMeleeKill",
		grantedEffectId = "MetaCastOnMeleeKillPlayer",
		additionalGrantedEffectId1 = "SupportMetaCastOnMeleeKillPlayer",
		tags = {
			strength = true,
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			trigger = true,
			meta = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Trigger, Meta",
		reqStr = 50,
		reqDex = 0,
		reqInt = 50,
		Tier = 0,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCastOnCriticalStrike"] = {
		name = "Cast on Critical",
		baseTypeName = "Cast on Critical",
		gameId = "Metadata/Items/Gems/SkillGemCastOnCritical",
		variantId = "CastOnCriticalStrike",
		grantedEffectId = "MetaCastOnCritPlayer",
		additionalGrantedEffectId1 = "SupportMetaCastOnCritPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			trigger = true,
			meta = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Trigger, Meta",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 14,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemExploitWeaknessSupport"] = {
		name = "Exploit Weakness",
		gameId = "Metadata/Items/Gems/SupportGemExploitWeakness",
		variantId = "ExploitWeaknessSupport",
		grantedEffectId = "SupportExploitWeaknessPlayer",
		tags = {
			support = true,
			payoff = true,
		},
		gemType = "Support",
		tagString = "Payoff",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDevastateSupport"] = {
		name = "Devastate",
		gameId = "Metadata/Items/Gems/SupportGemDevastate",
		variantId = "DevastateSupport",
		grantedEffectId = "SupportDevastatePlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemBarrierInvocation"] = {
		name = "Barrier Invocation",
		baseTypeName = "Barrier Invocation",
		gameId = "Metadata/Items/Gems/SkillGemBarrierInvocation",
		variantId = "BarrierInvocation",
		grantedEffectId = "MetaBarrierInvocationPlayer",
		additionalGrantedEffectId1 = "SupportBarrierInvocationPlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			trigger = true,
			meta = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Trigger, Meta",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 8,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemCastOnDodge"] = {
		name = "Cast on Dodge",
		baseTypeName = "Cast on Dodge",
		gameId = "Metadata/Items/Gems/SkillGemCastOnDodge",
		variantId = "CastOnDodge",
		grantedEffectId = "MetaCastOnDodgePlayer",
		additionalGrantedEffectId1 = "SupportMetaCastOnDodgePlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			trigger = true,
			meta = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Trigger, Meta",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 14,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemCastOnMeleeStun"] = {
		name = "Cast on Melee Stun",
		baseTypeName = "Cast on Melee Stun",
		gameId = "Metadata/Items/Gems/SkillGemCastOnMeleeStun",
		variantId = "CastOnMeleeStun",
		grantedEffectId = "MetaCastOnMeleeStunPlayer",
		additionalGrantedEffectId1 = "SupportMetaCastOnMeleeStunPlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			trigger = true,
			meta = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Trigger, Meta",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemEnragedWarcrySupport"] = {
		name = "Enraged Warcry",
		gameId = "Metadata/Items/Gems/SupportGemEnragedWarcry",
		variantId = "EnragedWarcrySupport",
		grantedEffectId = "SupportEnragedWarcryPlayer",
		tags = {
			support = true,
			attack = true,
			warcry = true,
		},
		gemType = "Support",
		tagString = "Attack, Warcry",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemLingeringIllusion"] = {
		name = "Lingering Illusion",
		baseTypeName = "Lingering Illusion",
		gameId = "Metadata/Items/Gems/SkillGemLingeringIllusion",
		variantId = "LingeringIllusion",
		grantedEffectId = "LingeringIllusionPlayer",
		additionalGrantedEffectId1 = "LingeringIllusionSpawnPlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			duration = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Duration",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 8,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemGhostDance"] = {
		name = "Ghost Dance",
		baseTypeName = "Ghost Dance",
		gameId = "Metadata/Items/Gems/SkillGemGhostDance",
		variantId = "GhostDance",
		grantedEffectId = "GhostDancePlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			duration = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Duration",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 4,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemAncestralWarriorTotem"] = {
		name = "Ancestral Warrior Totem",
		baseTypeName = "Ancestral Warrior Totem",
		gameId = "Metadata/Items/Gems/SkillGemAncestralWarriorTotem",
		variantId = "AncestralWarriorTotem",
		grantedEffectId = "AncestralWarriorTotemPlayer",
		additionalGrantedEffectId1 = "SupportAncestralWarriorTotemPlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			attack = true,
			totem = true,
			area = true,
			melee = true,
			physical = true,
			duration = true,
			meta = true,
		},
		gemType = "Attack",
		tagString = "Totem, AoE, Melee, Physical, Duration, Meta",
		weaponRequirements = "One Hand Mace, Two Hand Mace",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 13,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemExplosiveGrenade"] = {
		name = "Explosive Grenade",
		baseTypeName = "Explosive Grenade",
		gameId = "Metadata/Items/Gem/SkillGemExplosiveGrenade",
		variantId = "ExplosiveGrenade",
		grantedEffectId = "ExplosiveGrenadePlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			grenade = true,
			fire = true,
			detonator = true,
		},
		gemType = "Attack",
		tagString = "AoE, Projectile, Grenade, Fire, Detonator",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemFlashGrenade"] = {
		name = "Flash Grenade",
		baseTypeName = "Flash Grenade",
		gameId = "Metadata/Items/Gem/SkillGemFlashGrenade",
		variantId = "FlashGrenade",
		grantedEffectId = "FlashGrenadePlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			grenade = true,
		},
		gemType = "Attack",
		tagString = "AoE, Projectile, Grenade",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemOilGrenade"] = {
		name = "Oil Grenade",
		baseTypeName = "Oil Grenade",
		gameId = "Metadata/Items/Gem/SkillGemOilGrenade",
		variantId = "OilGrenade",
		grantedEffectId = "OilGrenadePlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			grenade = true,
			fire = true,
			duration = true,
		},
		gemType = "Attack",
		tagString = "AoE, Projectile, Grenade, Fire, Duration",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemToxicGrenade"] = {
		name = "Gas Grenade",
		baseTypeName = "Gas Grenade",
		gameId = "Metadata/Items/Gem/SkillGemGasGrenade",
		variantId = "ToxicGrenade",
		grantedEffectId = "ToxicGrenadePlayer",
		additionalStatSet1 = "ToxicGrenadeCloudPlayer",
		additionalStatSet2 = "ToxicGrenadeCloudExplosionPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			grenade = true,
			fire = true,
			chaos = true,
			duration = true,
		},
		gemType = "Attack",
		tagString = "AoE, Projectile, Grenade, Fire, Chaos, Duration",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemShockGrenade"] = {
		name = "Voltaic Grenade",
		baseTypeName = "Voltaic Grenade",
		gameId = "Metadata/Items/Gems/SkillGemVoltaicGrenade",
		variantId = "ShockGrenade",
		grantedEffectId = "ShockGrenadePlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			grenade = true,
			lightning = true,
		},
		gemType = "Attack",
		tagString = "AoE, Projectile, Grenade, Lightning",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPainOffering"] = {
		name = "Pain Offering",
		baseTypeName = "Pain Offering",
		gameId = "Metadata/Items/Gems/SkillGemPainOffering",
		variantId = "PainOffering",
		grantedEffectId = "PainOfferingPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			minion = true,
			buff = true,
			area = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Minion, Buff, AoE, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemTempestFlurry"] = {
		name = "Tempest Flurry",
		baseTypeName = "Tempest Flurry",
		gameId = "Metadata/Items/Gems/SkillGemTempestFlurry",
		variantId = "TempestFlurry",
		grantedEffectId = "TempestFlurryPlayer",
		additionalStatSet1 = "TempestFlurryPlayerThirdStrike",
		additionalStatSet2 = "TempestFlurryPlayerFinalStrike",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
			lightning = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike, Lightning",
		weaponRequirements = "Quarterstaff",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemStaggeringPalm"] = {
		name = "Staggering Palm",
		baseTypeName = "Staggering Palm",
		gameId = "Metadata/Items/Gems/SkillGemStaggeringPalm",
		variantId = "StaggeringPalm",
		grantedEffectId = "StaggeringPalmPlayer",
		additionalGrantedEffectId1 = "StaggeringPalmProjectilePlayer",
		additionalGrantedEffectId2 = "StaggeringPalmUnarmedProjectilePlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			buff = true,
			area = true,
			melee = true,
			strike = true,
			projectile = true,
			physical = true,
			duration = true,
		},
		gemType = "Attack",
		tagString = "Buff, AoE, Melee, Strike, Projectile, Physical, Duration",
		weaponRequirements = "Quarterstaff, Unarmed",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemChargedStaff"] = {
		name = "Charged Staff",
		baseTypeName = "Charged Staff",
		gameId = "Metadata/Items/Gems/SkillGemChargedStaff",
		variantId = "ChargedStaff",
		grantedEffectId = "ChargedStaffPlayer",
		additionalGrantedEffectId1 = "ChargedStaffShockwavePlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			attack = true,
			buff = true,
			area = true,
			duration = true,
			conditional = true,
		},
		gemType = "Spell",
		tagString = "Attack, Buff, AoE, Duration, Conditional",
		weaponRequirements = "Quarterstaff",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSiphoningStrike"] = {
		name = "Siphoning Strike",
		baseTypeName = "Siphoning Strike",
		gameId = "Metadata/Items/Gem/SkillGemSiphoningStrike",
		variantId = "SiphoningStrike",
		grantedEffectId = "SiphoningStrikePlayer",
		additionalStatSet1 = "SiphoningStrikePulsePlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
			lightning = true,
			payoff = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike, Lightning, Payoff",
		weaponRequirements = "Quarterstaff",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemTempestBell"] = {
		name = "Tempest Bell",
		baseTypeName = "Tempest Bell",
		gameId = "Metadata/Items/Gem/SkillGemTempestBell",
		variantId = "TempestBell",
		grantedEffectId = "TempestBellPlayer",
		additionalStatSet1 = "TempestBellSlamPlayer",
		additionalStatSet2 = "TempestBellShockwavePlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			sustained = true,
			duration = true,
			nova = true,
			conditional = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Sustained, Duration, Nova, Conditional",
		weaponRequirements = "Quarterstaff",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemChaosbolt"] = {
		name = "Chaos Bolt",
		baseTypeName = "Chaos Bolt",
		gameId = "Metadata/Items/Gems/SkillGemChaosbolt",
		variantId = "Chaosbolt",
		grantedEffectId = "WeaponGrantedChaosboltPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			projectile = true,
			chaos = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, Projectile, Chaos",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemFrozenLocus"] = {
		name = "Frozen Locus",
		baseTypeName = "Frozen Locus",
		gameId = "Metadata/Items/Gem/SkillGemFrozenLocus",
		variantId = "FrozenLocus",
		grantedEffectId = "FrozenLocusPlayer",
		additionalStatSet1 = "FrozenLocusGroundPlayer",
		additionalGrantedEffectId1 = "FrozenLocusExplodePlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			cold = true,
			duration = true,
			nova = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Cold, Duration, Nova",
		weaponRequirements = "Quarterstaff",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemBonestorm"] = {
		name = "Bonestorm",
		baseTypeName = "Bonestorm",
		gameId = "Metadata/Items/Gem/SkillGemBonestorm",
		variantId = "Bonestorm",
		grantedEffectId = "BonestormPlayer",
		additionalStatSet1 = "BonestormExplosionPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			area = true,
			projectile = true,
			sustained = true,
			physical = true,
			channelling = true,
		},
		gemType = "Spell",
		tagString = "AoE, Projectile, Sustained, Physical, Channelling",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemBoneCage"] = {
		name = "Bone Cage",
		baseTypeName = "Bone Cage",
		gameId = "Metadata/Items/Gem/SkillGemBoneCage",
		variantId = "BoneCage",
		grantedEffectId = "BoneCagePlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			physical = true,
			duration = true,
			nova = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Physical, Duration, Nova",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemNeuralOverload"] = {
		name = "Neural Overload",
		gameId = "Metadata/Items/Gems/SupportGemNeuralOverload",
		variantId = "NeuralOverload",
		grantedEffectId = "SupportNeuralOverloadPlayer",
		tags = {
			support = true,
			lightning = true,
		},
		gemType = "Support",
		tagString = "Lightning",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemPinpointCritical"] = {
		name = "Pinpoint Critical",
		gameId = "Metadata/Items/Gems/SupportGemPinpointCritical",
		variantId = "PinpointCritical",
		grantedEffectId = "SupportPinpointCriticalPlayer",
		tags = {
			support = true,
			critical = true,
		},
		gemType = "Support",
		tagString = "Critical",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemPerpetualCharge"] = {
		name = "Perpetual Charge",
		gameId = "Metadata/Items/Gems/SupportGemPerpetualCharge",
		variantId = "PerpetualCharge",
		grantedEffectId = "SupportPerpetualChargePlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCorrosion"] = {
		name = "Corrosion",
		gameId = "Metadata/Items/Gems/SupportGemCorrosion",
		variantId = "Corrosion",
		grantedEffectId = "SupportCorrosionPlayer",
		tags = {
			support = true,
			physical = true,
			chaos = true,
		},
		gemType = "Support",
		tagString = "Physical, Chaos",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemUnearth"] = {
		name = "Unearth",
		baseTypeName = "Unearth",
		gameId = "Metadata/Items/Gem/SkillGemUnearth",
		variantId = "Unearth",
		grantedEffectId = "UnearthPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			minion = true,
			area = true,
			physical = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, Minion, AoE, Physical, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemDetonatingArrow"] = {
		name = "Detonating Arrow",
		baseTypeName = "Detonating Arrow",
		gameId = "Metadata/Items/Gem/SkillGemDetonatingArrow",
		variantId = "DetonatingArrow",
		grantedEffectId = "DetonatingArrowPlayer",
		additionalStatSet1 = "DetonatingArrowExplosionPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			area = true,
			projectile = true,
			fire = true,
			channelling = true,
			detonator = true,
			stages = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, AoE, Projectile, Fire, Channelling, Detonator, Staged",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPoisonBurstArrow"] = {
		name = "Poisonburst Arrow",
		baseTypeName = "Poisonburst Arrow",
		gameId = "Metadata/Items/Gem/SkillGemPoisonBurstArrow",
		variantId = "PoisonBurstArrow",
		grantedEffectId = "PoisonBurstArrowPlayer",
		additionalStatSet1 = "PoisonBurstArrowCloudPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			area = true,
			projectile = true,
			chaos = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, AoE, Projectile, Chaos, Duration",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemFirebolt"] = {
		name = "Firebolt",
		baseTypeName = "Firebolt",
		gameId = "Metadata/Items/Gems/SkillGemFirebolt",
		variantId = "Firebolt",
		grantedEffectId = "FireboltPlayer",
		additionalStatSet1 = "FireboltExplosionPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			projectile = true,
			fire = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Projectile, Fire",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemToxicGrowth"] = {
		name = "Toxic Growth",
		baseTypeName = "Toxic Growth",
		gameId = "Metadata/Items/Gem/SkillGemToxicGrowth",
		variantId = "ToxicGrowth",
		grantedEffectId = "ToxicGrowthPlayer",
		additionalStatSet1 = "ToxicGrowthBurstPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			chaos = true,
		},
		gemType = "Attack",
		tagString = "AoE, Projectile, Chaos",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemStormcallerArrow"] = {
		name = "Stormcaller Arrow",
		baseTypeName = "Stormcaller Arrow",
		gameId = "Metadata/Items/Gem/SkillGemStormcallerArrow",
		variantId = "StormcallerArrow",
		grantedEffectId = "StormcallerArrowPlayer",
		additionalStatSet1 = "StormcallerArrowBoltPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			area = true,
			projectile = true,
			lightning = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, AoE, Projectile, Lightning, Duration",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemLightningRod"] = {
		name = "Lightning Rod",
		baseTypeName = "Lightning Rod",
		gameId = "Metadata/Items/Gem/SkillGemLightningRod",
		variantId = "LightningRod",
		grantedEffectId = "LightningRodPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			area = true,
			projectile = true,
			sustained = true,
			lightning = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, AoE, Projectile, Sustained, Lightning, Duration",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSnipe"] = {
		name = "Snipe",
		baseTypeName = "Snipe",
		gameId = "Metadata/Items/Gem/SkillGemSnipe",
		variantId = "Snipe",
		grantedEffectId = "SnipePlayer",
		additionalStatSet1 = "SnipeExplosionPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			area = true,
			projectile = true,
			channelling = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, AoE, Projectile, Channelling",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemGasArrow"] = {
		name = "Gas Arrow",
		baseTypeName = "Gas Arrow",
		gameId = "Metadata/Items/Gem/SkillGemGasArrow",
		variantId = "GasArrow",
		grantedEffectId = "GasArrowPlayer",
		additionalStatSet1 = "GasArrowGasDegenPlayer",
		additionalStatSet2 = "GasArrowGasExplosionPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			area = true,
			projectile = true,
			fire = true,
			chaos = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, AoE, Projectile, Fire, Chaos, Duration",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSolarOrb"] = {
		name = "Solar Orb",
		baseTypeName = "Solar Orb",
		gameId = "Metadata/Items/Gem/SkillGemSolarOrb",
		variantId = "SolarOrb",
		grantedEffectId = "SolarOrbPlayer",
		additionalStatSet1 = "SolarOrbAuraPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			area = true,
			sustained = true,
			fire = true,
			duration = true,
			orb = true,
		},
		gemType = "Spell",
		tagString = "AoE, Sustained, Fire, Duration, Orb",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemFireball"] = {
		name = "Fireball",
		baseTypeName = "Fireball",
		gameId = "Metadata/Items/Gem/SkillGemFireball",
		variantId = "Fireball",
		grantedEffectId = "FireballPlayer",
		additionalStatSet1 = "FireballExplosionPlayer",
		additionalStatSet2 = "FireballSecondaryProjectilePlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			projectile = true,
			fire = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Projectile, Fire",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemElectrocutingArrow"] = {
		name = "Electrocuting Arrow",
		baseTypeName = "Electrocuting Arrow",
		gameId = "Metadata/Items/Gem/SkillGemElectrocutingArrow",
		variantId = "ElectrocutingArrow",
		grantedEffectId = "ElectrocutingArrowPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			projectile = true,
			lightning = true,
			duration = true,
		},
		gemType = "Attack",
		tagString = "Projectile, Lightning, Duration",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemColdSnap"] = {
		name = "Cold Snap",
		baseTypeName = "Cold Snap",
		gameId = "Metadata/Items/Gem/SkillGemColdSnap",
		variantId = "ColdSnap",
		grantedEffectId = "ColdSnapPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			cold = true,
			payoff = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Cold, Payoff",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPerfectStrike"] = {
		name = "Perfect Strike",
		baseTypeName = "Perfect Strike",
		gameId = "Metadata/Items/Gem/SkillGemPerfectStrike",
		variantId = "PerfectStrike",
		grantedEffectId = "PerfectStrikePlayer",
		additionalStatSet1 = "PerfectStrikeShockwavePlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
			fire = true,
			duration = true,
			channelling = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike, Fire, Duration, Channelling",
		weaponRequirements = "One Hand Mace, Two Hand Mace",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemShieldBlock"] = {
		name = "Raise Shield",
		baseTypeName = "Raise Shield",
		gameId = "Metadata/Items/Gem/SkillGemShieldBlock",
		variantId = "ShieldBlock",
		grantedEffectId = "ShieldBlockPlayer",
		tags = {
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			physical = true,
			channelling = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Physical, Channelling",
		weaponRequirements = "Armoured Shield",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemResonatingShield"] = {
		name = "Resonating Shield",
		baseTypeName = "Resonating Shield",
		gameId = "Metadata/Items/Gem/SkillGemResonatingShield",
		variantId = "ResonatingShield",
		grantedEffectId = "ResonatingShieldPlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			sustained = true,
			physical = true,
			channelling = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Sustained, Physical, Channelling",
		weaponRequirements = "Armoured Shield",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemManaRemnants"] = {
		name = "Mana Remnants",
		baseTypeName = "Mana Remnants",
		gameId = "Metadata/Items/Gem/SkillGemManaRemnants",
		variantId = "ManaRemnants",
		grantedEffectId = "ManaRemnantsPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			remnant = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Remnant",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 4,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemMagmaBarrier"] = {
		name = "Magma Barrier",
		baseTypeName = "Magma Barrier",
		gameId = "Metadata/Items/Gem/SkillGemMagmaBarrier",
		variantId = "MagmaBarrier",
		grantedEffectId = "MagmaBarrierPlayer",
		additionalGrantedEffectId1 = "MagmaSprayPlayer",
		tags = {
			buff = true,
			strength = true,
			grants_active_skill = true,
			attack = true,
			persistent = true,
			area = true,
			melee = true,
			trigger = true,
			fire = true,
			duration = true,
		},
		gemType = "Buff",
		tagString = "Attack, Persistent, AoE, Melee, Trigger, Fire, Duration",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 4,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemFreezingShards"] = {
		name = "Freezing Shards",
		baseTypeName = "Freezing Shards",
		gameId = "Metadata/Items/Gems/SkillGemFreezingShards",
		variantId = "FreezingShards",
		grantedEffectId = "FreezingShardsPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			projectile = true,
			cold = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, Projectile, Cold",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemVineArrow"] = {
		name = "Vine Arrow",
		baseTypeName = "Vine Arrow",
		gameId = "Metadata/Items/Gem/SkillGemVineArrow",
		variantId = "VineArrow",
		grantedEffectId = "VineArrowPlayer",
		additionalStatSet1 = "VineArrowFlowerPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			area = true,
			projectile = true,
			chaos = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, AoE, Projectile, Chaos, Duration",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemRhoaMount"] = {
		name = "Rhoa Mount",
		baseTypeName = "Rhoa Mount",
		gameId = "Metadata/Items/Gems/SkillGemRhoaMount",
		variantId = "RhoaMount",
		grantedEffectId = "RhoaMountPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			minion = true,
			buff = true,
			companion = true,
			persistent = true,
		},
		gemType = "Minion",
		tagString = "Buff, Companion, Persistent",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 14,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSupportBurstingPlague"] = {
		name = "Bursting Plague",
		gameId = "Metadata/Items/Gems/SupportGemBurstingPlague",
		variantId = "SupportBurstingPlague",
		grantedEffectId = "SupportBurstingPlaguePlayer",
		additionalGrantedEffectId1 = "PlagueBurstPlayer",
		tags = {
			support = true,
			grants_active_skill = true,
			area = true,
			trigger = true,
			physical = true,
			chaos = true,
		},
		gemType = "Support",
		tagString = "AoE, Trigger, Physical, Chaos",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCorpseCloud"] = {
		name = "Decompose",
		baseTypeName = "Decompose",
		gameId = "Metadata/Items/Gems/SkillGemCorpseCloud",
		variantId = "CorpseCloud",
		grantedEffectId = "CorpseCloudPlayer",
		additionalStatSet1 = "CorpseCloudExplosionPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			area = true,
			fire = true,
			chaos = true,
			duration = true,
		},
		gemType = "Spell",
		tagString = "AoE, Fire, Chaos, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemWallFortress"] = {
		name = "Fortress",
		gameId = "Metadata/Items/Gems/SupportGemFortress",
		variantId = "WallFortress",
		grantedEffectId = "SupportWallFortressPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFrostfireSupport"] = {
		name = "Frostfire",
		gameId = "Metadata/Items/Gems/SupportGemFrostfire",
		variantId = "FrostfireSupport",
		grantedEffectId = "SupportFrostfirePlayer",
		tags = {
			support = true,
			cold = true,
			fire = true,
			payoff = true,
		},
		gemType = "Support",
		tagString = "Cold, Fire, Payoff",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemStormfireSupport"] = {
		name = "Stormfire",
		gameId = "Metadata/Items/Gems/SupportGemStormfire",
		variantId = "StormfireSupport",
		grantedEffectId = "SupportStormfirePlayer",
		tags = {
			support = true,
			lightning = true,
			fire = true,
		},
		gemType = "Support",
		tagString = "Lightning, Fire",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemIncreaseLimitSupport"] = {
		name = "Overabundance",
		gameId = "Metadata/Items/Gems/SupportGemOverabundance",
		variantId = "IncreaseLimitSupport",
		grantedEffectId = "SupportIncreaseLimitPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemElectrocuteSupport"] = {
		name = "Electrocute",
		gameId = "Metadata/Items/Gems/SupportGemElectrocute",
		variantId = "ElectrocuteSupport",
		grantedEffectId = "SupportElectrocutePlayer",
		tags = {
			support = true,
			lightning = true,
		},
		gemType = "Support",
		tagString = "Lightning",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemRagingSpirits"] = {
		name = "Raging Spirits",
		baseTypeName = "Raging Spirits",
		gameId = "Metadata/Items/Gem/SkillGemRagingSpirits",
		variantId = "RagingSpirits",
		grantedEffectId = "RagingSpiritsPlayer",
		tags = {
			buff = true,
			intelligence = true,
			grants_active_skill = true,
			minion = true,
			persistent = true,
			fire = true,
			duration = true,
		},
		gemType = "Buff",
		tagString = "Minion, Persistent, Fire, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 4,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemEnergyBarrierSupport"] = {
		name = "Energy Barrier",
		gameId = "Metadata/Items/Gems/SupportGemEnergyBarrier",
		variantId = "EnergyBarrierSupport",
		grantedEffectId = "SupportEnergyBarrierPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemKnockbackWaveSupport"] = {
		name = "Wind Wave",
		gameId = "Metadata/Items/Gems/SupportGemWindWave",
		variantId = "KnockbackWaveSupport",
		grantedEffectId = "SupportKnockbackWavePlayer",
		additionalGrantedEffectId1 = "KnockbackWavePlayer",
		tags = {
			support = true,
			grants_active_skill = true,
			area = true,
			trigger = true,
		},
		gemType = "Support",
		tagString = "AoE, Trigger",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemBitingFrostSupport"] = {
		name = "Biting Frost",
		gameId = "Metadata/Items/Gems/SupportGemBitingFrost",
		variantId = "BitingFrostSupport",
		grantedEffectId = "SupportBitingFrostPlayer",
		tags = {
			support = true,
			cold = true,
			payoff = true,
		},
		gemType = "Support",
		tagString = "Cold, Payoff",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemElementalDischarge"] = {
		name = "Elemental Discharge",
		gameId = "Metadata/Items/Gems/SupportGemElementalDischarge",
		variantId = "ElementalDischarge",
		grantedEffectId = "SupportElementalDischargePlayer",
		additionalGrantedEffectId1 = "TriggeredElementalDischargePlayer",
		tags = {
			support = true,
			grants_active_skill = true,
			spell = true,
			area = true,
			trigger = true,
			lightning = true,
			cold = true,
			fire = true,
			payoff = true,
		},
		gemType = "Support",
		tagString = "Spell, AoE, Trigger, Lightning, Cold, Fire, Payoff",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemHourglassSupport"] = {
		name = "Hourglass",
		gameId = "Metadata/Items/Gems/SupportGemHourglass",
		variantId = "HourglassSupport",
		grantedEffectId = "SupportHourglassPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFieryDeathSupport"] = {
		name = "Fiery Death",
		gameId = "Metadata/Items/Gems/SupportGemFieryDeath",
		variantId = "FieryDeathSupport",
		grantedEffectId = "SupportFieryDeathPlayer",
		additionalGrantedEffectId1 = "TriggeredFieryDeathPlayer",
		tags = {
			support = true,
			grants_active_skill = true,
			spell = true,
			area = true,
			fire = true,
		},
		gemType = "Support",
		tagString = "Spell, AoE, Fire",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemTwister"] = {
		name = "Twister",
		baseTypeName = "Twister",
		gameId = "Metadata/Items/Gem/SkillGemTwister",
		variantId = "Twister",
		grantedEffectId = "TwisterPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			area = true,
			projectile = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, AoE, Projectile, Duration",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemDetonateMinion"] = {
		name = "Detonate Minion",
		baseTypeName = "Explosive Demise",
		gameId = "Metadata/Items/Gem/SkillGemDetonateMinion",
		variantId = "DetonateMinion",
		grantedEffectId = "DestructiveLinkSkeletonBombadierMinion",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			spell = true,
			area = true,
			fire = true,
		},
		gemType = "Spell",
		tagString = "AoE, Fire",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemExplosiveSpear"] = {
		name = "Explosive Spear",
		baseTypeName = "Explosive Spear",
		gameId = "Metadata/Items/Gem/SkillGemExplosiveSpear",
		variantId = "ExplosiveSpear",
		grantedEffectId = "ExplosiveSpearPlayer",
		additionalStatSet1 = "ExplosiveSpearExplodePlayer",
		additionalStatSet2 = "ExplosiveSpearInfusedExplodePlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			area = true,
			projectile = true,
			fire = true,
			duration = true,
			detonator = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, AoE, Projectile, Fire, Duration, Detonator",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemElementalSiphon"] = {
		name = "Elemental Siphon",
		baseTypeName = "Elemental Siphon",
		gameId = "Metadata/Items/Gem/SkillGemElementalSiphon",
		variantId = "ElementalSiphon",
		grantedEffectId = "ElementalSiphonPlayer",
		additionalStatSet1 = "ElementalSiphonColdPlayer",
		additionalStatSet2 = "ElementalSiphonFirePlayer",
		additionalStatSet3 = "ElementalSiphonLightningPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			melee = true,
			strike = true,
			lightning = true,
			cold = true,
			fire = true,
			duration = true,
		},
		gemType = "Attack",
		tagString = "Melee, Strike, Lightning, Cold, Fire, Duration",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemThunderousLeap"] = {
		name = "Thunderous Leap",
		baseTypeName = "Thunderous Leap",
		gameId = "Metadata/Items/Gem/SkillGemThunderousLeap",
		variantId = "ThunderousLeap",
		grantedEffectId = "ThunderousLeapPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			slam = true,
			lightning = true,
			detonator = true,
			travel = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Slam, Lightning, Detonator, Travel",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemWindDancer"] = {
		name = "Wind Dancer",
		baseTypeName = "Wind Dancer",
		gameId = "Metadata/Items/Gem/SkillGemWindDancer",
		variantId = "WindDancer",
		grantedEffectId = "WindDancerPlayer",
		additionalGrantedEffectId1 = "TriggeredWindDancerPlayer",
		tags = {
			buff = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			persistent = true,
			area = true,
			melee = true,
			stages = true,
		},
		gemType = "Buff",
		tagString = "Attack, Persistent, AoE, Melee, Staged",
		weaponRequirements = "Any Martial Weapon",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 4,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemDeadlyHeraldsSupport"] = {
		name = "Deadly Herald",
		gameId = "Metadata/Items/Gems/SupportGemDeadlyHeralds",
		variantId = "DeadlyHeraldsSupport",
		grantedEffectId = "SupportDeadlyHeraldsPlayer",
		tags = {
			support = true,
			herald = true,
		},
		gemType = "Support",
		tagString = "Herald",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemManaFlareSupport"] = {
		name = "Mana Flare",
		gameId = "Metadata/Items/Gems/SupportGemManaFlare",
		variantId = "ManaFlareSupport",
		grantedEffectId = "SupportManaFlarePlayer",
		additionalGrantedEffectId1 = "TriggeredManaFlarePlayer",
		tags = {
			support = true,
			grants_active_skill = true,
			spell = true,
			area = true,
			trigger = true,
			fire = true,
			critical = true,
		},
		gemType = "Support",
		tagString = "Spell, AoE, Trigger, Fire, Critical",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemLockdownSupport"] = {
		name = "Lockdown",
		gameId = "Metadata/Items/Gems/SupportGemLockdown",
		variantId = "LockdownSupport",
		grantedEffectId = "SupportLockdownPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemOverpowerSupport"] = {
		name = "Overpower",
		gameId = "Metadata/Items/Gems/SupportGemOverpower",
		variantId = "OverpowerSupport",
		grantedEffectId = "SupportOverpowerPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemMobilitySupport"] = {
		name = "Mobility",
		gameId = "Metadata/Items/Gems/SupportGemMobility",
		variantId = "MobilitySupport",
		grantedEffectId = "SupportMobilityPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemPinSupport"] = {
		name = "Pin",
		gameId = "Metadata/Items/Gems/SupportGemPin",
		variantId = "PinSupport",
		grantedEffectId = "SupportPinPlayer",
		tags = {
			support = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Physical",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemAmbushSupport"] = {
		name = "Ambush",
		gameId = "Metadata/Items/Gems/SupportGemAmbush",
		variantId = "AmbushSupport",
		grantedEffectId = "SupportAmbushPlayer",
		tags = {
			support = true,
			critical = true,
		},
		gemType = "Support",
		tagString = "Critical",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemProfaneRitual"] = {
		name = "Profane Ritual",
		baseTypeName = "Profane Ritual",
		gameId = "Metadata/Items/Gem/SkillGemProfaneRitual",
		variantId = "ProfaneRitual",
		grantedEffectId = "ProfaneRitualPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			chaos = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Chaos, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSoulOffering"] = {
		name = "Soul Offering",
		baseTypeName = "Soul Offering",
		gameId = "Metadata/Items/Gem/SkillGemSoulOffering",
		variantId = "SoulOffering",
		grantedEffectId = "SoulOfferingPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			minion = true,
			buff = true,
			duration = true,
		},
		gemType = "Minion",
		tagString = "Buff, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 13,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemDarkEffigy"] = {
		name = "Dark Effigy",
		baseTypeName = "Dark Effigy",
		gameId = "Metadata/Items/Gem/SkillGemDarkEffigy",
		variantId = "DarkEffigy",
		grantedEffectId = "DarkEffigyPlayer",
		additionalGrantedEffectId1 = "DarkEffigyProjectilePlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			totem = true,
			area = true,
			projectile = true,
			chaos = true,
			duration = true,
		},
		gemType = "Spell",
		tagString = "Totem, AoE, Projectile, Chaos, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemManaFlaskSupport"] = {
		name = "Mana Bounty",
		gameId = "Metadata/Items/Gems/SupportGemManaBounty",
		variantId = "ManaFlaskSupport",
		grantedEffectId = "SupportManaFlaskPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemLifeFlaskSupport"] = {
		name = "Life Bounty",
		gameId = "Metadata/Items/Gems/SupportGemLifeBounty",
		variantId = "LifeFlaskSupport",
		grantedEffectId = "SupportLifeFlaskPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemSkeletalWarrior"] = {
		name = "Skeletal Warrior",
		baseTypeName = "Skeletal Warrior Minion",
		gameId = "Metadata/Items/Gems/SkillGemSkeletalWarriorWeaponSkill",
		variantId = "SkeletalWarrior",
		grantedEffectId = "SummonSkeletalWarriorsPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			minion = true,
			persistent = true,
			physical = true,
		},
		gemType = "Minion",
		tagString = "Persistent, Physical",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemMinionInstabilitySupport"] = {
		name = "Minion Instability",
		gameId = "Metadata/Items/Gem/SupportGemMinionInstability",
		variantId = "MinionInstabilitySupport",
		grantedEffectId = "SupportMinionInstabilityPlayer",
		tags = {
			support = true,
			minion = true,
			fire = true,
		},
		gemType = "Support",
		tagString = "Minion, Fire",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemEnduranceChargeOnArmourBreakSupport"] = {
		name = "Break Endurance",
		gameId = "Metadata/Items/Gem/SupportGemBreakEndurance",
		variantId = "EnduranceChargeOnArmourBreakSupport",
		grantedEffectId = "SupportEnduranceChargeOnArmourBreak",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCurseEffectSupport"] = {
		name = "Heightened Curse",
		gameId = "Metadata/Items/Gems/SupportGemHeightenedCurse",
		variantId = "CurseEffectSupport",
		grantedEffectId = "SupportCurseEffectPlayer",
		tags = {
			support = true,
			curse = true,
		},
		gemType = "Support",
		tagString = "Curse",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCorpseConservationSupport"] = {
		name = "Corpse Conservation",
		gameId = "Metadata/Items/Gem/SupportGemCorpseConservation",
		variantId = "CorpseConservationSupport",
		grantedEffectId = "SupportCorpseConservationPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDecayingHexSupport"] = {
		name = "Decaying Hex",
		gameId = "Metadata/Items/Gem/SupportGemDecayingHex",
		variantId = "DecayingHexSupport",
		grantedEffectId = "SupportDecayingHexPlayer",
		tags = {
			support = true,
			chaos = true,
			curse = true,
		},
		gemType = "Support",
		tagString = "Chaos, Curse",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFocusedCurseSupport"] = {
		name = "Focused Curse",
		gameId = "Metadata/Items/Gem/SupportGemFocusedCurse",
		variantId = "FocusedCurseSupport",
		grantedEffectId = "SupportFocusedCursePlayer",
		tags = {
			support = true,
			curse = true,
		},
		gemType = "Support",
		tagString = "Curse",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemRitualisticCurseSupport"] = {
		name = "Ritualistic Curse",
		gameId = "Metadata/Items/Gem/SupportGemRitualisticCurse",
		variantId = "RitualisticCurseSupport",
		grantedEffectId = "SupportRitualisticCursePlayer",
		tags = {
			support = true,
			curse = true,
		},
		gemType = "Support",
		tagString = "Curse",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemMinionPactSupport"] = {
		name = "Minion Pact",
		gameId = "Metadata/Items/Gem/SupportGemMinionPact",
		variantId = "MinionPactSupport",
		grantedEffectId = "SupportMinionPactPlayer",
		tags = {
			support = true,
			minion = true,
		},
		gemType = "Support",
		tagString = "Minion",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemLastGaspSupport"] = {
		name = "Last Gasp",
		gameId = "Metadata/Items/Gem/SupportGemLastGasp",
		variantId = "LastGaspSupport",
		grantedEffectId = "SupportLastGaspPlayer",
		tags = {
			support = true,
			minion = true,
			persistent = true,
			duration = true,
		},
		gemType = "Support",
		tagString = "Minion, Persistent, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemLifeOnCullSupport"] = {
		name = "Life Drain",
		gameId = "Metadata/Items/Gems/SupportGemLifeDrain",
		variantId = "LifeOnCullSupport",
		grantedEffectId = "SupportLifeOnCullPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemManaOnCullSupport"] = {
		name = "Soul Drain",
		gameId = "Metadata/Items/Gems/SupportGemSoulDrain",
		variantId = "ManaOnCullSupport",
		grantedEffectId = "SupportManaOnCullPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemGrimFeast"] = {
		name = "Grim Feast",
		baseTypeName = "Grim Feast",
		gameId = "Metadata/Items/Gem/SkillGemGrimFeast",
		variantId = "GrimFeast",
		grantedEffectId = "GrimFeastPlayer",
		tags = {
			buff = true,
			intelligence = true,
			grants_active_skill = true,
			minion = true,
			persistent = true,
			remnant = true,
		},
		gemType = "Buff",
		tagString = "Minion, Persistent, Remnant",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemInnervateSupport"] = {
		name = "Innervate",
		gameId = "Metadata/Items/Gem/SupportGemInnervate",
		variantId = "InnervateSupport",
		grantedEffectId = "SupportInnervatePlayer",
		tags = {
			support = true,
			attack = true,
			lightning = true,
			duration = true,
		},
		gemType = "Support",
		tagString = "Attack, Lightning, Duration",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCastOnMinionDeath"] = {
		name = "Cast on Minion Death",
		baseTypeName = "Cast on Minion Death",
		gameId = "Metadata/Items/Gems/SkillGemCastOnMinionDeath",
		variantId = "CastOnMinionDeath",
		grantedEffectId = "MetaCastOnMinionDeathPlayer",
		additionalGrantedEffectId1 = "SupportMetaCastOnMinionDeathPlayer",
		tags = {
			buff = true,
			intelligence = true,
			grants_active_skill = true,
			minion = true,
			persistent = true,
			trigger = true,
			meta = true,
		},
		gemType = "Buff",
		tagString = "Minion, Persistent, Trigger, Meta",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 8,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemMultiplePoisonSupport"] = {
		name = "Comorbidity",
		gameId = "Metadata/Items/Gems/SupportGemComorbidity",
		variantId = "MultiplePoisonSupport",
		grantedEffectId = "SupportMultiplePoisonPlayer",
		tags = {
			support = true,
			chaos = true,
		},
		gemType = "Support",
		tagString = "Chaos",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemMultipleChargesSupport"] = {
		name = "Profusion",
		gameId = "Metadata/Items/Gems/SupportGemProfusion",
		variantId = "MultipleChargesSupport",
		grantedEffectId = "SupportMultipleChargesPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemEmpoweredDamageSupport"] = {
		name = "Premeditation",
		gameId = "Metadata/Items/Gems/SupportGemPremeditation",
		variantId = "EmpoweredDamageSupport",
		grantedEffectId = "SupportEmpoweredDamagePlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemEmpoweredCullSupport"] = {
		name = "Murderous Intent",
		gameId = "Metadata/Items/Gems/SupportGemMurderousIntent",
		variantId = "EmpoweredCullSupport",
		grantedEffectId = "SupportEmpoweredCullPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemScavengedPlating"] = {
		name = "Scavenged Plating",
		baseTypeName = "Scavenged Plating",
		gameId = "Metadata/Items/Gem/SkillGemScavengedPlating",
		variantId = "ScavengedPlating",
		grantedEffectId = "ScavengedPlatingPlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			physical = true,
			duration = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Physical, Duration",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 4,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemManaDrain"] = {
		name = "Mana Drain",
		baseTypeName = "Mana Drain",
		gameId = "Metadata/Items/Gem/SkillGemManaDrain",
		variantId = "ManaDrain",
		grantedEffectId = "ManaDrainPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
		},
		gemType = "Spell",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemBoneBlast"] = {
		name = "Bone Blast",
		baseTypeName = "Bone Blast",
		gameId = "Metadata/Items/Gem/SkillGemBoneBlast",
		variantId = "BoneBlast",
		grantedEffectId = "BoneBlastPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			physical = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Physical, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemBlinkReservation"] = {
		name = "Blink",
		baseTypeName = "Blink",
		gameId = "Metadata/Items/Gem/SkillGemBlink",
		variantId = "BlinkReservation",
		grantedEffectId = "BlinkReservationPlayer",
		additionalGrantedEffectId1 = "BlinkPlayer",
		tags = {
			buff = true,
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			persistent = true,
			travel = true,
		},
		gemType = "Buff",
		tagString = "Spell, Persistent, Travel",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 14,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemMoltenBlast"] = {
		name = "Molten Blast",
		baseTypeName = "Molten Blast",
		gameId = "Metadata/Items/Gem/SkillGemMoltenBlast",
		variantId = "MoltenBlast",
		grantedEffectId = "MoltenBlastPlayer",
		additionalStatSet1 = "MoltenBlastSecondaryPlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			fire = true,
		},
		gemType = "Attack",
		tagString = "AoE, Projectile, Fire",
		weaponRequirements = "One Hand Mace, Two Hand Mace",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemMalice"] = {
		name = "Malice",
		baseTypeName = "Malice",
		gameId = "Metadata/Items/Gem/SkillGemMalice",
		variantId = "Malice",
		grantedEffectId = "MalicePlayer",
		tags = {
			strength = true,
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			aura = true,
			duration = true,
			critical = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Aura, Duration, Critical",
		reqStr = 25,
		reqDex = 0,
		reqInt = 75,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemHammerOfTheGods"] = {
		name = "Hammer of the Gods",
		baseTypeName = "Hammer of the Gods",
		gameId = "Metadata/Items/Gem/SkillGemHammerOfTheGods",
		variantId = "HammerOfTheGods",
		grantedEffectId = "HammerOfTheGodsPlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			slam = true,
			duration = true,
			conditional = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Slam, Duration, Conditional",
		weaponRequirements = "One Hand Mace, Two Hand Mace",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 13,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemLivingBombPlayer"] = {
		name = "Living Bomb",
		baseTypeName = "Living Bomb",
		gameId = "Metadata/Items/Gem/SkillGemLivingBombPlayer",
		variantId = "LivingBombPlayer",
		grantedEffectId = "LivingBombPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			area = true,
			fire = true,
			duration = true,
		},
		gemType = "Spell",
		tagString = "AoE, Fire, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemStampede"] = {
		name = "Stampede",
		baseTypeName = "Stampede",
		gameId = "Metadata/Items/Gem/SkillGemStampede",
		variantId = "Stampede",
		grantedEffectId = "StampedePlayer",
		additionalStatSet1 = "StampedeSlamPlayer",
		additionalStatSet2 = "StampedeEruptionPlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			slam = true,
			duration = true,
			travel = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Slam, Duration, Travel",
		weaponRequirements = "One Hand Mace, Two Hand Mace",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 11,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemShieldWall"] = {
		name = "Shield Wall",
		baseTypeName = "Shield Wall",
		gameId = "Metadata/Items/Gem/SkillGemShieldWall",
		variantId = "ShieldWall",
		grantedEffectId = "ShieldWallPlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			physical = true,
			duration = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Physical, Duration",
		weaponRequirements = "Armoured Shield",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemBlazingCluster"] = {
		name = "Ember Fusillade",
		baseTypeName = "Ember Fusillade",
		gameId = "Metadata/Items/Gems/SkillGemEmberFusillade",
		variantId = "BlazingCluster",
		grantedEffectId = "BlazingClusterPlayer",
		additionalStatSet1 = "BlazingClusterExplosionPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			projectile = true,
			fire = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Projectile, Fire, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemHighVelocityRounds"] = {
		name = "High Velocity Rounds",
		baseTypeName = "Load High Velocity Rounds",
		gameId = "Metadata/Items/Gem/SkillGemHighVelocityRounds",
		variantId = "HighVelocityRounds",
		grantedEffectId = "HighVelocityRoundsAmmoPlayer",
		additionalGrantedEffectId1 = "HighVelocityRoundsPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			ammunition = true,
			projectile = true,
			physical = true,
			payoff = true,
		},
		gemType = "Attack",
		tagString = "Ammunition, Projectile, Physical, Payoff",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemFragmentationRounds"] = {
		name = "Fragmentation Rounds",
		baseTypeName = "Load Fragmentation Rounds",
		gameId = "Metadata/Items/Gem/SkillGemFragmentationRounds",
		variantId = "FragmentationRounds",
		grantedEffectId = "FragmentationRoundsAmmoPlayer",
		additionalGrantedEffectId1 = "FragmentationRoundsPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			ammunition = true,
			projectile = true,
			payoff = true,
		},
		gemType = "Attack",
		tagString = "Ammunition, Projectile, Payoff",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSiegeCascade"] = {
		name = "Siege Cascade",
		baseTypeName = "Load Siege Cascade",
		gameId = "Metadata/Items/Gem/SkillGemSiegeCascade",
		variantId = "SiegeCascade",
		grantedEffectId = "SiegeCascadeAmmoPlayer",
		additionalGrantedEffectId1 = "SiegeCascadePlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			ammunition = true,
			projectile = true,
			sustained = true,
			fire = true,
			payoff = true,
		},
		gemType = "Attack",
		tagString = "AoE, Ammunition, Projectile, Sustained, Fire, Payoff",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 13,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemArmourPiercingRounds"] = {
		name = "Armour Piercing Rounds",
		baseTypeName = "Load Armour Piercing Rounds",
		gameId = "Metadata/Items/Gem/SkillGemArmourPiercingRounds",
		variantId = "ArmourPiercingRounds",
		grantedEffectId = "ArmourPiercingBoltsAmmoPlayer",
		additionalGrantedEffectId1 = "ArmourPiercingBoltsPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			ammunition = true,
			projectile = true,
			physical = true,
		},
		gemType = "Attack",
		tagString = "Ammunition, Projectile, Physical",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemExplosiveShot"] = {
		name = "Explosive Shot",
		baseTypeName = "Load Explosive Shot",
		gameId = "Metadata/Items/Gem/SkillGemExplosiveShot",
		variantId = "ExplosiveShot",
		grantedEffectId = "ExplosiveShotAmmoPlayer",
		additionalGrantedEffectId1 = "ExplosiveShotPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			ammunition = true,
			projectile = true,
			fire = true,
			detonator = true,
		},
		gemType = "Attack",
		tagString = "AoE, Ammunition, Projectile, Fire, Detonator",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemIncendiaryShot"] = {
		name = "Incendiary Shot",
		baseTypeName = "Load Incendiary Shot",
		gameId = "Metadata/Items/Gem/SkillGemIncendiaryShot",
		variantId = "IncendiaryShot",
		grantedEffectId = "IncendiaryShotAmmoPlayer",
		additionalGrantedEffectId1 = "IncendiaryShotPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			ammunition = true,
			projectile = true,
			fire = true,
		},
		gemType = "Attack",
		tagString = "AoE, Ammunition, Projectile, Fire",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemRapidShot"] = {
		name = "Rapid Shot",
		baseTypeName = "Load Rapid Shot",
		gameId = "Metadata/Items/Gem/SkillGemRapidShot",
		variantId = "RapidShot",
		grantedEffectId = "RapidShotAmmoPlayer",
		additionalGrantedEffectId1 = "RapidShotPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			ammunition = true,
			projectile = true,
			fire = true,
		},
		gemType = "Attack",
		tagString = "Ammunition, Projectile, Fire",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemGlacialBolt"] = {
		name = "Glacial Bolt",
		baseTypeName = "Load Glacial Bolt",
		gameId = "Metadata/Items/Gem/SkillGemGlacialBolt",
		variantId = "GlacialBolt",
		grantedEffectId = "GlacialBoltAmmoPlayer",
		additionalGrantedEffectId1 = "GlacialBoltPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			ammunition = true,
			projectile = true,
			cold = true,
			duration = true,
		},
		gemType = "Attack",
		tagString = "AoE, Ammunition, Projectile, Cold, Duration",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPermafrostBolts"] = {
		name = "Permafrost Bolts",
		baseTypeName = "Load Permafrost Bolts",
		gameId = "Metadata/Items/Gem/SkillGemPermafrostBolts",
		variantId = "PermafrostBolts",
		grantedEffectId = "PermafrostBoltsAmmoPlayer",
		additionalGrantedEffectId1 = "PermafrostBoltsPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			ammunition = true,
			projectile = true,
			cold = true,
		},
		gemType = "Attack",
		tagString = "AoE, Ammunition, Projectile, Cold",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemHailstormRounds"] = {
		name = "Hailstorm Rounds",
		baseTypeName = "Load Hailstorm Rounds",
		gameId = "Metadata/Items/Gem/SkillGemHailstormRounds",
		variantId = "HailstormRounds",
		grantedEffectId = "HailstormRoundsAmmoPlayer",
		additionalGrantedEffectId1 = "HailstormRoundsPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			ammunition = true,
			projectile = true,
			sustained = true,
			cold = true,
		},
		gemType = "Attack",
		tagString = "AoE, Ammunition, Projectile, Sustained, Cold",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 11,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemIceShards"] = {
		name = "Ice Shards",
		baseTypeName = "Load Ice Shards",
		gameId = "Metadata/Items/Gem/SkillGemIceShards",
		variantId = "IceShards",
		grantedEffectId = "IceShardsAmmoPlayer",
		additionalGrantedEffectId1 = "IceShardsPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			ammunition = true,
			projectile = true,
			sustained = true,
			cold = true,
			hazard = true,
		},
		gemType = "Attack",
		tagString = "AoE, Ammunition, Projectile, Sustained, Cold, Hazard",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPlasmaBlast"] = {
		name = "Plasma Blast",
		baseTypeName = "Load Plasma Blast",
		gameId = "Metadata/Items/Gem/SkillGemPlasmaBlast",
		variantId = "PlasmaBlast",
		grantedEffectId = "PlasmaBlastAmmoPlayer",
		additionalGrantedEffectId1 = "PlasmaBlastPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			ammunition = true,
			projectile = true,
			lightning = true,
			channelling = true,
		},
		gemType = "Attack",
		tagString = "AoE, Ammunition, Projectile, Lightning, Channelling",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 13,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemGalvanicShards"] = {
		name = "Galvanic Shards",
		baseTypeName = "Load Galvanic Shards",
		gameId = "Metadata/Items/Gem/SkillGemGalvanicShards",
		variantId = "GalvanicShards",
		grantedEffectId = "GalvanicShardsAmmoPlayer",
		additionalGrantedEffectId1 = "GalvanicShardsPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			ammunition = true,
			projectile = true,
			lightning = true,
			chaining = true,
		},
		gemType = "Attack",
		tagString = "Ammunition, Projectile, Lightning, Chaining",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemStormblastBolts"] = {
		name = "Stormblast Bolts",
		baseTypeName = "Load Stormblast Bolts",
		gameId = "Metadata/Items/Gem/SkillGemStormblastBolts",
		variantId = "StormblastBolts",
		grantedEffectId = "StormblastBoltsAmmoPlayer",
		additionalGrantedEffectId1 = "StormblastBoltsPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			ammunition = true,
			projectile = true,
			lightning = true,
			duration = true,
		},
		gemType = "Attack",
		tagString = "AoE, Ammunition, Projectile, Lightning, Duration",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemShockburstRounds"] = {
		name = "Shockburst Rounds",
		baseTypeName = "Load Shockburst Rounds",
		gameId = "Metadata/Items/Gem/SkillGemShockburstRounds",
		variantId = "ShockburstRounds",
		grantedEffectId = "ShockburstRoundsAmmoPlayer",
		additionalGrantedEffectId1 = "ShockburstRoundsPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			ammunition = true,
			projectile = true,
			lightning = true,
			payoff = true,
		},
		gemType = "Attack",
		tagString = "AoE, Ammunition, Projectile, Lightning, Payoff",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 11,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemRagingCrySupport"] = {
		name = "Raging Cry",
		gameId = "Metadata/Items/Gem/SupportGemRagingCry",
		variantId = "RagingCrySupport",
		grantedEffectId = "SupportRagingCryPlayer",
		tags = {
			support = true,
			warcry = true,
		},
		gemType = "Support",
		tagString = "Warcry",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemIncreasedArmourBreakSupport"] = {
		name = "Demolisher",
		gameId = "Metadata/Items/Gems/SupportGemDemolisher",
		variantId = "IncreasedArmourBreakSupport",
		grantedEffectId = "SupportIncreasedArmourBreakPlayer",
		tags = {
			support = true,
			spell = true,
		},
		gemType = "Support",
		tagString = "Spell",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemRageforgedSupport"] = {
		name = "Rageforged",
		gameId = "Metadata/Items/Gem/SupportGemRageforged",
		variantId = "RageforgedSupport",
		grantedEffectId = "SupportRageforgedPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemPlayerDefaultQuarterstaff"] = {
		name = "Quarterstaff Strike",
		baseTypeName = "Quarterstaff Strike",
		gameId = "Metadata/Items/Gem/SkillGemPlayerDefaultQuarterstaff",
		variantId = "PlayerDefaultQuarterstaff",
		grantedEffectId = "MeleeQuarterstaffPlayer",
		tags = {
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike",
		weaponRequirements = "Quarterstaff",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPlayerDefault1HMace"] = {
		name = "Mace Strike",
		baseTypeName = "Mace Strike",
		gameId = "Metadata/Items/Gem/SkillGemPlayerDefault1HMace",
		variantId = "PlayerDefault1HMace",
		grantedEffectId = "Melee1HMacePlayer",
		tags = {
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike",
		weaponRequirements = "One Hand Mace",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPlayerDefaultMaceMace"] = {
		name = "Mace Strike",
		baseTypeName = "Mace Strike",
		gameId = "Metadata/Items/Gem/SkillGemPlayerDefaultMaceMace",
		variantId = "PlayerDefaultMaceMace",
		grantedEffectId = "MeleeMaceMacePlayer",
		tags = {
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike",
		weaponRequirements = "One Hand Mace, Two Hand Mace",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPlayerDefault2HMace"] = {
		name = "Mace Strike",
		baseTypeName = "Mace Strike",
		gameId = "Metadata/Items/Gem/SkillGemPlayerDefault2HMace",
		variantId = "PlayerDefault2HMace",
		grantedEffectId = "Melee2HMacePlayer",
		tags = {
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike",
		weaponRequirements = "Two Hand Mace",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPlayerDefaultSpear"] = {
		name = "Spear Stab",
		baseTypeName = "Spear Stab",
		gameId = "Metadata/Items/Gem/SkillGemPlayerDefaultSpear",
		variantId = "PlayerDefaultSpear",
		grantedEffectId = "MeleeSpearPlayer",
		tags = {
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPlayerDefaultSpearOffHand"] = {
		name = "Spear Stab",
		baseTypeName = "Spear Stab",
		gameId = "Metadata/Items/Gem/SkillGemPlayerDefaultSpearOffHand",
		variantId = "PlayerDefaultSpearOffHand",
		grantedEffectId = "MeleeSpearOffHandPlayer",
		tags = {
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPlayerDefaultSpearThrow"] = {
		name = "Spear Throw",
		baseTypeName = "Spear Throw",
		gameId = "Metadata/Items/Gem/SkillGemPlayerDefaultSpearThrow",
		variantId = "PlayerDefaultSpearThrow",
		grantedEffectId = "SpearThrowPlayer",
		additionalStatSet1 = "SpearThrowFrenzyChargePlayer",
		tags = {
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			area = true,
			projectile = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, AoE, Projectile",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPlayerDefaultBow"] = {
		name = "Bow Shot",
		baseTypeName = "Bow Shot",
		gameId = "Metadata/Items/Gem/SkillGemPlayerDefaultBow",
		variantId = "PlayerDefaultBow",
		grantedEffectId = "MeleeBowPlayer",
		tags = {
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			projectile = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, Projectile",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPlayerDefaultCrossbow"] = {
		name = "Crossbow Shot",
		baseTypeName = "Crossbow Shot",
		gameId = "Metadata/Items/Gem/SkillGemPlayerDefaultCrossbow",
		variantId = "PlayerDefaultCrossbow",
		grantedEffectId = "MeleeCrossbowPlayer",
		additionalGrantedEffectId1 = "UnloadAmmoPlayer",
		tags = {
			grants_active_skill = true,
			attack = true,
			area = true,
			ammunition = true,
			projectile = true,
		},
		gemType = "Attack",
		tagString = "AoE, Ammunition, Projectile",
		weaponRequirements = "Crossbow",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemAscendancyLifeRemnants"] = {
		name = "Life Remnants",
		baseTypeName = "Life Remnants",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyLifeRemnants",
		variantId = "AscendancyLifeRemnants",
		grantedEffectId = "LifeRemnantsPlayer",
		tags = {
			grants_active_skill = true,
			buff = true,
			persistent = true,
			remnant = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Remnant",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemAscendancyExplosiveConcoction"] = {
		name = "Explosive Concoction",
		baseTypeName = "Explosive Concoction",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyExplosiveConcoction",
		variantId = "AscendancyExplosiveConcoction",
		grantedEffectId = "ExplosiveConcoctionPlayer",
		tags = {
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			fire = true,
			duration = true,
		},
		gemType = "Attack",
		tagString = "AoE, Projectile, Fire, Duration",
		weaponRequirements = "Unarmed",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemAscendancyFulminatingConcoction"] = {
		name = "Fulminating Concoction",
		baseTypeName = "Fulminating Concoction",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyFulminatingConcoction",
		variantId = "AscendancyFulminatingConcoction",
		grantedEffectId = "FulminatingConcoctionPlayer",
		tags = {
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			lightning = true,
			duration = true,
		},
		gemType = "Attack",
		tagString = "AoE, Projectile, Lightning, Duration",
		weaponRequirements = "Unarmed",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemAscendancyShatteringConcoction"] = {
		name = "Shattering Concoction",
		baseTypeName = "Shattering Concoction",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyShatteringConcoction",
		variantId = "AscendancyShatteringConcoction",
		grantedEffectId = "ShatteringConcoctionPlayer",
		tags = {
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			cold = true,
			duration = true,
		},
		gemType = "Attack",
		tagString = "AoE, Projectile, Cold, Duration",
		weaponRequirements = "Unarmed",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemAscendancyPoisonousConcoction"] = {
		name = "Poisonous Concoction",
		baseTypeName = "Poisonous Concoction",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyPoisonousConcoction",
		variantId = "AscendancyPoisonousConcoction",
		grantedEffectId = "PoisonousConcoctionPlayer",
		tags = {
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			physical = true,
			chaos = true,
		},
		gemType = "Attack",
		tagString = "AoE, Projectile, Physical, Chaos",
		weaponRequirements = "Unarmed",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemAscendancyBleedingConcoction"] = {
		name = "Bleeding Concoction",
		baseTypeName = "Bleeding Concoction",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyBleedingConcoction",
		variantId = "AscendancyBleedingConcoction",
		grantedEffectId = "BleedingConcoctionPlayer",
		tags = {
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			physical = true,
		},
		gemType = "Attack",
		tagString = "AoE, Projectile, Physical",
		weaponRequirements = "Unarmed",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemAscendancyMeditate"] = {
		name = "Meditate",
		baseTypeName = "Meditate",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyMeditate",
		variantId = "AscendancyMeditate",
		grantedEffectId = "MeditatePlayer",
		tags = {
			grants_active_skill = true,
			spell = true,
			channelling = true,
		},
		gemType = "Spell",
		tagString = "Channelling",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemAscendancyElementalStorm"] = {
		name = "Elemental Storm",
		baseTypeName = "Elemental Storm",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyElementalStorm",
		variantId = "AscendancyElementalStorm",
		grantedEffectId = "ElementalStormPlayer",
		additionalStatSet1 = "ElementalStormFirePlayer",
		additionalStatSet2 = "ElementalStormLightningPlayer",
		additionalStatSet3 = "ElementalStormColdPlayer",
		tags = {
			grants_active_skill = true,
			spell = true,
			area = true,
			trigger = true,
			lightning = true,
			cold = true,
			fire = true,
			duration = true,
			orb = true,
		},
		gemType = "Spell",
		tagString = "AoE, Trigger, Lightning, Cold, Fire, Duration, Orb",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemAscendancyUnboundAvatar"] = {
		name = "Unbound Avatar",
		baseTypeName = "Unbound Avatar",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyUnboundAvatar",
		variantId = "AscendancyUnboundAvatar",
		grantedEffectId = "UnboundAvatarPlayer",
		tags = {
			grants_active_skill = true,
			buff = true,
			lightning = true,
			cold = true,
			fire = true,
			duration = true,
			conditional = true,
		},
		gemType = "Buff",
		tagString = "Lightning, Cold, Fire, Duration, Conditional",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemAscendancySummonInfernalHound"] = {
		name = "Summon Infernal Hound",
		baseTypeName = "Summon Infernal Hound",
		gameId = "Metadata/Items/Gem/SkillGemAscendancySummonInfernalHound",
		variantId = "AscendancySummonInfernalHound",
		grantedEffectId = "SummonInfernalHoundPlayer",
		tags = {
			grants_active_skill = true,
			minion = true,
			companion = true,
			persistent = true,
			fire = true,
		},
		gemType = "Minion",
		tagString = "Companion, Persistent, Fire",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemTemporalRift"] = {
		name = "Temporal Rift",
		baseTypeName = "Temporal Rift",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyTemporalRift",
		variantId = "TemporalRift",
		grantedEffectId = "TemporalRiftPlayer",
		tags = {
			grants_active_skill = true,
			spell = true,
			buff = true,
			persistent = true,
		},
		gemType = "Spell",
		tagString = "Buff, Persistent",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemTimeFreeze"] = {
		name = "Time Freeze",
		baseTypeName = "Time Freeze",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyTimeFreeze",
		variantId = "TimeFreeze",
		grantedEffectId = "TimeFreezePlayer",
		tags = {
			grants_active_skill = true,
			spell = true,
			area = true,
			duration = true,
		},
		gemType = "Spell",
		tagString = "AoE, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemTimeSnap"] = {
		name = "Time Snap",
		baseTypeName = "Time Snap",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyTimeSnap",
		variantId = "TimeSnap",
		grantedEffectId = "TimeSnapPlayer",
		tags = {
			grants_active_skill = true,
			spell = true,
		},
		gemType = "Spell",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemAscendancyEncasedInJade"] = {
		name = "Encase in Jade",
		baseTypeName = "Encase in Jade",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyEncasedInJade",
		variantId = "AscendancyEncasedInJade",
		grantedEffectId = "EncaseInJadePlayer",
		tags = {
			grants_active_skill = true,
			spell = true,
			buff = true,
			physical = true,
			duration = true,
		},
		gemType = "Spell",
		tagString = "Buff, Physical, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemDemonForm"] = {
		name = "Demon Form",
		baseTypeName = "Demon Form",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyDemonForm",
		variantId = "DemonForm",
		grantedEffectId = "DemonFormPlayer",
		tags = {
			grants_active_skill = true,
			shapeshift = true,
			buff = true,
			persistent = true,
		},
		gemType = "Shapeshift",
		tagString = "Buff, Persistent",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSorceryWard"] = {
		name = "Sorcery Ward",
		baseTypeName = "Sorcery Ward",
		gameId = "Metadata/Items/Gem/SkillGemAscendancySorceryWard",
		variantId = "SorceryWard",
		grantedEffectId = "SorceryWardPlayer",
		tags = {
			grants_active_skill = true,
			spell = true,
			buff = true,
			persistent = true,
			lightning = true,
			cold = true,
			fire = true,
		},
		gemType = "Spell",
		tagString = "Buff, Persistent, Lightning, Cold, Fire",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemIntoTheBreach"] = {
		name = "Into the Breach",
		baseTypeName = "Into the Breach",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyIntoTheBreach",
		variantId = "IntoTheBreach",
		grantedEffectId = "IntoTheBreachPlayer",
		tags = {
			grants_active_skill = true,
			buff = true,
			persistent = true,
			remnant = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Remnant",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemAncestralSpriits"] = {
		name = "Ancestral Spirits",
		baseTypeName = "Ancestral Spirits",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyAncestralSpirits",
		variantId = "AncestralSpriits",
		grantedEffectId = "AncestralSpiritsPlayer",
		tags = {
			grants_active_skill = true,
			minion = true,
			trigger = true,
			duration = true,
		},
		gemType = "Minion",
		tagString = "Trigger, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemRitualSacrifice"] = {
		name = "Ritual Sacrifice",
		baseTypeName = "Ritual Sacrifice",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyRitualSacrifice",
		variantId = "RitualSacrifice",
		grantedEffectId = "RitualSacrificePlayer",
		tags = {
			grants_active_skill = true,
			buff = true,
			duration = true,
		},
		gemType = "Buff",
		tagString = "Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemBloodBoil"] = {
		name = "Blood Boil",
		baseTypeName = "Blood Boil",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyBloodBoil",
		variantId = "BloodBoil",
		grantedEffectId = "BloodBoilPlayer",
		tags = {
			grants_active_skill = true,
			buff = true,
			persistent = true,
			area = true,
			physical = true,
			duration = true,
		},
		gemType = "Buff",
		tagString = "Persistent, AoE, Physical, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemCastFireSpellOnHit"] = {
		name = "Fire Spell on Hit",
		baseTypeName = "Fire Spell on Melee Hit",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyFireSpellOnHit",
		variantId = "CastFireSpellOnHit",
		grantedEffectId = "MetaCastFireSpellOnHitPlayer",
		additionalGrantedEffectId1 = "SupportMetaCastFireSpellOnHitPlayer",
		tags = {
			grants_active_skill = true,
			buff = true,
			persistent = true,
			trigger = true,
			fire = true,
			meta = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Trigger, Fire, Meta",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemManifestWeapon"] = {
		name = "Manifest Weapon",
		baseTypeName = "Manifest Weapon",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyManifestWeapon",
		variantId = "ManifestWeapon",
		grantedEffectId = "ManifestWeaponPlayer",
		tags = {
			grants_active_skill = true,
			minion = true,
			companion = true,
			persistent = true,
		},
		gemType = "Minion",
		tagString = "Companion, Persistent",
		weaponRequirements = "Any Melee Martial Weapon",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemTemperWeapon"] = {
		name = "Temper Weapon",
		baseTypeName = "Temper Weapon",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyTemperWeapon",
		variantId = "TemperWeapon",
		grantedEffectId = "TemperWeaponPlayer",
		additionalGrantedEffectId1 = "TemperWeaponCombustionPlayer",
		tags = {
			grants_active_skill = true,
			attack = true,
			buff = true,
			area = true,
			melee = true,
			trigger = true,
			fire = true,
			channelling = true,
		},
		gemType = "Attack",
		tagString = "Buff, AoE, Melee, Trigger, Fire, Channelling",
		weaponRequirements = "Any Melee Martial Weapon",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSupportingFire"] = {
		name = "Supporting Fire",
		baseTypeName = "Supporting Fire",
		gameId = "Metadata/Items/Gems/SkillGemAscendancySupportingFire",
		variantId = "SupportingFire",
		grantedEffectId = "SupportingFirePlayer",
		additionalGrantedEffectId1 = "CommandDeathFromAbovePlayer",
		tags = {
			grants_active_skill = true,
			minion = true,
			persistent = true,
		},
		gemType = "Minion",
		tagString = "Persistent",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemArmourExplosionSupport"] = {
		name = "Armour Explosion",
		gameId = "Metadata/Items/Gem/SupportGemArmourExplosion",
		variantId = "ArmourExplosionSupport",
		grantedEffectId = "SupportArmourExplosionPlayer",
		additionalGrantedEffectId1 = "ArmourExplosionPlayer",
		tags = {
			support = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			trigger = true,
			fire = true,
			payoff = true,
		},
		gemType = "Support",
		tagString = "Attack, AoE, Trigger, Fire, Payoff",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemStompingGroundSupport"] = {
		name = "Stomping Ground",
		gameId = "Metadata/Items/Gem/SupportGemStompingGround",
		variantId = "StompingGroundSupport",
		grantedEffectId = "SupportStompingGroundPlayer",
		additionalGrantedEffectId1 = "StompingGroundShockwavePlayer",
		tags = {
			support = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			trigger = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Attack, AoE, Trigger, Physical",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCrescendoSupport"] = {
		name = "Crescendo",
		gameId = "Metadata/Items/Gems/SupportGemCrescendo",
		variantId = "CrescendoSupport",
		grantedEffectId = "SupportCrescendoPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
			strike = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee, Strike",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFireExposureSupport"] = {
		name = "Fire Exposure",
		gameId = "Metadata/Items/Gems/SupportGemFireExposure",
		variantId = "FireExposureSupport",
		grantedEffectId = "SupportFireExposurePlayer",
		tags = {
			support = true,
			fire = true,
			duration = true,
		},
		gemType = "Support",
		tagString = "Fire, Duration",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemLightningExposureSupport"] = {
		name = "Lightning Exposure",
		gameId = "Metadata/Items/Gems/SupportGemLightningExposure",
		variantId = "LightningExposureSupport",
		grantedEffectId = "SupportLightningExposurePlayer",
		tags = {
			support = true,
			lightning = true,
			duration = true,
		},
		gemType = "Support",
		tagString = "Lightning, Duration",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemColdExposureSupport"] = {
		name = "Cold Exposure",
		gameId = "Metadata/Items/Gems/SupportGemColdExposure",
		variantId = "ColdExposureSupport",
		grantedEffectId = "SupportColdExposurePlayer",
		tags = {
			support = true,
			cold = true,
			duration = true,
			critical = true,
		},
		gemType = "Support",
		tagString = "Cold, Duration, Critical",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDeadlyPoisonSupport"] = {
		name = "Deadly Poison",
		gameId = "Metadata/Items/Gems/SupportGemDeadlyPoison",
		variantId = "DeadlyPoisonSupport",
		grantedEffectId = "SupportDeadlyPoisonPlayer",
		tags = {
			support = true,
			chaos = true,
		},
		gemType = "Support",
		tagString = "Chaos",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDeepCutsSupport"] = {
		name = "Deep Cuts",
		gameId = "Metadata/Items/Gems/SupportGemDeepCuts",
		variantId = "DeepCutsSupport",
		grantedEffectId = "SupportDeepCutsPlayer",
		tags = {
			support = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Physical",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCorruptingCrySupport"] = {
		name = "Corrupting Cry",
		gameId = "Metadata/Items/Gems/SupportGemCorruptingCry",
		variantId = "CorruptingCrySupport",
		grantedEffectId = "SupportCorruptingCryPlayer",
		tags = {
			support = true,
			warcry = true,
			physical = true,
			duration = true,
		},
		gemType = "Support",
		tagString = "Warcry, Physical, Duration",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemWindowOfOpportunitySupport"] = {
		name = "Window of Opportunity",
		gameId = "Metadata/Items/Gems/SupportGemWindowOfOpportunity",
		variantId = "WindowOfOpportunitySupport",
		grantedEffectId = "SupportWindowOfOpportunityPlayer",
		tags = {
			support = true,
			channelling = true,
		},
		gemType = "Support",
		tagString = "Channelling",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFireMasterySupport"] = {
		name = "Fire Mastery",
		gameId = "Metadata/Items/Gems/SupportGemFireMastery",
		variantId = "FireMasterySupport",
		grantedEffectId = "SupportFireMasteryPlayer",
		tags = {
			support = true,
			fire = true,
		},
		gemType = "Support",
		tagString = "Fire",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemColdMasterySupport"] = {
		name = "Cold Mastery",
		gameId = "Metadata/Items/Gems/SupportGemColdMastery",
		variantId = "ColdMasterySupport",
		grantedEffectId = "SupportColdMasteryPlayer",
		tags = {
			support = true,
			cold = true,
		},
		gemType = "Support",
		tagString = "Cold",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemLightningMasterySupport"] = {
		name = "Lightning Mastery",
		gameId = "Metadata/Items/Gems/SupportGemLightningMastery",
		variantId = "LightningMasterySupport",
		grantedEffectId = "SupportLightningMasteryPlayer",
		tags = {
			support = true,
			lightning = true,
		},
		gemType = "Support",
		tagString = "Lightning",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemChaosMasterySupport"] = {
		name = "Chaos Mastery",
		gameId = "Metadata/Items/Gems/SupportGemChaosMastery",
		variantId = "ChaosMasterySupport",
		grantedEffectId = "SupportChaosMasteryPlayer",
		tags = {
			support = true,
			chaos = true,
		},
		gemType = "Support",
		tagString = "Chaos",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemPhysicalMasterySupport"] = {
		name = "Physical Mastery",
		gameId = "Metadata/Items/Gems/SupportGemPhysicalMastery",
		variantId = "PhysicalMasterySupport",
		grantedEffectId = "SupportPhysicalMasteryPlayer",
		tags = {
			support = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Physical",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemMinionMasterySupport"] = {
		name = "Minion Mastery",
		gameId = "Metadata/Items/Gems/SupportGemMinionMastery",
		variantId = "MinionMasterySupport",
		grantedEffectId = "SupportMinionMasteryPlayer",
		tags = {
			support = true,
			minion = true,
		},
		gemType = "Support",
		tagString = "Minion",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemShockchainArrow"] = {
		name = "Shockchain Arrow",
		baseTypeName = "Shockchain Arrow",
		gameId = "Metadata/Items/Gem/SkillGemShockchainArrow",
		variantId = "ShockchainArrow",
		grantedEffectId = "ShockchainArrowPlayer",
		additionalStatSet1 = "ShockchainArrowBeamPlayer",
		additionalStatSet2 = "ShockchainArrowExplosionPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			area = true,
			projectile = true,
			lightning = true,
			chaining = true,
			payoff = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, AoE, Projectile, Lightning, Chaining, Payoff",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 11,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemVaultingImpact"] = {
		name = "Vaulting Impact",
		baseTypeName = "Vaulting Impact",
		gameId = "Metadata/Items/Gem/SkillGemVaultingImpact",
		variantId = "VaultingImpact",
		grantedEffectId = "VaultingImpactPlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			slam = true,
			travel = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Slam, Travel",
		weaponRequirements = "Quarterstaff",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemStormWave"] = {
		name = "Storm Wave",
		baseTypeName = "Storm Wave",
		gameId = "Metadata/Items/Gem/SkillGemStormWave",
		variantId = "StormWave",
		grantedEffectId = "StormWavePlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			lightning = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Lightning",
		weaponRequirements = "Quarterstaff",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemMantraOfDestruction"] = {
		name = "Mantra of Destruction",
		baseTypeName = "Mantra of Destruction",
		gameId = "Metadata/Items/Gem/SkillGemMantraOfDestruction",
		variantId = "MantraOfDestruction",
		grantedEffectId = "MantraOfDestructionPlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			chaos = true,
			duration = true,
			conditional = true,
		},
		gemType = "Buff",
		tagString = "Chaos, Duration, Conditional",
		weaponRequirements = "Quarterstaff",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSwiftAfflictionSupport"] = {
		name = "Swift Affliction",
		gameId = "Metadata/Items/Gem/SupportGemSwiftAffliction",
		variantId = "SwiftAfflictionSupport",
		grantedEffectId = "SupportSwiftAfflictionPlayer",
		tags = {
			support = true,
			duration = true,
		},
		gemType = "Support",
		tagString = "Duration",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemIntenseAgonySupport"] = {
		name = "Intense Agony",
		gameId = "Metadata/Items/Gem/SupportGemIntenseAgony",
		variantId = "IntenseAgonySupport",
		grantedEffectId = "SupportIntenseAgonyPlayer",
		tags = {
			support = true,
			spell = true,
			duration = true,
		},
		gemType = "Support",
		tagString = "Spell, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDrainedAilmentSupport"] = {
		name = "Drain Ailments",
		gameId = "Metadata/Items/Gems/SupportGemDrainAilments",
		variantId = "DrainedAilmentSupport",
		grantedEffectId = "SupportDrainedAilmentPlayer",
		tags = {
			support = true,
			spell = true,
		},
		gemType = "Support",
		tagString = "Spell",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemChaoticFreezeSupport"] = {
		name = "Chaotic Freeze",
		gameId = "Metadata/Items/Gem/SupportGemChaoticFreeze",
		variantId = "ChaoticFreezeSupport",
		grantedEffectId = "SupportChaoticFreezePlayer",
		tags = {
			support = true,
			spell = true,
			chaos = true,
		},
		gemType = "Support",
		tagString = "Spell, Chaos",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemHinderSupport"] = {
		name = "Hinder",
		gameId = "Metadata/Items/Gem/SupportGemHinder",
		variantId = "HinderSupport",
		grantedEffectId = "SupportHinderPlayer",
		tags = {
			support = true,
			spell = true,
			chaos = true,
		},
		gemType = "Support",
		tagString = "Spell, Chaos",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemComboFinisherSupport"] = {
		name = "Combo Finisher",
		gameId = "Metadata/Items/Gems/SupportGemComboFinisher",
		variantId = "ComboFinisherSupport",
		grantedEffectId = "SupportComboFinisherPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
			strike = true,
			conditional = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee, Strike, Conditional",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemEncumberanceSupport"] = {
		name = "Encumbrance",
		gameId = "Metadata/Items/Gems/SupportGemEncumbrance",
		variantId = "EncumberanceSupport",
		grantedEffectId = "SupportEncumberancePlayer",
		tags = {
			support = true,
			spell = true,
		},
		gemType = "Support",
		tagString = "Spell",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemPrecisionSupport"] = {
		name = "Precision",
		gameId = "Metadata/Items/Gems/SupportGemPrecision",
		variantId = "PrecisionSupport",
		grantedEffectId = "SupportPrecisionPlayer",
		tags = {
			support = true,
			buff = true,
			persistent = true,
		},
		gemType = "Support",
		tagString = "Buff, Persistent",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemClaritySupport"] = {
		name = "Clarity",
		gameId = "Metadata/Items/Gems/SupportGemClarity",
		variantId = "ClaritySupport",
		grantedEffectId = "SupportClarityPlayer",
		tags = {
			support = true,
			buff = true,
			persistent = true,
		},
		gemType = "Support",
		tagString = "Buff, Persistent",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemVitalitySupport"] = {
		name = "Vitality",
		gameId = "Metadata/Items/Gems/SupportGemVitality",
		variantId = "VitalitySupport",
		grantedEffectId = "SupportVitalityPlayer",
		tags = {
			support = true,
			buff = true,
			persistent = true,
		},
		gemType = "Support",
		tagString = "Buff, Persistent",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemHerbalismSupport"] = {
		name = "Herbalism",
		gameId = "Metadata/Items/Gems/SupportGemHerbalism",
		variantId = "HerbalismSupport",
		grantedEffectId = "SupportHerbalismPlayer",
		tags = {
			support = true,
			buff = true,
			persistent = true,
		},
		gemType = "Support",
		tagString = "Buff, Persistent",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCannibalismSupport"] = {
		name = "Cannibalism",
		gameId = "Metadata/Items/Gems/SupportGemCannibalism",
		variantId = "CannibalismSupport",
		grantedEffectId = "SupportCannibalismPlayer",
		tags = {
			support = true,
			buff = true,
			persistent = true,
		},
		gemType = "Support",
		tagString = "Buff, Persistent",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemRuptureSupport"] = {
		name = "Rupture",
		gameId = "Metadata/Items/Gem/SupportGemRupture",
		variantId = "RuptureSupport",
		grantedEffectId = "SupportRupturePlayer",
		tags = {
			support = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Physical",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCullingStrikeSupport"] = {
		name = "Culling Strike",
		gameId = "Metadata/Items/Gem/SupportGemCullingStrike",
		variantId = "CullingStrikeSupport",
		grantedEffectId = "SupportCullingStrikePlayer",
		tags = {
			support = true,
			attack = true,
		},
		gemType = "Support",
		tagString = "Attack",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemSpellCascadeSupport"] = {
		name = "Spell Cascade",
		gameId = "Metadata/Items/Gem/SupportGemSpellCascade",
		variantId = "SpellCascadeSupport",
		grantedEffectId = "SupportSpellCascadePlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemArmourBreakSupport"] = {
		name = "Splinter",
		gameId = "Metadata/Items/Gems/SupportGemSplinter",
		variantId = "ArmourBreakSupport",
		grantedEffectId = "SupportArmourBreakPlayer",
		tags = {
			support = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Physical",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFarCombatSupport"] = {
		name = "Longshot",
		gameId = "Metadata/Items/Gems/SupportGemLongshot",
		variantId = "FarCombatSupport",
		grantedEffectId = "SupportFarCombatPlayer",
		tags = {
			support = true,
			attack = true,
		},
		gemType = "Support",
		tagString = "Attack",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDazingSupport"] = {
		name = "Discombobulate",
		gameId = "Metadata/Items/Gems/SupportGemDiscombobulate",
		variantId = "DazingSupport",
		grantedEffectId = "SupportDazingPlayer",
		tags = {
			support = true,
			attack = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Attack, Physical",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDazedBreakSupport"] = {
		name = "Break Posture",
		gameId = "Metadata/Items/Gems/SupportGemBreakPosture",
		variantId = "DazedBreakSupport",
		grantedEffectId = "SupportDazedBreakPlayer",
		tags = {
			support = true,
			attack = true,
		},
		gemType = "Support",
		tagString = "Attack",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDazingCrySupport"] = {
		name = "Dazing Cry",
		gameId = "Metadata/Items/Gem/SupportGemDazingCry",
		variantId = "DazingCrySupport",
		grantedEffectId = "SupportDazingCryPlayer",
		tags = {
			support = true,
			warcry = true,
		},
		gemType = "Support",
		tagString = "Warcry",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCursedGroundSupport"] = {
		name = "Cursed Ground",
		gameId = "Metadata/Items/Gem/SupportGemCursedGround",
		variantId = "CursedGroundSupport",
		grantedEffectId = "SupportCursedGroundPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemWeaponElementalDamageSupport"] = {
		name = "Primal Armament",
		gameId = "Metadata/Items/Gems/SupportGemPrimalArmament",
		variantId = "WeaponElementalDamageSupport",
		grantedEffectId = "SupportWeaponElementalDamagePlayer",
		tags = {
			support = true,
			attack = true,
		},
		gemType = "Support",
		tagString = "Attack",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCooldownReductionSupport"] = {
		name = "Ingenuity",
		gameId = "Metadata/Items/Gems/SupportGemIngenuity",
		variantId = "CooldownReductionSupport",
		grantedEffectId = "SupportCooldownReductionPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemAscendancyElementalExpression"] = {
		name = "Elemental Expression",
		baseTypeName = "Elemental Expression",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyElementalExpression",
		variantId = "AscendancyElementalExpression",
		grantedEffectId = "ElementalExpressionTriggeredPlayer",
		additionalStatSet1 = "ElementalExpressionFirePlayer",
		additionalStatSet2 = "ElementalExpressionColdPlayer",
		additionalStatSet3 = "ElementalExpressionLightningPlayer",
		tags = {
			grants_active_skill = true,
			spell = true,
			area = true,
			projectile = true,
			trigger = true,
			lightning = true,
			cold = true,
			fire = true,
			chaining = true,
		},
		gemType = "Spell",
		tagString = "AoE, Projectile, Trigger, Lightning, Cold, Fire, Chaining",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemIceShot"] = {
		name = "Ice Shot",
		baseTypeName = "Ice Shot",
		gameId = "Metadata/Items/Gem/SkillGemIceShot",
		variantId = "IceShot",
		grantedEffectId = "IceShotPlayer",
		additionalStatSet1 = "IceShotShardPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			projectile = true,
			cold = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, Projectile, Cold",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemWarBanner"] = {
		name = "War Banner",
		baseTypeName = "War Banner",
		gameId = "Metadata/Items/Gem/SkillGemWarBanner",
		variantId = "WarBanner",
		grantedEffectId = "WarBannerReservationPlayer",
		additionalGrantedEffectId1 = "WarBannerPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			banner = true,
			buff = true,
			persistent = true,
			area = true,
			aura = true,
			duration = true,
		},
		gemType = "Banner",
		tagString = "Buff, Persistent, AoE, Aura, Duration",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 4,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemDefianceBanner"] = {
		name = "Defiance Banner",
		baseTypeName = "Defiance Banner",
		gameId = "Metadata/Items/Gem/SkillGemDefianceBanner",
		variantId = "DefianceBanner",
		grantedEffectId = "DefianceBannerReservationPlayer",
		additionalGrantedEffectId1 = "DefianceBannerPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			banner = true,
			buff = true,
			persistent = true,
			area = true,
			aura = true,
			duration = true,
		},
		gemType = "Banner",
		tagString = "Buff, Persistent, AoE, Aura, Duration",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 8,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemDreadBanner"] = {
		name = "Dread Banner",
		baseTypeName = "Dread Banner",
		gameId = "Metadata/Items/Gem/SkillGemDreadBanner",
		variantId = "DreadBanner",
		grantedEffectId = "DreadBannerReservationPlayer",
		additionalGrantedEffectId1 = "DreadBannerPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			banner = true,
			buff = true,
			persistent = true,
			area = true,
			aura = true,
			duration = true,
		},
		gemType = "Banner",
		tagString = "Buff, Persistent, AoE, Aura, Duration",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 14,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemFreezingMark"] = {
		name = "Freezing Mark",
		baseTypeName = "Freezing Mark",
		gameId = "Metadata/Items/Gem/SkillGemFreezingMark",
		variantId = "FreezingMark",
		grantedEffectId = "FreezingMarkPlayer",
		additionalGrantedEffectId1 = "TriggeredFreezingMarkNovaPlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			attack = true,
			area = true,
			mark = true,
			cold = true,
			duration = true,
			nova = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, Attack, AoE, Mark, Cold, Duration, Nova",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemVoltaicMark"] = {
		name = "Voltaic Mark",
		baseTypeName = "Voltaic Mark",
		gameId = "Metadata/Items/Gem/SkillGemVoltaicMark",
		variantId = "VoltaicMark",
		grantedEffectId = "VoltaicMarkPlayer",
		additionalGrantedEffectId1 = "TriggeredVoltaicMarkNovaPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			attack = true,
			area = true,
			mark = true,
			lightning = true,
			duration = true,
			nova = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, Attack, AoE, Mark, Lightning, Duration, Nova",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemHandOfChayula"] = {
		name = "Hand of Chayula",
		baseTypeName = "Hand of Chayula",
		gameId = "Metadata/Items/Gem/SkillGemHandOfChayula",
		variantId = "HandOfChayula",
		grantedEffectId = "HandOfChayulaPlayer",
		additionalGrantedEffectId1 = "SupportHandOfChayulaPlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
			chaos = true,
			meta = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike, Chaos, Meta",
		weaponRequirements = "Quarterstaff, Unarmed",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemTimeOfNeed"] = {
		name = "Time of Need",
		baseTypeName = "Time of Need",
		gameId = "Metadata/Items/Gem/SkillGemTimeOfNeed",
		variantId = "TimeOfNeed",
		grantedEffectId = "TimeOfNeedPlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			duration = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Duration",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 8,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemElementalInvocation"] = {
		name = "Elemental Invocation",
		baseTypeName = "Elemental Invocation",
		gameId = "Metadata/Items/Gem/SkillGemElementalInvocation",
		variantId = "ElementalInvocation",
		grantedEffectId = "MetaElementalInvocationPlayer",
		additionalGrantedEffectId1 = "SupportElementalInvocationPlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			trigger = true,
			lightning = true,
			cold = true,
			fire = true,
			meta = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Trigger, Lightning, Cold, Fire, Meta",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 8,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemAttrition"] = {
		name = "Attrition",
		baseTypeName = "Attrition",
		gameId = "Metadata/Items/Gem/SkillGemAttrition",
		variantId = "Attrition",
		grantedEffectId = "AttritionPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
		},
		gemType = "Buff",
		tagString = "Persistent",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 14,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemElementalConflux"] = {
		name = "Elemental Conflux",
		baseTypeName = "Elemental Conflux",
		gameId = "Metadata/Items/Gem/SkillGemElementalConflux",
		variantId = "ElementalConflux",
		grantedEffectId = "ElementalConfluxPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			lightning = true,
			cold = true,
			fire = true,
			duration = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Lightning, Cold, Fire, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 14,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemEmergencyReload"] = {
		name = "Emergency Reload",
		baseTypeName = "Emergency Reload",
		gameId = "Metadata/Items/Gem/SkillGemEmergencyReload",
		variantId = "EmergencyReload",
		grantedEffectId = "EmergencyReloadPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			buff = true,
			duration = true,
		},
		gemType = "Buff",
		tagString = "Duration",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 11,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemBloodFountainSupport"] = {
		name = "Font of Blood",
		gameId = "Metadata/Items/Gems/SupportGemFontofBlood",
		variantId = "BloodFountainSupport",
		grantedEffectId = "SupportBloodFountainPlayer",
		tags = {
			support = true,
			totem = true,
		},
		gemType = "Support",
		tagString = "Totem",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemManaFountainSupport"] = {
		name = "Font of Mana",
		gameId = "Metadata/Items/Gems/SupportGemFontofMana",
		variantId = "ManaFountainSupport",
		grantedEffectId = "SupportManaFountainPlayer",
		tags = {
			support = true,
			totem = true,
		},
		gemType = "Support",
		tagString = "Totem",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemRageFountainSupport"] = {
		name = "Font of Rage",
		gameId = "Metadata/Items/Gems/SupportGemFontofRage",
		variantId = "RageFountainSupport",
		grantedEffectId = "SupportRageFountainPlayer",
		tags = {
			support = true,
			totem = true,
		},
		gemType = "Support",
		tagString = "Totem",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemAftershockChanceSupport"] = {
		name = "Aftershock",
		gameId = "Metadata/Items/Gems/SupportGemAftershock",
		variantId = "AftershockChanceSupport",
		grantedEffectId = "SupportAftershockChancePlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
			slam = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee, Slam",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemClusterGrenade"] = {
		name = "Cluster Grenade",
		baseTypeName = "Cluster Grenade",
		gameId = "Metadata/Items/Gem/SkillGemClusterGrenade",
		variantId = "ClusterGrenade",
		grantedEffectId = "ClusterGrenadePlayer",
		additionalGrantedEffectId1 = "ClusterGrenadeMiniPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			grenade = true,
			fire = true,
		},
		gemType = "Attack",
		tagString = "AoE, Projectile, Grenade, Fire",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 13,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemTornadoShot"] = {
		name = "Tornado Shot",
		baseTypeName = "Tornado Shot",
		gameId = "Metadata/Items/Gem/SkillGemTornadoShot",
		variantId = "TornadoShot",
		grantedEffectId = "TornadoShotPlayer",
		additionalStatSet1 = "TornadoShotNovaPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			area = true,
			projectile = true,
			physical = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, AoE, Projectile, Physical, Duration",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 11,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemChargeInfusion"] = {
		name = "Charge Infusion",
		baseTypeName = "Charge Infusion",
		gameId = "Metadata/Items/Gem/SkillGemChargeInfusion",
		variantId = "ChargeInfusion",
		grantedEffectId = "ChargeInfusionPlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
		},
		gemType = "Buff",
		tagString = "Persistent",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 14,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemHolyDescentSupport"] = {
		name = "Holy Descent",
		gameId = "Metadata/Items/Gem/SupportGemHolyDescent",
		variantId = "HolyDescentSupport",
		grantedEffectId = "SupportHolyDescentPlayer",
		tags = {
			support = true,
			area = true,
			duration = true,
		},
		gemType = "Support",
		tagString = "AoE, Duration",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemBurningRunesSupport"] = {
		name = "Burning Inscription",
		gameId = "Metadata/Items/Gems/SupportGemBurningInscription",
		variantId = "BurningRunesSupport",
		grantedEffectId = "SupportBurningRunesPlayer",
		additionalGrantedEffectId1 = "TriggeredBurningRunesPlayer",
		secondaryEffectName = "Burning Inscription",
		tags = {
			support = true,
			grants_active_skill = true,
			area = true,
			trigger = true,
			fire = true,
		},
		gemType = "Support",
		tagString = "AoE, Trigger, Fire",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemShardScavenger"] = {
		name = "Shard Scavenger",
		baseTypeName = "Shard Scavenger",
		gameId = "Metadata/Items/Gem/SkillGemShardScavenger",
		variantId = "ShardScavenger",
		grantedEffectId = "ShardScavengerPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			physical = true,
			lightning = true,
			cold = true,
			fire = true,
			duration = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Physical, Lightning, Cold, Fire, Duration",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 8,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemDoubleBarrelSupport"] = {
		name = "Double Barrel",
		gameId = "Metadata/Items/Gem/SupportGemDoubleBarrel",
		variantId = "DoubleBarrelSupport",
		grantedEffectId = "SupportDoubleBarrelPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemAutoReloadSupport"] = {
		name = "Auto Reload",
		gameId = "Metadata/Items/Gem/SupportGemAutoReload",
		variantId = "AutoReloadSupport",
		grantedEffectId = "SupportAutoReloadPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemAmmoConservationSupport"] = {
		name = "Ammo Conservation",
		gameId = "Metadata/Items/Gem/SupportGemAmmoConservation",
		variantId = "AmmoConservationSupport",
		grantedEffectId = "SupportAmmoConservationPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemNimbleReloadSupport"] = {
		name = "Nimble Reload",
		gameId = "Metadata/Items/Gem/SupportGemNimbleReload",
		variantId = "NimbleReloadSupport",
		grantedEffectId = "SupportNimbleReloadPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFreshClipSupport"] = {
		name = "Fresh Clip",
		gameId = "Metadata/Items/Gem/SupportGemFreshClip",
		variantId = "FreshClipSupport",
		grantedEffectId = "SupportFreshClipPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemOverwhelmingPresence"] = {
		name = "Overwhelming Presence",
		baseTypeName = "Overwhelming Presence",
		gameId = "Metadata/Items/Gem/SkillGemOverwhelmingPresence",
		variantId = "OverwhelmingPresence",
		grantedEffectId = "OverwhelmingPresencePlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			aura = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Aura",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 8,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemAlchemistsBoon"] = {
		name = "Alchemist's Boon",
		baseTypeName = "Alchemist's Boon",
		gameId = "Metadata/Items/Gem/SkillGemAlchemistsBoon",
		variantId = "AlchemistsBoon",
		grantedEffectId = "AlchemistsBoonPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			aura = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Aura",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 14,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemReapersInvocation"] = {
		name = "Reaper's Invocation",
		baseTypeName = "Reaper's Invocation",
		gameId = "Metadata/Items/Gem/SkillGemReapersInvocation",
		variantId = "ReapersInvocation",
		grantedEffectId = "MetaReapersInvocationPlayer",
		additionalGrantedEffectId1 = "SupportReapersInvocationPlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			trigger = true,
			meta = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Trigger, Meta",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 14,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSacrifice"] = {
		name = "Sacrifice",
		baseTypeName = "Sacrifice",
		gameId = "Metadata/Items/Gem/SkillGemSacrifice",
		variantId = "Sacrifice",
		grantedEffectId = "SacrificePlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			minion = true,
			buff = true,
			persistent = true,
		},
		gemType = "Minion",
		tagString = "Buff, Persistent",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 14,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemArchmage"] = {
		name = "Archmage",
		baseTypeName = "Archmage",
		gameId = "Metadata/Items/Gem/SkillGemArchmage",
		variantId = "Archmage",
		grantedEffectId = "ArchmagePlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			lightning = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Lightning",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 14,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemMagneticSalvo"] = {
		name = "Magnetic Salvo",
		baseTypeName = "Magnetic Salvo",
		gameId = "Metadata/Items/Gem/SkillGemMagneticSalvo",
		variantId = "MagneticSalvo",
		grantedEffectId = "MagneticSalvoPlayer",
		additionalStatSet1 = "MagneticSalvoEmpoweredPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			lightning = true,
		},
		gemType = "Attack",
		tagString = "AoE, Projectile, Lightning",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 13,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemFreezingSalvo"] = {
		name = "Freezing Salvo",
		baseTypeName = "Freezing Salvo",
		gameId = "Metadata/Items/Gem/SkillGemFreezingSalvo",
		variantId = "FreezingSalvo",
		grantedEffectId = "FreezingSalvoPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			cold = true,
			stages = true,
		},
		gemType = "Attack",
		tagString = "AoE, Projectile, Cold, Staged",
		weaponRequirements = "Bow",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemBerserk"] = {
		name = "Berserk",
		baseTypeName = "Berserk",
		gameId = "Metadata/Items/Gem/SkillGemBerserk",
		variantId = "Berserk",
		grantedEffectId = "BerserkPlayer",
		tags = {
			strength = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
		},
		gemType = "Buff",
		tagString = "Persistent",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 14,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemHeraldOfPlague"] = {
		name = "Herald of Plague",
		baseTypeName = "Herald of Plague",
		gameId = "Metadata/Items/Gem/SkillGemHeraldOfPlague",
		variantId = "HeraldOfPlague",
		grantedEffectId = "HeraldOfPlaguePlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			chaos = true,
			herald = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Chaos, Herald",
		weaponRequirements = "Any Martial Weapon",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 8,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemHeraldOfBlood"] = {
		name = "Herald of Blood",
		baseTypeName = "Herald of Blood",
		gameId = "Metadata/Items/Gem/SkillGemHeraldOfBlood",
		variantId = "HeraldOfBlood",
		grantedEffectId = "HeraldOfBloodPlayer",
		additionalStatSet1 = "HeraldOfBloodExplosionPlayer",
		tags = {
			buff = true,
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			persistent = true,
			area = true,
			physical = true,
			herald = true,
			payoff = true,
		},
		gemType = "Buff",
		tagString = "Attack, Persistent, AoE, Physical, Herald, Payoff",
		weaponRequirements = "Any Martial Weapon",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 4,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemWitheringPresence"] = {
		name = "Withering Presence",
		baseTypeName = "Withering Presence",
		gameId = "Metadata/Items/Gem/SkillGemWitheringPresence",
		variantId = "WitheringPresence",
		grantedEffectId = "WitheringPresencePlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			aura = true,
			chaos = true,
			duration = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Aura, Chaos, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 4,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemCombatFrenzy"] = {
		name = "Combat Frenzy",
		baseTypeName = "Combat Frenzy",
		gameId = "Metadata/Items/Gem/SkillGemCombatFrenzy",
		variantId = "CombatFrenzy",
		grantedEffectId = "CombatFrenzyPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
		},
		gemType = "Buff",
		tagString = "Persistent",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 8,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemCastOnBlock"] = {
		name = "Cast on Block",
		baseTypeName = "Cast on Block",
		gameId = "Metadata/Items/Gems/SkillGemCastOnBlock",
		variantId = "CastOnBlock",
		grantedEffectId = "MetaCastOnBlockPlayer",
		additionalGrantedEffectId1 = "SupportMetaCastOnBlockPlayer",
		tags = {
			buff = true,
			strength = true,
			intelligence = true,
			grants_active_skill = true,
			persistent = true,
			trigger = true,
			meta = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Trigger, Meta",
		reqStr = 50,
		reqDex = 0,
		reqInt = 50,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemGatheringStorm"] = {
		name = "Gathering Storm",
		baseTypeName = "Gathering Storm",
		gameId = "Metadata/Items/Gem/SkillGemGatheringStorm",
		variantId = "GatheringStorm",
		grantedEffectId = "GatheringStormPlayer",
		additionalStatSet1 = "GatheringStormPerfectPlayer",
		additionalStatSet2 = "GatheringStormExplodePlayer",
		tags = {
			dexterity = true,
			intelligence = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			lightning = true,
			duration = true,
			channelling = true,
			travel = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Lightning, Duration, Channelling, Travel",
		weaponRequirements = "Quarterstaff",
		reqStr = 0,
		reqDex = 50,
		reqInt = 50,
		Tier = 13,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemUpheavalSupport"] = {
		name = "Upheaval",
		gameId = "Metadata/Items/Gem/SupportGemUpheaval",
		variantId = "UpheavalSupport",
		grantedEffectId = "SupportUpheavalPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemGroundEffectDurationSupport"] = {
		name = "Despoiler",
		gameId = "Metadata/Items/Gems/SupportGemDespoiler",
		variantId = "GroundEffectDurationSupport",
		grantedEffectId = "SupportGroundEffectDurationPlayer",
		tags = {
			support = true,
			duration = true,
		},
		gemType = "Support",
		tagString = "Duration",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemTerrainChainSupport"] = {
		name = "Ricochet",
		gameId = "Metadata/Items/Gem/SupportGemRicochet",
		variantId = "TerrainChainSupport",
		grantedEffectId = "SupportRicochetPlayer",
		tags = {
			support = true,
			projectile = true,
		},
		gemType = "Support",
		tagString = "Projectile",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemTotemPlacementSpeedSupport"] = {
		name = "Ancestral Urgency",
		gameId = "Metadata/Items/Gem/SupportGemAncestralUrgency",
		variantId = "TotemPlacementSpeedSupport",
		grantedEffectId = "SupportAncestralUrgencyPlayer",
		tags = {
			support = true,
			totem = true,
		},
		gemType = "Support",
		tagString = "Totem",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemLongFuseSupport"] = {
		name = "Long Fuse",
		gameId = "Metadata/Items/Gem/SupportGemLongFuse",
		variantId = "LongFuseSupport",
		grantedEffectId = "SupportLongFusePlayer",
		tags = {
			support = true,
			grenade = true,
		},
		gemType = "Support",
		tagString = "Grenade",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemPayloadSupport"] = {
		name = "Payload",
		gameId = "Metadata/Items/Gem/SupportGemPayload",
		variantId = "PayloadSupport",
		grantedEffectId = "SupportPayloadPlayer",
		tags = {
			support = true,
			grenade = true,
		},
		gemType = "Support",
		tagString = "Grenade",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemStripAwaySupport"] = {
		name = "Strip Away",
		gameId = "Metadata/Items/Gem/SupportGemStripAway",
		variantId = "StripAwaySupport",
		grantedEffectId = "SupportStripAwayPlayer",
		tags = {
			support = true,
			spell = true,
		},
		gemType = "Support",
		tagString = "Spell",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemIronwoodSupport"] = {
		name = "Ironwood",
		gameId = "Metadata/Items/Gem/SupportGemIronwood",
		variantId = "IronwoodSupport",
		grantedEffectId = "SupportIronwoodPlayer",
		tags = {
			support = true,
			totem = true,
		},
		gemType = "Support",
		tagString = "Totem",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemConsideredCastingSupport"] = {
		name = "Considered Casting",
		gameId = "Metadata/Items/Gem/SupportGemConsideredCasting",
		variantId = "ConsideredCastingSupport",
		grantedEffectId = "SupportConsideredCastingPlayer",
		tags = {
			support = true,
			spell = true,
		},
		gemType = "Support",
		tagString = "Spell",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemWildshardsSupport"] = {
		name = "Wildshards",
		gameId = "Metadata/Items/Gem/SupportGemWildshards",
		variantId = "WildshardsSupport",
		grantedEffectId = "SupportWildshardsPlayer",
		tags = {
			support = true,
			spell = true,
			projectile = true,
		},
		gemType = "Support",
		tagString = "Spell, Projectile",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemIcicleSupport"] = {
		name = "Icicle",
		gameId = "Metadata/Items/Gem/SupportGemIcicle",
		variantId = "IcicleSupport",
		grantedEffectId = "SupportIciclePlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemGlacierSupport"] = {
		name = "Glacier",
		gameId = "Metadata/Items/Gem/SupportGemGlacier",
		variantId = "GlacierSupport",
		grantedEffectId = "SupportGlacierPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemHeftSupport"] = {
		name = "Heft",
		gameId = "Metadata/Items/Gem/SupportGemHeft",
		variantId = "HeftSupport",
		grantedEffectId = "SupportHeftPlayer",
		tags = {
			support = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Physical",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemTempestuousTempoSupport"] = {
		name = "Rising Tempest",
		gameId = "Metadata/Items/Gems/SupportGemRisingTempest",
		variantId = "TempestuousTempoSupport",
		grantedEffectId = "SupportTempestuousTempoPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemExtractionSupport"] = {
		name = "Extraction",
		gameId = "Metadata/Items/Gem/SupportGemExtraction",
		variantId = "ExtractionSupport",
		grantedEffectId = "SupportExtractionPlayer",
		tags = {
			support = true,
			spell = true,
		},
		gemType = "Support",
		tagString = "Spell",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemAstralProjectionSupport"] = {
		name = "Astral Projection",
		gameId = "Metadata/Items/Gem/SupportGemAstralProjection",
		variantId = "AstralProjectionSupport",
		grantedEffectId = "SupportAstralProjectionPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemPracticedComboSupport"] = {
		name = "Practiced Combo",
		gameId = "Metadata/Items/Gem/SupportGemPracticedCombo",
		variantId = "PracticedComboSupport",
		grantedEffectId = "SupportPracticedComboPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
			strike = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee, Strike",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemLeverageSupport"] = {
		name = "Leverage",
		gameId = "Metadata/Items/Gem/SupportGemLeverage",
		variantId = "LeverageSupport",
		grantedEffectId = "SupportLeveragePlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCulminationSupport"] = {
		name = "Culmination",
		gameId = "Metadata/Items/Gem/SupportGemCulmination",
		variantId = "CulminationSupport",
		grantedEffectId = "SupportCulminationPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
			conditional = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee, Conditional",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemPotentialSupport"] = {
		name = "Potential",
		gameId = "Metadata/Items/Gem/SupportGemPotential",
		variantId = "PotentialSupport",
		grantedEffectId = "SupportPotentialPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemExpanseSupport"] = {
		name = "Expanse",
		gameId = "Metadata/Items/Gem/SupportGemExpanse",
		variantId = "ExpanseSupport",
		grantedEffectId = "SupportExpansePlayer",
		tags = {
			support = true,
			area = true,
		},
		gemType = "Support",
		tagString = "AoE",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemExciseSupport"] = {
		name = "Excise",
		gameId = "Metadata/Items/Gem/SupportGemExcise",
		variantId = "ExciseSupport",
		grantedEffectId = "SupportExcisePlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemExecrateSupport"] = {
		name = "Execrate",
		gameId = "Metadata/Items/Gem/SupportGemExecrate",
		variantId = "ExecrateSupport",
		grantedEffectId = "SupportExecratePlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemEnergyRetentionSupport"] = {
		name = "Energy Retention",
		gameId = "Metadata/Items/Gem/SupportGemEnergyRetention",
		variantId = "EnergyRetentionSupport",
		grantedEffectId = "SupportEnergyRetentionPlayer",
		tags = {
			support = true,
			trigger = true,
		},
		gemType = "Support",
		tagString = "Trigger",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFerocitySupport"] = {
		name = "Ferocity",
		gameId = "Metadata/Items/Gem/SupportGemFerocity",
		variantId = "FerocitySupport",
		grantedEffectId = "SupportFerocityPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemAblationSupport"] = {
		name = "Ablation",
		gameId = "Metadata/Items/Gem/SupportGemAblation",
		variantId = "AblationSupport",
		grantedEffectId = "SupportAblationPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDanseMacabreSupport"] = {
		name = "Danse Macabre",
		gameId = "Metadata/Items/Gem/SupportGemDanseMacabre",
		variantId = "DanseMacabreSupport",
		grantedEffectId = "SupportDanseMacabrePlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCapacitorSupport"] = {
		name = "Capacitor",
		gameId = "Metadata/Items/Gem/SupportGemCapacitor",
		variantId = "CapacitorSupport",
		grantedEffectId = "SupportCapacitorPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemImpetusSupport"] = {
		name = "Impetus",
		gameId = "Metadata/Items/Gem/SupportGemImpetus",
		variantId = "ImpetusSupport",
		grantedEffectId = "SupportImpetusPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDissipateSupport"] = {
		name = "Dissipate",
		gameId = "Metadata/Items/Gem/SupportGemDissipate",
		variantId = "DissipateSupport",
		grantedEffectId = "SupportDissipatePlayer",
		tags = {
			support = true,
			remnant = true,
		},
		gemType = "Support",
		tagString = "Remnant",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFlukeSupport"] = {
		name = "Fluke",
		gameId = "Metadata/Items/Gem/SupportGemFluke",
		variantId = "FlukeSupport",
		grantedEffectId = "SupportFlukePlayer",
		tags = {
			support = true,
			trigger = true,
		},
		gemType = "Support",
		tagString = "Trigger",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemZenithSupport"] = {
		name = "Zenith",
		gameId = "Metadata/Items/Gem/SupportGemZenith",
		variantId = "ZenithSupport",
		grantedEffectId = "SupportZenithPlayer",
		tags = {
			support = true,
			spell = true,
		},
		gemType = "Support",
		tagString = "Spell",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemBiddingSupport"] = {
		name = "Bidding",
		gameId = "Metadata/Items/Gem/SupportGemBidding",
		variantId = "BiddingSupport",
		grantedEffectId = "SupportBiddingPlayer",
		tags = {
			support = true,
			command = true,
			minion = true,
		},
		gemType = "Support",
		tagString = "Command, Minion",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCommandmentSupport"] = {
		name = "Commandment",
		gameId = "Metadata/Items/Gem/SupportGemCommandment",
		variantId = "CommandmentSupport",
		grantedEffectId = "SupportCommandment",
		tags = {
			support = true,
			command = true,
			minion = true,
		},
		gemType = "Support",
		tagString = "Command, Minion",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFlowSupport"] = {
		name = "Flow",
		gameId = "Metadata/Items/Gem/SupportGemFlow",
		variantId = "FlowSupport",
		grantedEffectId = "SupportFlowPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemAdhereSupport"] = {
		name = "Adhere",
		gameId = "Metadata/Items/Gem/SupportGemAdhere",
		variantId = "AdhereSupport",
		grantedEffectId = "SupportAdherePlayer",
		tags = {
			support = true,
			grenade = true,
		},
		gemType = "Support",
		tagString = "Grenade",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemRetaliateSupport"] = {
		name = "Retaliate",
		gameId = "Metadata/Items/Gem/SupportGemRetaliate",
		variantId = "RetaliateSupport",
		grantedEffectId = "SupportRetaliatePlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemTremorsSupport"] = {
		name = "Tremors",
		gameId = "Metadata/Items/Gem/SupportGemTremors",
		variantId = "TremorsSupport",
		grantedEffectId = "SupportTremorsPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
			slam = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee, Slam",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemSalvoSupport"] = {
		name = "Salvo",
		gameId = "Metadata/Items/Gem/SupportGemSalvo",
		variantId = "SalvoSupport",
		grantedEffectId = "SupportSalvoPlayer",
		tags = {
			support = true,
			attack = true,
			projectile = true,
		},
		gemType = "Support",
		tagString = "Attack, Projectile",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCadenceSupport"] = {
		name = "Cadence",
		gameId = "Metadata/Items/Gem/SupportGemCadence",
		variantId = "CadenceSupport",
		grantedEffectId = "SupportCadencePlayer",
		tags = {
			support = true,
			attack = true,
		},
		gemType = "Support",
		tagString = "Attack",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemVoltSupport"] = {
		name = "Volt",
		gameId = "Metadata/Items/Gem/SupportGemVolt",
		variantId = "VoltSupport",
		grantedEffectId = "SupportVoltPlayer",
		tags = {
			support = true,
			attack = true,
			projectile = true,
			lightning = true,
			chaining = true,
		},
		gemType = "Support",
		tagString = "Attack, Projectile, Lightning, Chaining",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemBoneShrapnelSupport"] = {
		name = "Bone Shrapnel",
		gameId = "Metadata/Items/Gem/SupportGemBoneShrapnel",
		variantId = "BoneShrapnelSupport",
		grantedEffectId = "SupportBoneShrapnelPlayer",
		additionalGrantedEffectId1 = "TriggeredBoneShrapnelPlayer",
		secondaryEffectName = "Bone Shrapnel Explosion",
		tags = {
			support = true,
			grants_active_skill = true,
			area = true,
			trigger = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "AoE, Trigger, Physical",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemAlignmentSupport"] = {
		name = "Alignment",
		gameId = "Metadata/Items/Gem/SupportGemAlignment",
		variantId = "AlignmentSupport",
		grantedEffectId = "SupportAlignmentPlayer",
		tags = {
			support = true,
			attack = true,
			projectile = true,
		},
		gemType = "Support",
		tagString = "Attack, Projectile",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDerangeSupport"] = {
		name = "Derange",
		gameId = "Metadata/Items/Gem/SupportGemDerange",
		variantId = "DerangeSupport",
		grantedEffectId = "SupportDerangePlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemChargedShotsSupport"] = {
		name = "Charged Shots",
		gameId = "Metadata/Items/Gem/SupportGemChargedShots",
		variantId = "ChargedShotsSupport",
		grantedEffectId = "SupportChargedShotsPlayer",
		tags = {
			support = true,
			attack = true,
			projectile = true,
		},
		gemType = "Support",
		tagString = "Attack, Projectile",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemProfanitySupport"] = {
		name = "Profanity",
		gameId = "Metadata/Items/Gem/SupportGemProfanity",
		variantId = "ProfanitySupport",
		grantedEffectId = "SupportProfanityPlayer",
		tags = {
			support = true,
			chaos = true,
		},
		gemType = "Support",
		tagString = "Chaos",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemBurgeonSupport"] = {
		name = "Burgeon",
		gameId = "Metadata/Items/Gem/SupportGemBurgeon",
		variantId = "BurgeonSupport",
		grantedEffectId = "SupportBurgeonPlayer",
		tags = {
			support = true,
			channelling = true,
		},
		gemType = "Support",
		tagString = "Channelling",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemSteadfastSupport"] = {
		name = "Steadfast",
		gameId = "Metadata/Items/Gem/SupportGemSteadfast",
		variantId = "SteadfastSupport",
		grantedEffectId = "SupportSteadfastPlayer",
		tags = {
			support = true,
			channelling = true,
		},
		gemType = "Support",
		tagString = "Channelling",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFlamepierceSupport"] = {
		name = "Flamepierce",
		gameId = "Metadata/Items/Gem/SupportGemFlamepierce",
		variantId = "FlamepierceSupport",
		grantedEffectId = "SupportFlamepiercePlayer",
		tags = {
			support = true,
			projectile = true,
			fire = true,
		},
		gemType = "Support",
		tagString = "Projectile, Fire",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFreezeForkSupport"] = {
		name = "Freezefork",
		gameId = "Metadata/Items/Gem/SupportGemFreezefork",
		variantId = "FreezeForkSupport",
		grantedEffectId = "SupportFreezeforkPlayer",
		tags = {
			support = true,
			projectile = true,
			cold = true,
		},
		gemType = "Support",
		tagString = "Projectile, Cold",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemStormchainSupport"] = {
		name = "Stormchain",
		gameId = "Metadata/Items/Gem/SupportGemStormchain",
		variantId = "StormchainSupport",
		grantedEffectId = "SupportStormchainPlayer",
		tags = {
			support = true,
			projectile = true,
			lightning = true,
			chaining = true,
		},
		gemType = "Support",
		tagString = "Projectile, Lightning, Chaining",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemVerglasSupport"] = {
		name = "Verglas",
		gameId = "Metadata/Items/Gem/SupportGemVerglas",
		variantId = "VerglasSupport",
		grantedEffectId = "SupportVerglasPlayer",
		tags = {
			support = true,
			cold = true,
		},
		gemType = "Support",
		tagString = "Cold",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemEmbitterSupport"] = {
		name = "Embitter",
		gameId = "Metadata/Items/Gem/SupportGemEmbitter",
		variantId = "EmbitterSupport",
		grantedEffectId = "SupportEmbitterPlayer",
		tags = {
			support = true,
			cold = true,
		},
		gemType = "Support",
		tagString = "Cold",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemBattershoutSupport"] = {
		name = "Battershout",
		gameId = "Metadata/Items/Gem/SupportGemBattershout",
		variantId = "BattershoutSupport",
		grantedEffectId = "SupportBattershoutPlayer",
		additionalGrantedEffectId1 = "TriggeredBattershoutExplosionPlayer",
		tags = {
			support = true,
			grants_active_skill = true,
			warcry = true,
			area = true,
			trigger = true,
			physical = true,
			payoff = true,
		},
		gemType = "Support",
		tagString = "Warcry, AoE, Trigger, Physical, Payoff",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemNadirSupport"] = {
		name = "Nadir",
		gameId = "Metadata/Items/Gem/SupportGemNadir",
		variantId = "NadirSupport",
		grantedEffectId = "SupportNadirPlayer",
		tags = {
			support = true,
			stages = true,
		},
		gemType = "Support",
		tagString = "Staged",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemAbidingHexSupport"] = {
		name = "Abiding Hex",
		gameId = "Metadata/Items/Gem/SupportGemAbidingHex",
		variantId = "AbidingHexSupport",
		grantedEffectId = "SupportAbidingHexPlayer",
		tags = {
			support = true,
			curse = true,
		},
		gemType = "Support",
		tagString = "Curse",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemRustedSpikesSupport"] = {
		name = "Rusted Spikes",
		gameId = "Metadata/Items/Gem/SupportGemRustedSpikes",
		variantId = "RustedSpikesSupport",
		grantedEffectId = "SupportRustedSpikesPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCrazedMinionsSupport"] = {
		name = "Crazed Minions",
		gameId = "Metadata/Items/Gem/SupportGemCrazedMinions",
		variantId = "CrazedMinionsSupport",
		grantedEffectId = "SupportCrazedMinionsPlayer",
		tags = {
			support = true,
			minion = true,
			persistent = true,
		},
		gemType = "Support",
		tagString = "Minion, Persistent",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemSpectralVolleySupport"] = {
		name = "Spectral Volley",
		gameId = "Metadata/Items/Gem/SupportGemSpectralVolley",
		variantId = "SpectralVolleySupport",
		grantedEffectId = "SupportSpectralVolleyPlayer",
		tags = {
			support = true,
			attack = true,
			projectile = true,
		},
		gemType = "Support",
		tagString = "Attack, Projectile",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemRallySupport"] = {
		name = "Rally",
		gameId = "Metadata/Items/Gem/SupportGemRally",
		variantId = "RallySupport",
		grantedEffectId = "SupportRallyPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDeathmarchSupport"] = {
		name = "Deathmarch",
		gameId = "Metadata/Items/Gem/SupportGemDeathmarch",
		variantId = "DeathmarchSupport",
		grantedEffectId = "SupportDeathmarchPlayer",
		tags = {
			support = true,
			minion = true,
			persistent = true,
		},
		gemType = "Support",
		tagString = "Minion, Persistent",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemAcrimonySupport"] = {
		name = "Acrimony",
		gameId = "Metadata/Items/Gem/SupportGemAcrimony",
		variantId = "AcrimonySupport",
		grantedEffectId = "SupportAcrimonyPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemConcoctSupport"] = {
		name = "Concoct",
		gameId = "Metadata/Items/Gem/SupportGemConcoct",
		variantId = "ConcoctSupport",
		grantedEffectId = "SupportConcoctPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemAmbrosiaSupport"] = {
		name = "Ambrosia",
		gameId = "Metadata/Items/Gem/SupportGemAmbrosia",
		variantId = "AmbrosiaSupport",
		grantedEffectId = "SupportAmbrosiaPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDomainSupport"] = {
		name = "Domain",
		gameId = "Metadata/Items/Gem/SupportGemDomain",
		variantId = "DomainSupport",
		grantedEffectId = "SupportDomainPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFigureheadSupport"] = {
		name = "Figurehead",
		gameId = "Metadata/Items/Gem/SupportGemFigurehead",
		variantId = "FigureheadSupport",
		grantedEffectId = "SupportFigureheadPlayer",
		tags = {
			support = true,
			remnant = true,
		},
		gemType = "Support",
		tagString = "Remnant",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemInterludeSupport"] = {
		name = "Interlude",
		gameId = "Metadata/Items/Gem/SupportGemInterlude",
		variantId = "InterludeSupport",
		grantedEffectId = "SupportInterludePlayer",
		tags = {
			support = true,
			remnant = true,
		},
		gemType = "Support",
		tagString = "Remnant",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemReverberateSupport"] = {
		name = "Reverberate",
		gameId = "Metadata/Items/Gem/SupportGemReverberate",
		variantId = "ReverberateSupport",
		grantedEffectId = "SupportReveberatePlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
			slam = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee, Slam",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemClashSupport"] = {
		name = "Clash",
		gameId = "Metadata/Items/Gem/SupportGemClash",
		variantId = "ClashSupport",
		grantedEffectId = "SupportClashPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemMaladySupport"] = {
		name = "Malady",
		gameId = "Metadata/Items/Gem/SupportGemMalady",
		variantId = "MaladySupport",
		grantedEffectId = "SupportMaladyPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDazzleSupport"] = {
		name = "Dazzle",
		gameId = "Metadata/Items/Gem/SupportGemDazzle",
		variantId = "DazzleSupport",
		grantedEffectId = "SupportDazzlePlayer",
		tags = {
			support = true,
			attack = true,
		},
		gemType = "Support",
		tagString = "Attack",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemSyzygySupport"] = {
		name = "Syzygy",
		gameId = "Metadata/Items/Gem/SupportGemSyzygy",
		variantId = "SyzygySupport",
		grantedEffectId = "SupportSyzygyPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
			slam = true,
			fire = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee, Slam, Fire",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemUnyieldingSupport"] = {
		name = "Unyielding",
		gameId = "Metadata/Items/Gem/SupportGemUnyielding",
		variantId = "UnyieldingSupport",
		grantedEffectId = "SupportUnyieldingPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDesperationSupport"] = {
		name = "Desperation",
		gameId = "Metadata/Items/Gem/SupportGemDesperation",
		variantId = "DesperationSupport",
		grantedEffectId = "SupportDesperationPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemOverextendSupport"] = {
		name = "Overextend",
		gameId = "Metadata/Items/Gem/SupportGemOverextend",
		variantId = "OverextendSupport",
		grantedEffectId = "SupportOverextendPlayer",
		tags = {
			support = true,
			attack = true,
			critical = true,
		},
		gemType = "Support",
		tagString = "Attack, Critical",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemExcoriateSupport"] = {
		name = "Excoriate",
		gameId = "Metadata/Items/Gem/SupportGemExcoriate",
		variantId = "ExcoriateSupport",
		grantedEffectId = "SupportExcoriatePlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemUnbendingSupport"] = {
		name = "Unbending",
		gameId = "Metadata/Items/Gem/SupportGemUnbending",
		variantId = "UnbendingSupport",
		grantedEffectId = "SupportUnbendingPlayer",
		tags = {
			support = true,
			spell = true,
		},
		gemType = "Support",
		tagString = "Spell",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemRetreatSupport"] = {
		name = "Retreat",
		gameId = "Metadata/Items/Gem/SupportGemRetreat",
		variantId = "RetreatSupport",
		grantedEffectId = "SupportRetreatPlayer",
		tags = {
			support = true,
			projectile = true,
		},
		gemType = "Support",
		tagString = "Projectile",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemPursuitSupport"] = {
		name = "Pursuit",
		gameId = "Metadata/Items/Gem/SupportGemPursuit",
		variantId = "PursuitSupport",
		grantedEffectId = "SupportPursuitPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemReachSupport"] = {
		name = "Reach",
		gameId = "Metadata/Items/Gem/SupportGemReach",
		variantId = "ReachSupport",
		grantedEffectId = "SupportReachPlayer",
		tags = {
			support = true,
			attack = true,
			area = true,
		},
		gemType = "Support",
		tagString = "Attack, AoE",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCommiserateSupport"] = {
		name = "Commiserate",
		gameId = "Metadata/Items/Gem/SupportGemCommiserate",
		variantId = "CommiserateSupport",
		grantedEffectId = "SupportCommiseratePlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemBlindsideSupport"] = {
		name = "Blindside",
		gameId = "Metadata/Items/Gem/SupportGemBlindside",
		variantId = "BlindsideSupport",
		grantedEffectId = "SupportBlindsidePlayer",
		tags = {
			support = true,
			critical = true,
		},
		gemType = "Support",
		tagString = "Critical",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemHitAndRunSupport"] = {
		name = "Hit and Run",
		gameId = "Metadata/Items/Gem/SupportGemHitAndRun",
		variantId = "HitAndRunSupport",
		grantedEffectId = "SupportHitAndRunPlayer",
		tags = {
			support = true,
			attack = true,
			conditional = true,
		},
		gemType = "Support",
		tagString = "Attack, Conditional",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCracklingBarrierSupport"] = {
		name = "Crackling Barrier",
		gameId = "Metadata/Items/Gem/SupportGemCracklingBarrier",
		variantId = "CracklingBarrierSupport",
		grantedEffectId = "SupportCracklingBarrierPlayer",
		tags = {
			support = true,
			lightning = true,
			channelling = true,
		},
		gemType = "Support",
		tagString = "Lightning, Channelling",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDefySupport"] = {
		name = "Defy",
		gameId = "Metadata/Items/Gem/SupportGemDefy",
		variantId = "DefySupport",
		grantedEffectId = "SupportDefyPlayer",
		tags = {
			support = true,
			attack = true,
		},
		gemType = "Support",
		tagString = "Attack",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemVolatilitySupport"] = {
		name = "Volatility",
		gameId = "Metadata/Items/Gem/SupportGemVolatility",
		variantId = "VolatilitySupport",
		grantedEffectId = "SupportVolatilityPlayer",
		tags = {
			support = true,
			physical = true,
			chaos = true,
			critical = true,
		},
		gemType = "Support",
		tagString = "Physical, Chaos, Critical",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemMusterSupport"] = {
		name = "Muster",
		gameId = "Metadata/Items/Gem/SupportGemMuster",
		variantId = "MusterSupport",
		grantedEffectId = "SupportMusterPlayer",
		tags = {
			support = true,
			minion = true,
		},
		gemType = "Support",
		tagString = "Minion",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemEnormitySupport"] = {
		name = "Enormity",
		gameId = "Metadata/Items/Gem/SupportGemEnormity",
		variantId = "EnormitySupport",
		grantedEffectId = "SupportEnormityPlayer",
		tags = {
			support = true,
			minion = true,
			persistent = true,
		},
		gemType = "Support",
		tagString = "Minion, Persistent",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCaltropsSupport"] = {
		name = "Caltrops",
		gameId = "Metadata/Items/Gem/SupportGemCaltrops",
		variantId = "CaltropsSupport",
		grantedEffectId = "SupportCaltropsPlayer",
		additionalGrantedEffectId1 = "TriggeredCaltropsPlayer",
		tags = {
			support = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			trigger = true,
			physical = true,
			duration = true,
			hazard = true,
		},
		gemType = "Support",
		tagString = "Attack, AoE, Projectile, Trigger, Physical, Duration, Hazard",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemHaemocrystalsSupport"] = {
		name = "Haemocrystals",
		gameId = "Metadata/Items/Gem/SupportGemHaemocrystals",
		variantId = "HaemocrystalsSupport",
		grantedEffectId = "SupportHaemocrystalsPlayer",
		additionalGrantedEffectId1 = "TriggeredHaemocrystalsPlayer",
		tags = {
			support = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Attack, AoE, Physical",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemSparSupport"] = {
		name = "Spar",
		gameId = "Metadata/Items/Gem/SupportGemSpar",
		variantId = "SparSupport",
		grantedEffectId = "SupportSparPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDauntlessSupport"] = {
		name = "Dauntless",
		gameId = "Metadata/Items/Gem/SupportGemDauntless",
		variantId = "DauntlessSupport",
		grantedEffectId = "SupportDauntlessPlayer",
		tags = {
			support = true,
			conditional = true,
		},
		gemType = "Support",
		tagString = "Conditional",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemMagnetismSupport"] = {
		name = "Magnetism",
		gameId = "Metadata/Items/Gem/SupportGemMagnetism",
		variantId = "MagnetismSupport",
		grantedEffectId = "SupportMagnetismPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemAncestralCallSupport"] = {
		name = "Ancestral Call",
		gameId = "Metadata/Items/Gem/SupportGemAncestralCall",
		variantId = "AncestralCallSupport",
		grantedEffectId = "SupportAncestralCallPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
			strike = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee, Strike",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemQuillburstSupport"] = {
		name = "Quill Burst",
		gameId = "Metadata/Items/Gem/SupportGemQuillBurst",
		variantId = "QuillburstSupport",
		grantedEffectId = "SupportQuillburstPlayer",
		additionalGrantedEffectId1 = "TriggeredQuillburstPlayer",
		tags = {
			support = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			trigger = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Attack, AoE, Melee, Trigger, Physical",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemUnsteadyTempoSupport"] = {
		name = "Unsteady Tempo",
		gameId = "Metadata/Items/Gem/SupportGemUnsteadyTempo",
		variantId = "UnsteadyTempoSupport",
		grantedEffectId = "SupportUnsteadyTempoPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
			critical = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee, Critical",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemAncestralAidSupport"] = {
		name = "Ancestral Aid",
		gameId = "Metadata/Items/Gem/SupportGemAncestralAid",
		variantId = "AncestralAidSupport",
		grantedEffectId = "SupportAncestralAidPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
			strike = true,
			conditional = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee, Strike, Conditional",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemShockingLeapSupport"] = {
		name = "Shocking Leap",
		gameId = "Metadata/Items/Gem/SupportGemShockingLeap",
		variantId = "ShockingLeapSupport",
		grantedEffectId = "SupportShockingLeapPlayer",
		tags = {
			support = true,
			area = true,
			duration = true,
		},
		gemType = "Support",
		tagString = "AoE, Duration",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemBarbsSupport"] = {
		name = "Barbs",
		gameId = "Metadata/Items/Gem/SupportGemBarbs",
		variantId = "BarbsSupport",
		grantedEffectId = "SupportBarbsPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
			strike = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee, Strike",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemRimeSupport"] = {
		name = "Rime",
		gameId = "Metadata/Items/Gem/SupportGemRime",
		variantId = "RimeSupport",
		grantedEffectId = "SupportRimePlayer",
		tags = {
			support = true,
			cold = true,
		},
		gemType = "Support",
		tagString = "Cold",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemArmsLengthSupport"] = {
		name = "Arms Length",
		gameId = "Metadata/Items/Gem/SupportGemArmsLength",
		variantId = "ArmsLengthSupport",
		grantedEffectId = "SupportArmsLengthPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCharmBountySupport"] = {
		name = "Charm Bounty",
		gameId = "Metadata/Items/Gem/SupportGemCharmBounty",
		variantId = "CharmBountySupport",
		grantedEffectId = "SupportCharmBountyPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemThornskinSupport"] = {
		name = "Thornskin",
		gameId = "Metadata/Items/Gem/SupportGemThornskin",
		variantId = "ThornskinSupport",
		grantedEffectId = "SupportThornskinPlayer",
		tags = {
			support = true,
			buff = true,
			persistent = true,
		},
		gemType = "Support",
		tagString = "Buff, Persistent",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemMysticismSupport"] = {
		name = "Mysticism",
		gameId = "Metadata/Items/Gem/SupportGemMysticism",
		variantId = "MysticismSupport",
		grantedEffectId = "SupportMysticismPlayer",
		tags = {
			support = true,
			buff = true,
			persistent = true,
		},
		gemType = "Support",
		tagString = "Buff, Persistent",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDirestrikeSupport"] = {
		name = "Direstrike",
		gameId = "Metadata/Items/Gem/SupportGemDireStrike",
		variantId = "DirestrikeSupport",
		grantedEffectId = "SupportDirestrikePlayer",
		tags = {
			support = true,
			buff = true,
			persistent = true,
		},
		gemType = "Support",
		tagString = "Buff, Persistent",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemUpwellingSupport"] = {
		name = "Upwelling",
		gameId = "Metadata/Items/Gem/SupportGemUpwelling",
		variantId = "UpwellingSupport",
		grantedEffectId = "SupportUpwellingPlayer",
		tags = {
			support = true,
			buff = true,
			persistent = true,
		},
		gemType = "Support",
		tagString = "Buff, Persistent",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemWarmBloodedSupport"] = {
		name = "Warm Blooded",
		gameId = "Metadata/Items/Gem/SupportGemWarmBlooded",
		variantId = "WarmBloodedSupport",
		grantedEffectId = "SupportWarmbloodedPlayer",
		tags = {
			support = true,
			buff = true,
			persistent = true,
		},
		gemType = "Support",
		tagString = "Buff, Persistent",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCoolHeadedSupport"] = {
		name = "Cool Headed",
		gameId = "Metadata/Items/Gem/SupportGemCoolHeaded",
		variantId = "CoolHeadedSupport",
		grantedEffectId = "SupportCoolheadedPlayer",
		tags = {
			support = true,
			buff = true,
			persistent = true,
		},
		gemType = "Support",
		tagString = "Buff, Persistent",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemStrongHeartedSupport"] = {
		name = "Strong Hearted",
		gameId = "Metadata/Items/Gem/SupportGemStrongHearted",
		variantId = "StrongHeartedSupport",
		grantedEffectId = "SupportStrongHeartedPlayer",
		tags = {
			support = true,
			buff = true,
			persistent = true,
		},
		gemType = "Support",
		tagString = "Buff, Persistent",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemUntouchableSupport"] = {
		name = "Untouchable",
		gameId = "Metadata/Items/Gem/SupportGemUntouchable",
		variantId = "UntouchableSupport",
		grantedEffectId = "SupportUntouchablePlayer",
		tags = {
			support = true,
			attack = true,
		},
		gemType = "Support",
		tagString = "Attack",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemRefractionSupport"] = {
		name = "Refraction",
		gameId = "Metadata/Items/Gem/SupportGemRefraction",
		variantId = "RefractionSupport",
		grantedEffectId = "SupportRefractionPlayer",
		tags = {
			support = true,
			buff = true,
			persistent = true,
		},
		gemType = "Support",
		tagString = "Buff, Persistent",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemGreatwoodSupport"] = {
		name = "Greatwood",
		gameId = "Metadata/Items/Gem/SupportGemGreatwood",
		variantId = "GreatwoodSupport",
		grantedEffectId = "SupportGreatwoodPlayer",
		tags = {
			support = true,
			totem = true,
		},
		gemType = "Support",
		tagString = "Totem",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemUnabatingSupport"] = {
		name = "Unabating",
		gameId = "Metadata/Items/Gem/SupportGemUnabating",
		variantId = "UnabatingSupport",
		grantedEffectId = "SupportUnabatingPlayer",
		tags = {
			support = true,
			attack = true,
		},
		gemType = "Support",
		tagString = "Attack",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemPunchThroughSupport"] = {
		name = "Punch Through",
		gameId = "Metadata/Items/Gem/SupportGemPunchThrough",
		variantId = "PunchThroughSupport",
		grantedEffectId = "SupportPunchThroughPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemRipSupport"] = {
		name = "Rip",
		gameId = "Metadata/Items/Gem/SupportGemRip",
		variantId = "RipSupport",
		grantedEffectId = "SupportRipPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
			physical = true,
			duration = true,
			remnant = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee, Physical, Duration, Remnant",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemTearSupport"] = {
		name = "Tear",
		gameId = "Metadata/Items/Gem/SupportGemTear",
		variantId = "TearSupport",
		grantedEffectId = "SupportTearPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee, Physical",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemBrinkSupport"] = {
		name = "Brink",
		gameId = "Metadata/Items/Gem/SupportGemBrink",
		variantId = "BrinkSupport",
		grantedEffectId = "SupportBrinkPlayer",
		tags = {
			support = true,
			attack = true,
			melee = true,
		},
		gemType = "Support",
		tagString = "Attack, Melee",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemTirelessSupport"] = {
		name = "Tireless",
		gameId = "Metadata/Items/Gem/SupportGemTireless",
		variantId = "TirelessSupport",
		grantedEffectId = "SupportTirelessPlayer",
		tags = {
			support = true,
			warcry = true,
		},
		gemType = "Support",
		tagString = "Warcry",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemVolcanicEruptionSupport"] = {
		name = "Volcanic Eruption",
		gameId = "Metadata/Items/Gem/SupportGemVolcanicEruption",
		variantId = "VolcanicEruptionSupport",
		grantedEffectId = "SupportVolcanicEruptionPlayer",
		additionalGrantedEffectId1 = "TriggeredVolcanicEruptionPlayer",
		tags = {
			support = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
			projectile = true,
			trigger = true,
			fire = true,
		},
		gemType = "Support",
		tagString = "Attack, AoE, Melee, Strike, Projectile, Trigger, Fire",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemFirstBloodSupport"] = {
		name = "First Blood",
		gameId = "Metadata/Items/Gem/SupportGemFirstBlood",
		variantId = "FirstBloodSupport",
		grantedEffectId = "SupportFirstBloodPlayer",
		tags = {
			support = true,
			attack = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Attack, Physical",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemRetortSupport"] = {
		name = "Retort",
		gameId = "Metadata/Items/Gem/SupportGemRetort",
		variantId = "RetortSupport",
		grantedEffectId = "SupportRetortPlayer",
		tags = {
			support = true,
			attack = true,
		},
		gemType = "Support",
		tagString = "Attack",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemBloodInTheEyesSupport"] = {
		name = "Blood in the Eyes",
		gameId = "Metadata/Items/Gem/SupportGemBloodintheEyes",
		variantId = "BloodInTheEyesSupport",
		grantedEffectId = "SupportBloodInTheEyesPlayer",
		tags = {
			support = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Physical",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemTwofoldSupport"] = {
		name = "Twofold",
		gameId = "Metadata/Items/Gem/SupportGemTwofold",
		variantId = "TwofoldSupport",
		grantedEffectId = "SupportTwofoldPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemIncisionSupport"] = {
		name = "Incision",
		gameId = "Metadata/Items/Gem/SupportGemIncision",
		variantId = "IncisionSupport",
		grantedEffectId = "SupportIncisionPlayer",
		tags = {
			support = true,
			attack = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Attack, Physical",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCatharsisSupport"] = {
		name = "Catharsis",
		gameId = "Metadata/Items/Gem/SupportGemCatharsis",
		variantId = "CatharsisSupport",
		grantedEffectId = "SupportCatharsisPlayer",
		tags = {
			support = true,
			physical = true,
			chaos = true,
			conditional = true,
		},
		gemType = "Support",
		tagString = "Physical, Chaos, Conditional",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDelayedGratificationSupport"] = {
		name = "Delayed Gratification",
		gameId = "Metadata/Items/Gem/SupportGemDelayedGratification",
		variantId = "DelayedGratificationSupport",
		grantedEffectId = "SupportDelayedGratificationPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemLoyaltySupport"] = {
		name = "Loyalty",
		gameId = "Metadata/Items/Gem/SupportGemLoyalty",
		variantId = "LoyaltySupport",
		grantedEffectId = "SupportLoyaltyPlayer",
		tags = {
			support = true,
			minion = true,
		},
		gemType = "Support",
		tagString = "Minion",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemRearmSupport"] = {
		name = "Rearm",
		gameId = "Metadata/Items/Gem/SupportGemRearm",
		variantId = "RearmSupport",
		grantedEffectId = "SupportRearmPlayer",
		tags = {
			support = true,
			hazard = true,
		},
		gemType = "Support",
		tagString = "Hazard",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDelayedReactionSupport"] = {
		name = "Delayed Reaction",
		gameId = "Metadata/Items/Gem/SupportGemDelayedReaction",
		variantId = "DelayedReactionSupport",
		grantedEffectId = "SupportDelayedReactionPlayer",
		tags = {
			support = true,
			hazard = true,
		},
		gemType = "Support",
		tagString = "Hazard",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemVolatilePowerSupport"] = {
		name = "Volatile Power",
		gameId = "Metadata/Items/Gem/SupportGemVolatilePower",
		variantId = "VolatilePowerSupport",
		grantedEffectId = "SupportVolatilePowerPlayer",
		tags = {
			support = true,
			physical = true,
			chaos = true,
		},
		gemType = "Support",
		tagString = "Physical, Chaos",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemGambleshotSupport"] = {
		name = "Gambleshot",
		gameId = "Metadata/Items/Gem/SupportGemGambleshot",
		variantId = "GambleshotSupport",
		grantedEffectId = "SupportGambleshotPlayer",
		tags = {
			support = true,
			projectile = true,
		},
		gemType = "Support",
		tagString = "Projectile",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemUnerringPowerSupport"] = {
		name = "Unerring Power",
		gameId = "Metadata/Items/Gem/SupportGemUnerringPower",
		variantId = "UnerringPowerSupport",
		grantedEffectId = "SupportUnerringPowerPlayer",
		tags = {
			support = true,
			attack = true,
		},
		gemType = "Support",
		tagString = "Attack",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemImpaleSupport"] = {
		name = "Impale",
		gameId = "Metadata/Items/Gem/SupportGemImpale",
		variantId = "ImpaleSupport",
		grantedEffectId = "SupportImpalePlayer",
		tags = {
			support = true,
			attack = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Attack, Physical",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemPerfectionSupport"] = {
		name = "Perfection",
		gameId = "Metadata/Items/Gem/SupportGemPerfection",
		variantId = "PerfectionSupport",
		grantedEffectId = "SupportPerfectionPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemDeliberationSupport"] = {
		name = "Deliberation",
		gameId = "Metadata/Items/Gem/SupportGemDeliberation",
		variantId = "DeliberationSupport",
		grantedEffectId = "SupportDeliberationPlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemSeeRedSupport"] = {
		name = "See Red",
		gameId = "Metadata/Items/Gem/SupportGemSeeRed",
		variantId = "SeeRedSupport",
		grantedEffectId = "SupportSeeRedPlayer",
		tags = {
			support = true,
			attack = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Attack, Physical",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemExpediteSupport"] = {
		name = "Expedite",
		gameId = "Metadata/Items/Gem/SupportGemExpedite",
		variantId = "ExpediteSupport",
		grantedEffectId = "SupportExpeditePlayer",
		tags = {
			support = true,
		},
		gemType = "Support",
		tagString = "",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemUndermineSupport"] = {
		name = "Undermine",
		gameId = "Metadata/Items/Gem/SupportGemUndermine",
		variantId = "UndermineSupport",
		grantedEffectId = "SupportUnderminePlayer",
		tags = {
			support = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Physical",
		reqStr = 100,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemOutmaneuverSupport"] = {
		name = "Outmaneuver",
		gameId = "Metadata/Items/Gem/SupportGemOutmaneuver",
		variantId = "OutmaneuverSupport",
		grantedEffectId = "SupportOutmaneuverPlayer",
		tags = {
			support = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Physical",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 1,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemSingleOutSupport"] = {
		name = "Single Out",
		gameId = "Metadata/Items/Gem/SupportGemSingleOut",
		variantId = "SingleOutSupport",
		grantedEffectId = "SupportSingleOutPlayer",
		tags = {
			support = true,
			mark = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Mark, Physical",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemTumultSupport"] = {
		name = "Tumult",
		gameId = "Metadata/Items/Gem/SupportGemTumult",
		variantId = "TumultSupport",
		grantedEffectId = "SupportTumultPlayer",
		tags = {
			support = true,
			physical = true,
		},
		gemType = "Support",
		tagString = "Physical",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemInhibitorSupport"] = {
		name = "Inhibitor",
		gameId = "Metadata/Items/Gem/SupportGemInhibitor",
		variantId = "InhibitorSupport",
		grantedEffectId = "SupportInhibitorPlayer",
		tags = {
			support = true,
			spell = true,
		},
		gemType = "Support",
		tagString = "Spell",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemSuffuseSupport"] = {
		name = "Suffuse",
		gameId = "Metadata/Items/Gem/SupportGemSuffuse",
		variantId = "SuffuseSupport",
		grantedEffectId = "SupportSuffusePlayer",
		tags = {
			support = true,
			area = true,
		},
		gemType = "Support",
		tagString = "AoE",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 2,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemUniqueBreachLightningBolt"] = {
		name = "Lightning Bolt",
		baseTypeName = "Lightning Bolt",
		gameId = "Metadata/Items/Gem/SkillGemUniqueBreachLightningBolt",
		variantId = "UniqueBreachLightningBolt",
		grantedEffectId = "UniqueBreachLightningBoltPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			lightning = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Lightning",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemWhirlwindLance"] = {
		name = "Whirlwind Lance",
		baseTypeName = "Whirlwind Lance",
		gameId = "Metadata/Items/Gem/SkillGemWhirlwindLance",
		variantId = "WhirlwindLance",
		grantedEffectId = "WhirlwindLancePlayer",
		additionalStatSet1 = "WhirlwindLanceStormPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			area = true,
			projectile = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, AoE, Projectile, Duration",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 11,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPrimalStrikes"] = {
		name = "Primal Strikes",
		baseTypeName = "Primal Strikes",
		gameId = "Metadata/Items/Gem/SkillGemPrimalStrikes",
		variantId = "PrimalStrikes",
		grantedEffectId = "PrimalStrikesPlayer",
		additionalStatSet1 = "PrimalStrikesSecondPlayer",
		additionalStatSet2 = "PrimalStrikesFinalPlayer",
		additionalStatSet3 = "PrimalStrikesStagWavePlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
			lightning = true,
			payoff = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike, Lightning, Payoff",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 7,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemCullTheWeak"] = {
		name = "Cull The Weak",
		baseTypeName = "Cull the Weak",
		gameId = "Metadata/Items/Gem/SkillGemCullTheWeak",
		variantId = "CullTheWeak",
		grantedEffectId = "CullTheWeakPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemFangsOfFrost"] = {
		name = "Fangs of Frost",
		baseTypeName = "Fangs of Frost",
		gameId = "Metadata/Items/Gem/SkillGemFangsOfFrost",
		variantId = "FangsOfFrost",
		grantedEffectId = "FangsOfFrostPlayer",
		additionalStatSet1 = "FangsOfFrostBurstPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
			cold = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike, Cold",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemWindSerpentsFury"] = {
		name = "Wind Serpent's Fury",
		baseTypeName = "Wind Serpent's Fury",
		gameId = "Metadata/Items/Gem/SkillGemWindSerpentsFury",
		variantId = "WindSerpentsFury",
		grantedEffectId = "WindSerpentsFuryPlayer",
		additionalStatSet1 = "WindSerpentsFurySnakePlayer",
		additionalStatSet2 = "WindSerpentsFuryKnockbackExplosionPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			sustained = true,
			conditional = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Sustained, Conditional",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 13,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemRake"] = {
		name = "Rake",
		baseTypeName = "Rake",
		gameId = "Metadata/Items/Gem/SkillGemRake",
		variantId = "Rake",
		grantedEffectId = "RakePlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			strike = true,
			physical = true,
			travel = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Strike, Physical, Travel",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 3,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemBloodhoundsMark"] = {
		name = "Bloodhound's Mark",
		baseTypeName = "Bloodhound's Mark",
		gameId = "Metadata/Items/Gem/SkillGemBloodhoundsMark",
		variantId = "BloodhoundsMark",
		grantedEffectId = "BloodhoundsMarkPlayer",
		additionalGrantedEffectId1 = "BloodhoundsMarkExplosionPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			attack = true,
			area = true,
			mark = true,
			physical = true,
			duration = true,
			nova = true,
			payoff = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, Attack, AoE, Mark, Physical, Duration, Nova, Payoff",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemTameBeast"] = {
		name = "Tame Beast",
		baseTypeName = "Tame Beast",
		gameId = "Metadata/Items/Gem/SkillGemTameBeast",
		variantId = "TameBeast",
		grantedEffectId = "TameBeastPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			minion = true,
			companion = true,
			persistent = true,
			duration = true,
		},
		gemType = "Minion",
		tagString = "Companion, Persistent, Duration",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 9,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSpearOfSolaris"] = {
		name = "Spear of Solaris",
		baseTypeName = "Spear of Solaris",
		gameId = "Metadata/Items/Gem/SkillGemSpearOfSolaris",
		variantId = "SpearOfSolaris",
		grantedEffectId = "SpearOfSolarisPlayer",
		additionalStatSet1 = "SpearOfSolarisPulsePlayer",
		additionalStatSet2 = "SpearOfSolarisGroundPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			projectile = true,
			sustained = true,
			fire = true,
			conditional = true,
		},
		gemType = "Attack",
		tagString = "AoE, Projectile, Sustained, Fire, Conditional",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 13,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemStormLance"] = {
		name = "Storm Lance",
		baseTypeName = "Storm Lance",
		gameId = "Metadata/Items/Gem/SkillGemStormLance",
		variantId = "StormLance",
		grantedEffectId = "StormLancePlayer",
		additionalStatSet1 = "StormLanceInfusedPlayer",
		additionalStatSet2 = "StormLanceBeamPlayer",
		additionalStatSet3 = "StormLanceDetonatedBeamPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			attack = true,
			area = true,
			projectile = true,
			sustained = true,
			lightning = true,
			duration = true,
		},
		gemType = "Barrageable",
		tagString = "Attack, AoE, Projectile, Sustained, Lightning, Duration",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 5,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemParry"] = {
		name = "Parry",
		baseTypeName = "Parry",
		gameId = "Metadata/Items/Gem/SkillGemParry",
		variantId = "Parry",
		grantedEffectId = "ParryPlayer",
		tags = {
			grants_active_skill = true,
			attack = true,
			melee = true,
			physical = true,
			duration = true,
			channelling = true,
		},
		gemType = "Attack",
		tagString = "Melee, Physical, Duration, Channelling",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemElementalSundering"] = {
		name = "Elemental Sundering",
		baseTypeName = "Elemental Sundering",
		gameId = "Metadata/Items/Gem/SkillGemElementalSundering",
		variantId = "ElementalSundering",
		grantedEffectId = "ElementalSunderingPlayer",
		additionalStatSet1 = "ElementalSunderingColdPlayer",
		additionalStatSet2 = "ElementalSunderingFirePlayer",
		additionalStatSet3 = "ElementalSunderingLightningPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			melee = true,
			lightning = true,
			cold = true,
			fire = true,
			nova = true,
			payoff = true,
		},
		gemType = "Attack",
		tagString = "AoE, Melee, Lightning, Cold, Fire, Nova, Payoff",
		weaponRequirements = "Spear",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 11,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemSummonBeast"] = {
		name = "Companion: {0}",
		baseTypeName = "Companion: {0}",
		gameId = "Metadata/Items/Gems/SkillGemSummonBeast",
		variantId = "SummonBeast",
		grantedEffectId = "SummonBeastPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			barrageable = true,
			minion = true,
			companion = true,
			persistent = true,
		},
		gemType = "Barrageable",
		tagString = "Minion, Companion, Persistent",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemCastOnCharmUse"] = {
		name = "Cast on Charm Use",
		baseTypeName = "Cast on Charm Use",
		gameId = "Metadata/Items/Gems/SkillGemCastOnCharmUse",
		variantId = "CastOnCharmUse",
		grantedEffectId = "MetaCastOnCharmUsePlayer",
		additionalGrantedEffectId1 = "SupportMetaCastOnCharmUsePlayer",
		tags = {
			grants_active_skill = true,
			buff = true,
			persistent = true,
			trigger = true,
			meta = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Trigger, Meta",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemTrinity"] = {
		name = "Trinity",
		baseTypeName = "Trinity",
		gameId = "Metadata/Items/Gem/SkillGemTrinity",
		variantId = "Trinity",
		grantedEffectId = "TrinityPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			lightning = true,
			cold = true,
			fire = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Lightning, Cold, Fire",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 14,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemTrailOfCaltrops"] = {
		name = "Trail of Caltrops",
		baseTypeName = "Trail of Caltrops",
		gameId = "Metadata/Items/Gems/SkillGemTrailOfCaltrops",
		variantId = "TrailOfCaltrops",
		grantedEffectId = "TrailOfCaltropsPlayer",
		additionalGrantedEffectId1 = "TriggeredTrailOfCaltropsPlayer",
		tags = {
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			buff = true,
			persistent = true,
			area = true,
			projectile = true,
			physical = true,
			duration = true,
			hazard = true,
		},
		gemType = "Attack",
		tagString = "Buff, Persistent, AoE, Projectile, Physical, Duration, Hazard",
		reqStr = 0,
		reqDex = 100,
		reqInt = 0,
		Tier = 8,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemAscendancyTriggerElementalInfusion"] = {
		name = "Infuse Weapon",
		baseTypeName = "Infuse Weapon",
		gameId = "Metadata/Items/Gem/SkillGemAscendancyInfuseWeapon",
		variantId = "AscendancyTriggerElementalInfusion",
		grantedEffectId = "AmazonTriggerElementalInfusionPlayer",
		additionalStatSet1 = "AmazonTriggerFireInfusionPlayer",
		additionalStatSet2 = "AmazonTriggerLightningInfusionPlayer",
		tags = {
			grants_active_skill = true,
			attack = true,
			area = true,
			lightning = true,
			cold = true,
			fire = true,
		},
		gemType = "Attack",
		tagString = "AoE, Lightning, Cold, Fire",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemChaosSpearTriggerChaosInfusion"] = {
		name = "Chaotic Infusion",
		baseTypeName = "Chaotic Infusion",
		gameId = "Metadata/Items/Gem/SkillGemChaoticInfusion",
		variantId = "ChaosSpearTriggerChaosInfusion",
		grantedEffectId = "ChaosSpearTriggerChaosInfusionPlayer",
		tags = {
			grants_active_skill = true,
			attack = true,
			area = true,
			chaos = true,
		},
		gemType = "Attack",
		tagString = "AoE, Chaos",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemCastLightningSpellOnHit"] = {
		name = "Thundergod's Wrath",
		baseTypeName = "Thundergod's Wrath",
		gameId = "Metadata/Items/Gems/SkillGemLightningSpellOnHit",
		variantId = "CastLightningSpellOnHit",
		grantedEffectId = "MetaCastLightningSpellOnHitPlayer",
		additionalGrantedEffectId1 = "SupportMetaCastLightningSpellOnHitPlayer",
		tags = {
			strength = true,
			intelligence = true,
			grants_active_skill = true,
			buff = true,
			persistent = true,
			trigger = true,
			lightning = true,
			meta = true,
		},
		gemType = "Buff",
		tagString = "Persistent, Trigger, Lightning, Meta",
		reqStr = 50,
		reqDex = 0,
		reqInt = 50,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemConvalescence"] = {
		name = "Convalescence",
		baseTypeName = "Convalescence",
		gameId = "Metadata/Items/Gem/SkillGemConvalescence",
		variantId = "Convalescence",
		grantedEffectId = "ConvalescencePlayer",
		additionalGrantedEffectId1 = "ConvalescenceActivePlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			buff = true,
			persistent = true,
			duration = true,
		},
		gemType = "Spell",
		tagString = "Buff, Persistent, Duration",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 4,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemExplodingPoisonToad"] = {
		name = "Bursting Fen Toad",
		baseTypeName = "Bursting Fen Toad",
		gameId = "Metadata/Items/Gem/SkillGemExplodingPoisonToad",
		variantId = "ExplodingPoisonToad",
		grantedEffectId = "ExplodingPoisonToadPlayer",
		tags = {
			grants_active_skill = true,
			attack = true,
			area = true,
			physical = true,
			chaos = true,
		},
		gemType = "Attack",
		tagString = "AoE, Physical, Chaos",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemIcestorm"] = {
		name = "Icestorm",
		baseTypeName = "Icestorm",
		gameId = "Metadata/Items/Gem/SkillGemIcestorm",
		variantId = "Icestorm",
		grantedEffectId = "IcestormPlayer",
		additionalStatSet1 = "IcestormEmpoweredPlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			barrageable = true,
			spell = true,
			area = true,
			sustained = true,
			cold = true,
			duration = true,
			payoff = true,
		},
		gemType = "Barrageable",
		tagString = "Spell, AoE, Sustained, Cold, Duration, Payoff",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemPinnacleOfPower"] = {
		name = "Pinnacle of Power",
		baseTypeName = "Pinnacle of Power",
		gameId = "Metadata/Items/Gem/SkillGemPinnacleOfPower",
		variantId = "PinnacleOfPower",
		grantedEffectId = "PinnacleOfPowerPlayer",
		tags = {
			grants_active_skill = true,
			spell = true,
			buff = true,
			lightning = true,
			cold = true,
			fire = true,
			duration = true,
			conditional = true,
		},
		gemType = "Spell",
		tagString = "Buff, Lightning, Cold, Fire, Duration, Conditional",
		reqStr = 0,
		reqDex = 0,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 1,
	},
	["Metadata/Items/Gems/SkillGemCrossbowRequiem"] = {
		name = "Requiem",
		baseTypeName = "Compose Requiem",
		gameId = "Metadata/Items/Gem/SkillGemCrossbowRequiem",
		variantId = "CrossbowRequiem",
		grantedEffectId = "CrossbowRequiemAmmoPlayer",
		additionalGrantedEffectId1 = "CrossbowRequiemPlayer",
		tags = {
			strength = true,
			dexterity = true,
			grants_active_skill = true,
			attack = true,
			area = true,
			ammunition = true,
			projectile = true,
			cold = true,
			conditional = true,
		},
		gemType = "Attack",
		tagString = "AoE, Ammunition, Projectile, Cold, Conditional",
		weaponRequirements = "Crossbow",
		reqStr = 50,
		reqDex = 50,
		reqInt = 0,
		Tier = 0,
		naturalMaxLevel = 20,
	},
	["Metadata/Items/Gems/SkillGemHeartOfIce"] = {
		name = "Heart of Ice",
		baseTypeName = "Heart of Ice",
		gameId = "Metadata/Items/Gem/SkillGemHeartOfIce",
		variantId = "HeartOfIce",
		grantedEffectId = "HeartOfIcePlayer",
		tags = {
			intelligence = true,
			grants_active_skill = true,
			spell = true,
			buff = true,
			persistent = true,
			aura = true,
			cold = true,
		},
		gemType = "Spell",
		tagString = "Buff, Persistent, Aura, Cold",
		reqStr = 0,
		reqDex = 0,
		reqInt = 100,
		Tier = 0,
		naturalMaxLevel = 20,
	},
}