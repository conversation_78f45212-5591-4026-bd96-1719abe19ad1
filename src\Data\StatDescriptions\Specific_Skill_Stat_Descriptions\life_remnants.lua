-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=99
					}
				},
				text="{0}% chance to spawn a Remnant on killing an enemy"
			},
			[2]={
				limit={
					[1]={
						[1]=100,
						[2]="#"
					}
				},
				text="Spawn a Remnant on killing an enemy"
			}
		},
		stats={
			[1]="life_remnants_chance_to_spawn_orb_on_killing_enemy_%"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Spawn a Remnant on Critically Hitting a target, no more than once per second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Spawn a Remnant on Critically Hitting a target, no more than once every {0} seconds"
			}
		},
		stats={
			[1]="life_remnants_spawn_remnant_on_crit_vs_enemy_every_X_ms"
		}
	},
	[3]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Each Remnant grants {0} Life"
			}
		},
		stats={
			[1]="life_remnants_gain_per_globe"
		}
	},
	[4]={
		[1]={
		},
		stats={
			[1]="virtual_life_remnants_gain_per_globe"
		}
	},
	["life_remnants_chance_to_spawn_orb_on_killing_enemy_%"]=1,
	["life_remnants_gain_per_globe"]=3,
	["life_remnants_spawn_remnant_on_crit_vs_enemy_every_X_ms"]=2,
	parent="skill_stat_descriptions",
	["virtual_life_remnants_gain_per_globe"]=4
}