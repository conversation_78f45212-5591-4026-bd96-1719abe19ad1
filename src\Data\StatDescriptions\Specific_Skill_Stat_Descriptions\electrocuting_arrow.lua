-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="active_skill_base_secondary_area_of_effect_radius"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d} second to Arrow duration"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d} seconds to Arrow duration"
			},
			[3]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Arrow duration is {0} second"
			},
			[4]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Arrow duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration",
			[2]="quality_display_base_skill_effect_duration_is_gem"
		}
	},
	[3]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	["active_skill_base_secondary_area_of_effect_radius"]=1,
	["base_skill_effect_duration"]=2,
	parent="skill_stat_descriptions",
	["quality_display_base_skill_effect_duration_is_gem"]=2,
	["skill_effect_duration"]=3
}