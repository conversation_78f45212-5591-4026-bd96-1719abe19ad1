-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[2]={
		stats={
			[1]="total_number_of_projectiles_to_fire"
		}
	},
	[3]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[4]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Cloud radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Cloud radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[5]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Cloud Duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Cloud Duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[6]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	["active_skill_area_of_effect_radius"]=3,
	["active_skill_base_area_of_effect_radius"]=4,
	["base_skill_effect_duration"]=5,
	parent="specific_skill_stat_descriptions/toxic_grenade_statset_0",
	["skill_effect_duration"]=6,
	["total_number_of_projectiles_to_fire"]=2
}