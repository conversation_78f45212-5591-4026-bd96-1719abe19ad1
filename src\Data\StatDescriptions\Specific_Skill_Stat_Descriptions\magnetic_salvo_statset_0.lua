-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="quality_display_base_number_of_projectiles_is_gem"
		}
	},
	[2]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[3]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Impact radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Impact radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=0,
						[2]=0
					}
				},
				text="Fires up to {0:+d} missile"
			},
			[2]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]=0,
						[2]=0
					}
				},
				text="Fires up to {0:+d} missiles"
			},
			[3]={
				limit={
					[1]={
						[1]=2,
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					},
					[3]={
						[1]="#",
						[2]="#"
					}
				},
				text="Fires up to {0} missiles"
			}
		},
		stats={
			[1]="base_number_of_projectiles",
			[2]="skill_can_fire_arrows",
			[3]="quality_display_base_number_of_projectiles_is_gem"
		}
	},
	[5]={
		[1]={
		},
		stats={
			[1]="total_number_of_projectiles_to_fire"
		}
	},
	["active_skill_area_of_effect_radius"]=2,
	["active_skill_base_area_of_effect_radius"]=3,
	["base_number_of_projectiles"]=4,
	parent="skill_stat_descriptions",
	["quality_display_base_number_of_projectiles_is_gem"]=4,
	["skill_can_fire_arrows"]=4,
	["total_number_of_projectiles_to_fire"]=5
}