-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Banner Aura radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Banner Aura radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[3]={
		[1]={
			[1]={
				[1]={
					k="per_minute_to_per_second_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Aura grants {0} Flask charges per second"
			}
		},
		stats={
			[1]="base_skill_buff_flask_charge_per_min_to_apply"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Aura grants {0}% more Stun Threshold and\nElemental Ailment Threshold"
			},
			[2]={
				[1]={
					k="negate",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]=-1
					}
				},
				text="Aura grants {0}% less Stun Threshold and\nElemental Ailment Threshold"
			}
		},
		stats={
			[1]="base_skill_buff_stun_and_ailment_threshold_+%_final_to_apply"
		}
	},
	[5]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Banner duration is {0} second, or until you leave the Aura"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Banner duration is {0} seconds, or until you leave the Aura"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[6]={
		[1]={
		},
		stats={
			[1]="skill_aura_buff_flask_charge_per_min_magnitude_to_apply"
		}
	},
	[7]={
		[1]={
		},
		stats={
			[1]="skill_aura_buff_stun_and_ailment_threshold_+%_final_magnitude_to_apply"
		}
	},
	[8]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	["active_skill_area_of_effect_radius"]=1,
	["active_skill_base_area_of_effect_radius"]=2,
	["base_skill_buff_flask_charge_per_min_to_apply"]=3,
	["base_skill_buff_stun_and_ailment_threshold_+%_final_to_apply"]=4,
	["base_skill_effect_duration"]=5,
	parent="skill_stat_descriptions",
	["skill_aura_buff_flask_charge_per_min_magnitude_to_apply"]=6,
	["skill_aura_buff_stun_and_ailment_threshold_+%_final_magnitude_to_apply"]=7,
	["skill_effect_duration"]=8
}