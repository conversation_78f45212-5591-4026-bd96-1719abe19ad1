-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games
local itemBases = ...

itemBases["Makeshift Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ezomyte_basetype = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 7, PhysicalMax = 12, CritChanceBase = 5, AttackRateBase = 1.6, Range = 120, ReloadTimeBase = 0.8, },
	req = { },
}
itemBases["Tense Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ezomyte_basetype = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicit = "(20-30)% increased Bolt Speed",
	implicitModTypes = { {  }, },
	weapon = { PhysicalMin = 8, PhysicalMax = 15, Crit<PERSON>hanceBase = 5, AttackRateBase = 1.6, Range = 120, ReloadTimeBase = 0.85, },
	req = { str = 8, dex = 8, },
}
itemBases["Sturdy Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ezomyte_basetype = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 11, PhysicalMax = 26, CritChanceBase = 5, AttackRateBase = 1.55, Range = 120, ReloadTimeBase = 0.75, },
	req = { level = 10, str = 15, dex = 15, },
}
itemBases["Varnished Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, maraketh_basetype = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 12, PhysicalMax = 36, CritChanceBase = 5, AttackRateBase = 1.6, Range = 120, ReloadTimeBase = 0.8, },
	req = { level = 16, str = 22, dex = 22, },
}
itemBases["Dyad Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, maraketh_basetype = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicit = "Loads an additional bolt",
	implicitModTypes = { { "attack" }, },
	weapon = { PhysicalMin = 9, PhysicalMax = 37, CritChanceBase = 5, AttackRateBase = 1.6, Range = 120, ReloadTimeBase = 1.1, },
	req = { level = 20, str = 27, dex = 27, },
}
itemBases["Alloy Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, maraketh_basetype = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 12, PhysicalMax = 50, CritChanceBase = 5, AttackRateBase = 1.7, Range = 120, ReloadTimeBase = 0.7, },
	req = { level = 26, str = 34, dex = 34, },
}
itemBases["Bombard Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, vaal_basetype = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicit = "Grenade Skills Fire an additional Projectile",
	implicitModTypes = { {  }, },
	weapon = { PhysicalMin = 14, PhysicalMax = 56, CritChanceBase = 5, AttackRateBase = 1.65, Range = 120, ReloadTimeBase = 0.75, },
	req = { level = 33, str = 43, dex = 43, },
}
itemBases["Construct Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, vaal_basetype = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 18, PhysicalMax = 72, CritChanceBase = 5, AttackRateBase = 1.6, Range = 120, ReloadTimeBase = 0.8, },
	req = { level = 38, str = 49, dex = 49, },
}
itemBases["Blackfire Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 20, PhysicalMax = 80, CritChanceBase = 7, AttackRateBase = 1.6, Range = 120, ReloadTimeBase = 0.85, },
	req = { level = 45, str = 57, dex = 57, },
}
itemBases["Piercing Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicit = "(20-30)% chance to Pierce an Enemy",
	implicitModTypes = { {  }, },
	weapon = { PhysicalMin = 20, PhysicalMax = 82, CritChanceBase = 5, AttackRateBase = 1.65, Range = 120, ReloadTimeBase = 0.85, },
	req = { level = 48, str = 61, dex = 61, },
}
itemBases["Cumbrous Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicit = "+1 to maximum number of Summoned Ballista Totems",
	implicitModTypes = { {  }, },
	weapon = { PhysicalMin = 19, PhysicalMax = 76, CritChanceBase = 5, AttackRateBase = 1.6, Range = 120, ReloadTimeBase = 0.9, },
	req = { level = 52, str = 66, dex = 66, },
}
itemBases["Dedalian Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 25, PhysicalMax = 99, CritChanceBase = 7, AttackRateBase = 1.55, Range = 120, ReloadTimeBase = 0.85, },
	req = { level = 56, str = 71, dex = 71, },
}
itemBases["Esoteric Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 28, PhysicalMax = 113, CritChanceBase = 5, AttackRateBase = 1.6, Range = 120, ReloadTimeBase = 0.8, },
	req = { level = 65, str = 81, dex = 81, },
}
itemBases["Taut Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicit = "(20-30)% increased Bolt Speed",
	implicitModTypes = { {  }, },
	weapon = { PhysicalMin = 20, PhysicalMax = 79, CritChanceBase = 5, AttackRateBase = 1.6, Range = 120, ReloadTimeBase = 0.85, },
	req = { level = 45, str = 57, dex = 57, },
}
itemBases["Robust Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 22, PhysicalMax = 89, CritChanceBase = 5, AttackRateBase = 1.55, Range = 120, ReloadTimeBase = 0.75, },
	req = { level = 48, str = 61, dex = 61, },
}
itemBases["Painted Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 23, PhysicalMax = 92, CritChanceBase = 5, AttackRateBase = 1.6, Range = 120, ReloadTimeBase = 0.8, },
	req = { level = 51, str = 65, dex = 65, },
}
itemBases["Twin Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicit = "Loads an additional bolt",
	implicitModTypes = { { "attack" }, },
	weapon = { PhysicalMin = 21, PhysicalMax = 83, CritChanceBase = 5, AttackRateBase = 1.6, Range = 120, ReloadTimeBase = 1.1, },
	req = { level = 55, str = 69, dex = 69, },
}
itemBases["Cannonade Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicit = "Grenade Skills Fire an additional Projectile",
	implicitModTypes = { {  }, },
	weapon = { PhysicalMin = 23, PhysicalMax = 90, CritChanceBase = 5, AttackRateBase = 1.65, Range = 120, ReloadTimeBase = 0.75, },
	req = { level = 59, str = 74, dex = 74, },
}
itemBases["Bleak Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 27, PhysicalMax = 109, CritChanceBase = 5, AttackRateBase = 1.6, Range = 120, ReloadTimeBase = 0.8, },
	req = { level = 62, str = 78, dex = 78, },
}
itemBases["Stout Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 30, PhysicalMax = 119, CritChanceBase = 5, AttackRateBase = 1.55, Range = 120, ReloadTimeBase = 0.75, },
	req = { level = 67, str = 95, dex = 95, },
}
itemBases["Engraved Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 31, PhysicalMax = 124, CritChanceBase = 5, AttackRateBase = 1.6, Range = 120, ReloadTimeBase = 0.8, },
	req = { level = 72, str = 106, dex = 106, },
}
itemBases["Flexed Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicit = "(20-30)% increased Bolt Speed",
	implicitModTypes = { {  }, },
	weapon = { PhysicalMin = 32, PhysicalMax = 127, CritChanceBase = 5, AttackRateBase = 1.6, Range = 120, ReloadTimeBase = 0.85, },
	req = { level = 77, str = 116, dex = 116, },
}
itemBases["Gemini Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicit = "Loads an additional bolt",
	implicitModTypes = { { "attack" }, },
	weapon = { PhysicalMin = 28, PhysicalMax = 112, CritChanceBase = 5, AttackRateBase = 1.6, Range = 120, ReloadTimeBase = 1.1, },
	req = { level = 78, str = 116, dex = 116, },
}
itemBases["Siege Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicit = "Grenade Skills Fire an additional Projectile",
	implicitModTypes = { {  }, },
	weapon = { PhysicalMin = 29, PhysicalMax = 115, CritChanceBase = 5, AttackRateBase = 1.65, Range = 120, ReloadTimeBase = 0.75, },
	req = { level = 79, str = 116, dex = 116, },
}
itemBases["Desolate Crossbow"] = {
	type = "Crossbow",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ranged = true, weapon = true, twohand = true, crossbow = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 33, PhysicalMax = 132, CritChanceBase = 5, AttackRateBase = 1.6, Range = 120, ReloadTimeBase = 0.8, },
	req = { level = 77, str = 116, dex = 116, },
}
