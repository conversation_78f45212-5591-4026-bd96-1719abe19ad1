-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games
local itemBases = ...

itemBases["Crude Claw"] = {
	type = "Claw",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, ezomyte_basetype = true, default = true, weapon = true, one_hand_weapon = true, claw = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 4, PhysicalMax = 10, CritChanceBase = 5, AttackRateBase = 1.65, Range = 11, },
	req = { },
}
itemBases["Pict Claw"] = {
	type = "Claw",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, ezomyte_basetype = true, default = true, weapon = true, one_hand_weapon = true, claw = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 4, PhysicalMax = 16, CritChanceBase = 5, AttackRateBase = 1.7, Range = 11, },
	req = { level = 6, dex = 16, },
}
itemBases["Wolfbone Claw"] = {
	type = "Claw",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, ezomyte_basetype = true, default = true, weapon = true, one_hand_weapon = true, claw = true, },
	implicit = "Grants 8 Life per Enemy Hit",
	implicitModTypes = { { "resource", "life", "attack" }, },
	weapon = { PhysicalMin = 7, PhysicalMax = 23, CritChanceBase = 5, AttackRateBase = 1.6, Range = 11, },
	req = { level = 12, dex = 29, },
}
itemBases["Forked Claw"] = {
	type = "Claw",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, onehand = true, default = true, weapon = true, one_hand_weapon = true, claw = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 10, PhysicalMax = 26, CritChanceBase = 5, AttackRateBase = 1.55, Range = 11, },
	req = { level = 16, dex = 38, },
}
itemBases["Plated Claw"] = {
	type = "Claw",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, onehand = true, default = true, weapon = true, one_hand_weapon = true, claw = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 13, PhysicalMax = 31, CritChanceBase = 5, AttackRateBase = 1.6, Range = 11, },
	req = { level = 22, dex = 52, },
}
itemBases["Edged Claw"] = {
	type = "Claw",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, onehand = true, default = true, weapon = true, one_hand_weapon = true, claw = true, },
	implicit = "(15-25)% chance to Blind Enemies on hit",
	implicitModTypes = { {  }, },
	weapon = { PhysicalMin = 11, PhysicalMax = 38, CritChanceBase = 5, AttackRateBase = 1.7, Range = 11, },
	req = { level = 28, dex = 65, },
}
itemBases["Arced Claw"] = {
	type = "Claw",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, vaal_basetype = true, default = true, weapon = true, one_hand_weapon = true, claw = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 12, PhysicalMax = 46, CritChanceBase = 5, AttackRateBase = 1.65, Range = 11, },
	req = { level = 33, dex = 76, },
}
itemBases["Hooked Claw"] = {
	type = "Claw",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, vaal_basetype = true, default = true, weapon = true, one_hand_weapon = true, claw = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 16, PhysicalMax = 49, CritChanceBase = 5, AttackRateBase = 1.55, Range = 11, },
	req = { level = 36, dex = 83, },
}
itemBases["Razorglass Claw"] = {
	type = "Claw",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, default = true, weapon = true, one_hand_weapon = true, claw = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 23, PhysicalMax = 54, CritChanceBase = 5, AttackRateBase = 1.6, Range = 11, },
	req = { level = 45, dex = 104, },
}
itemBases["Sharktooth Claw"] = {
	type = "Claw",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, default = true, weapon = true, one_hand_weapon = true, claw = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 25, PhysicalMax = 52, CritChanceBase = 5, AttackRateBase = 1.7, Range = 11, },
	req = { level = 49, dex = 112, },
}
itemBases["Armoured Claw"] = {
	type = "Claw",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, default = true, weapon = true, one_hand_weapon = true, claw = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 27, PhysicalMax = 63, CritChanceBase = 5, AttackRateBase = 1.55, Range = 11, },
	req = { level = 52, dex = 119, },
}
itemBases["Piercing Claw"] = {
	type = "Claw",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, default = true, weapon = true, one_hand_weapon = true, claw = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 23, PhysicalMax = 68, CritChanceBase = 5, AttackRateBase = 1.6, Range = 11, },
	req = { level = 55, dex = 126, },
}
itemBases["Talon Claw"] = {
	type = "Claw",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, default = true, weapon = true, one_hand_weapon = true, claw = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 23, PhysicalMax = 79, CritChanceBase = 5, AttackRateBase = 1.65, Range = 11, },
	req = { level = 65, dex = 148, },
}
