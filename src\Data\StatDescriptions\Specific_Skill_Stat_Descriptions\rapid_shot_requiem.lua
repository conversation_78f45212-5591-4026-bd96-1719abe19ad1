-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="number_of_crossbow_bolts"
		}
	},
	[2]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[3]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d} metre to impact radius"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]=0,
						[2]=0
					}
				},
				text="{0:+d} metres to impact radius"
			},
			[3]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Impact radius is {0} metre"
			},
			[4]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Impact radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius",
			[2]="quality_display_active_skill_base_area_of_effect_radius_is_gem"
		}
	},
	[4]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Anguish impacts within a {0} metre radius of the target"
			}
		},
		stats={
			[1]="active_skill_base_secondary_area_of_effect_radius"
		}
	},
	[5]={
		[1]={
		},
		stats={
			[1]="active_skill_secondary_area_of_effect_radius"
		}
	},
	["active_skill_area_of_effect_radius"]=2,
	["active_skill_base_area_of_effect_radius"]=3,
	["active_skill_base_secondary_area_of_effect_radius"]=4,
	["active_skill_secondary_area_of_effect_radius"]=5,
	["number_of_crossbow_bolts"]=1,
	parent="skill_stat_descriptions",
	["quality_display_active_skill_base_area_of_effect_radius_is_gem"]=3
}