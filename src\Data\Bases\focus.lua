-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games
local itemBases = ...


itemBases["Twig Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { focus = true, ezomyte_basetype = true, int_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 12, },
	req = { },
}
itemBases["Woven Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { focus = true, ezomyte_basetype = true, int_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 15, },
	req = { level = 6, int = 13, },
}
itemBases["Antler Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { focus = true, ezomyte_basetype = true, int_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 17, },
	req = { level = 10, int = 20, },
}
itemBases["Engraved Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, focus = true, int_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 21, },
	req = { level = 16, int = 30, },
}
itemBases["Tonal Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, focus = true, int_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 25, },
	req = { level = 22, int = 42, },
}
itemBases["Crystal Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, focus = true, int_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 28, },
	req = { level = 26, int = 49, },
}
itemBases["Voodoo Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { focus = true, int_armour = true, armour = true, vaal_basetype = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 32, },
	req = { level = 33, int = 61, },
}
itemBases["Plumed Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { focus = true, int_armour = true, armour = true, vaal_basetype = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 34, },
	req = { level = 36, int = 66, },
}
itemBases["Jade Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, focus = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 40, },
	req = { level = 45, int = 83, },
}
itemBases["Paua Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, focus = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 43, },
	req = { level = 51, int = 94, },
}
itemBases["Elegant Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, focus = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 44, },
	req = { level = 52, int = 95, },
}
itemBases["Attuned Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, focus = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 47, },
	req = { level = 57, int = 104, },
}
itemBases["Magus Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, focus = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 52, },
	req = { level = 65, int = 118, },
}
itemBases["Wreath Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, focus = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 40, },
	req = { level = 45, int = 83, },
}
itemBases["Staghorn Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, focus = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 42, },
	req = { level = 48, int = 88, },
}
itemBases["Jingling Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, focus = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 43, },
	req = { level = 51, int = 94, },
}
itemBases["Arrayed Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, focus = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 46, },
	req = { level = 55, int = 101, },
}
itemBases["Cultist Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, focus = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 49, },
	req = { level = 59, int = 108, },
}
itemBases["Hallowed Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, focus = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 50, },
	req = { level = 61, int = 112, },
}
itemBases["Druidic Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, focus = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 52, },
	req = { level = 65, int = 118, },
}
itemBases["Leyline Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, focus = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 58, },
	req = { level = 70, int = 129, },
}
itemBases["Sacred Focus"] = {
	type = "Focus",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, focus = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 63, },
	req = { level = 75, int = 139, },
}
