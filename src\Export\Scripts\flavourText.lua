--
-- export flavour text data
--
local function normalizeId(id)
	id = tostring(id)
	-- remove trailing underscores only. We can't match Hash sadly.
	return id:gsub("_+$", "")
end

local function cleanAndSplit(str)
	-- Normalize newlines
	str = str:gsub("\r\n", "\n")

	local lines = {}
	for line in str:gmatch("[^\n]+") do
		line = line:match("^%s*(.-)%s*$") -- trim each line
		if line ~= "" then
			-- Escape quotes
			line = line:gsub('"', '\\"')
			table.insert(lines, line)
		end
	end

	return lines
end

local uniqueNameLookup = {}
local unmatchedIds = {}
local forcedNameMap = {
	["FourUniqueSceptre6"]   = "Guiding Palm",
	["FourUniqueSceptre6a"]  = "Guiding Palm of the Heart",
	["FourUniqueSceptre6b"]  = "Guiding Palm of the Eye",
	["FourUniqueSceptre6c"]  = "Guiding Palm of the Mind",
}

for row in dat("UniqueStashLayout"):Rows() do
	-- We use Text2 because Words.Text has "The Immortan" instead of "The Road Warrior". Everything else so far matches up.
	local name = row.WordsKey.Text2
	local id = normalizeId(row.ItemVisualIdentity.Id)
	uniqueNameLookup[id] = name
	unmatchedIds[id] = name
end

local out = io.open("../Data/FlavourText.lua", "w")
out:write('-- This file is automatically generated, do not edit!\n')
out:write('-- Flavour text data (c) Grinding Gear Games\n\n')
out:write('return {\n')

local index = 1
for c in dat("FlavourText"):Rows() do
	local id = normalizeId(c.Id)
	local name = forcedNameMap[id] or uniqueNameLookup[id]

	if name then
		local lines = cleanAndSplit(tostring(c.Text))
		out:write('\t[', index, '] = {\n')
		out:write('\t\tid = "', tostring(c.Id), '",\n')
		out:write('\t\tname = "', name, '",\n')
		out:write('\t\ttext = {\n')
		for _, line in ipairs(lines) do
			out:write('\t\t\t"', line, '",\n')
		end
		out:write('\t\t},\n')
		out:write('\t},\n')
		index = index + 1
		unmatchedIds[id] = nil
	end
end

out:write('}\n')
out:close()

print("Flavour Texts exported.")

-- Print unmatched
print("Unique items from UniqueStashLayout without flavour text:")
for id, name in pairs(unmatchedIds) do
	print(string.format("Id: %s, Name: %s", id, name))
end
