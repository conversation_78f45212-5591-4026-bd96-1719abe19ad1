-- This file is automatically generated, do not edit!
-- Path of Building
--
-- Spectre active skills
-- Skill data (c) Grinding Gear Games
--
local skills, mod, flag, skill = ...

--ABTT = Add Buff to Target Triggered
--CGE = Monster Cast Ground Effect
--DTT = Detach Dash to Target
--EA = Empty Action
--EAA = Empty Action Attack
--EAS = Empty Action Spell
--EDS = Effect Driven Spell + Effect Driven Attack
--EG = Execute Geal
--GA = Geometry Attack
--GPS = Geometry Projectile Spell
--GPA = Geometry Projectile Attack
--GS = Geometry Spell
--GT = Geometry Trigger
--MAAS = Melee At Animation Speed
--MAS = Monster Attack Skills
--MDD = Monster Detonate Dead
--MMA = Monster Mortar Attack
--MMS = Monster Mortar Spell
--MPW = Monster Projectile Weapon
--MPS = Monster Projectile Spell
--SO = Spawn Object
--SSM = Summon Specific Monster
--TC = Table Charge

skills["ABTTProcessionBannerDrain"] = {
	name = "Banner",
	hidden = true,
	skillTypes = { [SkillType.Buff] = true, [SkillType.Duration] = true, [SkillType.Spell] = true, [SkillType.Triggerable] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Banner",
			baseEffectiveness = 7.5,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			statMap = {
				["base_physical_damage_taken_per_minute"] = {
					skill("PhysicalDot", nil),
					div = 60,
				},
			},
			baseFlags = {
				buff = true,
				duration = true,
			},
			stats = {
				"base_physical_damage_taken_per_minute",
				"is_area_damage",
			},
			levels = {
				[1] = { 16.666667039196, statInterpolation = { 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["AzmeriFabricationDespair"] = {
	name = "Despair",
	hidden = true,
	description = "Curse all targets in an area after a short delay, lowering their Chaos Resistance.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Area] = true, [SkillType.Duration] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.Cascadable] = true, [SkillType.Chaos] = true, [SkillType.AppliesCurse] = true, [SkillType.CanRapidFire] = true, [SkillType.AreaSpell] = true, [SkillType.UsableWhileMoving] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Despair",
			statDescriptionScope = "despair",
			statMap = {
				["base_chaos_damage_resistance_%"] = {
					mod("ChaosResist", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Curse" }),
				},
			},
			baseFlags = {
				spell = true,
				curse = true,
				area = true,
				duration = true,
			},
			constantStats = {
				{ "base_chaos_damage_resistance_%", -30 },
				{ "active_skill_area_of_effect_radius_+%_final", 100 },
			},
			stats = {
				"base_deal_no_damage",
				"curse_apply_as_aura",
				"infinite_skill_effect_duration",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["AzmeriFabricationEnfeeble"] = {
	name = "Enfeeble",
	hidden = true,
	description = "Curse all targets in an area after a short delay, making them deal less damage.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Area] = true, [SkillType.Duration] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.Cascadable] = true, [SkillType.AppliesCurse] = true, [SkillType.CanRapidFire] = true, [SkillType.AreaSpell] = true, [SkillType.UsableWhileMoving] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Enfeeble",
			statDescriptionScope = "enfeeble",
			statMap = {
				["base_skill_buff_damage_+%_final_to_apply"] = {
					mod("Damage", "MORE", nil, 0, 0, { type = "GlobalEffect", effectType = "Curse" }, { type = "Condition", var = "Unique", neg = true }),
				},
				["base_skill_buff_damage_+%_final_vs_unique_to_apply"] = {
					mod("Damage", "MORE", nil, 0, 0, { type = "GlobalEffect", effectType = "Curse" }, { type = "Condition", var = "Unique" }),
				},
			},
			baseFlags = {
				spell = true,
				curse = true,
				area = true,
				duration = true,
			},
			constantStats = {
				{ "accuracy_rating_+%", -60 },
				{ "base_skill_buff_damage_+%_final_to_apply", -60 },
				{ "base_skill_buff_damage_+%_final_vs_unique_to_apply", -23 },
				{ "active_skill_area_of_effect_radius_+%_final", 100 },
			},
			stats = {
				"base_deal_no_damage",
				"curse_apply_as_aura",
				"infinite_skill_effect_duration",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["AzmeriFabricationTemporalChains"] = {
	name = "Temporal Chains",
	hidden = true,
	description = "Curse all enemies in an area, Slowing them and making other effects on them expire more slowly.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Area] = true, [SkillType.Duration] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.Cascadable] = true, [SkillType.AppliesCurse] = true, [SkillType.CanRapidFire] = true, [SkillType.AreaSpell] = true, [SkillType.UsableWhileMoving] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Temporal Chains",
			statDescriptionScope = "temporal_chains",
			baseFlags = {
				spell = true,
				curse = true,
				area = true,
				duration = true,
			},
			constantStats = {
				{ "base_skill_debuff_action_speed_+%_final_to_inflict", -30 },
				{ "base_temporal_chains_other_buff_time_passed_+%_to_apply", -38 },
				{ "active_skill_area_of_effect_radius_+%_final", 100 },
			},
			stats = {
				"base_deal_no_damage",
				"curse_apply_as_aura",
				"infinite_skill_effect_duration",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["AzmeriPictBowRainOfSpores"] = {
	name = "Toxic Rain",
	hidden = true,
	description = "Fire arrows into the air that rain down around the targeted area, dealing damage to enemies they hit and creating spore pods where they land. Each spore pod deals chaos damage over time to nearby enemies and slows their movement speed. The pods last for a duration before bursting, dealing area damage.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Area] = true, [SkillType.ProjectileSpeed] = true, [SkillType.Totemable] = true, [SkillType.Trappable] = true, [SkillType.Mineable] = true, [SkillType.Damage] = true, [SkillType.Chaos] = true, [SkillType.Duration] = true, [SkillType.DamageOverTime] = true, [SkillType.ProjectileNumber] = true, [SkillType.Chaos] = true, [SkillType.Triggerable] = true, [SkillType.Rain] = true, [SkillType.Bow] = true, [SkillType.GroundTargetedProjectile] = true, },
	weaponTypes = {
		["Bow"] = true,
	},
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 0.25, storedUses = 1, levelRequirement = 0, cooldown = 8, },
	},
	statSets = {
		[1] = {
			label = "Toxic Rain",
			baseEffectiveness = 4,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				area = true,
				projectile = true,
				duration = true,
			},
			constantStats = {
				{ "base_skill_effect_duration", 2000 },
				{ "base_number_of_arrows", 2 },
				{ "active_skill_base_physical_damage_%_to_convert_to_chaos", 100 },
				{ "rain_of_spores_vines_movement_speed_+%_final", -5 },
				{ "minimum_rain_of_spores_movement_speed_+%_final_cap", -30 },
				{ "active_skill_area_of_effect_radius_+%_final", 20 },
			},
			stats = {
				"base_chaos_damage_to_deal_per_minute",
				"base_is_projectile",
				"is_area_damage",
				"skill_can_fire_arrows",
				"cannot_pierce",
				"action_attack_or_cast_time_uses_animation_length",
				"base_skill_cannot_be_blocked",
			},
			levels = {
				[1] = { 16.666667039196, statInterpolation = { 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["BloodMageBloodTendrils"] = {
	name = "Exsanguinate",
	hidden = true,
	description = "Expel your own blood as Chaining blood tendrils in a cone in front of you. Enemies hit by the tendrils take Physical damage and are inflicted with a Debuff that deals Physical damage over time.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.Chains] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.Physical] = true, [SkillType.CanRapidFire] = true, [SkillType.DamageOverTime] = true, [SkillType.Duration] = true, [SkillType.UsableWhileMoving] = true, },
	castTime = 1.67,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, storedUses = 1, levelRequirement = 0, cooldown = 0.5, },
	},
	statSets = {
		[1] = {
			label = "Exsanguinate",
			baseEffectiveness = 2.5,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "exsanguinate",
			baseFlags = {
				spell = true,
				hit = true,
				triggerable = true,
				duration = true,
				chaining = true,
			},
			constantStats = {
				{ "base_skill_effect_duration", 1000 },
				{ "number_of_chains", 1 },
				{ "spell_maximum_action_distance_+%", -40 },
				{ "active_skill_base_radius_+", -8 },
			},
			stats = {
				"spell_minimum_base_physical_damage",
				"spell_maximum_base_physical_damage",
				"base_physical_damage_to_deal_per_minute",
				"blood_tendrils_beam_count",
				"spell_damage_modifiers_apply_to_skill_dot",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, 66.666668156783, 2, statInterpolation = { 3, 3, 3, 1, }, actorLevel = 1, },
			},
		},
	}
}
skills["BoneCultistZealotFirestorm"] = {
	name = "Firestorm",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 3,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 6, storedUses = 1, levelRequirement = 0, cooldown = 10, },
	},
	statSets = {
		[1] = {
			label = "Firestorm",
			baseEffectiveness = 9,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				hit = true,
				triggerable = true,
			},
			constantStats = {
				{ "spell_maximum_action_distance_+%", -50 },
				{ "active_skill_base_physical_damage_%_to_convert_to_fire", 80 },
			},
			stats = {
				"spell_minimum_base_physical_damage",
				"spell_maximum_base_physical_damage",
				"is_area_damage",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["BoneCultistZealotLightningstorm"] = {
	name = "Lightning Storm",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 1.5,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 6, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Lightning Storm",
			baseEffectiveness = 5,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "geometry_spell",
			baseFlags = {
				spell = true,
				hit = true,
				triggerable = true,
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
				"is_area_damage",
			},
			levels = {
				[1] = { 0.5, 1.5, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["BurdenedWretchSlam"] = {
	name = "Slam",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, },
	castTime = 4.8,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 3, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Slam",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				triggerable = true,
			},
			constantStats = {
				{ "main_hand_base_maximum_attack_distance", 51 },
				{ "melee_range_+", 40 },
			},
			stats = {
				"is_area_damage",
				"action_attack_or_cast_time_uses_animation_length",
				"base_skill_can_be_blocked",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["BurdenedWretchSlamUnique"] = {
	name = "Slam",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, },
	castTime = 4.8,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 3, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Slam",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				triggerable = true,
			},
			constantStats = {
				{ "main_hand_base_maximum_attack_distance", 51 },
				{ "melee_range_+", 40 },
			},
			stats = {
				"is_area_damage",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["CGEBloodPriestBoilingBlood"] = {
	name = "Boiling Blood",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Area] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, [SkillType.Duration] = true, [SkillType.AreaSpell] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Boiling Blood",
			baseEffectiveness = 12,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				area = true,
				triggerable = true,
				duration = true,
			},
			constantStats = {
				{ "base_skill_effect_duration", 3000 },
				{ "ground_blood_art_variation", 1003 },
				{ "active_skill_area_of_effect_radius_+%_final", -25 },
			},
			stats = {
				"base_physical_damage_to_deal_per_minute",
			},
			levels = {
				[1] = { 16.666667039196, statInterpolation = { 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["CGESanctifiedMonstrosityPusGround"] = {
	name = "Pus Ground",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Area] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, [SkillType.Duration] = true, [SkillType.AreaSpell] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Pus Ground",
			baseEffectiveness = 8,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				area = true,
				triggerable = true,
				duration = true,
			},
			constantStats = {
				{ "active_skill_area_of_effect_radius_+%_final", -40 },
				{ "base_skill_effect_duration", 4000 },
				{ "ground_caustic_art_variation", 1030 },
			},
			stats = {
				"base_chaos_damage_to_deal_per_minute",
				"is_area_damage",
			},
			levels = {
				[1] = { 16.666667039196, statInterpolation = { 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["CoffinWretchBabySoulrend1"] = {
	name = "Soulrend",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Area] = true, [SkillType.Duration] = true, [SkillType.Projectile] = true, [SkillType.DamageOverTime] = true, [SkillType.Chaos] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.Trappable] = true, [SkillType.Mineable] = true, [SkillType.Totemable] = true, [SkillType.DegenOnlySpellDamage] = true, [SkillType.AreaSpell] = true, },
	castTime = 2.3,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Soulrend",
			baseEffectiveness = 13.25,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				triggerable = true,
				spell = true,
				area = true,
				duration = true,
				projectile = true,
			},
			constantStats = {
				{ "base_skill_effect_duration", 500 },
				{ "base_skill_area_of_effect_+%", -40 },
				{ "spell_maximum_action_distance_+%", -55 },
				{ "skill_effect_duration_+%", 100 },
				{ "number_of_additional_projectiles", 2 },
			},
			stats = {
				"base_cold_damage_to_deal_per_minute",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"maintain_projectile_direction_when_using_contact_position",
				"distribute_projectiles_over_contact_points",
			},
			levels = {
				[1] = { 16.666667039196, statInterpolation = { 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["CultistBeastSunder"] = {
	name = "Sunder",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { attackSpeedMultiplier = -25, baseMultiplier = 1.75, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Sunder",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				triggerable = true,
				attack = true,
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["DeathKnightSlamEAA"] = {
	name = "Slam",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, },
	castTime = 2,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 2, storedUses = 1, levelRequirement = 0, cooldown = 8, },
	},
	statSets = {
		[1] = {
			label = "Slam",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				triggerable = true,
			},
			constantStats = {
				{ "base_skill_effect_duration", 3000 },
				{ "active_skill_attack_speed_+%_final", -20 },
			},
			stats = {
				"is_area_damage",
				"action_attack_or_cast_time_uses_animation_length",
				"base_skill_can_be_blocked",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["DTTHellscapeStabbySkyStab"] = {
	name = "Basic Attack",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 2.5,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 1.5, storedUses = 1, levelRequirement = 0, cooldown = 8, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
				projectile = true,
			},
			constantStats = {
				{ "attack_maximum_action_distance_+", 37 },
				{ "active_skill_base_physical_damage_%_to_convert_to_lightning", 40 },
			},
			stats = {
				"skill_can_fire_arrows",
				"skill_can_fire_wand_projectiles",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["DTTMantisRatLeap"] = {
	name = "Leap",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Movement] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 7, },
	},
	statSets = {
		[1] = {
			label = "Leap",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				triggerable = true,
				attack = true,
			},
			constantStats = {
				{ "walk_emerge_extra_distance", -7 },
				{ "leap_slam_minimum_distance", 40 },
				{ "spell_maximum_action_distance_+%", -32 },
			},
			stats = {
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["EDSGolemancerReapLeft"] = {
	name = "Basic Attack",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 1.5,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 1.6, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack",
			baseEffectiveness = 2.0371999740601,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
			},
			constantStats = {
				{ "active_skill_base_physical_damage_%_to_convert_to_cold", 70 },
				{ "voll_slam_damage_+%_final_at_centre", 30 },
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["EDSPyramidHandLightningLance"] = {
	name = "",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "",
			baseEffectiveness = 2,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				triggerable = true,
				spell = true,
				hit = true,
			},
			constantStats = {
				{ "spell_maximum_action_distance_+%", -65 },
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
				"is_area_damage",
				"action_attack_or_cast_time_uses_animation_length",
				"base_skill_can_be_blocked",
				"base_skill_can_be_avoided_by_dodge_roll",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["EDSShellMonsterFlamethrower"] = {
	name = "Flamethrower",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 3,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, storedUses = 1, levelRequirement = 0, cooldown = 11, },
	},
	statSets = {
		[1] = {
			label = "Flamethrower",
			baseEffectiveness = 1.5,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				triggerable = true,
			},
			constantStats = {
				{ "spell_maximum_action_distance_+%", -75 },
				{ "monster_penalty_against_minions_damage_+%_final_vs_player_minions", -40 },
				{ "ignite_chance_+%", 100 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"is_area_damage",
				"base_skill_can_be_blocked",
				"base_skill_can_be_avoided_by_dodge_roll",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["EDSShellMonsterPoisonSpray"] = {
	name = "Poison Spray",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 3,
	qualityStats = {
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 11, },
	},
	statSets = {
		[1] = {
			label = "Poison Spray",
			baseEffectiveness = 0.69999998807907,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				triggerable = true,
			},
			constantStats = {
				{ "spell_maximum_action_distance_+%", -65 },
				{ "base_chance_to_poison_on_hit_%", 50 },
			},
			stats = {
				"spell_minimum_base_chaos_damage",
				"spell_maximum_base_chaos_damage",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["ExpeditionGroundLaser"] = {
	name = "Ground Laser",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 2,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, storedUses = 1, levelRequirement = 0, cooldown = 2, },
		[2] = { critChance = 5, storedUses = 1, levelRequirement = 0, cooldown = 2, },
	},
	statSets = {
		[1] = {
			label = "Ground Laser",
			baseEffectiveness = 0.56000000238419,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				triggerable = true,
			},
			constantStats = {
				{ "ignite_art_variation", 3 },
				{ "ignite_chance_+%", 10 },
				{ "spell_maximum_action_distance_+%", -65 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"active_skill_ignite_effect_+%_final",
				"is_area_damage",
				"cannot_stun",
				"disable_visual_hit_effect",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, 50, statInterpolation = { 3, 3, 1, }, actorLevel = 1, },
				[2] = { 0.80000001192093, 1.2000000476837, 200, statInterpolation = { 3, 3, 1, }, actorLevel = 68, },
			},
		},
	}
}
skills["FarudinWarlockBugRend"] = {
	name = "Rend",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Area] = true, [SkillType.Duration] = true, [SkillType.Projectile] = true, [SkillType.DamageOverTime] = true, [SkillType.Chaos] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.Trappable] = true, [SkillType.Mineable] = true, [SkillType.Totemable] = true, [SkillType.DegenOnlySpellDamage] = true, [SkillType.AreaSpell] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Rend",
			baseEffectiveness = 2.25,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				area = true,
				duration = true,
				projectile = true,
				triggerable = true,
			},
			constantStats = {
				{ "active_skill_projectile_speed_+%_variation_final", 25 },
				{ "active_skill_area_of_effect_radius_+%_final", -30 },
				{ "base_skill_effect_duration", 1000 },
				{ "spell_maximum_action_distance_+%", -40 },
			},
			stats = {
				"base_physical_damage_to_deal_per_minute",
				"base_is_projectile",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { 50.000001117587, statInterpolation = { 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["FungalArtilleryMortar"] = {
	name = "Mortar",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1.333,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Mortar",
			baseEffectiveness = 3.7999999523163,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
			},
			constantStats = {
				{ "projectile_spread_radius", 5 },
				{ "monster_projectile_variation", 1120 },
				{ "spell_maximum_action_distance_+%", -35 },
			},
			stats = {
				"spell_minimum_base_physical_damage",
				"spell_maximum_base_physical_damage",
				"is_area_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"global_poison_on_hit",
				"base_skill_can_be_avoided_by_dodge_roll",
				"projectile_ballistic_angle_from_reference_event",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["GADeathKnightOverheadslamforward"] = {
	name = "Overhead Slam",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 1.75, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Overhead Slam",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				triggerable = true,
			},
			constantStats = {
				{ "base_skill_effect_duration", 3000 },
				{ "active_skill_attack_speed_+%_final", -20 },
				{ "attack_maximum_action_distance_+", 30 },
			},
			stats = {
				"is_area_damage",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["GSArmourCasterVolatileExplode"] = {
	name = "Volatile Mote",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Volatile Mote",
			baseEffectiveness = 0.56000000238419,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "geometry_spell",
			baseFlags = {
				spell = true,
				triggerable = true,
			},
			baseMods = {
				skill("cooldown", 15),
			},
			constantStats = {
				{ "global_chance_to_blind_on_hit_%", 50 },
				{ "active_skill_shock_chance_+%_final", 50 },
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
				"is_area_damage",
			},
			levels = {
				[1] = { 0.5, 1.5, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["GSCenobiteBloaterOnDeath"] = {
	name = "Death Explosion",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Death Explosion",
			baseEffectiveness = 10,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "geometry_spell",
			baseFlags = {
				spell = true,
				triggerable = true,
			},
			constantStats = {
				{ "voll_slam_damage_+%_final_at_centre", 35 },
			},
			stats = {
				"spell_minimum_base_physical_damage",
				"spell_maximum_base_physical_damage",
				"is_area_damage",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["GSMercurialCasterBlast"] = {
	name = "Rune Blast",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 2.5,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, storedUses = 1, levelRequirement = 0, cooldown = 6, },
		[2] = { critChance = 5, storedUses = 1, levelRequirement = 0, cooldown = 6, },
		[3] = { critChance = 5, storedUses = 1, levelRequirement = 0, cooldown = 6, },
	},
	statSets = {
		[1] = {
			label = "Rune Blast",
			baseEffectiveness = 6,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				triggerable = true,
			},
			constantStats = {
				{ "ignite_art_variation", 4 },
				{ "ignite_chance_+%", 40 },
				{ "spell_maximum_action_distance_+%", -35 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"is_area_damage",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
				[2] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 45, },
				[3] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 68, },
			},
		},
	}
}
skills["GACenobiteBloaterSlam"] = {
	name = "Slam",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 2, storedUses = 1, levelRequirement = 0, cooldown = 6, },
	},
	statSets = {
		[1] = {
			label = "Slam",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				triggerable = true,
				attack = true,
				melee = true,
			},
			constantStats = {
				{ "melee_range_+", 15 },
			},
			stats = {
				"is_area_damage",
				"action_attack_or_cast_time_uses_animation_length",
				"base_skill_cannot_be_blocked",
				"base_skill_cannot_be_avoided_by_dodge_roll",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["GAFigureheadSlamGhostFlame"] = {
	name = "Slam",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, },
	castTime = 4.8,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 2, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Slam",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				triggerable = true,
				attack = true,
			},
			constantStats = {
				{ "melee_range_+", 42 },
				{ "active_skill_base_physical_damage_%_to_convert_to_fire", 40 },
			},
			stats = {
				"is_area_damage",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["GASaltGolemMelee"] = {
	name = "",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				triggerable = true,
				attack = true,
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["GAHellscapeFleshLeapImpact"] = {
	name = "Leap Slam",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 1.15, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Leap Slam",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				triggerable = true,
			},
			baseMods = {
				skill("cooldown", 5),
			},
			constantStats = {
				{ "active_skill_base_physical_damage_%_to_convert_to_fire", 40 },
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["GAHellscapePaleEliteSkyStab"] = {
	name = "Stab Attack",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 1.25, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Stab Attack",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				triggerable = true,
			},
			constantStats = {
				{ "active_skill_shock_chance_+%_final", 50 },
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["GAMantisRatDualStrike"] = {
	name = "Dual Strike",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 1.05, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Dual Strike",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				triggerable = true,
				attack = true,
			},
			constantStats = {
				{ "active_skill_base_physical_damage_%_to_convert_to_lightning", 50 },
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["GAMediumBeetleChargedSunder"] = {
	name = "Charged Sunder",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, },
	castTime = 2,
	qualityStats = {
	},
	levels = {
		[1] = { attackSpeedMultiplier = -25, storedUses = 1, baseMultiplier = 2, cooldown = 5.5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Charged Sunder",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				triggerable = true,
				attack = true,
			},
			constantStats = {
				{ "attack_maximum_action_distance_+", 20 },
				{ "active_skill_base_physical_damage_%_to_convert_to_lightning", 60 },
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 20, },
				[3] = { actorLevel = 21, },
				[4] = { actorLevel = 84, },
			},
		},
	}
}
skills["GAMediumBeetleSunder"] = {
	name = "Sunder",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, },
	castTime = 2,
	qualityStats = {
	},
	levels = {
		[1] = { attackSpeedMultiplier = -25, storedUses = 1, baseMultiplier = 1.35, cooldown = 5.5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Sunder",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				triggerable = true,
				attack = true,
			},
			constantStats = {
				{ "attack_maximum_action_distance_+", 14 },
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["GAMutewindWomanSpearStab1"] = {
	name = "Spear Stab",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 0.5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Spear Stab",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				triggerable = true,
				attack = true,
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["GATwoHeadedTitanSlam"] = {
	name = "Slam",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Slam",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				triggerable = true,
				attack = true,
			},
			constantStats = {
				{ "attack_maximum_action_distance_+", 20 },
			},
			stats = {
				"is_area_damage",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["GATwoHeadedTitanStomp"] = {
	name = "Stomp",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Stomp",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				triggerable = true,
				attack = true,
			},
			constantStats = {
				{ "attack_maximum_action_distance_+", -8 },
			},
			stats = {
				"is_area_damage",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["GoreChargerCharge"] = {
	name = "Charge",
	hidden = true,
	description = "Charges at an enemy, bashing it with the character's shield and striking it. This knocks it back and stuns it. Enemies in the way are pushed to the side. Damage and stun are proportional to distance travelled. Cannot be supported by Multistrike.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.Melee] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Movement] = true, [SkillType.Travel] = true, },
	weaponTypes = {
		["None"] = true,
		["One Handed Sword"] = true,
		["One Handed Mace"] = true,
		["Flail"] = true,
		["Spear"] = true,
		["One Handed Axe"] = true,
		["Dagger"] = true,
		["Claw"] = true,
	},
	castTime = 0.8,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 1.43, storedUses = 1, levelRequirement = 0, cooldown = 4.5, },
	},
	statSets = {
		[1] = {
			label = "Charge",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
			},
			constantStats = {
				{ "base_movement_velocity_+%", 92 },
			},
			stats = {
				"ignores_proximity_shield",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["GraveyardGhostDashToTarget"] = {
	name = "Dash",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Movement] = true, },
	castTime = 0.93,
	qualityStats = {
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 4, },
	},
	statSets = {
		[1] = {
			label = "Dash",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
			},
			stats = {
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["GraveyardSpookyGhostExplode"] = {
	name = "Sword Barrage",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 2.5,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 7, storedUses = 1, levelRequirement = 0, cooldown = 7, },
	},
	statSets = {
		[1] = {
			label = "Sword Barrage",
			baseEffectiveness = 7,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "geometry_spell",
			baseFlags = {
				triggerable = true,
				spell = true,
				hit = true,
			},
			constantStats = {
				{ "freeze_as_though_dealt_damage_+%", 100 },
			},
			stats = {
				"spell_minimum_base_cold_damage",
				"spell_maximum_base_cold_damage",
				"is_area_damage",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["GSDesertBatZap"] = {
	name = "Zap",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 6, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Zap",
			baseEffectiveness = 1.25,
			incrementalEffectiveness = 0.3910000026226,
			damageIncrementalEffectiveness = 0.012749999761581,
			statDescriptionScope = "geometry_spell",
			baseFlags = {
				triggerable = true,
				spell = true,
				hit = true,
			},
			baseMods = {
				skill("cooldown", 6),
			},
			constantStats = {
				{ "generic_skill_trigger_id", 1 },
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
				"is_area_damage",
				"base_skill_can_be_blocked",
			},
			levels = {
				[1] = { 0.5, 1.5, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["GSExpeditionBoneCultistEggExplosion"] = {
	name = "Pustule",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 7, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Pustule",
			baseEffectiveness = 6,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "geometry_spell",
			baseFlags = {
				triggerable = true,
				spell = true,
				hit = true,
			},
			baseMods = {
				skill("cooldown", 6),
			},
			constantStats = {
				{ "active_skill_base_cold_damage_%_to_convert_to_chaos", 40 },
			},
			stats = {
				"spell_minimum_base_cold_damage",
				"spell_maximum_base_cold_damage",
				"is_area_damage",
				"base_is_projectile",
				"base_skill_can_be_blocked",
				"base_skill_can_be_avoided_by_dodge_roll",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["GSHellscapeDemonEliteBeamNuke"] = {
	name = "Beam",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 2,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, storedUses = 1, levelRequirement = 0, cooldown = 7, },
	},
	statSets = {
		[1] = {
			label = "Beam",
			baseEffectiveness = 9.1999998092651,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				triggerable = true,
				spell = true,
				hit = true,
			},
			constantStats = {
				{ "spell_maximum_action_distance_+%", -40 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"is_area_damage",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["GSHellscapePaleEliteBoltImpact"] = {
	name = "Bolt Impact",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Bolt Impact",
			baseEffectiveness = 3,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "geometry_spell",
			baseFlags = {
				triggerable = true,
				spell = true,
				hit = true,
			},
			constantStats = {
				{ "generic_skill_trigger_id", 1 },
				{ "shock_art_variation", 10 },
				{ "damage_hit_effect_index", 103 },
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
			},
			levels = {
				[1] = { 0.5, 1.5, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["GSHellscapePaleEliteOmegaBeam"] = {
	name = "Omega Beam",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 2.333,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, storedUses = 1, levelRequirement = 0, cooldown = 10, },
	},
	statSets = {
		[1] = {
			label = "Omega Beam",
			baseEffectiveness = 6.1500000953674,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "geometry_spell",
			baseFlags = {
				triggerable = true,
				spell = true,
				hit = true,
			},
			constantStats = {
				{ "spell_maximum_action_distance_+%", -55 },
				{ "shock_art_variation", 10 },
				{ "damage_hit_effect_index", 103 },
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
			},
			levels = {
				[1] = { 0.5, 1.5, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["GSProwlingShadeIceBeam"] = {
	name = "Ice Beam",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 3.2,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 8, storedUses = 1, levelRequirement = 0, cooldown = 8, },
	},
	statSets = {
		[1] = {
			label = "Ice Beam",
			baseEffectiveness = 5.5,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "geometry_spell",
			baseFlags = {
				triggerable = true,
				spell = true,
				hit = true,
			},
			constantStats = {
				{ "active_skill_hit_damage_freeze_multiplier_+%_final", 250 },
				{ "spell_maximum_action_distance_+%", -45 },
			},
			stats = {
				"spell_minimum_base_cold_damage",
				"spell_maximum_base_cold_damage",
				"is_area_damage",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 35, },
			},
		},
	}
}
skills["GSRagingFireSpiritsVolatileSanctum"] = {
	name = "Self-Destruct",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Self-Destruct",
			baseEffectiveness = 3,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "geometry_spell",
			baseFlags = {
				triggerable = true,
				spell = true,
				hit = true,
			},
			constantStats = {
				{ "spell_maximum_action_distance_+%", -92 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"is_area_damage",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["GSRagingTimeSpiritsVolatileSanctum"] = {
	name = "Self-Destruct",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Self-Destruct",
			baseEffectiveness = 3,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "geometry_spell",
			baseFlags = {
				triggerable = true,
				spell = true,
				hit = true,
			},
			constantStats = {
				{ "spell_maximum_action_distance_+%", -92 },
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
				"is_area_damage",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { 0.5, 1.5, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["GSVaalConstructSkitterbotGrenadeExplode"] = {
	name = "Grenade Explosion",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Grenade Explosion",
			baseEffectiveness = 4.5999999046326,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "geometry_spell",
			baseFlags = {
				triggerable = true,
				spell = true,
				hit = true,
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"is_area_damage",
				"skill_ignore_moving_slowdown_on_shift_attack",
				"base_is_projectile",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["GSWarlockRaiseBugs"] = {
	name = "Raise Bugs",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 18, },
	},
	statSets = {
		[1] = {
			label = "Raise Bugs",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "geometry_spell",
			baseFlags = {
				spell = true,
			},
			stats = {
				"is_area_damage",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["HellscapeDemonFodderFaceLaser"] = {
	name = "Laser",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 2,
	qualityStats = {
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 6, },
	},
	statSets = {
		[1] = {
			label = "Laser",
			baseEffectiveness = 0.77499997615814,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				triggerable = true,
				spell = true,
				hit = true,
			},
			constantStats = {
				{ "spell_maximum_action_distance_+%", -60 },
				{ "ignite_chance_+%", 200 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"is_area_damage",
				"cannot_stun",
				"base_skill_can_be_blocked",
				"base_skill_can_be_avoided_by_dodge_roll",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["HyenaCentaurMeleeStab"] = {
	name = "Basic Attack",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 1.25, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
			},
			constantStats = {
				{ "attack_maximum_action_distance_+", 14 },
				{ "main_hand_local_maim_on_hit_%", 30 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["HyenaCentaurMeleeSwipe"] = {
	name = "Swipe",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 0.75, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Swipe",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["HyenaCentaurSpearThrow"] = {
	name = "Spear Throw",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 1.5, storedUses = 1, levelRequirement = 0, cooldown = 4.5, },
	},
	statSets = {
		[1] = {
			label = "Spear Throw",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				projectile = true,
				triggerable = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1020 },
				{ "spell_maximum_action_distance_+%", -25 },
				{ "projectile_spread_radius", 3 },
				{ "main_hand_local_maim_on_hit_%", 30 },
			},
			stats = {
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"projectile_ballistic_angle_from_reference_event",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MASExtraAttackDistance6"] = {
	name = "Basic Attack",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
			},
			constantStats = {
				{ "attack_maximum_action_distance_+", 6 },
			},
			stats = {
				"skill_can_fire_arrows",
				"skill_can_fire_wand_projectiles",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MASExtraAttackDistance20"] = {
	name = "Basic Attack",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
			},
			constantStats = {
				{ "attack_maximum_action_distance_+", 20 },
			},
			stats = {
				"skill_can_fire_arrows",
				"skill_can_fire_wand_projectiles",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MASFireConvertAltArtFireArrow"] = {
	name = "Basic Attack",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				projectile = true,
			},
			constantStats = {
				{ "active_skill_base_physical_damage_%_to_convert_to_fire", 60 },
				{ "ignite_chance_+%", 50 },
				{ "arrow_projectile_variation", 1000 },
			},
			stats = {
				"skill_can_fire_arrows",
				"skill_can_fire_wand_projectiles",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"projectile_ballistic_angle_from_reference_event",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MASStatueWretchPush"] = {
	name = "Basic Attack",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 0.1, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
			},
			stats = {
				"skill_can_fire_arrows",
				"skill_can_fire_wand_projectiles",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MASKelpDregCrossbow"] = {
	name = "Basic Attack",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
				projectile = true,
			},
			constantStats = {
				{ "arrow_projectile_variation", 1001 },
			},
			stats = {
				"skill_can_fire_arrows",
				"skill_can_fire_wand_projectiles",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"maintain_projectile_direction_when_using_contact_position",
				"check_for_targets_between_initiator_and_projectile_source",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MeleeAtAnimationSpeedBow"] = {
	name = "Basic Attack",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				projectile = true,
				melee = true,
			},
			stats = {
				"skill_can_fire_arrows",
				"skill_can_fire_wand_projectiles",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"maintain_projectile_direction_when_using_contact_position",
				"check_for_targets_between_initiator_and_projectile_source",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MeleeAtAnimationSpeedComboTEMP"] = {
	name = "Basic Attack",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
			},
			stats = {
				"skill_can_fire_arrows",
				"skill_can_fire_wand_projectiles",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MeleeAtAnimationSpeedFire"] = {
	name = "Basic Attack (Fire)",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack (Fire)",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
			},
			constantStats = {
				{ "active_skill_base_physical_damage_%_to_convert_to_fire", 40 },
			},
			stats = {
				"skill_can_fire_arrows",
				"skill_can_fire_wand_projectiles",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MeleeAtAnimationSpeedFireCombo35"] = {
	name = "Basic Attack (Fire)",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack (Fire)",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
			},
			constantStats = {
				{ "active_skill_base_physical_damage_%_to_convert_to_fire", 35 },
			},
			stats = {
				"skill_can_fire_arrows",
				"skill_can_fire_wand_projectiles",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MeleeAtAnimationSpeedLightning"] = {
	name = "Basic Attack (Lightning)",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack (Lightning)",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
			},
			constantStats = {
				{ "active_skill_base_physical_damage_%_to_convert_to_lightning", 40 },
			},
			stats = {
				"skill_can_fire_arrows",
				"skill_can_fire_wand_projectiles",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MMSBaneSapling"] = {
	name = "Basic Spell",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1.77,
	qualityStats = {
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 2, },
	},
	statSets = {
		[1] = {
			label = "Basic Spell",
			baseEffectiveness = 1.5,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "projectile_spread_radius", 5 },
				{ "active_skill_base_physical_damage_%_to_convert_to_chaos", 40 },
				{ "spell_maximum_action_distance_+%", -40 },
				{ "monster_projectile_variation", 1340 },
			},
			stats = {
				"spell_minimum_base_physical_damage",
				"spell_maximum_base_physical_damage",
				"is_area_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"projectile_ballistic_angle_from_target_distance",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}

skills["MMSBoneRabbleMortar"] = {
	name = "Mortar",
	hidden = true,
	description = "Generic monster mortar skill. Like Monster Projectile but has an impact effect.",
	skillTypes = { [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.Area] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.AreaSpell] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 6, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Mortar",
			baseEffectiveness = 3.5,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				projectile = true,
				spell = true,
				area = true,
			},
			constantStats = {
				{ "projectile_spread_radius", 12 },
				{ "number_of_chains", 3 },
				{ "monster_mortar_bounce_angle_variance", 90 },
				{ "spell_maximum_action_distance_+%", -55 },
				{ "mortar_projectile_distance_override", 23 },
				{ "active_skill_shock_chance_+%_final", 50 },
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
				"is_area_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { 0.5, 1.5, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MMSHellscapeDemonEliteTripleMortar"] = {
	name = "Triple Mortar",
	hidden = true,
	description = "Generic monster mortar skill. Like Monster Projectile but has an impact effect.",
	skillTypes = { [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.Area] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.AreaSpell] = true, },
	castTime = 1.5,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Triple Mortar",
			baseEffectiveness = 2.5,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				triggerable = true,
				spell = true,
				area = true,
				hit = true,
			},
			constantStats = {
				{ "spell_maximum_action_distance_+%", -40 },
				{ "projectile_spread_radius", 14 },
				{ "active_skill_ignite_chance_+%_final", 100 },
				{ "skill_speed_+%", -10 },
				{ "number_of_additional_projectiles", 2 },
			},
			stats = {
				"spell_minimum_base_physical_damage",
				"spell_maximum_base_physical_damage",
				"is_area_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"distribute_projectiles_over_contact_points",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MMSVaalGuardGrenade"] = {
	name = "Explosive Grenade",
	hidden = true,
	description = "Fire a bouncing Grenade that unleashes a devastating fiery blast when its fuse expires.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.Area] = true, [SkillType.ProjectileNumber] = true, [SkillType.ProjectileSpeed] = true, [SkillType.Cooldown] = true, [SkillType.Grenade] = true, [SkillType.Fire] = true, [SkillType.UsableWhileMoving] = true, [SkillType.DetonatesAfterTime] = true, [SkillType.Projectile] = true, },
	weaponTypes = {
		["Crossbow"] = true,
	},
	castTime = 1.5,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 4.4, storedUses = 1, levelRequirement = 0, cooldown = 2, },
	},
	statSets = {
		[1] = {
			label = "Explosive Grenade",
			baseEffectiveness = 3,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "explosive_grenade",
			baseFlags = {
				attack = true,
				area = true,
				projectile = true,
				duration = true,
			},
			constantStats = {
				{ "base_skill_detonation_time", 1500 },
				{ "active_skill_base_physical_damage_%_to_convert_to_fire", 20 },
				{ "melee_range_+", 40 },
			},
			stats = {
				"active_skill_base_area_of_effect_radius",
				"base_is_projectile",
				"projectile_ballistic_angle_from_reference_event",
				"projectile_uses_contact_position",
				"is_area_damage",
				"ballistic_projectiles_always_bounce",
			},
			levels = {
				[1] = { 18, statInterpolation = { 1, }, actorLevel = 1, },
			},
		},
	}
}
skills["MMSVaalGuardOilTrap"] = {
	name = "Oil Grenade",
	hidden = true,
	description = "Fire a bouncing Grenade that bursts in a spray of Oil when the fuse expires or when it impacts an Enemy, dealing minimal damage but covering the ground and nearby enemies in Oil. Oil created this way can be Ignited by Detonator Skills or Ignited Ground.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.Area] = true, [SkillType.ProjectileNumber] = true, [SkillType.ProjectileSpeed] = true, [SkillType.Cooldown] = true, [SkillType.Duration] = true, [SkillType.Grenade] = true, [SkillType.Fire] = true, [SkillType.UsableWhileMoving] = true, [SkillType.CreatesGroundEffect] = true, [SkillType.DetonatesAfterTime] = true, [SkillType.Projectile] = true, },
	weaponTypes = {
		["Crossbow"] = true,
	},
	castTime = 1.5,
	qualityStats = {
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 2, },
	},
	statSets = {
		[1] = {
			label = "Oil Grenade",
			baseEffectiveness = 2.5,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "oil_grenade",
			baseFlags = {
				attack = true,
				area = true,
				projectile = true,
				duration = true,
			},
			constantStats = {
				{ "ground_oil_art_variation", 2002 },
				{ "base_skill_detonation_time", 1300 },
				{ "base_secondary_skill_effect_duration", 7000 },
				{ "melee_range_+", 40 },
			},
			stats = {
				"active_skill_base_area_of_effect_radius",
				"skill_base_covered_in_oil_movement_speed_+%_final_to_apply",
				"skill_base_ground_oil_movement_speed_+%_final_to_apply",
				"skill_base_ground_oil_exposure_-_to_total_fire_resistance",
				"skill_base_covered_in_oil_exposure_-_to_total_fire_resistance",
				"base_is_projectile",
				"projectile_ballistic_angle_from_reference_event",
				"projectile_uses_contact_position",
				"is_area_damage",
				"ballistic_projectiles_always_bounce",
			},
			levels = {
				[1] = { 20, -25, -25, 10, 10, statInterpolation = { 1, 1, 1, 1, 1, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSArmourCasterBasic"] = {
	name = "Fireball",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1.17,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Fireball",
			baseEffectiveness = 2.7709999084473,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 197 },
				{ "spell_maximum_action_distance_+%", -30 },
				{ "ignite_art_variation", 3 },
				{ "base_number_of_projectiles", 1 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"disable_visual_hit_effect",
				"distribute_projectiles_over_contact_points",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSAzmeriPictStaffProj"] = {
	name = "Chaos Bolt",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 6, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Chaos Bolt",
			baseEffectiveness = 4,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 231 },
				{ "spell_maximum_action_distance_+%", -40 },
			},
			stats = {
				"spell_minimum_base_chaos_damage",
				"spell_maximum_base_chaos_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"action_attack_or_cast_time_uses_animation_length",
				"maintain_projectile_direction_when_using_contact_position",
				"check_for_targets_between_initiator_and_projectile_source",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSAzmeriPictStaffProj2"] = {
	name = "Chaos Bolt",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 6, storedUses = 1, levelRequirement = 0, cooldown = 5, },
	},
	statSets = {
		[1] = {
			label = "Chaos Bolt",
			baseEffectiveness = 4,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			baseMods = {
				mod("ProjectileCount", "BASE", 2),
			},
			constantStats = {
				{ "monster_projectile_variation", 231 },
				{ "spell_maximum_action_distance_+%", -40 },
			},
			stats = {
				"spell_minimum_base_chaos_damage",
				"spell_maximum_base_chaos_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"action_attack_or_cast_time_uses_animation_length",
				"maintain_projectile_direction_when_using_contact_position",
				"check_for_targets_between_initiator_and_projectile_source",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSBloodMageBloodProjectile"] = {
	name = "Blood Projectile",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 2.57,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, storedUses = 1, levelRequirement = 0, cooldown = 6, },
	},
	statSets = {
		[1] = {
			label = "Blood Projectile",
			baseEffectiveness = 3,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1041 },
				{ "number_of_additional_projectiles", 2 },
			},
			stats = {
				"spell_minimum_base_physical_damage",
				"spell_maximum_base_physical_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"check_for_targets_between_initiator_and_projectile_source",
				"projectile_uses_contact_direction",
				"distribute_projectiles_over_contact_points",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSBoneRabbleBurningArrow"] = {
	name = "Burning Arrow",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 1.5,
	qualityStats = {
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 6, },
	},
	statSets = {
		[1] = {
			label = "Burning Arrow",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1032 },
				{ "ignite_chance_+%", 50 },
				{ "non_skill_base_physical_damage_%_to_gain_as_fire", 30 },
				{ "active_skill_base_physical_damage_%_to_convert_to_fire", 60 },
			},
			stats = {
				"use_scaled_contact_offset",
				"projectile_uses_contact_position",
				"base_is_projectile",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MPSBoneCultistNecromancerLightning"] = {
	name = "Basic Spell (Lightning)",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1.333,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Spell (Lightning)",
			baseEffectiveness = 3.6749999523163,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1019 },
				{ "spell_maximum_action_distance_+%", -40 },
				{ "active_skill_shock_chance_+%_final", 20 },
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
				"base_is_projectile",
				"use_scaled_contact_offset",
				"projectile_uses_contact_position",
				"maintain_projectile_direction_when_using_contact_position",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { 0.5, 1.5, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSBoneCultistZealotFire"] = {
	name = "Basic Spell (Fire)",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1.333,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Spell (Fire)",
			baseEffectiveness = 3.2000000476837,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1017 },
				{ "spell_maximum_action_distance_+%", -50 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"base_is_projectile",
				"use_scaled_contact_offset",
				"projectile_uses_contact_position",
				"maintain_projectile_direction_when_using_contact_position",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSBoneCultistZealotLightning"] = {
	name = "Basic Spell (Lightning)",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1.333,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 6, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Spell (Lightning)",
			baseEffectiveness = 3,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1018 },
				{ "spell_maximum_action_distance_+%", -40 },
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
				"base_is_projectile",
				"use_scaled_contact_offset",
				"projectile_uses_contact_position",
				"maintain_projectile_direction_when_using_contact_position",
			},
			levels = {
				[1] = { 0.5, 1.5, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSBreachEliteBoneProjectile"] = {
	name = "Basic Spell (Cold)",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 8, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Spell (Cold)",
			baseEffectiveness = 3,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1361 },
				{ "spell_maximum_action_distance_+%", -30 },
			},
			stats = {
				"spell_minimum_base_cold_damage",
				"spell_maximum_base_cold_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"maintain_projectile_direction_when_using_contact_position",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSBreachEliteFallenLunarisMonsterChaosSpark"] = {
	name = "Chaos Spark",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, storedUses = 1, levelRequirement = 0, cooldown = 10, },
	},
	statSets = {
		[1] = {
			label = "Chaos Spark",
			baseEffectiveness = 3.25,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1360 },
				{ "number_of_additional_projectiles", 5 },
				{ "projectile_spread_radius", 12 },
			},
			stats = {
				"spell_minimum_base_chaos_damage",
				"spell_maximum_base_chaos_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"maintain_projectile_direction_when_using_contact_position",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_ballistic_angle_from_reference_event",
				"ballistic_projectiles_always_bounce",
				"distribute_projectiles_over_contact_points",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSChaosGodTriHeadLizardBasicProjectile"] = {
	name = "Basic Spell (Chaos)",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Spell (Chaos)",
			baseEffectiveness = 3,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1370 },
				{ "spell_maximum_action_distance_+%", -30 },
				{ "number_of_additional_projectiles", 2 },
				{ "projectile_spread_radius", 16 },
				{ "projectile_speed_variation_+%", 10 },
			},
			stats = {
				"spell_minimum_base_chaos_damage",
				"spell_maximum_base_chaos_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"maintain_projectile_direction_when_using_contact_position",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_ballistic_angle_from_target_distance",
				"distribute_projectiles_over_contact_points",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSExpeditionBoneCultistProjectiles"] = {
	name = "Basic Spell (Cold)",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1.333,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 7, levelRequirement = 0, },
		[2] = { critChance = 7, levelRequirement = 0, },
		[3] = { critChance = 7, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Spell (Cold)",
			baseEffectiveness = 3.75,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 192 },
				{ "spell_maximum_action_distance_+%", -35 },
				{ "active_skill_base_cold_damage_%_to_convert_to_chaos", 40 },
			},
			stats = {
				"spell_minimum_base_cold_damage",
				"spell_maximum_base_cold_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
				[2] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 45, },
				[3] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 68, },
			},
		},
	}
}
skills["MPSHellscapeDemonFodderProj"] = {
	name = "Fireball",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1.25,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 6, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Fireball",
			baseEffectiveness = 2.25,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 206 },
				{ "spell_maximum_action_distance_+%", -38 },
				{ "active_skill_ignite_chance_+%_final", 100 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"action_attack_or_cast_time_uses_animation_length",
				"maintain_projectile_direction_when_using_contact_position",
				"check_for_targets_between_initiator_and_projectile_source",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSHellscapeFleshEliteBasicProj"] = {
	name = "Basic Spell (Physical)",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1.166,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Spell (Physical)",
			baseEffectiveness = 2.8499999046326,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 207 },
				{ "projectile_ballistic_gravity_override", -40 },
			},
			stats = {
				"spell_minimum_base_physical_damage",
				"spell_maximum_base_physical_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSHellscapePaleHammerhead"] = {
	name = "Basic Spell (Physical)",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1.166,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Spell (Physical)",
			baseEffectiveness = 2.4500000476837,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 205 },
				{ "spell_maximum_action_distance_+%", -40 },
				{ "base_chance_to_inflict_bleeding_%", 50 },
			},
			stats = {
				"spell_minimum_base_physical_damage",
				"spell_maximum_base_physical_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"always_pierce",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSMercurialCasterEnrage"] = {
	name = "Basic Spell",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1.33,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Spell",
			baseEffectiveness = 2.5,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 199 },
				{ "spell_maximum_action_distance_+%", -30 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"spell_minimum_base_cold_damage",
				"spell_maximum_base_cold_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"disable_visual_hit_effect",
			},
			levels = {
				[1] = { 0.40000000596046, 0.60000002384186, 0.40000000596046, 0.60000002384186, statInterpolation = { 3, 3, 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSRedSkeletonCaster"] = {
	name = "Basic Spell (Cold)",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1.5,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 7, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Spell (Cold)",
			baseEffectiveness = 3.2999999523163,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1024 },
				{ "spell_maximum_action_distance_+%", -35 },
			},
			stats = {
				"spell_minimum_base_cold_damage",
				"spell_maximum_base_cold_damage",
				"use_scaled_contact_offset",
				"projectile_uses_contact_position",
				"base_is_projectile",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSSkeletonMancerBasicProj"] = {
	name = "Basic Spell",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Spell",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1103 },
				{ "spell_maximum_action_distance_+%", -30 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"spell_minimum_base_physical_damage",
				"spell_maximum_base_physical_damage",
				"base_is_projectile",
				"use_scaled_contact_offset",
				"projectile_uses_contact_position",
				"maintain_projectile_direction_when_using_contact_position",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { 0.40000000596046, 0.60000002384186, 0.40000000596046, 0.60000002384186, statInterpolation = { 3, 3, 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSVaalBloodPriestProj"] = {
	name = "Blood Projectile",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Blood Projectile",
			baseEffectiveness = 3,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1047 },
				{ "spell_maximum_action_distance_+%", -35 },
			},
			stats = {
				"spell_minimum_base_physical_damage",
				"spell_maximum_base_physical_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"action_attack_or_cast_time_uses_animation_length",
				"check_for_targets_between_initiator_and_projectile_source",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSVaalConstructCannon"] = {
	name = "Cannon",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Cannon",
			baseEffectiveness = 2,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1088 },
				{ "active_skill_base_physical_damage_%_to_convert_to_fire", 40 },
				{ "spell_maximum_action_distance_+%", -40 },
			},
			stats = {
				"spell_minimum_base_physical_damage",
				"spell_maximum_base_physical_damage",
				"base_is_projectile",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_ballistic_angle_from_reference_event",
				"projectile_uses_contact_position",
				"projectile_uses_contact_direction",
				"use_scaled_contact_offset",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPAVaalHumanoidCannon"] = {
	name = "Cannon",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Cannon",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1094 },
				{ "active_skill_base_physical_damage_%_to_convert_to_fire", 40 },
				{ "spell_maximum_action_distance_+%", -40 },
			},
			stats = {
				"base_is_projectile",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_ballistic_angle_from_reference_event",
				"projectile_uses_contact_position",
				"projectile_uses_contact_direction",
				"use_scaled_contact_offset",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MPSVaalHumanoidCannonNapalmMiniBlob"] = {
	name = "Napalm Cannon",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Napalm Cannon",
			baseEffectiveness = 1.5,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1128 },
				{ "number_of_additional_projectiles", 2 },
				{ "projectile_spread_radius", 15 },
				{ "active_skill_projectile_speed_+%_variation_final", 25 },
				{ "number_of_chains", 1 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"base_is_projectile",
				"projectile_ballistic_angle_from_target_distance",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPSVaalSunApparitionBasicProj"] = {
	name = "Basic Spell",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Spell",
			baseEffectiveness = 2.8499999046326,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				triggerable = true,
				hit = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1042 },
				{ "spell_maximum_action_distance_+%", -40 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"action_attack_or_cast_time_uses_animation_length",
				"check_for_targets_between_initiator_and_projectile_source",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["MPWAzmeriPitifulFabricationSkullThrow"] = {
	name = "Skull Throw",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 1.15, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Skull Throw",
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				projectile = true,
				triggerable = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 161 },
				{ "spell_maximum_action_distance_+%", -40 },
			},
			stats = {
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"action_attack_or_cast_time_uses_animation_length",
				"maintain_projectile_direction_when_using_contact_position",
				"check_for_targets_between_initiator_and_projectile_source",
				"projectile_ballistic_angle_from_reference_event",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MPWCleansedMonstrosityRailgun"] = {
	name = "Railgun",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 2.15, storedUses = 1, levelRequirement = 0, cooldown = 15, },
	},
	statSets = {
		[1] = {
			label = "Railgun",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				projectile = true,
				triggerable = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1157 },
				{ "spell_maximum_action_distance_+%", -40 },
			},
			stats = {
				"base_is_projectile",
				"action_attack_or_cast_time_uses_animation_length",
				"check_for_targets_between_initiator_and_projectile_source",
				"projectile_uses_contact_position",
				"maintain_projectile_direction_when_using_contact_position",
				"use_scaled_contact_offset",
				"projectile_ballistic_angle_from_target_distance",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MPWDrudgeExplosiveGrenade"] = {
	name = "Explosive Grenade",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Explosive Grenade",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				triggerable = true,
				projectile = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1121 },
				{ "spell_maximum_action_distance_+%", -30 },
				{ "projectile_spread_radius", 18 },
				{ "number_of_additional_projectiles", 1 },
				{ "projectile_speed_variation_+%", 30 },
			},
			stats = {
				"base_is_projectile",
				"projectile_uses_contact_position",
				"maintain_projectile_direction_when_using_contact_position",
				"action_attack_or_cast_time_uses_animation_length",
				"base_deal_no_damage",
				"ballistic_projectiles_always_bounce",
				"projectile_ballistic_angle_from_reference_event",
				"distribute_projectiles_over_contact_points",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MPWExpeditionArbalestProjectile"] = {
	name = "Basic Attack",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 1.5,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				triggerable = true,
				projectile = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 145 },
				{ "active_skill_base_physical_damage_%_to_convert_to_cold", 60 },
			},
			stats = {
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MPWExpeditionArbalestSnipe"] = {
	name = "Snipe",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 2,
	qualityStats = {
	},
	levels = {
		[1] = { attackSpeedMultiplier = -25, storedUses = 1, baseMultiplier = 2.65, cooldown = 8, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Snipe",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				triggerable = true,
				projectile = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 146 },
				{ "active_skill_base_physical_damage_%_to_convert_to_cold", 60 },
				{ "number_of_projectiles_override", 1 },
			},
			stats = {
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"always_pierce",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MPWFarudinSpearThrow"] = {
	name = "Spear Throw",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 3,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 0.5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Spear Throw",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				projectile = true,
				triggerable = true,
			},
			baseMods = {
				skill("cooldown", 8),
			},
			constantStats = {
				{ "number_of_projectiles_override", 1 },
				{ "melee_range_+", 40 },
				{ "monster_projectile_variation", 1026 },
			},
			stats = {
				"use_scaled_contact_offset",
				"projectile_uses_contact_position",
				"base_is_projectile",
				"action_attack_or_cast_time_uses_animation_length",
				"always_pierce",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MPWKelpDregPuncture"] = {
	name = "Puncture",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 1.5, levelRequirement = 0, },
		[2] = { baseMultiplier = 1.5, levelRequirement = 0, },
		[3] = { baseMultiplier = 1.5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Puncture",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				triggerable = true,
				projectile = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1055 },
				{ "chance_to_poison_on_hit_with_attacks_%", 50 },
			},
			stats = {
				"active_skill_bleeding_effect_+%_final",
				"use_scaled_contact_offset",
				"projectile_uses_contact_position",
				"base_is_projectile",
				"attacks_inflict_bleeding_on_hit",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { 0, statInterpolation = { 1, }, actorLevel = 1, },
				[2] = { 25, statInterpolation = { 1, }, actorLevel = 30, },
				[3] = { 50, statInterpolation = { 1, }, actorLevel = 60, },
			},
		},
	}
}
skills["MPWVaalSavageBlowDart"] = {
	name = "Blow Dart",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 2,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 0.75, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Blow Dart",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				triggerable = true,
				projectile = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1031 },
				{ "melee_range_+", 35 },
				{ "spell_maximum_action_distance_+%", -50 },
			},
			stats = {
				"base_is_projectile",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"global_poison_on_hit",
				"projectiles_not_offset",
				"projectile_ballistic_angle_from_reference_event",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["MutewindBanditWomanLeap"] = {
	name = "Leap Slam",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Attack] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Leap Slam",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				triggerable = true,
				attack = true,
			},
			baseMods = {
				skill("cooldown", 10),
			},
			stats = {
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["QuillCrabSpikeBurst"] = {
	name = "Spike Burst",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 0.7, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Spike Burst",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				projectile = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1000 },
				{ "projectile_angle_variance", 0 },
				{ "monster_reverse_point_blank_damage_-%_at_minimum_range", 20 },
				{ "active_skill_base_physical_damage_%_to_convert_to_fire", 40 },
				{ "projectile_spread_radius", 13 },
			},
			stats = {
				"base_is_projectile",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"projectiles_not_offset",
				"projectile_ballistic_angle_from_reference_event",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["QuillCrabSpikeBurstPoison"] = {
	name = "Spike Burst",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 0.7, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Spike Burst",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				projectile = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1004 },
				{ "projectile_angle_variance", 0 },
				{ "monster_reverse_point_blank_damage_-%_at_minimum_range", 20 },
				{ "projectile_spread_radius", 13 },
			},
			stats = {
				"base_is_projectile",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"projectiles_not_offset",
				"global_poison_on_hit",
				"projectile_ballistic_angle_from_reference_event",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["QuillCrabSpikeBurstTropical"] = {
	name = "Spike Burst",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 0.7, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Spike Burst",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				projectile = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1049 },
				{ "projectile_angle_variance", 0 },
				{ "monster_reverse_point_blank_damage_-%_at_minimum_range", 20 },
				{ "active_skill_base_physical_damage_%_to_convert_to_cold", 40 },
				{ "projectile_spread_radius", 13 },
			},
			stats = {
				"base_is_projectile",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"projectile_ballistic_angle_from_reference_event",
				"projectiles_not_offset",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["RisenArbalestBasicProjectile"] = {
	name = "Basic Attack",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 1.25, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Attack",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				projectile = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1023 },
			},
			stats = {
				"base_is_projectile",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["RisenArbalestSnipe"] = {
	name = "Snipe",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 2,
	qualityStats = {
	},
	levels = {
		[1] = { attackSpeedMultiplier = -25, storedUses = 1, baseMultiplier = 2.65, cooldown = 8, levelRequirement = 0, },
		[2] = { attackSpeedMultiplier = -25, storedUses = 1, cooldown = 8, levelRequirement = 0, },
		[3] = { attackSpeedMultiplier = -25, storedUses = 1, cooldown = 8, levelRequirement = 0, },
		[4] = { attackSpeedMultiplier = -25, storedUses = 1, cooldown = 8, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Snipe",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				projectile = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1021 },
				{ "active_skill_base_physical_damage_%_to_convert_to_fire", 60 },
			},
			stats = {
				"skill_can_fire_arrows",
				"base_is_projectile",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["RisenArbalestRainOfArrows"] = {
	name = "Rain of Arrows",
	hidden = true,
	description = "Fires a large number of arrows into the air, to land in an area around the target after a short delay. Consumes your Frenzy Charges on use to fire additional arrows.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Area] = true, [SkillType.ProjectileSpeed] = true, [SkillType.Totemable] = true, [SkillType.Trappable] = true, [SkillType.Mineable] = true, [SkillType.Triggerable] = true, [SkillType.Rain] = true, [SkillType.CanRapidFire] = true, [SkillType.Sustained] = true, [SkillType.UsableWhileMoving] = true, [SkillType.SkillConsumesFrenzyChargesOnUse] = true, [SkillType.Bow] = true, [SkillType.GroundTargetedProjectile] = true, },
	weaponTypes = {
		["Bow"] = true,
	},
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { attackSpeedMultiplier = -25, storedUses = 1, cooldown = 8, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Rain of Arrows",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				projectile = true,
			},
			stats = {
				"is_area_damage",
				"base_is_projectile",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SerpentClanCurse"] = {
	name = "Vulnerability",
	hidden = true,
	description = "Curse all targets in an area after a short delay, making Hits against them ignore a portion of their Armour.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Area] = true, [SkillType.Duration] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.Cascadable] = true, [SkillType.AppliesCurse] = true, [SkillType.CanRapidFire] = true, [SkillType.AreaSpell] = true, [SkillType.Physical] = true, [SkillType.UsableWhileMoving] = true, },
	castTime = 1.5,
	qualityStats = {
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 6, },
	},
	statSets = {
		[1] = {
			label = "Vulnerability",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "vulnerability",
			statMap = {
				["physical_damage_taken_+%"] = {
					mod("PhysicalDamageTaken", "INC", nil, 0, 0, { type = "GlobalEffect", effectType = "Curse" }),
				},
				["receive_bleeding_chance_%_when_hit_by_attack"] = {
					mod("SelfBleedChance", "BASE", nil, 0, 0, { type = "GlobalEffect", effectType = "Curse" }),
				},
			},
			baseFlags = {
				area = true,
				duration = true,
				curse = true,
			},
			constantStats = {
				{ "base_skill_effect_duration", 4000 },
				{ "physical_damage_taken_+%", 30 },
				{ "receive_bleeding_chance_%_when_hit_by_attack", 20 },
				{ "hex_remove_at_effect_variance", 600 },
				{ "active_skill_area_of_effect_radius_+%_final", 9 },
			},
			stats = {
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["SerpentClanTailWhip"] = {
	name = "Tail Whip",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Melee] = true, [SkillType.ProjectilesFromUser] = true, },
	castTime = 1.5,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 1.7, storedUses = 1, levelRequirement = 0, cooldown = 8, },
		[2] = { baseMultiplier = 1.7, storedUses = 1, levelRequirement = 0, cooldown = 8, },
	},
	statSets = {
		[1] = {
			label = "Tail Whip",
			baseEffectiveness = 0,
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
			},
			constantStats = {
				{ "voll_slam_damage_+%_final_at_centre", 50 },
				{ "monster_penalty_against_minions_damage_+%_final_vs_player_minions", 100 },
				{ "attack_maximum_action_distance_+", 6 },
			},
			stats = {
				"action_attack_or_cast_time_uses_animation_length",
				"base_skill_can_be_avoided_by_dodge_roll",
				"base_skill_can_be_blocked",
			},
			levels = {
				[1] = { actorLevel = 1, },
				[2] = { actorLevel = 68, },
			},
		},
	}
}
skills["ShellMonsterDeathMortar"] = {
	name = "Death Mortar",
	hidden = true,
	description = "Generic monster mortar skill. Like Monster Projectile but has an impact effect.",
	skillTypes = { [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.Area] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.AreaSpell] = true, },
	castTime = 1.5,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Death Mortar",
			baseEffectiveness = 1.6499999761581,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				area = true,
			},
			constantStats = {
				{ "projectile_spread_radius", 6 },
				{ "mortar_projectile_distance_override", 10 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"is_area_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"maintain_projectile_direction_when_using_contact_position",
				"base_skill_can_be_blocked",
				"base_skill_can_be_avoided_by_dodge_roll",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["ShellMonsterDeathMortarPoison"] = {
	name = "Death Mortar",
	hidden = true,
	description = "Generic monster mortar skill. Like Monster Projectile but has an impact effect.",
	skillTypes = { [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.Area] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.AreaSpell] = true, },
	castTime = 1.5,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Death Mortar",
			baseEffectiveness = 2,
			incrementalEffectiveness = 0.21999999880791,
			damageIncrementalEffectiveness = 0.013000000268221,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				area = true,
			},
			constantStats = {
				{ "projectile_spread_radius", 6 },
				{ "mortar_projectile_distance_override", 10 },
				{ "base_chance_to_poison_on_hit_%", 33 },
			},
			stats = {
				"spell_minimum_base_physical_damage",
				"spell_maximum_base_physical_damage",
				"is_area_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"maintain_projectile_direction_when_using_contact_position",
				"base_skill_can_be_blocked",
				"base_skill_can_be_avoided_by_dodge_roll",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["ShellMonsterFirehose"] = {
	name = "Firehose",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 3,
	qualityStats = {
	},
	levels = {
		[1] = { storedUses = 1, levelRequirement = 0, cooldown = 8, },
	},
	statSets = {
		[1] = {
			label = "Firehose",
			baseEffectiveness = 1.5,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				triggerable = true,
			},
			constantStats = {
				{ "wall_maximum_length", 78 },
				{ "leap_slam_minimum_distance", 20 },
				{ "monster_penalty_against_minions_damage_+%_final_vs_player_minions", -40 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"is_area_damage",
				"cannot_stun",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["ShellMonsterSprayMortar"] = {
	name = "Mortar",
	hidden = true,
	description = "Generic monster mortar skill. Like Monster Projectile but has an impact effect.",
	skillTypes = { [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.Area] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.AreaSpell] = true, },
	castTime = 1.5,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Mortar",
			baseEffectiveness = 1.6499999761581,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				area = true,
			},
			constantStats = {
				{ "projectile_spread_radius", 10 },
				{ "mortar_projectile_distance_override", 30 },
			},
			stats = {
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"is_area_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"maintain_projectile_direction_when_using_contact_position",
				"projectile_uses_contact_direction",
				"base_skill_can_be_blocked",
				"base_skill_can_be_avoided_by_dodge_roll",
				"no_additional_projectiles",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["ShellMonsterSprayMortarPoison"] = {
	name = "Mortar",
	hidden = true,
	description = "Generic monster mortar skill. Like Monster Projectile but has an impact effect.",
	skillTypes = { [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.Area] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.AreaSpell] = true, },
	castTime = 1.5,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Mortar",
			baseEffectiveness = 2,
			incrementalEffectiveness = 0.21999999880791,
			damageIncrementalEffectiveness = 0.013000000268221,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				area = true,
			},
			constantStats = {
				{ "projectile_spread_radius", 10 },
				{ "mortar_projectile_distance_override", 30 },
				{ "base_chance_to_poison_on_hit_%", 33 },
			},
			stats = {
				"spell_minimum_base_physical_damage",
				"spell_maximum_base_physical_damage",
				"is_area_damage",
				"base_is_projectile",
				"projectile_uses_contact_position",
				"use_scaled_contact_offset",
				"maintain_projectile_direction_when_using_contact_position",
				"projectile_uses_contact_direction",
				"base_skill_can_be_blocked",
				"base_skill_can_be_avoided_by_dodge_roll",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["SkelemancerSkelenado"] = {
	name = "Spark",
	hidden = true,
	description = "Launch a spray of sparking Projectiles that travel erratically along the ground until they hit an enemy or expire.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Damage] = true, [SkillType.Duration] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.Lightning] = true, [SkillType.CanRapidFire] = true, [SkillType.Invokable] = true, [SkillType.UsableWhileMoving] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Spark",
			baseEffectiveness = 3,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "spark",
			baseFlags = {
				spell = true,
				projectile = true,
				hit = true,
			},
			constantStats = {
				{ "base_skill_effect_duration", 6000 },
				{ "monster_projectile_variation", 1000 },
				{ "base_projectile_speed_+%", -70 },
				{ "skill_visual_scale_+%", -20 },
				{ "number_of_projectiles_override", 1 },
				{ "spark_min_time_between_target_clearing_ms", 200 },
			},
			stats = {
				"spell_minimum_base_physical_damage",
				"spell_maximum_base_physical_damage",
				"spell_minimum_base_fire_damage",
				"spell_maximum_base_fire_damage",
				"base_is_projectile",
				"projectiles_not_offset",
				"always_pierce",
			},
			levels = {
				[1] = { 0.40000000596046, 0.60000002384186, 0.40000000596046, 0.60000002384186, statInterpolation = { 3, 3, 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["SpookyGhostLightningBounce"] = {
	name = "Basic Spell",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Triggerable] = true, [SkillType.Damage] = true, },
	castTime = 1.5,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 6, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Spell",
			baseEffectiveness = 5,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				triggerable = true,
				spell = true,
				projectile = true,
			},
			constantStats = {
				{ "ballistic_bounce_behavior_variation", 7 },
				{ "number_of_chains", 10 },
				{ "monster_projectile_variation", 1091 },
				{ "shock_chance_+%", 50 },
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
				"base_is_projectile",
				"always_pierce",
				"projectile_uses_contact_position",
				"projectile_uses_contact_direction",
			},
			levels = {
				[1] = { 0.5, 1.5, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["SpookyWraithProjectileExplosionCold"] = {
	name = "Basic Spell",
	hidden = true,
	skillTypes = { [SkillType.Triggerable] = true, [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.Projectile] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 7, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Basic Spell",
			baseEffectiveness = 2.7999999523163,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "geometry_spell",
			baseFlags = {
				triggerable = true,
				spell = true,
				projectile = true,
			},
			constantStats = {
				{ "additional_chance_to_freeze_chilled_enemies_%", 50 },
			},
			stats = {
				"spell_minimum_base_cold_damage",
				"spell_maximum_base_cold_damage",
				"is_area_damage",
				"base_skill_can_be_blocked",
				"base_is_projectile",
				"base_skill_can_be_avoided_by_dodge_roll",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["TBBreachElitePaleLightningBoltSpammableLeft"] = {
	name = "Lightning Bolt",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Triggerable] = true, [SkillType.Attack] = true, [SkillType.Damage] = true, },
	castTime = 1.333,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Lightning Bolt",
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				hit = true,
				triggerable = true,
			},
			constantStats = {
				{ "generic_skill_trigger_skills_with_id", 1 },
			},
			stats = {
				"base_deal_no_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["TBHellscapePaleLightningBoltSpammableLeft"] = {
	name = "Lightning Bolt",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Triggerable] = true, [SkillType.Attack] = true, [SkillType.Damage] = true, },
	castTime = 1.333,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Lightning Bolt",
			baseEffectiveness = 0.75,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				hit = true,
				triggerable = true,
			},
			constantStats = {
				{ "generic_skill_trigger_skills_with_id", 1 },
				{ "shock_art_variation", 10 },
				{ "damage_hit_effect_index", 103 },
				{ "active_skill_cast_speed_+%_final", 15 },
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
			},
			levels = {
				[1] = { 0.5, 1.5, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["TBVaalPyramidBeam"] = {
	name = "Pyramid Beam",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Triggerable] = true, [SkillType.Attack] = true, [SkillType.Damage] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Pyramid Beam",
			baseEffectiveness = 2.25,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				attack = true,
				triggerable = true,
			},
			constantStats = {
				{ "active_skill_shock_chance_+%_final", 15 },
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
				"action_attack_or_cast_time_uses_animation_length",
			},
			levels = {
				[1] = { 0.5, 1.5, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["TCHellscapePaleElite2Charge"] = {
	name = "Charge",
	hidden = true,
	description = "Charges at an enemy, bashing it with the character's shield and striking it. This knocks it back and stuns it. Enemies in the way are pushed to the side. Damage and stun are proportional to distance travelled. Cannot be supported by Multistrike.",
	skillTypes = { [SkillType.Attack] = true, [SkillType.Melee] = true, [SkillType.MeleeSingleTarget] = true, [SkillType.Movement] = true, [SkillType.Travel] = true, },
	weaponTypes = {
		["None"] = true,
		["One Handed Sword"] = true,
		["One Handed Mace"] = true,
		["Flail"] = true,
		["Spear"] = true,
		["One Handed Axe"] = true,
		["Dagger"] = true,
		["Claw"] = true,
	},
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { baseMultiplier = 0.3, storedUses = 1, levelRequirement = 0, cooldown = 8, },
	},
	statSets = {
		[1] = {
			label = "Charge",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				attack = true,
				melee = true,
			},
			stats = {
				"ignores_proximity_shield",
				"is_area_damage",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["UrchinSlingProjectile"] = {
	name = "Sling Rock",
	hidden = true,
	skillTypes = { [SkillType.Attack] = true, [SkillType.RangedAttack] = true, [SkillType.MirageArcherCanUse] = true, [SkillType.Projectile] = true, [SkillType.ProjectilesFromUser] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 1,
	qualityStats = {
	},
	levels = {
		[1] = { attackSpeedMultiplier = -24, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Sling Rock",
			incrementalEffectiveness = 0.054999999701977,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				projectile = true,
				attack = true,
				triggerable = true,
			},
			constantStats = {
				{ "monster_projectile_variation", 1001 },
				{ "spell_maximum_action_distance_+%", -40 },
			},
			stats = {
				"base_is_projectile",
				"projectile_uses_contact_position",
				"action_attack_or_cast_time_uses_animation_length",
				"projectile_ballistic_angle_from_reference_event",
			},
			levels = {
				[1] = { actorLevel = 1, },
			},
		},
	}
}
skills["VaalBloodPriestDetonateDead"] = {
	name = "Detonate Dead",
	hidden = true,
	skillTypes = { [SkillType.Area] = true, [SkillType.Damage] = true, [SkillType.Triggerable] = true, },
	castTime = 1.25,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Detonate Dead",
			baseEffectiveness = 6,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				area = true,
				triggerable = true,
			},
			constantStats = {
				{ "base_skill_effect_duration", 2000 },
				{ "upheaval_number_of_spikes", 4 },
				{ "active_skill_base_physical_damage_%_to_convert_to_fire", 25 },
			},
			stats = {
				"spell_minimum_base_physical_damage",
				"spell_maximum_base_physical_damage",
				"is_area_damage",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["VaalBloodPriestExsanguinate"] = {
	name = "Exsanguinate",
	hidden = true,
	description = "Expel your own blood as Chaining blood tendrils in a cone in front of you. Enemies hit by the tendrils take Physical damage and are inflicted with a Debuff that deals Physical damage over time.",
	skillTypes = { [SkillType.Spell] = true, [SkillType.Damage] = true, [SkillType.Trappable] = true, [SkillType.Totemable] = true, [SkillType.Mineable] = true, [SkillType.Chains] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.Physical] = true, [SkillType.CanRapidFire] = true, [SkillType.DamageOverTime] = true, [SkillType.Duration] = true, [SkillType.UsableWhileMoving] = true, },
	castTime = 2.2,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 5, levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Exsanguinate",
			baseEffectiveness = 2.5,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "exsanguinate",
			baseFlags = {
				spell = true,
				hit = true,
				triggerable = true,
				duration = true,
				chaining = true,
			},
			constantStats = {
				{ "base_skill_effect_duration", 1000 },
				{ "number_of_chains", 1 },
				{ "spell_maximum_action_distance_+%", -40 },
				{ "active_skill_base_radius_+", -8 },
			},
			stats = {
				"spell_minimum_base_physical_damage",
				"spell_maximum_base_physical_damage",
				"base_physical_damage_to_deal_per_minute",
				"blood_tendrils_beam_count",
				"spell_damage_modifiers_apply_to_skill_dot",
			},
			levels = {
				[1] = { 0.80000001192093, 1.2000000476837, 66.666668156783, 2, statInterpolation = { 3, 3, 3, 1, }, actorLevel = 1, },
			},
		},
	}
}
skills["VaalBloodPriestSoulrend"] = {
	name = "Soulrend",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Area] = true, [SkillType.Duration] = true, [SkillType.Projectile] = true, [SkillType.DamageOverTime] = true, [SkillType.Chaos] = true, [SkillType.Multicastable] = true, [SkillType.Triggerable] = true, [SkillType.Trappable] = true, [SkillType.Mineable] = true, [SkillType.Totemable] = true, [SkillType.DegenOnlySpellDamage] = true, [SkillType.AreaSpell] = true, },
	castTime = 3.7,
	qualityStats = {
	},
	levels = {
		[1] = { levelRequirement = 0, },
	},
	statSets = {
		[1] = {
			label = "Soulrend",
			baseEffectiveness = 4,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				area = true,
				duration = true,
				projectile = true,
				triggerable = true,
			},
			constantStats = {
				{ "base_skill_effect_duration", 500 },
				{ "spell_maximum_action_distance_+%", -35 },
			},
			stats = {
				"base_physical_damage_to_deal_per_minute",
				"base_is_projectile",
				"projectile_uses_contact_position",
			},
			levels = {
				[1] = { 50.000001117587, statInterpolation = { 3, }, actorLevel = 1, },
			},
		},
	}
}
skills["VaalHumanoidShockRifle"] = {
	name = "Shock Rifle",
	hidden = true,
	skillTypes = { [SkillType.Spell] = true, [SkillType.Projectile] = true, [SkillType.Damage] = true, },
	castTime = 4,
	qualityStats = {
	},
	levels = {
		[1] = { critChance = 6, storedUses = 1, levelRequirement = 0, cooldown = 9, },
	},
	statSets = {
		[1] = {
			label = "Shock Rifle",
			baseEffectiveness = 5,
			incrementalEffectiveness = 0.10000000149012,
			damageIncrementalEffectiveness = 0.017500000074506,
			statDescriptionScope = "skill_stat_descriptions",
			baseFlags = {
				spell = true,
				projectile = true,
				hit = true,
			},
			constantStats = {
				{ "active_skill_projectile_damage_+%_final", 50 },
				{ "spectral_throw_deceleration_override", 100 },
				{ "spell_maximum_action_distance_+%", -45 },
				{ "base_skill_effect_duration", 200 },
			},
			stats = {
				"spell_minimum_base_lightning_damage",
				"spell_maximum_base_lightning_damage",
				"is_area_damage",
				"base_is_projectile",
			},
			levels = {
				[1] = { 0.5, 1.5, statInterpolation = { 3, 3, }, actorLevel = 1, },
			},
		},
	}
}