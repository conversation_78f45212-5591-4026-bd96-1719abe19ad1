-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				[1]={
					k="per_minute_to_per_second",
					v=1
				},
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Wall deals {0} Fire Damage per second"
			}
		},
		stats={
			[1]="base_fire_damage_to_deal_per_minute"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Lingering Ignite duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Lingering Ignite duration is {0} seconds"
			}
		},
		stats={
			[1]="base_secondary_skill_effect_duration"
		}
	},
	[3]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Wall duration is {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Wall duration is {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[4]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Projectiles which pass through the wall deal {0} to {1} Added Fire Damage"
			}
		},
		stats={
			[1]="flame_wall_minimum_added_fire_damage",
			[2]="flame_wall_maximum_added_fire_damage"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Projectiles which pass through the wall gain extra {0}% damage as Fire"
			}
		},
		stats={
			[1]="flame_wall_projectiles_gain_all_damage_%_as_fire"
		}
	},
	[6]={
		[1]={
			[1]={
				[1]={
					k="per_minute_to_per_second",
					v=1
				},
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Lingering Ignite deals {0} Fire Damage per second"
			}
		},
		stats={
			[1]="secondary_base_fire_damage_to_deal_per_minute"
		}
	},
	[7]={
		[1]={
			[1]={
				[1]={
					k="per_minute_to_per_second",
					v=1
				},
				limit={
					[1]={
						[1]=1,
						[2]="#"
					}
				},
				text="Lingering Ignite damage per second@{0}"
			}
		},
		stats={
			[1]="secondary_intermediary_fire_skill_dot_damage_to_deal_per_minute"
		}
	},
	[8]={
		[1]={
		},
		stats={
			[1]="secondary_skill_effect_duration"
		}
	},
	[9]={
		[1]={
		},
		stats={
			[1]="skill_effect_duration"
		}
	},
	[10]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Ignites as though dealing {0} to {1} Fire damage"
			}
		},
		stats={
			[1]="spell_minimum_base_fire_damage",
			[2]="spell_maximum_base_fire_damage"
		}
	},
	["base_fire_damage_to_deal_per_minute"]=1,
	["base_secondary_skill_effect_duration"]=2,
	["base_skill_effect_duration"]=3,
	["flame_wall_maximum_added_fire_damage"]=4,
	["flame_wall_minimum_added_fire_damage"]=4,
	["flame_wall_projectiles_gain_all_damage_%_as_fire"]=5,
	parent="skill_stat_descriptions",
	["secondary_base_fire_damage_to_deal_per_minute"]=6,
	["secondary_intermediary_fire_skill_dot_damage_to_deal_per_minute"]=7,
	["secondary_skill_effect_duration"]=8,
	["skill_effect_duration"]=9,
	["spell_maximum_base_fire_damage"]=10,
	["spell_minimum_base_fire_damage"]=10
}