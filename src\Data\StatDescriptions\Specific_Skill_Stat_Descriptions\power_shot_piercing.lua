-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		stats={
			[1]="cannot_break_armour"
		}
	},
	[2]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=1,
						[2]=1
					}
				},
				text="Bolts Pierce {0} Target"
			},
			[2]={
				[1]={
					k="canonical_line",
					v=true
				},
				limit={
					[1]={
						[1]=2,
						[2]="#"
					}
				},
				text="Bolts Pierce {0} Targets"
			}
		},
		stats={
			[1]="projectile_base_number_of_targets_to_pierce"
		}
	},
	["cannot_break_armour"]=1,
	parent="skill_stat_descriptions",
	["projectile_base_number_of_targets_to_pierce"]=2
}