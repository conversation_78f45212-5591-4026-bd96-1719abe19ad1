-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games
local itemBases = ...

itemBases["Thawing Charm"] = {
	type = "Charm",
	quality = 20,
	tags = { flask = true, utility_flask = true, default = true, },
	implicit = "Used when you become Frozen",
	implicitModTypes = { {  }, },
	charm = { duration = 3, chargesUsed = 40, chargesMax = 40, buff = { "Immune to Freeze" }, },
	req = { level = 12, },
}
itemBases["Staunching Charm"] = {
	type = "Charm",
	quality = 20,
	tags = { flask = true, utility_flask = true, default = true, },
	implicit = "Used when you start Bleeding",
	implicitModTypes = { {  }, },
	charm = { duration = 3, chargesUsed = 30, chargesMax = 40, buff = { "You are Immune to Bleeding" }, },
	req = { level = 18, },
}
itemBases["Antidote Charm"] = {
	type = "Charm",
	quality = 20,
	tags = { flask = true, utility_flask = true, default = true, },
	implicit = "Used when you become Poisoned",
	implicitModTypes = { {  }, },
	charm = { duration = 3, chargesUsed = 20, chargesMax = 40, buff = { "Immune to Poison" }, },
	req = { level = 24, },
}
itemBases["Dousing Charm"] = {
	type = "Charm",
	quality = 20,
	tags = { flask = true, utility_flask = true, default = true, },
	implicit = "Used when you become Ignited",
	implicitModTypes = { {  }, },
	charm = { duration = 3, chargesUsed = 30, chargesMax = 40, buff = { "Immune to Ignite" }, },
	req = { level = 32, },
}
itemBases["Grounding Charm"] = {
	type = "Charm",
	quality = 20,
	tags = { flask = true, utility_flask = true, default = true, },
	implicit = "Used when you become Shocked",
	implicitModTypes = { {  }, },
	charm = { duration = 3, chargesUsed = 30, chargesMax = 40, buff = { "Immune to Shock" }, },
	req = { level = 32, },
}
itemBases["Stone Charm"] = {
	type = "Charm",
	quality = 20,
	tags = { flask = true, utility_flask = true, default = true, },
	implicit = "Used when you become Stunned",
	implicitModTypes = { {  }, },
	charm = { duration = 3, chargesUsed = 20, chargesMax = 40, buff = { "Cannot be Stunned" }, },
	req = { level = 8, },
}
itemBases["Silver Charm"] = {
	type = "Charm",
	quality = 20,
	tags = { flask = true, utility_flask = true, default = true, },
	implicit = "Used when you are affected by a Slow",
	implicitModTypes = { {  }, },
	charm = { duration = 3, chargesUsed = 20, chargesMax = 40, buff = { "Your speed is unaffected by Slows" }, },
	req = { level = 10, },
}
itemBases["Ruby Charm"] = {
	type = "Charm",
	quality = 20,
	tags = { flask = true, utility_flask = true, default = true, },
	implicit = "Used when you take Fire damage from a Hit",
	implicitModTypes = { {  }, },
	charm = { duration = 4, chargesUsed = 20, chargesMax = 40, buff = { "+25% to Fire Resistance" }, },
	req = { level = 5, },
}
itemBases["Sapphire Charm"] = {
	type = "Charm",
	quality = 20,
	tags = { flask = true, utility_flask = true, default = true, },
	implicit = "Used when you take Cold damage from a Hit",
	implicitModTypes = { {  }, },
	charm = { duration = 4, chargesUsed = 20, chargesMax = 40, buff = { "+25% to Cold Resistance" }, },
	req = { level = 5, },
}
itemBases["Topaz Charm"] = {
	type = "Charm",
	quality = 20,
	tags = { flask = true, utility_flask = true, default = true, },
	implicit = "Used when you take Lightning damage from a Hit",
	implicitModTypes = { {  }, },
	charm = { duration = 4, chargesUsed = 20, chargesMax = 40, buff = { "+25% to Lightning Resistance" }, },
	req = { level = 5, },
}
itemBases["Amethyst Charm"] = {
	type = "Charm",
	quality = 20,
	tags = { flask = true, utility_flask = true, default = true, },
	implicit = "Used when you take Chaos damage from a Hit",
	implicitModTypes = { {  }, },
	charm = { duration = 4, chargesUsed = 30, chargesMax = 40, buff = { "+18% to Chaos Resistance" }, },
	req = { level = 40, },
}
itemBases["Golden Charm"] = {
	type = "Charm",
	quality = 20,
	tags = { flask = true, utility_flask = true, default = true, },
	implicit = "Used when you Kill a Rare or Unique Enemy",
	implicitModTypes = { {  }, },
	charm = { duration = 1, chargesUsed = 80, chargesMax = 80, buff = { "15% increased Rarity of Items found" }, },
	req = { level = 50, },
}

itemBases["Lesser Life Flask"] = {
	type = "Flask",
	subType = "Life",
	quality = 20,
	tags = { flask = true, life_flask = true, default = true, },
	implicitModTypes = { },
	flask = { life = 50, duration = 3, chargesUsed = 10, chargesMax = 60, },
	req = { },
}
itemBases["Medium Life Flask"] = {
	type = "Flask",
	subType = "Life",
	quality = 20,
	tags = { flask = true, life_flask = true, default = true, },
	implicitModTypes = { },
	flask = { life = 90, duration = 5, chargesUsed = 10, chargesMax = 65, },
	req = { level = 4, },
}
itemBases["Greater Life Flask"] = {
	type = "Flask",
	subType = "Life",
	quality = 20,
	tags = { flask = true, life_flask = true, default = true, },
	implicitModTypes = { },
	flask = { life = 150, duration = 4, chargesUsed = 10, chargesMax = 70, },
	req = { level = 10, },
}
itemBases["Grand Life Flask"] = {
	type = "Flask",
	subType = "Life",
	quality = 20,
	tags = { flask = true, life_flask = true, default = true, },
	implicitModTypes = { },
	flask = { life = 260, duration = 5, chargesUsed = 10, chargesMax = 75, },
	req = { level = 16, },
}
itemBases["Giant Life Flask"] = {
	type = "Flask",
	subType = "Life",
	quality = 20,
	tags = { flask = true, life_flask = true, default = true, },
	implicitModTypes = { },
	flask = { life = 340, duration = 4, chargesUsed = 10, chargesMax = 75, },
	req = { level = 23, },
}
itemBases["Colossal Life Flask"] = {
	type = "Flask",
	subType = "Life",
	quality = 20,
	tags = { flask = true, life_flask = true, default = true, },
	implicitModTypes = { },
	flask = { life = 450, duration = 4, chargesUsed = 10, chargesMax = 75, },
	req = { level = 30, },
}
itemBases["Gargantuan Life Flask"] = {
	type = "Flask",
	subType = "Life",
	quality = 20,
	tags = { flask = true, life_flask = true, default = true, },
	implicitModTypes = { },
	flask = { life = 710, duration = 5, chargesUsed = 10, chargesMax = 75, },
	req = { level = 40, },
}
itemBases["Transcendent Life Flask"] = {
	type = "Flask",
	subType = "Life",
	quality = 20,
	tags = { flask = true, life_flask = true, default = true, },
	implicitModTypes = { },
	flask = { life = 840, duration = 4, chargesUsed = 10, chargesMax = 75, },
	req = { level = 50, },
}
itemBases["Ultimate Life Flask"] = {
	type = "Flask",
	subType = "Life",
	quality = 20,
	tags = { flask = true, life_flask = true, default = true, },
	implicitModTypes = { },
	flask = { life = 920, duration = 3, chargesUsed = 10, chargesMax = 75, },
	req = { level = 60, },
}

itemBases["Lesser Mana Flask"] = {
	type = "Flask",
	subType = "Mana",
	quality = 20,
	tags = { flask = true, mana_flask = true, default = true, },
	implicitModTypes = { },
	flask = { mana = 50, duration = 2, chargesUsed = 10, chargesMax = 60, },
	req = { },
}
itemBases["Medium Mana Flask"] = {
	type = "Flask",
	subType = "Mana",
	quality = 20,
	tags = { flask = true, mana_flask = true, default = true, },
	implicitModTypes = { },
	flask = { mana = 70, duration = 3, chargesUsed = 10, chargesMax = 65, },
	req = { level = 4, },
}
itemBases["Greater Mana Flask"] = {
	type = "Flask",
	subType = "Mana",
	quality = 20,
	tags = { flask = true, mana_flask = true, default = true, },
	implicitModTypes = { },
	flask = { mana = 90, duration = 2.5, chargesUsed = 10, chargesMax = 70, },
	req = { level = 10, },
}
itemBases["Grand Mana Flask"] = {
	type = "Flask",
	subType = "Mana",
	quality = 20,
	tags = { flask = true, mana_flask = true, default = true, },
	implicitModTypes = { },
	flask = { mana = 110, duration = 2.5, chargesUsed = 10, chargesMax = 75, },
	req = { level = 16, },
}
itemBases["Giant Mana Flask"] = {
	type = "Flask",
	subType = "Mana",
	quality = 20,
	tags = { flask = true, mana_flask = true, default = true, },
	implicitModTypes = { },
	flask = { mana = 165, duration = 3.5, chargesUsed = 10, chargesMax = 75, },
	req = { level = 23, },
}
itemBases["Colossal Mana Flask"] = {
	type = "Flask",
	subType = "Mana",
	quality = 20,
	tags = { flask = true, mana_flask = true, default = true, },
	implicitModTypes = { },
	flask = { mana = 165, duration = 2.5, chargesUsed = 10, chargesMax = 75, },
	req = { level = 30, },
}
itemBases["Gargantuan Mana Flask"] = {
	type = "Flask",
	subType = "Mana",
	quality = 20,
	tags = { flask = true, mana_flask = true, default = true, },
	implicitModTypes = { },
	flask = { mana = 185, duration = 2, chargesUsed = 10, chargesMax = 75, },
	req = { level = 40, },
}
itemBases["Transcendent Mana Flask"] = {
	type = "Flask",
	subType = "Mana",
	quality = 20,
	tags = { flask = true, mana_flask = true, default = true, },
	implicitModTypes = { },
	flask = { mana = 285, duration = 3.5, chargesUsed = 10, chargesMax = 75, },
	req = { level = 50, },
}
itemBases["Ultimate Mana Flask"] = {
	type = "Flask",
	subType = "Mana",
	quality = 20,
	tags = { flask = true, mana_flask = true, default = true, },
	implicitModTypes = { },
	flask = { mana = 310, duration = 3, chargesUsed = 10, chargesMax = 75, },
	req = { level = 60, },
}
