-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games
local itemBases = ...


itemBases["Rough Greaves"] = {
	type = "Boots",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, str_armour = true, armour = true, ezomyte_basetype = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 22, },
	req = { },
}
itemBases["Iron Greaves"] = {
	type = "Boots",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, str_armour = true, armour = true, ezomyte_basetype = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 47, },
	req = { level = 11, str = 20, },
}
itemBases["Bronze Greaves"] = {
	type = "Boots",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, boots = true, str_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 60, },
	req = { level = 16, str = 29, },
}
itemBases["Trimmed Greaves"] = {
	type = "Boots",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, boots = true, str_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 87, },
	req = { level = 27, str = 47, },
}
itemBases["Stone Greaves"] = {
	type = "Boots",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, str_armour = true, armour = true, vaal_basetype = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 102, },
	req = { level = 33, str = 57, },
}
itemBases["Reefsteel Greaves"] = {
	type = "Boots",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 132, },
	req = { level = 45, str = 78, },
}
itemBases["Monument Greaves"] = {
	type = "Boots",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 150, },
	req = { level = 52, str = 89, },
}
itemBases["Totemic Greaves"] = {
	type = "Boots",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 182, },
	req = { level = 65, str = 111, },
}
itemBases["Plated Greaves"] = {
	type = "Boots",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 132, },
	req = { level = 45, str = 78, },
}
itemBases["Lionheart Greaves"] = {
	type = "Boots",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 147, },
	req = { level = 51, str = 88, },
}
itemBases["Elegant Greaves"] = {
	type = "Boots",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 157, },
	req = { level = 55, str = 95, },
}
itemBases["Carved Greaves"] = {
	type = "Boots",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 167, },
	req = { level = 59, str = 101, },
}
itemBases["Bulwark Greaves"] = {
	type = "Boots",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 182, },
	req = { level = 65, str = 111, },
}
itemBases["Ornate Greaves"] = {
	type = "Boots",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 200, },
	req = { level = 70, str = 121, },
}
itemBases["Vaal Greaves"] = {
	type = "Boots",
	subType = "Armour",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, armour = true, str_armour = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 219, },
	req = { level = 75, str = 131, },
}

itemBases["Rawhide Boots"] = {
	type = "Boots",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, ezomyte_basetype = true, dex_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 15, },
	req = { },
}
itemBases["Laced Boots"] = {
	type = "Boots",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, ezomyte_basetype = true, dex_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 38, },
	req = { level = 11, dex = 20, },
}
itemBases["Embossed Boots"] = {
	type = "Boots",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, boots = true, dex_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 50, },
	req = { level = 16, dex = 29, },
}
itemBases["Steeltoe Boots"] = {
	type = "Boots",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, boots = true, dex_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 79, },
	req = { level = 28, dex = 49, },
}
itemBases["Lizardscale Boots"] = {
	type = "Boots",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, dex_armour = true, armour = true, vaal_basetype = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 90, },
	req = { level = 33, dex = 57, },
}
itemBases["Flared Boots"] = {
	type = "Boots",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 119, },
	req = { level = 45, dex = 78, },
}
itemBases["Leatherplate Boots"] = {
	type = "Boots",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 135, },
	req = { level = 52, dex = 89, },
}
itemBases["Embroidered Boots"] = {
	type = "Boots",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 166, },
	req = { level = 65, dex = 111, },
}
itemBases["Bound Boots"] = {
	type = "Boots",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 119, },
	req = { level = 45, dex = 78, },
}
itemBases["Sleek Boots"] = {
	type = "Boots",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 133, },
	req = { level = 51, dex = 88, },
}
itemBases["Studded Boots"] = {
	type = "Boots",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 143, },
	req = { level = 55, dex = 95, },
}
itemBases["Serpentscale Boots"] = {
	type = "Boots",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 152, },
	req = { level = 59, dex = 101, },
}
itemBases["Cinched Boots"] = {
	type = "Boots",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 166, },
	req = { level = 65, dex = 111, },
}
itemBases["Cavalry Boots"] = {
	type = "Boots",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 183, },
	req = { level = 70, dex = 121, },
}
itemBases["Dragonscale Boots"] = {
	type = "Boots",
	subType = "Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 200, },
	req = { level = 75, dex = 131, },
}

itemBases["Straw Sandals"] = {
	type = "Boots",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, ezomyte_basetype = true, int_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 14, },
	req = { },
}
itemBases["Wrapped Sandals"] = {
	type = "Boots",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, ezomyte_basetype = true, int_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 22, },
	req = { level = 11, int = 20, },
}
itemBases["Lattice Sandals"] = {
	type = "Boots",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, boots = true, int_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 25, },
	req = { level = 16, int = 29, },
}
itemBases["Silk Slippers"] = {
	type = "Boots",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, boots = true, int_armour = true, armour = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 34, },
	req = { level = 27, int = 47, },
}
itemBases["Feathered Sandals"] = {
	type = "Boots",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, int_armour = true, armour = true, vaal_basetype = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 38, },
	req = { level = 33, int = 57, },
}
itemBases["Flax Sandals"] = {
	type = "Boots",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 48, },
	req = { level = 45, int = 78, },
}
itemBases["Studded Sandals"] = {
	type = "Boots",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 53, },
	req = { level = 52, int = 89, },
}
itemBases["Elaborate Sandals"] = {
	type = "Boots",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 63, },
	req = { level = 65, int = 111, },
}
itemBases["Laced Sandals"] = {
	type = "Boots",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 48, },
	req = { level = 45, int = 78, },
}
itemBases["Bangled Sandals"] = {
	type = "Boots",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 52, },
	req = { level = 51, int = 88, },
}
itemBases["Elegant Slippers"] = {
	type = "Boots",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 55, },
	req = { level = 55, int = 95, },
}
itemBases["Dunerunner Sandals"] = {
	type = "Boots",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 58, },
	req = { level = 59, int = 101, },
}
itemBases["Bound Sandals"] = {
	type = "Boots",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 63, },
	req = { level = 65, int = 111, },
}
itemBases["Luxurious Slippers"] = {
	type = "Boots",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 69, },
	req = { level = 70, int = 121, },
}
itemBases["Sandsworn Sandals"] = {
	type = "Boots",
	subType = "Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { EnergyShield = 75, },
	req = { level = 75, int = 131, },
}

itemBases["Mail Sabatons"] = {
	type = "Boots",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, ezomyte_basetype = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 19, Evasion = 15, },
	req = { level = 6, str = 8, dex = 8, },
}
itemBases["Braced Sabatons"] = {
	type = "Boots",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, str_dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 33, Evasion = 28, },
	req = { level = 16, str = 17, dex = 17, },
}
itemBases["Stacked Sabatons"] = {
	type = "Boots",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, vaal_basetype = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 56, Evasion = 50, },
	req = { level = 33, str = 32, dex = 32, },
}
itemBases["Covered Sabatons"] = {
	type = "Boots",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 73, Evasion = 65, },
	req = { level = 45, str = 43, dex = 43, },
}
itemBases["Flexile Sabatons"] = {
	type = "Boots",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 82, Evasion = 74, },
	req = { level = 52, str = 50, dex = 50, },
}
itemBases["Bold Sabatons"] = {
	type = "Boots",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 100, Evasion = 91, },
	req = { level = 65, str = 61, dex = 61, },
}
itemBases["Soldiering Sabatons"] = {
	type = "Boots",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 73, Evasion = 65, },
	req = { level = 45, str = 43, dex = 43, },
}
itemBases["Goldwork Sabatons"] = {
	type = "Boots",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 81, Evasion = 73, },
	req = { level = 51, str = 49, dex = 49, },
}
itemBases["Bastion Sabatons"] = {
	type = "Boots",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 92, Evasion = 84, },
	req = { level = 59, str = 56, dex = 56, },
}
itemBases["Veteran Sabatons"] = {
	type = "Boots",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 100, Evasion = 91, },
	req = { level = 65, str = 61, dex = 61, },
}
itemBases["Noble Sabatons"] = {
	type = "Boots",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 110, Evasion = 101, },
	req = { level = 70, str = 66, dex = 66, },
}
itemBases["Fortress Sabatons"] = {
	type = "Boots",
	subType = "Armour/Evasion",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 120, Evasion = 110, },
	req = { level = 75, str = 71, dex = 71, },
}

itemBases["Padded Leggings"] = {
	type = "Boots",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, ezomyte_basetype = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 18, EnergyShield = 9, },
	req = { level = 5, str = 7, int = 7, },
}
itemBases["Secured Leggings"] = {
	type = "Boots",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, str_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 33, EnergyShield = 14, },
	req = { level = 16, str = 17, int = 17, },
}
itemBases["Pelt Leggings"] = {
	type = "Boots",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, vaal_basetype = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 56, EnergyShield = 21, },
	req = { level = 33, str = 32, int = 32, },
}
itemBases["Weaver Leggings"] = {
	type = "Boots",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 73, EnergyShield = 26, },
	req = { level = 45, str = 43, int = 43, },
}
itemBases["Gilt Leggings"] = {
	type = "Boots",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 82, EnergyShield = 29, },
	req = { level = 52, str = 50, int = 50, },
}
itemBases["Pious Leggings"] = {
	type = "Boots",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 100, EnergyShield = 35, },
	req = { level = 65, str = 61, int = 61, },
}
itemBases["Adherent Leggings"] = {
	type = "Boots",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 73, EnergyShield = 26, },
	req = { level = 45, str = 43, int = 43, },
}
itemBases["Bound Leggings"] = {
	type = "Boots",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 81, EnergyShield = 29, },
	req = { level = 51, str = 49, int = 49, },
}
itemBases["Shamanistic Leggings"] = {
	type = "Boots",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 92, EnergyShield = 32, },
	req = { level = 59, str = 56, int = 56, },
}
itemBases["Faithful Leggings"] = {
	type = "Boots",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 100, EnergyShield = 35, },
	req = { level = 65, str = 61, int = 61, },
}
itemBases["Apostle Leggings"] = {
	type = "Boots",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 110, EnergyShield = 38, },
	req = { level = 70, str = 66, int = 66, },
}
itemBases["Warlock Leggings"] = {
	type = "Boots",
	subType = "Armour/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 120, EnergyShield = 42, },
	req = { level = 75, str = 71, int = 71, },
}

itemBases["Frayed Shoes"] = {
	type = "Boots",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, ezomyte_basetype = true, armour = true, default = true, dex_int_armour = true, },
	implicitModTypes = { },
	armour = { Evasion = 13, EnergyShield = 9, },
	req = { level = 5, dex = 7, int = 7, },
}
itemBases["Threaded Shoes"] = {
	type = "Boots",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, boots = true, armour = true, default = true, dex_int_armour = true, },
	implicitModTypes = { },
	armour = { Evasion = 28, EnergyShield = 14, },
	req = { level = 16, dex = 17, int = 17, },
}
itemBases["Hunting Shoes"] = {
	type = "Boots",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, vaal_basetype = true, armour = true, default = true, dex_int_armour = true, },
	implicitModTypes = { },
	armour = { Evasion = 50, EnergyShield = 21, },
	req = { level = 33, dex = 32, int = 32, },
}
itemBases["Steelpoint Shoes"] = {
	type = "Boots",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { dex_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 65, EnergyShield = 26, },
	req = { level = 45, dex = 43, int = 43, },
}
itemBases["Velour Shoes"] = {
	type = "Boots",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { dex_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 74, EnergyShield = 29, },
	req = { level = 52, dex = 50, int = 50, },
}
itemBases["Bladed Shoes"] = {
	type = "Boots",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { dex_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 91, EnergyShield = 35, },
	req = { level = 65, dex = 61, int = 61, },
}
itemBases["Wayfarer Shoes"] = {
	type = "Boots",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { dex_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 65, EnergyShield = 26, },
	req = { level = 45, dex = 43, int = 43, },
}
itemBases["Silverbuckled Shoes"] = {
	type = "Boots",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { dex_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 73, EnergyShield = 29, },
	req = { level = 51, dex = 49, int = 49, },
}
itemBases["Treerunner Shoes"] = {
	type = "Boots",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { dex_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 84, EnergyShield = 32, },
	req = { level = 59, dex = 56, int = 56, },
}
itemBases["Wanderer Shoes"] = {
	type = "Boots",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { dex_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 91, EnergyShield = 35, },
	req = { level = 65, dex = 61, int = 61, },
}
itemBases["Charmed Shoes"] = {
	type = "Boots",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { dex_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 101, EnergyShield = 38, },
	req = { level = 70, dex = 66, int = 66, },
}
itemBases["Quickslip Shoes"] = {
	type = "Boots",
	subType = "Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { dex_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Evasion = 110, EnergyShield = 42, },
	req = { level = 75, dex = 71, int = 71, },
}

itemBases["Grand Cuisses"] = {
	type = "Boots",
	subType = "Armour/Evasion/Energy Shield",
	quality = 20,
	socketLimit = 2,
	tags = { str_dex_int_armour = true, armour = true, boots = true, default = true, },
	implicitModTypes = { },
	armour = { Armour = 66, Evasion = 60, EnergyShield = 23, },
	req = { level = 65, str = 43, dex = 43, int = 43, },
}

itemBases["Golden Caligae"] = {
	type = "Boots",
	quality = 20,
	socketLimit = 2,
	tags = { boots = true, not_for_sale = true, armour = true, demigods = true, default = true, },
	implicit = "+(8-16)% to all Elemental Resistances",
	implicitModTypes = { { "elemental", "fire", "cold", "lightning", "resistance" }, },
	armour = { },
	req = { level = 12, },
}
