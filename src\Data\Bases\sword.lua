-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games
local itemBases = ...

itemBases["Golden Blade"] = {
	type = "One Handed Sword",
	quality = 20,
	socketLimit = 2,
	tags = { demigods = true, onehand = true, not_for_sale = true, weapon = true, sword = true, one_hand_weapon = true, default = true, },
	implicit = "+(16-24) to all Attributes",
	implicitModTypes = { { "attribute" }, },
	weapon = { PhysicalMin = 3, PhysicalMax = 28, CritChanceBase = 5, AttackRateBase = 1.1, Range = 11, },
	req = { },
}
itemBases["Energy Blade One Handed"] = {
	type = "One Handed Sword",
	quality = 20,
	hidden = true,
	socketLimit = 2,
	tags = { onehand = true, not_for_sale = true, weapon = true, sword = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 0, PhysicalMax = 0, CritChanceBase = 7, AttackRateBase = 1.7, Range = 11, },
	req = { },
}
itemBases["Shortsword"] = {
	type = "One Handed Sword",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, ezomyte_basetype = true, weapon = true, sword = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 6, PhysicalMax = 9, CritChanceBase = 5, AttackRateBase = 1.55, Range = 11, },
	req = { },
}
itemBases["Broadsword"] = {
	type = "One Handed Sword",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, ezomyte_basetype = true, weapon = true, sword = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 8, PhysicalMax = 13, CritChanceBase = 5, AttackRateBase = 1.6, Range = 11, },
	req = { level = 6, str = 10, dex = 10, },
}
itemBases["Vampiric Blade"] = {
	type = "One Handed Sword",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, ezomyte_basetype = true, weapon = true, sword = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 11, PhysicalMax = 20, CritChanceBase = 5, AttackRateBase = 1.5, Range = 11, },
	req = { level = 11, str = 16, dex = 16, },
}
itemBases["Scimitar"] = {
	type = "One Handed Sword",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, onehand = true, weapon = true, sword = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 14, PhysicalMax = 23, CritChanceBase = 5, AttackRateBase = 1.55, Range = 11, },
	req = { level = 16, str = 22, dex = 22, },
}
itemBases["Charred Shortsword"] = {
	type = "One Handed Sword",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, onehand = true, weapon = true, sword = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 19, PhysicalMax = 34, CritChanceBase = 5, AttackRateBase = 1.6, Range = 11, },
	req = { level = 21, str = 28, dex = 28, },
}
itemBases["Sickle Sword"] = {
	type = "One Handed Sword",
	quality = 20,
	socketLimit = 2,
	tags = { maraketh_basetype = true, onehand = true, weapon = true, sword = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 18, PhysicalMax = 38, CritChanceBase = 5, AttackRateBase = 1.5, Range = 11, },
	req = { level = 28, str = 37, dex = 37, },
}
itemBases["Falchion"] = {
	type = "One Handed Sword",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, vaal_basetype = true, weapon = true, sword = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 27, PhysicalMax = 37, CritChanceBase = 5, AttackRateBase = 1.5, Range = 11, },
	req = { level = 33, str = 43, dex = 43, },
}
itemBases["Treasured Blade"] = {
	type = "One Handed Sword",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, vaal_basetype = true, weapon = true, sword = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 27, PhysicalMax = 45, CritChanceBase = 5, AttackRateBase = 1.55, Range = 11, },
	req = { level = 40, str = 51, dex = 51, },
}
itemBases["Cutlass"] = {
	type = "One Handed Sword",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, weapon = true, sword = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 33, PhysicalMax = 49, CritChanceBase = 5, AttackRateBase = 1.5, Range = 11, },
	req = { level = 45, str = 57, dex = 57, },
}
itemBases["Runic Shortsword"] = {
	type = "One Handed Sword",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, weapon = true, sword = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 26, PhysicalMax = 55, CritChanceBase = 5, AttackRateBase = 1.65, Range = 11, },
	req = { level = 50, str = 63, dex = 63, },
}
itemBases["Messer"] = {
	type = "One Handed Sword",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, weapon = true, sword = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 30, PhysicalMax = 56, CritChanceBase = 5, AttackRateBase = 1.6, Range = 11, },
	req = { level = 52, str = 66, dex = 66, },
}
itemBases["Commander Sword"] = {
	type = "One Handed Sword",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, weapon = true, sword = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 36, PhysicalMax = 60, CritChanceBase = 5, AttackRateBase = 1.5, Range = 11, },
	req = { level = 54, str = 68, dex = 68, },
}
itemBases["Dark Blade"] = {
	type = "One Handed Sword",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, weapon = true, sword = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 43, PhysicalMax = 65, CritChanceBase = 5, AttackRateBase = 1.55, Range = 11, },
	req = { level = 65, str = 81, dex = 81, },
}
itemBases["Golden Blade"] = {
	type = "One Handed Sword",
	quality = 20,
	socketLimit = 2,
	tags = { onehand = true, ezomyte_basetype = true, weapon = true, sword = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 6, PhysicalMax = 9, CritChanceBase = 5, AttackRateBase = 1.55, Range = 11, },
	req = { },
}

itemBases["Energy Blade One Handed"] = {
	type = "One Handed Sword",
	quality = 20,
	hidden = true,
	socketLimit = 2,
	tags = { onehand = true, not_for_sale = true, weapon = true, sword = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 0, PhysicalMax = 0, CritChanceBase = 7, AttackRateBase = 1.7, Range = 11, },
	req = { },
}

itemBases["Keyblade"] = {
	type = "Two Handed Sword",
	quality = 20,
	hidden = true,
	socketLimit = 3,
	tags = { two_hand_weapon = true, not_for_sale = true, weapon = true, sword = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 1, PhysicalMax = 1, CritChanceBase = 5, AttackRateBase = 1.2, Range = 13, },
	req = { },
}
itemBases["Energy Blade Two Handed"] = {
	type = "Two Handed Sword",
	quality = 20,
	hidden = true,
	socketLimit = 3,
	tags = { two_hand_weapon = true, not_for_sale = true, weapon = true, sword = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 0, PhysicalMax = 0, CritChanceBase = 7, AttackRateBase = 1.6, Range = 13, },
	req = { },
}
itemBases["Corroded Longsword"] = {
	type = "Two Handed Sword",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ezomyte_basetype = true, weapon = true, sword = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 9, PhysicalMax = 16, CritChanceBase = 5, AttackRateBase = 1.3, Range = 13, },
	req = { },
}
itemBases["Iron Greatsword"] = {
	type = "Two Handed Sword",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ezomyte_basetype = true, weapon = true, sword = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 13, PhysicalMax = 23, CritChanceBase = 5, AttackRateBase = 1.35, Range = 13, },
	req = { level = 6, str = 10, dex = 10, },
}
itemBases["Blessed Claymore"] = {
	type = "Two Handed Sword",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, ezomyte_basetype = true, weapon = true, sword = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 19, PhysicalMax = 32, CritChanceBase = 5, AttackRateBase = 1.3, Range = 13, },
	req = { level = 11, str = 16, dex = 16, },
}
itemBases["Broad Greatsword"] = {
	type = "Two Handed Sword",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, maraketh_basetype = true, weapon = true, sword = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 25, PhysicalMax = 42, CritChanceBase = 5, AttackRateBase = 1.25, Range = 13, },
	req = { level = 16, str = 22, dex = 22, },
}
itemBases["Rippled Greatsword"] = {
	type = "Two Handed Sword",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, maraketh_basetype = true, weapon = true, sword = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 28, PhysicalMax = 52, CritChanceBase = 5, AttackRateBase = 1.3, Range = 13, },
	req = { level = 22, str = 30, dex = 30, },
}
itemBases["Arced Longsword"] = {
	type = "Two Handed Sword",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, maraketh_basetype = true, weapon = true, sword = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 31, PhysicalMax = 58, CritChanceBase = 5, AttackRateBase = 1.35, Range = 13, },
	req = { level = 28, str = 37, dex = 37, },
}
itemBases["Stone Greatsword"] = {
	type = "Two Handed Sword",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, vaal_basetype = true, weapon = true, sword = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 40, PhysicalMax = 67, CritChanceBase = 5, AttackRateBase = 1.3, Range = 13, },
	req = { level = 33, str = 43, dex = 43, },
}
itemBases["Obsidian Greatsword"] = {
	type = "Two Handed Sword",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, vaal_basetype = true, weapon = true, sword = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 49, PhysicalMax = 73, CritChanceBase = 5, AttackRateBase = 1.25, Range = 13, },
	req = { level = 36, str = 46, dex = 46, },
}
itemBases["Keen Greatsword"] = {
	type = "Two Handed Sword",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, weapon = true, sword = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 45, PhysicalMax = 94, CritChanceBase = 5, AttackRateBase = 1.3, Range = 13, },
	req = { level = 45, str = 57, dex = 57, },
}
itemBases["Ancient Greatblade"] = {
	type = "Two Handed Sword",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, weapon = true, sword = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 55, PhysicalMax = 103, CritChanceBase = 5, AttackRateBase = 1.25, Range = 13, },
	req = { level = 49, str = 62, dex = 62, },
}
itemBases["Flanged Greatblade"] = {
	type = "Two Handed Sword",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, weapon = true, sword = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 51, PhysicalMax = 106, CritChanceBase = 5, AttackRateBase = 1.3, Range = 13, },
	req = { level = 52, str = 66, dex = 66, },
}
itemBases["Regalia Longsword"] = {
	type = "Two Handed Sword",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, weapon = true, sword = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 61, PhysicalMax = 92, CritChanceBase = 5, AttackRateBase = 1.35, Range = 13, },
	req = { level = 54, str = 68, dex = 68, },
}
itemBases["Ultra Greatsword"] = {
	type = "Two Handed Sword",
	quality = 20,
	socketLimit = 3,
	tags = { two_hand_weapon = true, weapon = true, sword = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 72, PhysicalMax = 119, CritChanceBase = 5, AttackRateBase = 1.3, Range = 13, },
	req = { level = 65, str = 81, dex = 81, },
}

itemBases["Energy Blade Two Handed"] = {
	type = "Two Handed Sword",
	quality = 20,
	hidden = true,
	socketLimit = 3,
	tags = { two_hand_weapon = true, not_for_sale = true, weapon = true, sword = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 0, PhysicalMax = 0, CritChanceBase = 7, AttackRateBase = 1.6, Range = 13, },
	req = { },
}
