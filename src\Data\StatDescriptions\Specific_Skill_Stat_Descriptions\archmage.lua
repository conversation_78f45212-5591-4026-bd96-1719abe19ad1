-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Non-Channelling Spells Gain {0}% of Damage as extra Lightning damage for each 100 maximum Mana you have"
			}
		},
		stats={
			[1]="archmage_all_damage_%_to_gain_as_lightning_to_grant_to_non_channelling_spells_per_100_max_mana"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="divide_by_one_hundred_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Non-Channelling Spells cost an additional {0}% of your maximum Mana"
			}
		},
		stats={
			[1]="archmage_max_mana_permyriad_to_add_to_non_channelled_spell_mana_cost"
		}
	},
	[3]={
		[1]={
		},
		stats={
			[1]="archmage_non_channelled_spell_mana_cost_+"
		}
	},
	[4]={
		[1]={
		},
		stats={
			[1]="archmage_all_damage_%_to_gain_as_lightning_to_grant_to_non_channelling_spells"
		}
	},
	["archmage_all_damage_%_to_gain_as_lightning_to_grant_to_non_channelling_spells"]=4,
	["archmage_all_damage_%_to_gain_as_lightning_to_grant_to_non_channelling_spells_per_100_max_mana"]=1,
	["archmage_max_mana_permyriad_to_add_to_non_channelled_spell_mana_cost"]=2,
	["archmage_non_channelled_spell_mana_cost_+"]=3,
	parent="skill_stat_descriptions"
}