name: Update SimpleGraphic DLLs
on:
    repository_dispatch:
        types: [update-simple-graphic]
jobs:
    update:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout
              uses: actions/checkout@v3
              with:
                ref: 'dev'
            - name: Download DLLs
              uses: robinraju/release-downloader@v1
              with:
                repository: ${{ github.repository_owner }}/PathOfBuilding-SimpleGraphic
                tag: ${{ github.event.client_payload.tag }}
                fileName: SimpleGraphicDLLs-x64-windows.tar
                extract: true
                out-file-path: runtime
            - name: Create Pull Request
              uses: peter-evans/create-pull-request@v5
              with:
                title: Update to SimpleGraphic ${{ github.event.client_payload.tag }}
                branch: simple-graphic-${{ github.event.client_payload.tag }}
                body: |
                    Update DLLs to SimpleGraphic-${{ github.event.client_payload.tag }} from ${{ github.event.client_payload.release_link }}
                commit-message: Update DLLs to SimpleGraphic-${{ github.event.client_payload.tag }}
              