-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games
local itemBases = ...

itemBases["Dull Hatchet"] = {
	type = "One Handed Axe",
	quality = 20,
	socketLimit = 2,
	tags = { axe = true, onehand = true, ezomyte_basetype = true, weapon = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 4, PhysicalMax = 10, CritChanceBase = 5, AttackRateBase = 1.5, Range = 11, },
	req = { },
}
itemBases["Hook Axe"] = {
	type = "One Handed Axe",
	quality = 20,
	socketLimit = 2,
	tags = { axe = true, onehand = true, ezomyte_basetype = true, weapon = true, one_hand_weapon = true, default = true, },
	implicit = "Gain 1 Rage on Hit",
	implicitModTypes = { {  }, },
	weapon = { PhysicalMin = 5, PhysicalMax = 14, CritChanceBase = 5, AttackRateBase = 1.45, Range = 11, },
	req = { str = 10, },
}
itemBases["Bearded Axe"] = {
	type = "One Handed Axe",
	quality = 20,
	socketLimit = 2,
	tags = { axe = true, onehand = true, ezomyte_basetype = true, weapon = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 8, PhysicalMax = 21, CritChanceBase = 5, AttackRateBase = 1.45, Range = 11, },
	req = { level = 10, str = 20, dex = 10, },
}
itemBases["Extended Cleaver"] = {
	type = "One Handed Axe",
	quality = 20,
	socketLimit = 2,
	tags = { axe = true, onehand = true, maraketh_basetype = true, weapon = true, one_hand_weapon = true, default = true, },
	implicit = "Has no Accuracy Penalty from Range",
	implicitModTypes = { {  }, },
	weapon = { PhysicalMin = 8, PhysicalMax = 25, CritChanceBase = 5, AttackRateBase = 1.5, Range = 11, },
	req = { level = 16, str = 30, dex = 14, },
}
itemBases["Bandit Hatchet"] = {
	type = "One Handed Axe",
	quality = 20,
	socketLimit = 2,
	tags = { axe = true, onehand = true, maraketh_basetype = true, weapon = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 13, PhysicalMax = 31, CritChanceBase = 5, AttackRateBase = 1.5, Range = 11, },
	req = { level = 20, str = 37, dex = 16, },
}
itemBases["Crescent Axe"] = {
	type = "One Handed Axe",
	quality = 20,
	socketLimit = 2,
	tags = { axe = true, onehand = true, maraketh_basetype = true, weapon = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 18, PhysicalMax = 37, CritChanceBase = 6.5, AttackRateBase = 1.4, Range = 11, },
	req = { level = 26, str = 48, dex = 20, },
}
itemBases["Carving Hatchet"] = {
	type = "One Handed Axe",
	quality = 20,
	socketLimit = 2,
	tags = { axe = true, onehand = true, vaal_basetype = true, weapon = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 19, PhysicalMax = 44, CritChanceBase = 5, AttackRateBase = 1.5, Range = 11, },
	req = { level = 33, str = 60, dex = 25, },
}
itemBases["Sacrificial Axe"] = {
	type = "One Handed Axe",
	quality = 20,
	socketLimit = 2,
	tags = { axe = true, onehand = true, vaal_basetype = true, weapon = true, one_hand_weapon = true, default = true, },
	implicit = "Gain (28-35) Mana per Enemy Killed",
	implicitModTypes = { { "resource", "mana" }, },
	weapon = { PhysicalMin = 20, PhysicalMax = 42, CritChanceBase = 5, AttackRateBase = 1.5, Range = 11, },
	req = { level = 37, str = 67, dex = 27, },
}
itemBases["Boarding Hatchet"] = {
	type = "One Handed Axe",
	quality = 20,
	socketLimit = 2,
	tags = { axe = true, onehand = true, weapon = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 24, PhysicalMax = 55, CritChanceBase = 5, AttackRateBase = 1.55, Range = 11, },
	req = { level = 45, str = 81, dex = 33, },
}
itemBases["Fury Cleaver"] = {
	type = "One Handed Axe",
	quality = 20,
	socketLimit = 2,
	tags = { axe = true, onehand = true, weapon = true, one_hand_weapon = true, default = true, },
	implicit = "10% increased Damage taken",
	implicitModTypes = { {  }, },
	weapon = { PhysicalMin = 30, PhysicalMax = 62, CritChanceBase = 5, AttackRateBase = 1.55, Range = 11, },
	req = { level = 48, str = 86, dex = 35, },
}
itemBases["Battle Axe"] = {
	type = "One Handed Axe",
	quality = 20,
	socketLimit = 2,
	tags = { axe = true, onehand = true, weapon = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 28, PhysicalMax = 65, CritChanceBase = 5, AttackRateBase = 1.5, Range = 11, },
	req = { level = 52, str = 93, dex = 37, },
}
itemBases["Profane Cleaver"] = {
	type = "One Handed Axe",
	quality = 20,
	socketLimit = 2,
	tags = { axe = true, onehand = true, weapon = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 26, PhysicalMax = 69, CritChanceBase = 5, AttackRateBase = 1.5, Range = 11, },
	req = { level = 54, str = 96, dex = 38, },
}
itemBases["Dread Hatchet"] = {
	type = "One Handed Axe",
	quality = 20,
	socketLimit = 2,
	tags = { axe = true, onehand = true, weapon = true, one_hand_weapon = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 34, PhysicalMax = 79, CritChanceBase = 5, AttackRateBase = 1.5, Range = 11, },
	req = { level = 65, str = 116, dex = 45, },
}

itemBases["Splitting Greataxe"] = {
	type = "Two Handed Axe",
	quality = 20,
	socketLimit = 3,
	tags = { axe = true, ezomyte_basetype = true, two_hand_weapon = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 8, PhysicalMax = 20, CritChanceBase = 5, AttackRateBase = 1.2, Range = 13, },
	req = { },
}
itemBases["Light Halberd"] = {
	type = "Two Handed Axe",
	quality = 20,
	socketLimit = 3,
	tags = { axe = true, ezomyte_basetype = true, two_hand_weapon = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 10, PhysicalMax = 25, CritChanceBase = 5, AttackRateBase = 1.25, Range = 13, },
	req = { str = 10, },
}
itemBases["Executioner Greataxe"] = {
	type = "Two Handed Axe",
	quality = 20,
	socketLimit = 3,
	tags = { axe = true, ezomyte_basetype = true, two_hand_weapon = true, weapon = true, twohand = true, default = true, },
	implicit = "Culling Strike",
	implicitModTypes = { {  }, },
	weapon = { PhysicalMin = 10, PhysicalMax = 41, CritChanceBase = 5, AttackRateBase = 1.2, Range = 13, },
	req = { level = 10, str = 20, dex = 10, },
}
itemBases["Arched Greataxe"] = {
	type = "Two Handed Axe",
	quality = 20,
	socketLimit = 3,
	tags = { axe = true, maraketh_basetype = true, two_hand_weapon = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 17, PhysicalMax = 59, CritChanceBase = 5, AttackRateBase = 1.15, Range = 13, },
	req = { level = 16, str = 30, dex = 14, },
}
itemBases["Elegant Glaive"] = {
	type = "Two Handed Axe",
	quality = 20,
	socketLimit = 3,
	tags = { axe = true, maraketh_basetype = true, two_hand_weapon = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 19, PhysicalMax = 64, CritChanceBase = 5, AttackRateBase = 1.2, Range = 13, },
	req = { level = 20, str = 37, dex = 16, },
}
itemBases["Savage Greataxe"] = {
	type = "Two Handed Axe",
	quality = 20,
	socketLimit = 3,
	tags = { axe = true, maraketh_basetype = true, two_hand_weapon = true, weapon = true, twohand = true, default = true, },
	implicit = "Gain (34-43) Life per Enemy Killed",
	implicitModTypes = { { "resource", "life" }, },
	weapon = { PhysicalMin = 23, PhysicalMax = 70, CritChanceBase = 5, AttackRateBase = 1.2, Range = 13, },
	req = { level = 27, str = 50, dex = 21, },
}
itemBases["Rending Halberd"] = {
	type = "Two Handed Axe",
	quality = 20,
	socketLimit = 3,
	tags = { axe = true, vaal_basetype = true, two_hand_weapon = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 27, PhysicalMax = 91, CritChanceBase = 6.5, AttackRateBase = 1.2, Range = 13, },
	req = { level = 33, str = 60, dex = 25, },
}
itemBases["Jagged Greataxe"] = {
	type = "Two Handed Axe",
	quality = 20,
	socketLimit = 3,
	tags = { axe = true, vaal_basetype = true, two_hand_weapon = true, weapon = true, twohand = true, default = true, },
	implicit = "(15-25)% chance to cause Bleeding on Hit",
	implicitModTypes = { { "bleed", "physical", "attack", "ailment" }, },
	weapon = { PhysicalMin = 43, PhysicalMax = 99, CritChanceBase = 5, AttackRateBase = 1.15, Range = 13, },
	req = { level = 40, str = 72, dex = 29, },
}
itemBases["Reaver Glaive"] = {
	type = "Two Handed Axe",
	quality = 20,
	socketLimit = 3,
	tags = { axe = true, two_hand_weapon = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 43, PhysicalMax = 113, CritChanceBase = 5, AttackRateBase = 1.2, Range = 13, },
	req = { level = 45, str = 81, dex = 33, },
}
itemBases["Ember Greataxe"] = {
	type = "Two Handed Axe",
	quality = 20,
	socketLimit = 3,
	tags = { axe = true, two_hand_weapon = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { FireMin = 58, FireMax = 154, CritChanceBase = 5, AttackRateBase = 1.2, Range = 13, },
	req = { level = 50, str = 89, dex = 36, },
}
itemBases["Ceremonial Halberd"] = {
	type = "Two Handed Axe",
	quality = 20,
	socketLimit = 3,
	tags = { axe = true, two_hand_weapon = true, weapon = true, twohand = true, default = true, },
	implicit = "Cannot use Projectile Attacks",
	implicitModTypes = { {  }, },
	weapon = { PhysicalMin = 53, PhysicalMax = 123, CritChanceBase = 5, AttackRateBase = 1.2, Range = 13, },
	req = { level = 52, str = 93, dex = 37, },
}
itemBases["Monument Greataxe"] = {
	type = "Two Handed Axe",
	quality = 20,
	socketLimit = 3,
	tags = { axe = true, two_hand_weapon = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 48, PhysicalMax = 143, CritChanceBase = 5, AttackRateBase = 1.2, Range = 13, },
	req = { level = 57, str = 102, dex = 40, },
}
itemBases["Vile Greataxe"] = {
	type = "Two Handed Axe",
	quality = 20,
	socketLimit = 3,
	tags = { axe = true, two_hand_weapon = true, weapon = true, twohand = true, default = true, },
	implicitModTypes = { },
	weapon = { PhysicalMin = 59, PhysicalMax = 155, CritChanceBase = 5, AttackRateBase = 1.2, Range = 13, },
	req = { level = 65, str = 116, dex = 45, },
}
