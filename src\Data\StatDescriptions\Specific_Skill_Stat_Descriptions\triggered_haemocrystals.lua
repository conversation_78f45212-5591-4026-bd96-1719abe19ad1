-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
		},
		stats={
			[1]="active_skill_area_of_effect_radius"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Haemocrystal radius is {0} metre"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Haemocrystal radius is {0} metres"
			}
		},
		stats={
			[1]="active_skill_base_area_of_effect_radius"
		}
	},
	[3]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=1000,
						[2]=1000
					}
				},
				text="Haemocrystals detonate after {0} second"
			},
			[2]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Haemocrystals detonate after {0} seconds"
			}
		},
		stats={
			[1]="base_skill_effect_duration"
		}
	},
	[4]={
		[1]={
			[1]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]=10,
						[2]=10
					}
				},
				text="Haemocrystals are randomly created within {0} metre of the Consumed Bleeding"
			},
			[2]={
				[1]={
					k="divide_by_ten_1dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Haemocrystals are randomly created within {0} metres of the Consumed Bleeding"
			}
		},
		stats={
			[1]="support_haemocrystals_crystal_creation_radius"
		}
	},
	[5]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]=100,
						[2]=100
					}
				},
				text="Haemocrystals deal Damage equal to the expected remaining Damage of the Consumed Bleeding"
			},
			[2]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Haemocrystals deal Damage equal to {0}% of the expected remaining Damage of the Consumed Bleeding"
			}
		},
		stats={
			[1]="support_haemocrystals_physical_damage_equal_to_%_of_remaining_bleed_damage"
		}
	},
	["active_skill_area_of_effect_radius"]=1,
	["active_skill_base_area_of_effect_radius"]=2,
	["base_skill_effect_duration"]=3,
	parent="skill_stat_descriptions",
	["support_haemocrystals_crystal_creation_radius"]=4,
	["support_haemocrystals_physical_damage_equal_to_%_of_remaining_bleed_damage"]=5
}