-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	["groups"] = {
		[1000000000] = {
			["y"] = -6500, 
			["x"] = -6500, 
			["n"] = {
				[1] = 1, 
				[2] = 2, 
				[3] = 3, 
				[4] = 4, 
				[5] = 5, 
				[6] = 6, 
				[7] = 7, 
				[8] = 8, 
				[9] = 9, 
				[10] = 10, 
				[11] = 11, 
				[12] = 12, 
				[13] = 13, 
				[14] = 14, 
				[15] = 15, 
				[16] = 16, 
				[17] = 17, 
				[18] = 18, 
				[19] = 19, 
				[20] = 20, 
				[21] = 21, 
				[22] = 22, 
				[23] = 23, 
				[24] = 24, 
				[25] = 25, 
				[26] = 26, 
				[27] = 27, 
				[28] = 28, 
				[29] = 29, 
				[30] = 30, 
				[31] = 31, 
				[32] = 32, 
				[33] = 33, 
				[34] = 34, 
				[35] = 35, 
				[36] = 36, 
				[37] = 37, 
				[38] = 38, 
				[39] = 39, 
				[40] = 40, 
				[41] = 41, 
				[42] = 42, 
				[43] = 43, 
				[44] = 44, 
				[45] = 45, 
				[46] = 46, 
				[47] = 47, 
				[48] = 48, 
				[49] = 49, 
				[50] = 50, 
				[51] = 51, 
				[52] = 52, 
				[53] = 53, 
				[54] = 54, 
				[55] = 55, 
				[56] = 56, 
				[57] = 57, 
				[58] = 58, 
				[59] = 59, 
				[60] = 60, 
				[61] = 61, 
				[62] = 62, 
				[63] = 63, 
				[64] = 64, 
				[65] = 65, 
				[66] = 66, 
				[67] = 67, 
				[68] = 68, 
				[69] = 69, 
				[70] = 70, 
				[71] = 71, 
				[72] = 72, 
				[73] = 73, 
				[74] = 74, 
				[75] = 75, 
				[76] = 76, 
				[77] = 77, 
				[78] = 78, 
				[79] = 79, 
				[80] = 80, 
				[81] = 81, 
				[82] = 82, 
				[83] = 83, 
				[84] = 84, 
				[85] = 85, 
				[86] = 86, 
				[87] = 87, 
				[88] = 88, 
				[89] = 89, 
				[90] = 90, 
				[91] = 91, 
				[92] = 92, 
				[93] = 93, 
				[94] = 94, 
				[95] = 95, 
				[96] = 96, 
				[97] = 97, 
				[98] = 98, 
				[99] = 99, 
				[100] = 100, 
				[101] = 101, 
				[102] = 102, 
				[103] = 103, 
				[104] = 104, 
				[105] = 105, 
				[106] = 106, 
				[107] = 107, 
				[108] = 108, 
				[109] = 109, 
				[110] = 110, 
				[111] = 111, 
				[112] = 112, 
				[113] = 113, 
				[114] = 114, 
				[115] = 115, 
				[116] = 116, 
				[117] = 117, 
				[118] = 118, 
				[119] = 119, 
				[120] = 120, 
				[121] = 121, 
				[122] = 122, 
				[123] = 123, 
				[124] = 124, 
				[125] = 125, 
				[126] = 126, 
				[127] = 127, 
				[128] = 128, 
				[129] = 129, 
				[130] = 130, 
				[131] = 131, 
				[132] = 132, 
				[133] = 133, 
				[134] = 134, 
				[135] = 135, 
				[136] = 136, 
				[137] = 137, 
				[138] = 138, 
				[139] = 139, 
				[140] = 140, 
				[141] = 141, 
				[142] = 142, 
				[143] = 143, 
				[144] = 144, 
				[145] = 145, 
				[146] = 146, 
				[147] = 147, 
				[148] = 148, 
				[149] = 149, 
				[150] = 150, 
				[151] = 151, 
				[152] = 152, 
				[153] = 153, 
				[154] = 154, 
				[155] = 155, 
				[156] = 156, 
				[157] = 157, 
				[158] = 158, 
				[159] = 159, 
				[160] = 160, 
				[161] = 161, 
				[162] = 162, 
				[163] = 163, 
				[164] = 164, 
				[165] = 165, 
				[166] = 166, 
				[167] = 167, 
				[168] = 168, 
				[169] = 169, 
				[170] = 170, 
				[171] = 171, 
				[172] = 172, 
				[173] = 173, 
				[174] = 174, 
				[175] = 175, 
				[176] = 176, 
				[177] = 177, 
				[178] = 178, 
				[179] = 179, 
				[180] = 180, 
				[181] = 181, 
				[182] = 182, 
				[183] = 183, 
				[184] = 184, 
				[185] = 185, 
				[186] = 186, 
				[187] = 187, 
				[188] = 188, 
				[189] = 189, 
				[190] = 190, 
				[191] = 191, 
				[192] = 192, 
				[193] = 193, 
				[194] = 194, 
				[195] = 195, 
			}, 
			["oo"] = {
			}, 
		}, 
	}, 
	["nodes"] = {
		[1] = {
			["id"] = "vaal_keystone_1", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "All Damage taken bypasses Energy Shield", 
				[2] = "50% of Elemental Damage taken as Chaos Damage", 
				[3] = "+5% to maximum Chaos Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Divine Flesh", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/DivineFlesh.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_divine_flesh", 
			}, 
			["stats"] = {
				["keystone_divine_flesh"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9393, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 0, 
			["g"] = 1000000000, 
		}, 
		[2] = {
			["id"] = "vaal_keystone_2", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "Life Recharges instead of Energy Shield", 
				[2] = "Life Recovery from Flasks applies to Energy Shield instead", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Eternal Youth", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalYouth.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_eternal_youth", 
			}, 
			["stats"] = {
				["keystone_eternal_youth"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9397, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 3, 
			["g"] = 1000000000, 
		}, 
		[3] = {
			["id"] = "vaal_keystone_2_v2", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "Energy Shield starts at zero", 
				[2] = "Cannot Recharge or Regenerate Energy Shield", 
				[3] = "Lose 5% of Energy Shield per second", 
				[4] = "Life Leech effects are not removed when Unreserved Life is Filled", 
				[5] = "Life Leech effects Recover Energy Shield instead while on Full Life", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Immortal Ambition", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/SoulTetherKeystone.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "dummy_stat_display_nothing", 
			}, 
			["stats"] = {
				["dummy_stat_display_nothing"] = {
					["max"] = 1, 
					["min"] = 1, 
					["index"] = 1, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 6, 
			["g"] = 1000000000, 
		}, 
		[4] = {
			["id"] = "vaal_keystone_3", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Corrupted Soul", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/CorruptedDefences.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "dummy_stat_display_nothing", 
			}, 
			["stats"] = {
				["dummy_stat_display_nothing"] = {
					["max"] = 1, 
					["min"] = 1, 
					["index"] = 1, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 9, 
			["g"] = 1000000000, 
		}, 
		[5] = {
			["id"] = "vaal_small_fire_damage", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(7-12)% increased Fire Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Fire Damage", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "fire_damage_+%", 
			}, 
			["stats"] = {
				["fire_damage_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 856, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 79420, 
			["g"] = 1000000000, 
		}, 
		[6] = {
			["id"] = "vaal_small_cold_damage", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(7-12)% increased Cold Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Cold Damage", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "cold_damage_+%", 
			}, 
			["stats"] = {
				["cold_damage_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 857, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 69885, 
			["g"] = 1000000000, 
		}, 
		[7] = {
			["id"] = "vaal_small_lightning_damage", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(7-12)% increased Lightning Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Lightning Damage", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "lightning_damage_+%", 
			}, 
			["stats"] = {
				["lightning_damage_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 858, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 59010, 
			["g"] = 1000000000, 
		}, 
		[8] = {
			["id"] = "vaal_small_physical_damage", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(7-12)% increased Physical Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Physical Damage", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "physical_damage_+%", 
			}, 
			["stats"] = {
				["physical_damage_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 9380, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 75322, 
			["g"] = 1000000000, 
		}, 
		[9] = {
			["id"] = "vaal_small_chaos_damage", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(7-12)% increased Chaos Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Chaos Damage", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "chaos_damage_+%", 
			}, 
			["stats"] = {
				["chaos_damage_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 859, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 8097, 
			["g"] = 1000000000, 
		}, 
		[10] = {
			["id"] = "vaal_small_minion_damage", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "Minions deal (8-13)% increased Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Minion Damage", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "minion_damage_+%", 
			}, 
			["stats"] = {
				["minion_damage_+%"] = {
					["max"] = 13, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 8, 
					["statOrder"] = 1655, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 66110, 
			["g"] = 1000000000, 
		}, 
		[11] = {
			["id"] = "vaal_small_attack_damage", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(7-12)% increased Attack Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Attack Damage", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "attack_damage_+%", 
			}, 
			["stats"] = {
				["attack_damage_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 1095, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 33933, 
			["g"] = 1000000000, 
		}, 
		[12] = {
			["id"] = "vaal_small_spell_damage", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(7-12)% increased Spell Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Spell Damage", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "spell_damage_+%", 
			}, 
			["stats"] = {
				["spell_damage_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 854, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 73573, 
			["g"] = 1000000000, 
		}, 
		[13] = {
			["id"] = "vaal_small_area_damage", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(7-12)% increased Area Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Area Damage", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "area_damage_+%", 
			}, 
			["stats"] = {
				["area_damage_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 1710, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 42668, 
			["g"] = 1000000000, 
		}, 
		[14] = {
			["id"] = "vaal_small_projectile_damage", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(7-12)% increased Projectile Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Projectile Damage", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "projectile_damage_+%", 
			}, 
			["stats"] = {
				["projectile_damage_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 1675, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 56859, 
			["g"] = 1000000000, 
		}, 
		[15] = {
			["id"] = "vaal_small_damage_over_time", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(7-12)% increased Damage over Time", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Damage Over Time", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "damage_over_time_+%", 
			}, 
			["stats"] = {
				["damage_over_time_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 1106, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 11446, 
			["g"] = 1000000000, 
		}, 
		[16] = {
			["id"] = "vaal_small_area_of_effect", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(4-7)% increased Area of Effect", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Area of Effect", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_skill_area_of_effect_+%", 
			}, 
			["stats"] = {
				["base_skill_area_of_effect_+%"] = {
					["max"] = 7, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 4, 
					["statOrder"] = 1564, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 5489, 
			["g"] = 1000000000, 
		}, 
		[17] = {
			["id"] = "vaal_small_projectile_speed", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(7-12)% increased Projectile Speed", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Projectile Speed", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_projectile_speed_+%", 
			}, 
			["stats"] = {
				["base_projectile_speed_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 877, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 97416, 
			["g"] = 1000000000, 
		}, 
		[18] = {
			["id"] = "vaal_small_critical_strike_chance", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(7-14)% increased Critical Hit Chance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Critical Strike Chance", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "critical_strike_chance_+%", 
			}, 
			["stats"] = {
				["critical_strike_chance_+%"] = {
					["max"] = 14, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 9353, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 70539, 
			["g"] = 1000000000, 
		}, 
		[19] = {
			["id"] = "vaal_small_critical_strike_multiplier", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(6-10)% increased Critical Damage Bonus", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Critical Strike Multiplier", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_critical_strike_multiplier_+", 
			}, 
			["stats"] = {
				["base_critical_strike_multiplier_+"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 6, 
					["statOrder"] = 929, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 17595, 
			["g"] = 1000000000, 
		}, 
		[20] = {
			["id"] = "vaal_small_attack_speed", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(3-4)% increased Attack Speed", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Attack Speed", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "attack_speed_+%", 
			}, 
			["stats"] = {
				["attack_speed_+%"] = {
					["max"] = 4, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 3, 
					["statOrder"] = 933, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 97479, 
			["g"] = 1000000000, 
		}, 
		[21] = {
			["id"] = "vaal_small_cast_speed", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(2-3)% increased Cast Speed", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Cast Speed", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_cast_speed_+%", 
			}, 
			["stats"] = {
				["base_cast_speed_+%"] = {
					["max"] = 3, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 2, 
					["statOrder"] = 934, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 27693, 
			["g"] = 1000000000, 
		}, 
		[22] = {
			["id"] = "vaal_small_movement_speed", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(2-3)% increased Movement Speed", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Movement Speed", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_movement_velocity_+%", 
			}, 
			["stats"] = {
				["base_movement_velocity_+%"] = {
					["max"] = 3, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 2, 
					["statOrder"] = 828, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 35409, 
			["g"] = 1000000000, 
		}, 
		[23] = {
			["id"] = "vaal_small_chance_to_ignite", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(3-6)% chance to Ignite", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Ignite Chance", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_chance_to_ignite_%", 
			}, 
			["stats"] = {
				["base_chance_to_ignite_%"] = {
					["max"] = 6, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 3, 
					["statOrder"] = 977, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 47236, 
			["g"] = 1000000000, 
		}, 
		[24] = {
			["id"] = "vaal_small_chance_to_freeze", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(3-6)% chance to Freeze", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Freeze Chance", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_chance_to_freeze_%", 
			}, 
			["stats"] = {
				["base_chance_to_freeze_%"] = {
					["max"] = 6, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 3, 
					["statOrder"] = 979, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 67900, 
			["g"] = 1000000000, 
		}, 
		[25] = {
			["id"] = "vaal_small_chance_to_shock", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(3-6)% chance to Shock", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Shock Chance", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_chance_to_shock_%", 
			}, 
			["stats"] = {
				["base_chance_to_shock_%"] = {
					["max"] = 6, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 3, 
					["statOrder"] = 981, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 57514, 
			["g"] = 1000000000, 
		}, 
		[26] = {
			["id"] = "vaal_small_duration", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(4-7)% increased Skill Effect Duration", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Skill Duration", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "skill_effect_duration_+%", 
			}, 
			["stats"] = {
				["skill_effect_duration_+%"] = {
					["max"] = 7, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 4, 
					["statOrder"] = 1579, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 79693, 
			["g"] = 1000000000, 
		}, 
		[27] = {
			["id"] = "vaal_small_life", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(2-4)% increased maximum Life", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Life", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "maximum_life_+%", 
			}, 
			["stats"] = {
				["maximum_life_+%"] = {
					["max"] = 4, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 2, 
					["statOrder"] = 872, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 45174, 
			["g"] = 1000000000, 
		}, 
		[28] = {
			["id"] = "vaal_small_mana", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(4-6)% increased maximum Mana", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Mana", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "maximum_mana_+%", 
			}, 
			["stats"] = {
				["maximum_mana_+%"] = {
					["max"] = 6, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 4, 
					["statOrder"] = 874, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 18201, 
			["g"] = 1000000000, 
		}, 
		[29] = {
			["id"] = "vaal_small_mana_regeneration", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(12-17)% increased Mana Regeneration Rate", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Mana Regeneration", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "mana_regeneration_rate_+%", 
			}, 
			["stats"] = {
				["mana_regeneration_rate_+%"] = {
					["max"] = 17, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 12, 
					["statOrder"] = 965, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 65999, 
			["g"] = 1000000000, 
		}, 
		[30] = {
			["id"] = "vaal_small_armour", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(7-12)% increased Armour", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Armour", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "physical_damage_reduction_rating_+%", 
			}, 
			["stats"] = {
				["physical_damage_reduction_rating_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 866, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 21117, 
			["g"] = 1000000000, 
		}, 
		[31] = {
			["id"] = "vaal_small_evasion", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(7-12)% increased Evasion Rating", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Evasion", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "evasion_rating_+%", 
			}, 
			["stats"] = {
				["evasion_rating_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 868, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 59672, 
			["g"] = 1000000000, 
		}, 
		[32] = {
			["id"] = "vaal_small_energy_shield", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(3-5)% increased maximum Energy Shield", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Energy Shield", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "maximum_energy_shield_+%", 
			}, 
			["stats"] = {
				["maximum_energy_shield_+%"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 3, 
					["statOrder"] = 870, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 14411, 
			["g"] = 1000000000, 
		}, 
		[33] = {
			["id"] = "vaal_small_attack_block", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "+1% to Block chance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Block", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "additional_block_%", 
			}, 
			["stats"] = {
				["additional_block_%"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 2132, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 82991, 
			["g"] = 1000000000, 
		}, 
		[34] = {
			["id"] = "vaal_small_spell_block", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "1% Chance to Block Spell Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Spell Block", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_spell_block_%", 
			}, 
			["stats"] = {
				["base_spell_block_%"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 864, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 58330, 
			["g"] = 1000000000, 
		}, 
		[35] = {
			["id"] = "vaal_small_attack_dodge", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "3% chance to Avoid Elemental Ailments", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Avoid Elemental Ailments", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "avoid_all_elemental_status_%", 
			}, 
			["stats"] = {
				["avoid_all_elemental_status_%"] = {
					["max"] = 3, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 3, 
					["statOrder"] = 1533, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 2479, 
			["g"] = 1000000000, 
		}, 
		[36] = {
			["id"] = "vaal_small_spell_dodge", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "+2% chance to Suppress Spell Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Spell Suppression", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_spell_suppression_chance_%", 
			}, 
			["stats"] = {
				["base_spell_suppression_chance_%"] = {
					["max"] = 2, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 2, 
					["statOrder"] = 954, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 83640, 
			["g"] = 1000000000, 
		}, 
		[37] = {
			["id"] = "vaal_small_aura_effect", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "(2-4)% increased effect of Non-Curse Auras from your Skills", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Aura Effect", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "non_curse_aura_effect_+%", 
			}, 
			["stats"] = {
				["non_curse_aura_effect_+%"] = {
					["max"] = 4, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 2, 
					["statOrder"] = 3181, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 4960, 
			["g"] = 1000000000, 
		}, 
		[38] = {
			["id"] = "vaal_small_curse_effect", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "2% increased Effect of your Curses", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Curse Effect", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "curse_effect_+%", 
			}, 
			["stats"] = {
				["curse_effect_+%"] = {
					["max"] = 2, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 2, 
					["statOrder"] = 2266, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 82957, 
			["g"] = 1000000000, 
		}, 
		[39] = {
			["id"] = "vaal_small_fire_resistance", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "+(9-14)% to Fire Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Fire Resistance", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_fire_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_fire_damage_resistance_%"] = {
					["max"] = 14, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 9, 
					["statOrder"] = 950, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 62650, 
			["g"] = 1000000000, 
		}, 
		[40] = {
			["id"] = "vaal_small_cold_resistance", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "+(9-14)% to Cold Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Cold Resistance", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_cold_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_cold_damage_resistance_%"] = {
					["max"] = 14, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 9, 
					["statOrder"] = 951, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 82675, 
			["g"] = 1000000000, 
		}, 
		[41] = {
			["id"] = "vaal_small_lightning_resistance", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "+(9-14)% to Lightning Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Lightning Resistance", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_lightning_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_lightning_damage_resistance_%"] = {
					["max"] = 14, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 9, 
					["statOrder"] = 952, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 18075, 
			["g"] = 1000000000, 
		}, 
		[42] = {
			["id"] = "vaal_small_chaos_resistance", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "+(6-10)% to Chaos Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Chaos Resistance", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_chaos_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_chaos_damage_resistance_%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 6, 
					["statOrder"] = 953, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 25548, 
			["g"] = 1000000000, 
		}, 
		[43] = {
			["id"] = "vaal_notable_fire_damage_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(25-35)% increased Fire Damage", 
				[2] = "Damage Penetrates (2-4)% Fire Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Ritual of Immolation", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "fire_damage_+%", 
				[2] = "base_reduce_enemy_fire_resistance_%", 
			}, 
			["stats"] = {
				["base_reduce_enemy_fire_resistance_%"] = {
					["max"] = 4, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 2, 
					["statOrder"] = 2627, 
				}, 
				["fire_damage_+%"] = {
					["max"] = 35, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 856, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 92114, 
			["g"] = 1000000000, 
		}, 
		[44] = {
			["id"] = "vaal_notable_fire_damage_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(25-35)% increased Fire Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Revitalising Flames", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "dummy_stat_display_nothing", 
				[2] = "fire_damage_+%", 
			}, 
			["stats"] = {
				["dummy_stat_display_nothing"] = {
					["max"] = 20, 
					["min"] = 20, 
					["index"] = 2, 
				}, 
				["fire_damage_+%"] = {
					["max"] = 35, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 856, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 31696, 
			["g"] = 1000000000, 
		}, 
		[45] = {
			["id"] = "vaal_notable_fire_damage_3", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(25-35)% increased Fire Damage", 
				[2] = "10% of Physical Damage Converted to Fire Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Flesh to Flames", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "fire_damage_+%", 
				[2] = "non_skill_base_physical_damage_%_to_convert_to_fire", 
			}, 
			["stats"] = {
				["non_skill_base_physical_damage_%_to_convert_to_fire"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 1638, 
				}, 
				["fire_damage_+%"] = {
					["max"] = 35, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 856, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 7855, 
			["g"] = 1000000000, 
		}, 
		[46] = {
			["id"] = "vaal_notable_cold_damage_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(25-35)% increased Cold Damage", 
				[2] = "Damage Penetrates (2-4)% Cold Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Ritual of Stillness", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "cold_damage_+%", 
				[2] = "base_reduce_enemy_cold_resistance_%", 
			}, 
			["stats"] = {
				["cold_damage_+%"] = {
					["max"] = 35, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 857, 
				}, 
				["base_reduce_enemy_cold_resistance_%"] = {
					["max"] = 4, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 2, 
					["statOrder"] = 2628, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 35484, 
			["g"] = 1000000000, 
		}, 
		[47] = {
			["id"] = "vaal_notable_cold_damage_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(25-35)% increased Cold Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Revitalising Frost", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "dummy_stat_display_nothing", 
				[2] = "cold_damage_+%", 
			}, 
			["stats"] = {
				["cold_damage_+%"] = {
					["max"] = 35, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 857, 
				}, 
				["dummy_stat_display_nothing"] = {
					["max"] = 20, 
					["min"] = 20, 
					["index"] = 2, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 92100, 
			["g"] = 1000000000, 
		}, 
		[48] = {
			["id"] = "vaal_notable_cold_damage_3", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(25-35)% increased Cold Damage", 
				[2] = "10% of Physical Damage Converted to Cold Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Flesh to Frost", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "cold_damage_+%", 
				[2] = "non_skill_base_physical_damage_%_to_convert_to_cold", 
			}, 
			["stats"] = {
				["cold_damage_+%"] = {
					["max"] = 35, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 857, 
				}, 
				["non_skill_base_physical_damage_%_to_convert_to_cold"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 1640, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 2503, 
			["g"] = 1000000000, 
		}, 
		[49] = {
			["id"] = "vaal_notable_lightning_damage_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(25-35)% increased Lightning Damage", 
				[2] = "Damage Penetrates (2-4)% Lightning Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Ritual of Thunder", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "lightning_damage_+%", 
				[2] = "base_reduce_enemy_lightning_resistance_%", 
			}, 
			["stats"] = {
				["lightning_damage_+%"] = {
					["max"] = 35, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 858, 
				}, 
				["base_reduce_enemy_lightning_resistance_%"] = {
					["max"] = 4, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 2, 
					["statOrder"] = 2629, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 67692, 
			["g"] = 1000000000, 
		}, 
		[50] = {
			["id"] = "vaal_notable_lightning_damage_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(25-35)% increased Lightning Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Revitalising Lightning", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "dummy_stat_display_nothing", 
				[2] = "lightning_damage_+%", 
			}, 
			["stats"] = {
				["lightning_damage_+%"] = {
					["max"] = 35, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 858, 
				}, 
				["dummy_stat_display_nothing"] = {
					["max"] = 20, 
					["min"] = 20, 
					["index"] = 2, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 74451, 
			["g"] = 1000000000, 
		}, 
		[51] = {
			["id"] = "vaal_notable_lightning_damage_3", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(25-35)% increased Lightning Damage", 
				[2] = "10% of Physical Damage Converted to Lightning Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Flesh to Lightning", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "lightning_damage_+%", 
				[2] = "non_skill_base_physical_damage_%_to_convert_to_lightning", 
			}, 
			["stats"] = {
				["lightning_damage_+%"] = {
					["max"] = 35, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 858, 
				}, 
				["non_skill_base_physical_damage_%_to_convert_to_lightning"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 1642, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 55329, 
			["g"] = 1000000000, 
		}, 
		[52] = {
			["id"] = "vaal_notable_physical_damage_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(2-4)% chance to deal Double Damage", 
				[2] = "(25-35)% increased Physical Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Ritual of Might", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "chance_to_deal_double_damage_%", 
				[2] = "physical_damage_+%", 
			}, 
			["stats"] = {
				["physical_damage_+%"] = {
					["max"] = 35, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 9380, 
				}, 
				["chance_to_deal_double_damage_%"] = {
					["max"] = 4, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 2, 
					["statOrder"] = 4972, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 11777, 
			["g"] = 1000000000, 
		}, 
		[53] = {
			["id"] = "vaal_notable_physical_damage_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(25-35)% increased Physical Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Revitalising Winds", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "dummy_stat_display_nothing", 
				[2] = "physical_damage_+%", 
			}, 
			["stats"] = {
				["physical_damage_+%"] = {
					["max"] = 35, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 9380, 
				}, 
				["dummy_stat_display_nothing"] = {
					["max"] = 20, 
					["min"] = 20, 
					["index"] = 2, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 13357, 
			["g"] = 1000000000, 
		}, 
		[54] = {
			["id"] = "vaal_notable_physical_damage_3", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "Bleeding you inflict deals Damage 10% faster", 
				[2] = "(25-35)% increased Physical Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Bloody Savagery", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "faster_bleed_%", 
				[2] = "physical_damage_+%", 
			}, 
			["stats"] = {
				["physical_damage_+%"] = {
					["max"] = 35, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 9380, 
				}, 
				["faster_bleed_%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 5814, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 3316, 
			["g"] = 1000000000, 
		}, 
		[55] = {
			["id"] = "vaal_notable_chaos_damage_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(25-35)% increased Chaos Damage", 
				[2] = "25% chance to inflict Withered for 2 seconds on Hit", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Ritual of Shadows", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "chaos_damage_+%", 
				[2] = "withered_on_hit_for_2_seconds_%_chance", 
			}, 
			["stats"] = {
				["withered_on_hit_for_2_seconds_%_chance"] = {
					["max"] = 25, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 25, 
					["statOrder"] = 4000, 
				}, 
				["chaos_damage_+%"] = {
					["max"] = 35, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 859, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 49930, 
			["g"] = 1000000000, 
		}, 
		[56] = {
			["id"] = "vaal_notable_chaos_damage_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(25-35)% increased Chaos Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Revitalising Darkness", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "dummy_stat_display_nothing", 
				[2] = "chaos_damage_+%", 
			}, 
			["stats"] = {
				["dummy_stat_display_nothing"] = {
					["max"] = 20, 
					["min"] = 20, 
					["index"] = 2, 
				}, 
				["chaos_damage_+%"] = {
					["max"] = 35, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 859, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 68927, 
			["g"] = 1000000000, 
		}, 
		[57] = {
			["id"] = "vaal_notable_spell_damage_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(25-35)% increased Spell Damage", 
				[2] = "(35-50)% increased Critical Hit Chance for Spells", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Thaumaturgical Aptitude", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "spell_damage_+%", 
				[2] = "spell_critical_strike_chance_+%", 
			}, 
			["stats"] = {
				["spell_damage_+%"] = {
					["max"] = 35, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 854, 
				}, 
				["spell_critical_strike_chance_+%"] = {
					["max"] = 50, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 35, 
					["statOrder"] = 927, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 50654, 
			["g"] = 1000000000, 
		}, 
		[58] = {
			["id"] = "vaal_notable_minion_damage_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "Minions have (15-20)% increased maximum Life", 
				[2] = "Minions deal (25-35)% increased Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Hierarchy", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "minion_maximum_life_+%", 
				[2] = "minion_damage_+%", 
			}, 
			["stats"] = {
				["minion_maximum_life_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 15, 
					["statOrder"] = 955, 
				}, 
				["minion_damage_+%"] = {
					["max"] = 35, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 1655, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 85555, 
			["g"] = 1000000000, 
		}, 
		[59] = {
			["id"] = "vaal_notable_damage_over_time_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(25-35)% increased Damage over Time", 
				[2] = "(7-11)% increased Skill Effect Duration", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Exquisite Pain", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "damage_over_time_+%", 
				[2] = "skill_effect_duration_+%", 
			}, 
			["stats"] = {
				["damage_over_time_+%"] = {
					["max"] = 35, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 1106, 
				}, 
				["skill_effect_duration_+%"] = {
					["max"] = 11, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 7, 
					["statOrder"] = 1579, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 13953, 
			["g"] = 1000000000, 
		}, 
		[60] = {
			["id"] = "vaal_notable_life_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(6-10)% increased maximum Life", 
				[2] = "Regenerate (0.7-1.2)% of Life per second", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Ritual of Flesh", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "maximum_life_+%", 
				[2] = "life_regeneration_rate_per_minute_%", 
			}, 
			["stats"] = {
				["maximum_life_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 6, 
					["statOrder"] = 872, 
				}, 
				["life_regeneration_rate_per_minute_%"] = {
					["max"] = 1.2, 
					["fmt"] = "g", 
					["index"] = 2, 
					["min"] = 0.7, 
					["statOrder"] = 1627, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 29305, 
			["g"] = 1000000000, 
		}, 
		[61] = {
			["id"] = "vaal_notable_life_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(6-10)% increased maximum Life", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Flesh Worship", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "dummy_stat_display_nothing", 
				[2] = "maximum_life_+%", 
			}, 
			["stats"] = {
				["maximum_life_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 6, 
					["statOrder"] = 872, 
				}, 
				["dummy_stat_display_nothing"] = {
					["max"] = 40, 
					["min"] = 40, 
					["index"] = 2, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 87752, 
			["g"] = 1000000000, 
		}, 
		[62] = {
			["id"] = "vaal_notable_mana_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(17-23)% increased maximum Mana", 
				[2] = "(15-25)% increased Mana Regeneration Rate", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Ritual of Memory", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "maximum_mana_+%", 
				[2] = "mana_regeneration_rate_+%", 
			}, 
			["stats"] = {
				["maximum_mana_+%"] = {
					["max"] = 23, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 17, 
					["statOrder"] = 874, 
				}, 
				["mana_regeneration_rate_+%"] = {
					["max"] = 25, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 15, 
					["statOrder"] = 965, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 76549, 
			["g"] = 1000000000, 
		}, 
		[63] = {
			["id"] = "vaal_notable_armour_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(30-40)% increased Armour", 
				[2] = "(3-4)% additional Physical Damage Reduction", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Automaton Studies", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "physical_damage_reduction_rating_+%", 
				[2] = "base_additional_physical_damage_reduction_%", 
			}, 
			["stats"] = {
				["physical_damage_reduction_rating_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 30, 
					["statOrder"] = 866, 
				}, 
				["base_additional_physical_damage_reduction_%"] = {
					["max"] = 4, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 3, 
					["statOrder"] = 943, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 69557, 
			["g"] = 1000000000, 
		}, 
		[64] = {
			["id"] = "vaal_notable_evasion_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(30-40)% increased Evasion Rating", 
				[2] = "(5-7)% chance to Blind Enemies on Hit", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Construct Studies", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "evasion_rating_+%", 
				[2] = "global_chance_to_blind_on_hit_%", 
			}, 
			["stats"] = {
				["global_chance_to_blind_on_hit_%"] = {
					["max"] = 7, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 5, 
					["statOrder"] = 9362, 
				}, 
				["evasion_rating_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 30, 
					["statOrder"] = 868, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 64898, 
			["g"] = 1000000000, 
		}, 
		[65] = {
			["id"] = "vaal_notable_energy_shield_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(8-12)% increased maximum Energy Shield", 
				[2] = "(10-15)% increased Energy Shield Recharge Rate", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Energy Flow Studies", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "maximum_energy_shield_+%", 
				[2] = "energy_shield_recharge_rate_+%", 
			}, 
			["stats"] = {
				["maximum_energy_shield_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 8, 
					["statOrder"] = 870, 
				}, 
				["energy_shield_recharge_rate_+%"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 956, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 83885, 
			["g"] = 1000000000, 
		}, 
		[66] = {
			["id"] = "vaal_notable_energy_shield_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(8-12)% increased maximum Energy Shield", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Soul Worship", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "dummy_stat_display_nothing", 
				[2] = "maximum_energy_shield_+%", 
			}, 
			["stats"] = {
				["maximum_energy_shield_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 8, 
					["statOrder"] = 870, 
				}, 
				["dummy_stat_display_nothing"] = {
					["max"] = 30, 
					["min"] = 30, 
					["index"] = 2, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 1049, 
			["g"] = 1000000000, 
		}, 
		[67] = {
			["id"] = "vaal_notable_block_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(6-10) Life gained when you Block", 
				[2] = "+5% to Block chance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Blood-Quenched Bulwark", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "life_gained_on_block", 
				[2] = "additional_block_%", 
			}, 
			["stats"] = {
				["additional_block_%"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 5, 
					["statOrder"] = 2132, 
				}, 
				["life_gained_on_block"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 6, 
					["statOrder"] = 1451, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 5394, 
			["g"] = 1000000000, 
		}, 
		[68] = {
			["id"] = "vaal_notable_block_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "5% Chance to Block Spell Damage", 
				[2] = "(20-30)% increased Defences from Equipped Shield", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Thaumaturgical Protection", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_spell_block_%", 
				[2] = "shield_armour_+%", 
			}, 
			["stats"] = {
				["base_spell_block_%"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 5, 
					["statOrder"] = 864, 
				}, 
				["shield_armour_+%"] = {
					["max"] = 30, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 20, 
					["statOrder"] = 1673, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 76907, 
			["g"] = 1000000000, 
		}, 
		[69] = {
			["id"] = "vaal_notable_dodge_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(8-10)% chance to Avoid Elemental Ailments", 
				[2] = "(8-10)% chance to Avoid being Stunned", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Jungle Paths", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "avoid_all_elemental_status_%", 
				[2] = "base_avoid_stun_%", 
			}, 
			["stats"] = {
				["base_avoid_stun_%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 8, 
					["statOrder"] = 1541, 
				}, 
				["avoid_all_elemental_status_%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 8, 
					["statOrder"] = 1533, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 40498, 
			["g"] = 1000000000, 
		}, 
		[70] = {
			["id"] = "vaal_notable_dodge_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+(8-10)% to all Elemental Resistances", 
				[2] = "+6% chance to Suppress Spell Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Temple Paths", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_resist_all_elements_%", 
				[2] = "base_spell_suppression_chance_%", 
			}, 
			["stats"] = {
				["base_spell_suppression_chance_%"] = {
					["max"] = 6, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 6, 
					["statOrder"] = 954, 
				}, 
				["base_resist_all_elements_%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 8, 
					["statOrder"] = 949, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 95964, 
			["g"] = 1000000000, 
		}, 
		[71] = {
			["id"] = "vaal_notable_aura_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "20% increased Area of Effect of Aura Skills", 
				[2] = "(7-10)% increased effect of Non-Curse Auras from your Skills", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Commanding Presence", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_aura_area_of_effect_+%", 
				[2] = "non_curse_aura_effect_+%", 
			}, 
			["stats"] = {
				["non_curse_aura_effect_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 3181, 
				}, 
				["base_aura_area_of_effect_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 20, 
					["statOrder"] = 1885, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 98699, 
			["g"] = 1000000000, 
		}, 
		[72] = {
			["id"] = "vaal_notable_curse_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "(4-6)% increased Effect of your Curses", 
				[2] = "Curse Skills have 20% increased Skill Effect Duration", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Ancient Hex", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "curse_effect_+%", 
				[2] = "curse_skill_effect_duration_+%", 
			}, 
			["stats"] = {
				["curse_effect_+%"] = {
					["max"] = 6, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 4, 
					["statOrder"] = 2266, 
				}, 
				["curse_skill_effect_duration_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 20, 
					["statOrder"] = 5310, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 38490, 
			["g"] = 1000000000, 
		}, 
		[73] = {
			["id"] = "vaal_notable_fire_resistance_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+1% to Maximum Fire Resistance", 
				[2] = "+(20-30)% to Fire Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Cult of Fire", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_maximum_fire_damage_resistance_%", 
				[2] = "base_fire_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_maximum_fire_damage_resistance_%"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 1, 
					["statOrder"] = 945, 
				}, 
				["base_fire_damage_resistance_%"] = {
					["max"] = 30, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 950, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 29481, 
			["g"] = 1000000000, 
		}, 
		[74] = {
			["id"] = "vaal_notable_cold_resistance_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+1% to Maximum Cold Resistance", 
				[2] = "+(20-30)% to Cold Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Cult of Ice", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_maximum_cold_damage_resistance_%", 
				[2] = "base_cold_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_cold_damage_resistance_%"] = {
					["max"] = 30, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 951, 
				}, 
				["base_maximum_cold_damage_resistance_%"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 1, 
					["statOrder"] = 946, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 27195, 
			["g"] = 1000000000, 
		}, 
		[75] = {
			["id"] = "vaal_notable_lightning_resistance_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+1% to Maximum Lightning Resistance", 
				[2] = "+(20-30)% to Lightning Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Cult of Lightning", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_maximum_lightning_damage_resistance_%", 
				[2] = "base_lightning_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_maximum_lightning_damage_resistance_%"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 1, 
					["statOrder"] = 947, 
				}, 
				["base_lightning_damage_resistance_%"] = {
					["max"] = 30, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 952, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 88478, 
			["g"] = 1000000000, 
		}, 
		[76] = {
			["id"] = "vaal_notable_chaos_resistance_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+1% to Maximum Chaos Resistance", 
				[2] = "+(13-19)% to Chaos Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Cult of Chaos", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_maximum_chaos_damage_resistance_%", 
				[2] = "base_chaos_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_chaos_damage_resistance_%"] = {
					["max"] = 19, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 13, 
					["statOrder"] = 953, 
				}, 
				["base_maximum_chaos_damage_resistance_%"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 1, 
					["statOrder"] = 948, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 95624, 
			["g"] = 1000000000, 
		}, 
		[77] = {
			["id"] = "vaal_notable_random_offense", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Might of the Vaal", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableOffensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
			}, 
			["stats"] = {
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 59351, 
			["g"] = 1000000000, 
		}, 
		[78] = {
			["id"] = "vaal_notable_random_defence", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Legacy of the Vaal", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/VaalNotableDefensive.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
			}, 
			["stats"] = {
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 75827, 
			["g"] = 1000000000, 
		}, 
		[79] = {
			["id"] = "karui_keystone_1", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "DNT Ded.", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Strength of Blood", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/StrengthOfBlood.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_strength_of_blood", 
			}, 
			["stats"] = {
				["keystone_strength_of_blood"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9417, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 12, 
			["g"] = 1000000000, 
		}, 
		[80] = {
			["id"] = "karui_keystone_2", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "50% of Cold and Lightning Damage taken as Fire Damage", 
				[2] = "50% less Cold Resistance", 
				[3] = "50% less Lightning Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Tempered by War", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/TemperedByWar.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_tempered_by_war", 
			}, 
			["stats"] = {
				["keystone_tempered_by_war"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9420, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 15, 
			["g"] = 1000000000, 
		}, 
		[81] = {
			["id"] = "karui_keystone_3", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "Block Chance is doubled", 
				[2] = "You take 50% of Damage from Blocked Hits", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Glancing Blows", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/GlancingBlows.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_glancing_blows", 
			}, 
			["stats"] = {
				["keystone_glancing_blows"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9399, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 18, 
			["g"] = 1000000000, 
		}, 
		[82] = {
			["id"] = "karui_keystone_3_v2", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "Mana Recovery from Regeneration is not applied", 
				[2] = "1 Rage Regenerated for every 25 Mana Regeneration per Second", 
				[3] = "Skills Cost +3 Rage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Chainbreaker", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/FocusedRage.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_focused_rage", 
			}, 
			["stats"] = {
				["keystone_focused_rage"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9398, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 21, 
			["g"] = 1000000000, 
		}, 
		[83] = {
			["id"] = "maraketh_keystone_1", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "20% less Attack Damage taken if you haven't been Hit by an Attack Recently", 
				[2] = "10% more chance to Evade Attacks if you have been Hit by an Attack Recently", 
				[3] = "20% more Attack Damage taken if you have been Hit by an Attack Recently", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Wind Dancer", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/WindDancer.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_wind_dancer", 
			}, 
			["stats"] = {
				["keystone_wind_dancer"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9424, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 24, 
			["g"] = 1000000000, 
		}, 
		[84] = {
			["id"] = "maraketh_keystone_1_v2", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "Cannot use Charms", 
				[2] = "30% more Recovery from Flasks", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "The Traitor", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/OasisKeystone.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_oasis", 
			}, 
			["stats"] = {
				["keystone_oasis"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9408, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 27, 
			["g"] = 1000000000, 
		}, 
		[85] = {
			["id"] = "maraketh_keystone_2", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "Can't use Helmets", 
				[2] = "Your Critical Hit Chance is Lucky", 
				[3] = "Your Damage with Critical Hits is Lucky", 
				[4] = "Enemies' Damage with Critical Hits against you is Lucky", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Dance with Death", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/SharpandBrittle.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_sharp_and_brittle", 
			}, 
			["stats"] = {
				["keystone_sharp_and_brittle"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9415, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 30, 
			["g"] = 1000000000, 
		}, 
		[86] = {
			["id"] = "maraketh_keystone_3", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "You are Blind", 
				[2] = "Blind does not affect your Light Radius", 
				[3] = "25% more Melee Critical Hit Chance while Blinded", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Second Sight", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/TheBlindMonk.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_blind_monk", 
			}, 
			["stats"] = {
				["keystone_blind_monk"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9389, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 33, 
			["g"] = 1000000000, 
		}, 
		[87] = {
			["id"] = "templar_keystone_1", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "Damage over Time bypasses your Energy Shield", 
				[2] = "While not on Full Life, Sacrifice 1% of Mana per Second to Recover that much Life", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "The Agnostic", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/MiracleMaker.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "unique_body_armour_black_doubt_drain_%_mana_to_recover_life_until_full_and_dot_bypasses_es", 
			}, 
			["stats"] = {
				["unique_body_armour_black_doubt_drain_%_mana_to_recover_life_until_full_and_dot_bypasses_es"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9038, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 36, 
			["g"] = 1000000000, 
		}, 
		[88] = {
			["id"] = "templar_keystone_1_v2", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "Armour applies to Fire, Cold and Lightning Damage taken from Hits instead of Physical Damage", 
				[2] = "-15% to all maximum Elemental Resistances", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Transcendence", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/TranscendenceKeystone.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_prismatic_bulwark", 
			}, 
			["stats"] = {
				["keystone_prismatic_bulwark"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9410, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 39, 
			["g"] = 1000000000, 
		}, 
		[89] = {
			["id"] = "templar_keystone_2", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "3% more Spell Damage per Power Charge", 
				[2] = "Gain Power Charges instead of Frenzy Charges", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Inner Conviction", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/InnerConviction.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_quiet_might", 
			}, 
			["stats"] = {
				["keystone_quiet_might"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9411, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 42, 
			["g"] = 1000000000, 
		}, 
		[90] = {
			["id"] = "templar_keystone_3", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "80% of Maximum Mana is Converted to twice that much Armour", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Power of Purpose", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/PowerOfPurpose.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_mental_conditioning", 
			}, 
			["stats"] = {
				["keystone_mental_conditioning"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9406, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 45, 
			["g"] = 1000000000, 
		}, 
		[91] = {
			["id"] = "templar_devotion_node", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "+10 to Devotion", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Devotion", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/DevotionNode.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_devotion", 
			}, 
			["stats"] = {
				["base_devotion"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 9321, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 6194, 
			["g"] = 1000000000, 
		}, 
		[92] = {
			["id"] = "templar_notable_fire_conversion", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "15% of Physical Damage Converted to Fire Damage while you have at least 150 Devotion", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Heated Devotion", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/DevotionNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "non_skill_physical_damage_%_to_convert_to_fire_at_devotion_threshold", 
			}, 
			["stats"] = {
				["non_skill_physical_damage_%_to_convert_to_fire_at_devotion_threshold"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 15, 
					["statOrder"] = 8106, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 36277, 
			["g"] = 1000000000, 
		}, 
		[93] = {
			["id"] = "templar_notable_cold_conversion", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "15% of Physical Damage Converted to Cold Damage while you have at least 150 Devotion", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Calming Devotion", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/DevotionNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "non_skill_physical_damage_%_to_convert_to_cold_at_devotion_threshold", 
			}, 
			["stats"] = {
				["non_skill_physical_damage_%_to_convert_to_cold_at_devotion_threshold"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 15, 
					["statOrder"] = 8104, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 64088, 
			["g"] = 1000000000, 
		}, 
		[94] = {
			["id"] = "templar_notable_lightning_conversion", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "15% of Physical Damage Converted to Lightning Damage while you have at least 150 Devotion", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Thundrous Devotion", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/DevotionNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "non_skill_physical_damage_%_to_convert_to_lightning_at_devotion_threshold", 
			}, 
			["stats"] = {
				["non_skill_physical_damage_%_to_convert_to_lightning_at_devotion_threshold"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 15, 
					["statOrder"] = 8108, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 94707, 
			["g"] = 1000000000, 
		}, 
		[95] = {
			["id"] = "templar_notable_mana_added_as_energy_shield", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "Gain 5% of Maximum Mana as Extra Maximum Energy Shield while you have at least 150 Devotion", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Thoughts and Prayers", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/DevotionNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "mana_%_to_gain_as_energy_shield_at_devotion_threshold", 
			}, 
			["stats"] = {
				["mana_%_to_gain_as_energy_shield_at_devotion_threshold"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 5, 
					["statOrder"] = 7030, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 74973, 
			["g"] = 1000000000, 
		}, 
		[96] = {
			["id"] = "templar_notable_arcane_surge", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "Gain Arcane Surge on Hit with Spells if you have at least 150 Devotion", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Zealot", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/DevotionNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "gain_arcane_surge_on_hit_at_devotion_threshold", 
			}, 
			["stats"] = {
				["gain_arcane_surge_on_hit_at_devotion_threshold"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 5989, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 10172, 
			["g"] = 1000000000, 
		}, 
		[97] = {
			["id"] = "templar_notable_minimum_endurance_charge", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+1 to Minimum Endurance Charges while you have at least 150 Devotion", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Enduring Faith", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/DevotionNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "minimum_endurance_charges_at_devotion_threshold", 
			}, 
			["stats"] = {
				["minimum_endurance_charges_at_devotion_threshold"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 7877, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 17606, 
			["g"] = 1000000000, 
		}, 
		[98] = {
			["id"] = "templar_notable_minimum_power_charge", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+1 to Minimum Power Charges while you have at least 150 Devotion", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Powerful Faith", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/DevotionNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "minimum_power_charges_at_devotion_threshold", 
			}, 
			["stats"] = {
				["minimum_power_charges_at_devotion_threshold"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 7883, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 5178, 
			["g"] = 1000000000, 
		}, 
		[99] = {
			["id"] = "templar_notable_minimum_frenzy_charge", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+1 to Minimum Frenzy Charges while you have at least 150 Devotion", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Frenzied Faith", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/DevotionNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "minimum_frenzy_charges_at_devotion_threshold", 
			}, 
			["stats"] = {
				["minimum_frenzy_charges_at_devotion_threshold"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 7879, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 22257, 
			["g"] = 1000000000, 
		}, 
		[100] = {
			["id"] = "templar_notable_consecrated_ground_ailments", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "Immune to Elemental Ailments while on Consecrated Ground if you have at least 150 Devotion", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Cloistered", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/DevotionNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "immune_to_elemental_ailments_while_on_consecrated_ground_at_devotion_threshold", 
			}, 
			["stats"] = {
				["immune_to_elemental_ailments_while_on_consecrated_ground_at_devotion_threshold"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 6447, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 14760, 
			["g"] = 1000000000, 
		}, 
		[101] = {
			["id"] = "templar_notable_additional_physical_reduction", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "5% additional Physical Damage Reduction while you have at least 150 Devotion", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Martyr's Might", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/DevotionNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "physical_damage_reduction_%_at_devotion_threshold", 
			}, 
			["stats"] = {
				["physical_damage_reduction_%_at_devotion_threshold"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 5, 
					["statOrder"] = 8222, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 42889, 
			["g"] = 1000000000, 
		}, 
		[102] = {
			["id"] = "templar_notable_max_resistances", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+1% to all maximum Resistances if you have at least 150 Devotion", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Intolerance of Sin", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/DevotionNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "additional_maximum_all_resistances_%_at_devotion_threshold", 
			}, 
			["stats"] = {
				["additional_maximum_all_resistances_%_at_devotion_threshold"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 4099, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 60270, 
			["g"] = 1000000000, 
		}, 
		[103] = {
			["id"] = "templar_notable_fire_exposure", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "10% chance to inflict Fire Exposure on Hit if you have at least 150 Devotion", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Smite the Wicked", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/DevotionNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "inflict_fire_exposure_on_hit_%_chance_at_devotion_threshold", 
			}, 
			["stats"] = {
				["inflict_fire_exposure_on_hit_%_chance_at_devotion_threshold"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 6497, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 27127, 
			["g"] = 1000000000, 
		}, 
		[104] = {
			["id"] = "templar_notable_cold_exposure", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "10% chance to inflict Cold Exposure on Hit if you have at least 150 Devotion", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Smite the Ignorant", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/DevotionNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "inflict_cold_exposure_on_hit_%_chance_at_devotion_threshold", 
			}, 
			["stats"] = {
				["inflict_cold_exposure_on_hit_%_chance_at_devotion_threshold"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 6494, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 82503, 
			["g"] = 1000000000, 
		}, 
		[105] = {
			["id"] = "templar_notable_lightning_exposure", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "10% chance to inflict Lightning Exposure on Hit if you have at least 150 Devotion", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Smite the Heretical", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/DevotionNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "inflict_lightning_exposure_on_hit_%_chance_at_devotion_threshold", 
			}, 
			["stats"] = {
				["inflict_lightning_exposure_on_hit_%_chance_at_devotion_threshold"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 6502, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 28525, 
			["g"] = 1000000000, 
		}, 
		[106] = {
			["id"] = "eternal_keystone_1", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "Life Recovery from Flasks also applies to Energy Shield", 
				[2] = "30% less Life Recovery from Flasks", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Supreme Decadence", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/SupremeDecadence.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_emperors_heart", 
			}, 
			["stats"] = {
				["keystone_emperors_heart"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9396, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 48, 
			["g"] = 1000000000, 
		}, 
		[107] = {
			["id"] = "eternal_keystone_2", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "Nearby Allies and Enemies Share Charges with you", 
				[2] = "Enemies Hitting you have 10% chance to gain an Endurance, ", 
				[3] = "Frenzy or Power Charge", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Supreme Grandstanding", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/SupremeGrandstand.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_magnetic_charge", 
			}, 
			["stats"] = {
				["keystone_magnetic_charge"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9405, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 51, 
			["g"] = 1000000000, 
		}, 
		[108] = {
			["id"] = "eternal_keystone_3", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "Auras from your Skills can only affect you", 
				[2] = "Aura Skills have 1% more Aura Effect per 2% of maximum Mana they Reserve", 
				[3] = "40% more Mana Reservation of Aura Skills", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Supreme Ego", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/SupremeEgo.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_supreme_ego", 
			}, 
			["stats"] = {
				["keystone_supreme_ego"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9418, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 54, 
			["g"] = 1000000000, 
		}, 
		[109] = {
			["id"] = "eternal_keystone_3_v2", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "Ignore Attribute Requirements", 
				[2] = "Gain no inherent bonuses from Attributes", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Supreme Ostentation", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/SupremeProdigy.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_supreme_prodigy", 
			}, 
			["stats"] = {
				["keystone_supreme_prodigy"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9419, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 57, 
			["g"] = 1000000000, 
		}, 
		[110] = {
			["id"] = "eternal_small_blank", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Price of Glory", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireBlank.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
			}, 
			["stats"] = {
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 20196, 
			["g"] = 1000000000, 
		}, 
		[111] = {
			["id"] = "eternal_notable_crit_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "80% increased Critical Hit Chance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Flawless Execution", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "critical_strike_chance_+%", 
			}, 
			["stats"] = {
				["critical_strike_chance_+%"] = {
					["max"] = 80, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 80, 
					["statOrder"] = 9353, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 14977, 
			["g"] = 1000000000, 
		}, 
		[112] = {
			["id"] = "eternal_notable_crit_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "40% increased Critical Damage Bonus", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Brutal Execution", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_critical_strike_multiplier_+", 
			}, 
			["stats"] = {
				["base_critical_strike_multiplier_+"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 929, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 76777, 
			["g"] = 1000000000, 
		}, 
		[113] = {
			["id"] = "eternal_notable_endurance_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "Gain 1 Endurance Charge every second if you've been Hit Recently", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Eternal Resilience", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "gain_endurance_charge_per_second_if_have_been_hit_recently", 
			}, 
			["stats"] = {
				["gain_endurance_charge_per_second_if_have_been_hit_recently"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 6009, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 5183, 
			["g"] = 1000000000, 
		}, 
		[114] = {
			["id"] = "eternal_notable_endurance_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "8% increased Armour per Endurance Charge", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Eternal Fortitude", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "physical_damage_reduction_rating_+%_per_endurance_charge", 
			}, 
			["stats"] = {
				["physical_damage_reduction_rating_+%_per_endurance_charge"] = {
					["max"] = 8, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 8, 
					["statOrder"] = 8231, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 80316, 
			["g"] = 1000000000, 
		}, 
		[115] = {
			["id"] = "eternal_notable_endurance_3", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "10% increased Damage per Endurance Charge", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Eternal Dominance", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "damage_+%_per_endurance_charge", 
			}, 
			["stats"] = {
				["damage_+%_per_endurance_charge"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 2831, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 68905, 
			["g"] = 1000000000, 
		}, 
		[116] = {
			["id"] = "eternal_notable_frenzy_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "10% chance to gain a Frenzy Charge on Hit", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Eternal Fervour", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "add_frenzy_charge_on_skill_hit_%", 
			}, 
			["stats"] = {
				["add_frenzy_charge_on_skill_hit_%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 1522, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 14480, 
			["g"] = 1000000000, 
		}, 
		[117] = {
			["id"] = "eternal_notable_frenzy_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "8% increased Evasion Rating per Frenzy Charge", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Eternal Adaptiveness", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "evasion_rating_+%_per_frenzy_charge", 
			}, 
			["stats"] = {
				["evasion_rating_+%_per_frenzy_charge"] = {
					["max"] = 8, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 8, 
					["statOrder"] = 1371, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 93682, 
			["g"] = 1000000000, 
		}, 
		[118] = {
			["id"] = "eternal_notable_frenzy_3", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "10% increased Damage per Frenzy Charge", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Eternal Bloodlust", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "damage_+%_per_frenzy_charge", 
			}, 
			["stats"] = {
				["damage_+%_per_frenzy_charge"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 2911, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 80835, 
			["g"] = 1000000000, 
		}, 
		[119] = {
			["id"] = "eternal_notable_power_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "15% chance to gain a Power Charge on Critical Hit", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Eternal Subjugation", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "add_power_charge_on_critical_strike_%", 
			}, 
			["stats"] = {
				["add_power_charge_on_critical_strike_%"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 15, 
					["statOrder"] = 1519, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 38654, 
			["g"] = 1000000000, 
		}, 
		[120] = {
			["id"] = "eternal_notable_power_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "4% increased Energy Shield per Power Charge", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Eternal Separation", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "energy_shield_+%_per_power_charge", 
			}, 
			["stats"] = {
				["energy_shield_+%_per_power_charge"] = {
					["max"] = 4, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 4, 
					["statOrder"] = 5727, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 79623, 
			["g"] = 1000000000, 
		}, 
		[121] = {
			["id"] = "eternal_notable_power_3", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "10% increased Damage per Power Charge", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Eternal Exploitation", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "damage_+%_per_power_charge", 
			}, 
			["stats"] = {
				["damage_+%_per_power_charge"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 5374, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 6774, 
			["g"] = 1000000000, 
		}, 
		[122] = {
			["id"] = "eternal_notable_chill_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "30% increased Magnitude of Chill you inflict", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Rites of Lunaris", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "chill_effect_+%", 
			}, 
			["stats"] = {
				["chill_effect_+%"] = {
					["max"] = 30, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 30, 
					["statOrder"] = 9368, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 68329, 
			["g"] = 1000000000, 
		}, 
		[123] = {
			["id"] = "eternal_notable_chill_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "80% chance to Avoid being Chilled", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Rites of Solaris", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_avoid_chill_%", 
			}, 
			["stats"] = {
				["base_avoid_chill_%"] = {
					["max"] = 80, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 80, 
					["statOrder"] = 1534, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 52806, 
			["g"] = 1000000000, 
		}, 
		[124] = {
			["id"] = "eternal_notable_shock_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "30% increased Magnitude of Shock you inflict", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Virtue Gem Surgery", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "shock_effect_+%", 
			}, 
			["stats"] = {
				["shock_effect_+%"] = {
					["max"] = 30, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 30, 
					["statOrder"] = 9381, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 79878, 
			["g"] = 1000000000, 
		}, 
		[125] = {
			["id"] = "eternal_notable_shock_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "80% chance to Avoid being Shocked", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Rural Life", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_avoid_shock_%", 
			}, 
			["stats"] = {
				["base_avoid_shock_%"] = {
					["max"] = 80, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 80, 
					["statOrder"] = 1538, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 7265, 
			["g"] = 1000000000, 
		}, 
		[126] = {
			["id"] = "eternal_notable_block_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+8% to Block chance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "City Walls", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "additional_block_%", 
			}, 
			["stats"] = {
				["additional_block_%"] = {
					["max"] = 8, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 8, 
					["statOrder"] = 2132, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 51502, 
			["g"] = 1000000000, 
		}, 
		[127] = {
			["id"] = "eternal_notable_block_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "8% Chance to Block Spell Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Sceptre Pinnacle", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_spell_block_%", 
			}, 
			["stats"] = {
				["base_spell_block_%"] = {
					["max"] = 8, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 8, 
					["statOrder"] = 864, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 69709, 
			["g"] = 1000000000, 
		}, 
		[128] = {
			["id"] = "eternal_notable_dodge_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "20% chance to Avoid Elemental Ailments", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Secret Tunnels", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "avoid_all_elemental_status_%", 
			}, 
			["stats"] = {
				["avoid_all_elemental_status_%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 1533, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 54984, 
			["g"] = 1000000000, 
		}, 
		[129] = {
			["id"] = "eternal_notable_dodge_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+12% chance to Suppress Spell Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Purity Rebel", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_spell_suppression_chance_%", 
			}, 
			["stats"] = {
				["base_spell_suppression_chance_%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 12, 
					["statOrder"] = 954, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 9731, 
			["g"] = 1000000000, 
		}, 
		[130] = {
			["id"] = "eternal_notable_aura_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "12% increased effect of Non-Curse Auras from your Skills", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Superiority", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "non_curse_aura_effect_+%", 
			}, 
			["stats"] = {
				["non_curse_aura_effect_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 12, 
					["statOrder"] = 3181, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 65124, 
			["g"] = 1000000000, 
		}, 
		[131] = {
			["id"] = "eternal_notable_minion_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "Minions deal 80% increased Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Slum Lord", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "minion_damage_+%", 
			}, 
			["stats"] = {
				["minion_damage_+%"] = {
					["max"] = 80, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 80, 
					["statOrder"] = 1655, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 7309, 
			["g"] = 1000000000, 
		}, 
		[132] = {
			["id"] = "eternal_notable_minion_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "Minions have 80% increased maximum Life", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Axiom Warden", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "minion_maximum_life_+%", 
			}, 
			["stats"] = {
				["minion_maximum_life_+%"] = {
					["max"] = 80, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 80, 
					["statOrder"] = 955, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 19927, 
			["g"] = 1000000000, 
		}, 
		[133] = {
			["id"] = "eternal_notable_spell_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "80% increased Spell Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Gemling Inquisition", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "spell_damage_+%", 
			}, 
			["stats"] = {
				["spell_damage_+%"] = {
					["max"] = 80, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 80, 
					["statOrder"] = 854, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 80563, 
			["g"] = 1000000000, 
		}, 
		[134] = {
			["id"] = "eternal_notable_spell_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "80% increased Critical Hit Chance for Spells", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Gemling Ambush", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "spell_critical_strike_chance_+%", 
			}, 
			["stats"] = {
				["spell_critical_strike_chance_+%"] = {
					["max"] = 80, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 80, 
					["statOrder"] = 927, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 68382, 
			["g"] = 1000000000, 
		}, 
		[135] = {
			["id"] = "eternal_notable_fire_attack_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "80% increased Fire Damage with Attack Skills", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Night of a Thousand Ribbons", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "fire_damage_with_attack_skills_+%", 
			}, 
			["stats"] = {
				["fire_damage_with_attack_skills_+%"] = {
					["max"] = 80, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 80, 
					["statOrder"] = 5843, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 99311, 
			["g"] = 1000000000, 
		}, 
		[136] = {
			["id"] = "eternal_notable_cold_attack_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "80% increased Cold Damage with Attack Skills", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Bloody Flowers' Rebellion", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "cold_damage_with_attack_skills_+%", 
			}, 
			["stats"] = {
				["cold_damage_with_attack_skills_+%"] = {
					["max"] = 80, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 80, 
					["statOrder"] = 5126, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 67225, 
			["g"] = 1000000000, 
		}, 
		[137] = {
			["id"] = "eternal_notable_lightning_attack_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "80% increased Lightning Damage with Attack Skills", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Chitus' Heart", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "lightning_damage_with_attack_skills_+%", 
			}, 
			["stats"] = {
				["lightning_damage_with_attack_skills_+%"] = {
					["max"] = 80, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 80, 
					["statOrder"] = 6653, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 82524, 
			["g"] = 1000000000, 
		}, 
		[138] = {
			["id"] = "eternal_notable_physical_damage_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "80% increased Physical Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Gemling Training", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "physical_damage_+%", 
			}, 
			["stats"] = {
				["physical_damage_+%"] = {
					["max"] = 80, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 80, 
					["statOrder"] = 9380, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 98014, 
			["g"] = 1000000000, 
		}, 
		[139] = {
			["id"] = "eternal_notable_physical_damage_2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "80% increased Melee Physical Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Rigwald's Might", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "melee_physical_damage_+%", 
			}, 
			["stats"] = {
				["melee_physical_damage_+%"] = {
					["max"] = 80, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 80, 
					["statOrder"] = 1661, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 36656, 
			["g"] = 1000000000, 
		}, 
		[140] = {
			["id"] = "eternal_notable_bleed_damage_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "Bleeding you inflict deals Damage 10% faster", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Geofri's End", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "faster_bleed_%", 
			}, 
			["stats"] = {
				["faster_bleed_%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 5814, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 36158, 
			["g"] = 1000000000, 
		}, 
		[141] = {
			["id"] = "eternal_notable_projectile_attack_damage_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "80% increased Projectile Attack Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Lioneye's Focus", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "projectile_attack_damage_+%", 
			}, 
			["stats"] = {
				["projectile_attack_damage_+%"] = {
					["max"] = 80, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 80, 
					["statOrder"] = 1676, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 94297, 
			["g"] = 1000000000, 
		}, 
		[142] = {
			["id"] = "eternal_notable_attack_speed_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "15% increased Attack Speed", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Voll's Coup", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "attack_speed_+%", 
			}, 
			["stats"] = {
				["attack_speed_+%"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 15, 
					["statOrder"] = 933, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 94732, 
			["g"] = 1000000000, 
		}, 
		[143] = {
			["id"] = "eternal_notable_cast_speed_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "15% increased Cast Speed", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Dialla's Wit", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_cast_speed_+%", 
			}, 
			["stats"] = {
				["base_cast_speed_+%"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 15, 
					["statOrder"] = 934, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 81833, 
			["g"] = 1000000000, 
		}, 
		[144] = {
			["id"] = "eternal_notable_rarity_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "40% increased Rarity of Items found", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Discerning Taste", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_item_found_rarity_+%", 
			}, 
			["stats"] = {
				["base_item_found_rarity_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 911, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 86198, 
			["g"] = 1000000000, 
		}, 
		[145] = {
			["id"] = "eternal_notable_armour_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "80% increased Armour", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Gleaming Legion", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "physical_damage_reduction_rating_+%", 
			}, 
			["stats"] = {
				["physical_damage_reduction_rating_+%"] = {
					["max"] = 80, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 80, 
					["statOrder"] = 866, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 67997, 
			["g"] = 1000000000, 
		}, 
		[146] = {
			["id"] = "eternal_notable_evasion_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "80% increased Evasion Rating", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Shadowy Streets", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "evasion_rating_+%", 
			}, 
			["stats"] = {
				["evasion_rating_+%"] = {
					["max"] = 80, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 80, 
					["statOrder"] = 868, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 65414, 
			["g"] = 1000000000, 
		}, 
		[147] = {
			["id"] = "eternal_notable_fire_resistance_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+50% to Fire Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Crematorium Worker", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_fire_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_fire_damage_resistance_%"] = {
					["max"] = 50, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 50, 
					["statOrder"] = 950, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 54300, 
			["g"] = 1000000000, 
		}, 
		[148] = {
			["id"] = "eternal_notable_cold_resistance_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+50% to Cold Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Street Urchin", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_cold_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_cold_damage_resistance_%"] = {
					["max"] = 50, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 50, 
					["statOrder"] = 951, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 88577, 
			["g"] = 1000000000, 
		}, 
		[149] = {
			["id"] = "eternal_notable_lightning_resistance_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+50% to Lightning Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Baleful Augmentation", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_lightning_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_lightning_damage_resistance_%"] = {
					["max"] = 50, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 50, 
					["statOrder"] = 952, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 40681, 
			["g"] = 1000000000, 
		}, 
		[150] = {
			["id"] = "eternal_notable_chaos_resistance_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+37% to Chaos Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "With Eyes Open", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_chaos_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_chaos_damage_resistance_%"] = {
					["max"] = 37, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 37, 
					["statOrder"] = 953, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 5337, 
			["g"] = 1000000000, 
		}, 
		[151] = {
			["id"] = "eternal_notable_life_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "10% increased maximum Life", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Robust Diet", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "maximum_life_+%", 
			}, 
			["stats"] = {
				["maximum_life_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 872, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 35290, 
			["g"] = 1000000000, 
		}, 
		[152] = {
			["id"] = "eternal_notable_mana_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "30% increased maximum Mana", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Pooled Resources", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "maximum_mana_+%", 
			}, 
			["stats"] = {
				["maximum_mana_+%"] = {
					["max"] = 30, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 30, 
					["statOrder"] = 874, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 1685, 
			["g"] = 1000000000, 
		}, 
		[153] = {
			["id"] = "eternal_notable_mana_regen_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "50% increased Mana Regeneration Rate", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Laureate", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "mana_regeneration_rate_+%", 
			}, 
			["stats"] = {
				["mana_regeneration_rate_+%"] = {
					["max"] = 50, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 50, 
					["statOrder"] = 965, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 9588, 
			["g"] = 1000000000, 
		}, 
		[154] = {
			["id"] = "eternal_notable_accuracy_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "25% increased Accuracy Rating", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "War Games", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireOffensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "accuracy_rating_+%", 
			}, 
			["stats"] = {
				["accuracy_rating_+%"] = {
					["max"] = 25, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 1270, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 56816, 
			["g"] = 1000000000, 
		}, 
		[155] = {
			["id"] = "eternal_notable_flask_duration_1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "20% increased Flask Effect Duration", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Freshly Brewed", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/EternalEmpireDefensiveNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "flask_duration_+%", 
			}, 
			["stats"] = {
				["flask_duration_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 881, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 66099, 
			["g"] = 1000000000, 
		}, 
		[156] = {
			["id"] = "kalguur_keystone_1", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "Strength's inherent bonus is 1% increased Energy Shield per 2 Strength instead", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Circular Teachings", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranStrKeystone.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_alternate_strength_bonus", 
			}, 
			["stats"] = {
				["keystone_alternate_strength_bonus"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9387, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 60, 
			["g"] = 1000000000, 
		}, 
		[157] = {
			["id"] = "kalguur_keystone_2", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "Dexterity's inherent bonus is 1% increased Armour per 2 Dexterity instead", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Knightly Tenets", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranDexKeystone.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_alternate_dexterity_bonus", 
			}, 
			["stats"] = {
				["keystone_alternate_dexterity_bonus"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9385, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 63, 
			["g"] = 1000000000, 
		}, 
		[158] = {
			["id"] = "kalguur_keystone_3", 
			["in"] = {
			}, 
			["not"] = false, 
			["sd"] = {
				[1] = "Intelligence's inherent bonus is 1% increased Evasion Rating per 2 Intelligence instead", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Black Scythe Training", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranIntKeystone.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = true, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "keystone_alternate_intelligence_bonus", 
			}, 
			["stats"] = {
				["keystone_alternate_intelligence_bonus"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 9386, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 4, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 66, 
			["g"] = 1000000000, 
		}, 
		[159] = {
			["id"] = "kalguur_notable1", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "40% increased Fire Damage", 
				[2] = "+15 to Intelligence", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Scorched Earth", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranStrNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "fire_damage_+%", 
				[2] = "base_intelligence", 
			}, 
			["stats"] = {
				["base_intelligence"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 15, 
					["statOrder"] = 9326, 
				}, 
				["fire_damage_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 856, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 79440, 
			["g"] = 1000000000, 
		}, 
		[160] = {
			["id"] = "kalguur_notable2", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "20% increased Mana Regeneration Rate", 
				[2] = "40% increased Physical Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "War Tactics", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranStrNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "mana_regeneration_rate_+%", 
				[2] = "physical_damage_+%", 
			}, 
			["stats"] = {
				["physical_damage_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 9380, 
				}, 
				["mana_regeneration_rate_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 20, 
					["statOrder"] = 965, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 97049, 
			["g"] = 1000000000, 
		}, 
		[161] = {
			["id"] = "kalguur_notable3", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+2% to Maximum Fire Resistance", 
				[2] = "10% reduced Freeze Duration on you", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Kalguuran Forged", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranStrNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_maximum_fire_damage_resistance_%", 
				[2] = "base_self_freeze_duration_-%", 
			}, 
			["stats"] = {
				["base_maximum_fire_damage_resistance_%"] = {
					["max"] = 2, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 2, 
					["statOrder"] = 945, 
				}, 
				["base_self_freeze_duration_-%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 988, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 33869, 
			["g"] = 1000000000, 
		}, 
		[162] = {
			["id"] = "kalguur_notable4", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "40% increased Armour", 
				[2] = "+10% to Cold Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Born of Middengard", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranStrNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "physical_damage_reduction_rating_+%", 
				[2] = "base_cold_damage_resistance_%", 
			}, 
			["stats"] = {
				["physical_damage_reduction_rating_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 866, 
				}, 
				["base_cold_damage_resistance_%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 951, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 20074, 
			["g"] = 1000000000, 
		}, 
		[163] = {
			["id"] = "kalguur_notable5", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "2% increased maximum Mana", 
				[2] = "30% increased Stun Buildup", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Force of Will", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranStrNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "maximum_mana_+%", 
				[2] = "hit_damage_stun_multiplier_+%", 
			}, 
			["stats"] = {
				["maximum_mana_+%"] = {
					["max"] = 2, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 2, 
					["statOrder"] = 874, 
				}, 
				["hit_damage_stun_multiplier_+%"] = {
					["max"] = 30, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 30, 
					["statOrder"] = 973, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 29349, 
			["g"] = 1000000000, 
		}, 
		[164] = {
			["id"] = "kalguur_notable6", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "10% increased Curse Duration", 
				[2] = "Regenerate 1.5% of Life per second", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Runic Flows", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranStrNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_curse_duration_+%", 
				[2] = "life_regeneration_rate_per_minute_%", 
			}, 
			["stats"] = {
				["base_curse_duration_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 1472, 
				}, 
				["life_regeneration_rate_per_minute_%"] = {
					["max"] = 1.5, 
					["fmt"] = "g", 
					["index"] = 1, 
					["min"] = 1.5, 
					["statOrder"] = 1627, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 39254, 
			["g"] = 1000000000, 
		}, 
		[165] = {
			["id"] = "kalguur_notable7", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "10% faster start of Energy Shield Recharge", 
				[2] = "Break 40% increased Armour", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Siege Mentality", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranStrNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "energy_shield_delay_-%", 
				[2] = "armour_break_amount_+%", 
			}, 
			["stats"] = {
				["armour_break_amount_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 4253, 
				}, 
				["energy_shield_delay_-%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 1372, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 74833, 
			["g"] = 1000000000, 
		}, 
		[166] = {
			["id"] = "kalguur_notable8", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "15% increased Critical Spell Damage Bonus", 
				[2] = "40% increased Melee Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Rune Knight", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranStrNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_spell_critical_strike_multiplier_+", 
				[2] = "melee_damage_+%", 
			}, 
			["stats"] = {
				["base_spell_critical_strike_multiplier_+"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 15, 
					["statOrder"] = 931, 
				}, 
				["melee_damage_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 1126, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 56253, 
			["g"] = 1000000000, 
		}, 
		[167] = {
			["id"] = "kalguur_notable9", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "40% increased chance to Ignite", 
				[2] = "Minions have +15% to all Elemental Resistances", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Fiery Leadership", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranStrNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "ignite_chance_+%", 
				[2] = "minion_elemental_resistance_%", 
			}, 
			["stats"] = {
				["minion_elemental_resistance_%"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 15, 
					["statOrder"] = 2562, 
				}, 
				["ignite_chance_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 978, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 82715, 
			["g"] = 1000000000, 
		}, 
		[168] = {
			["id"] = "kalguur_notable10", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+7% to Chaos Resistance", 
				[2] = "40% increased Totem Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Druidic Alliance", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranStrNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_chaos_damage_resistance_%", 
				[2] = "totem_damage_+%", 
			}, 
			["stats"] = {
				["totem_damage_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 1091, 
				}, 
				["base_chaos_damage_resistance_%"] = {
					["max"] = 7, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 7, 
					["statOrder"] = 953, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 52436, 
			["g"] = 1000000000, 
		}, 
		[169] = {
			["id"] = "kalguur_notable11", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "10% increased effect of Arcane Surge on you", 
				[2] = "20% increased Magnitude of Bleeding you inflict", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Steel and Sorcery", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranStrNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "arcane_surge_effect_on_self_+%", 
				[2] = "base_bleeding_effect_+%", 
			}, 
			["stats"] = {
				["arcane_surge_effect_on_self_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 2913, 
				}, 
				["base_bleeding_effect_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 4554, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 2468, 
			["g"] = 1000000000, 
		}, 
		[170] = {
			["id"] = "kalguur_notable12", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "10% increased Cold Exposure Effect", 
				[2] = "Empowered Attacks deal 50% increased Damage", 
				[3] = "10% increased Fire Exposure Effect", 
				[4] = "10% increased Lightning Exposure Effect", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Triskelion's Light", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranStrNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "cold_exposure_effect_+%", 
				[2] = "empowered_attack_damage_+%", 
				[3] = "fire_exposure_effect_+%", 
				[4] = "lightning_exposure_effect_+%", 
			}, 
			["stats"] = {
				["lightning_exposure_effect_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 6658, 
				}, 
				["cold_exposure_effect_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 3, 
					["min"] = 10, 
					["statOrder"] = 5128, 
				}, 
				["empowered_attack_damage_+%"] = {
					["max"] = 50, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 50, 
					["statOrder"] = 5646, 
				}, 
				["fire_exposure_effect_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 4, 
					["min"] = 10, 
					["statOrder"] = 5845, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 30208, 
			["g"] = 1000000000, 
		}, 
		[171] = {
			["id"] = "kalguur_notable13", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+2% to Maximum Lightning Resistance", 
				[2] = "10% reduced Ignite Duration on you", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Stormtossed Voyager", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranDexNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_maximum_lightning_damage_resistance_%", 
				[2] = "base_self_ignite_duration_-%", 
			}, 
			["stats"] = {
				["base_self_ignite_duration_-%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 986, 
				}, 
				["base_maximum_lightning_damage_resistance_%"] = {
					["max"] = 2, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 2, 
					["statOrder"] = 947, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 29546, 
			["g"] = 1000000000, 
		}, 
		[172] = {
			["id"] = "kalguur_notable14", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "40% increased Lightning Damage", 
				[2] = "+15 to Strength", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Wrest Control", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranDexNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "lightning_damage_+%", 
				[2] = "base_strength", 
			}, 
			["stats"] = {
				["lightning_damage_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 858, 
				}, 
				["base_strength"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 15, 
					["statOrder"] = 9322, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 20764, 
			["g"] = 1000000000, 
		}, 
		[173] = {
			["id"] = "kalguur_notable15", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "40% increased Evasion Rating", 
				[2] = "+10% to Fire Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Firedancer", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranDexNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "evasion_rating_+%", 
				[2] = "base_fire_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_fire_damage_resistance_%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 950, 
				}, 
				["evasion_rating_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 868, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 7170, 
			["g"] = 1000000000, 
		}, 
		[174] = {
			["id"] = "kalguur_notable16", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "10% increased Attack Speed", 
				[2] = "15% increased Stun Threshold", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Mercenary's Lot", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranDexNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "attack_speed_+%", 
				[2] = "stun_threshold_+%", 
			}, 
			["stats"] = {
				["stun_threshold_+%"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 15, 
					["statOrder"] = 2900, 
				}, 
				["attack_speed_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 933, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 95734, 
			["g"] = 1000000000, 
		}, 
		[175] = {
			["id"] = "kalguur_notable17", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "40% increased Projectile Damage", 
				[2] = "Gain 1 Rage on Melee Hit", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Vorana's Fury", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranDexNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "projectile_damage_+%", 
				[2] = "gain_x_rage_on_melee_hit", 
			}, 
			["stats"] = {
				["projectile_damage_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 1675, 
				}, 
				["gain_x_rage_on_melee_hit"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 1, 
					["statOrder"] = 6085, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 81354, 
			["g"] = 1000000000, 
		}, 
		[176] = {
			["id"] = "kalguur_notable18", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "6% increased Block chance", 
				[2] = "40% increased Life Recovery from Flasks", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Sensible Precautions", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranDexNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "block_chance_+%", 
				[2] = "flask_life_to_recover_+%", 
			}, 
			["stats"] = {
				["flask_life_to_recover_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 1730, 
				}, 
				["block_chance_+%"] = {
					["max"] = 6, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 6, 
					["statOrder"] = 1068, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 78304, 
			["g"] = 1000000000, 
		}, 
		[177] = {
			["id"] = "kalguur_notable19", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "30% increased Accuracy Rating", 
				[2] = "10% increased Magnitude of Bleeding you inflict", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Aim for the Jugular", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranDexNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "accuracy_rating_+%", 
				[2] = "base_bleeding_effect_+%", 
			}, 
			["stats"] = {
				["accuracy_rating_+%"] = {
					["max"] = 30, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 30, 
					["statOrder"] = 1270, 
				}, 
				["base_bleeding_effect_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 4554, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 15373, 
			["g"] = 1000000000, 
		}, 
		[178] = {
			["id"] = "kalguur_notable20", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "2% increased maximum Life", 
				[2] = "40% increased Mana Recovery from Flasks", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Survival Plan", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranDexNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "maximum_life_+%", 
				[2] = "flask_mana_to_recover_+%", 
			}, 
			["stats"] = {
				["flask_mana_to_recover_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 1731, 
				}, 
				["maximum_life_+%"] = {
					["max"] = 2, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 2, 
					["statOrder"] = 872, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 27792, 
			["g"] = 1000000000, 
		}, 
		[179] = {
			["id"] = "kalguur_notable21", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "10% increased amount of Life Leeched", 
				[2] = "20% increased Magnitude of Poison you inflict", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "War of Attrition", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranDexNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_life_leech_amount_+%", 
				[2] = "base_poison_effect_+%", 
			}, 
			["stats"] = {
				["base_poison_effect_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 8263, 
				}, 
				["base_life_leech_amount_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 1831, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 27192, 
			["g"] = 1000000000, 
		}, 
		[180] = {
			["id"] = "kalguur_notable22", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "40% increased chance to Shock", 
				[2] = "15% increased Totem Life", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Guerilla Warfare", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranDexNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "shock_chance_+%", 
				[2] = "totem_life_+%", 
			}, 
			["stats"] = {
				["totem_life_+%"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 15, 
					["statOrder"] = 1465, 
				}, 
				["shock_chance_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 982, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 45680, 
			["g"] = 1000000000, 
		}, 
		[181] = {
			["id"] = "kalguur_notable23", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "30% increased Flask Effect Duration", 
				[2] = "15% increased Warcry Cooldown Recovery Rate", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "One for the Road", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranDexNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "flask_duration_+%", 
				[2] = "warcry_cooldown_speed_+%", 
			}, 
			["stats"] = {
				["flask_duration_+%"] = {
					["max"] = 30, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 30, 
					["statOrder"] = 881, 
				}, 
				["warcry_cooldown_speed_+%"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 15, 
					["statOrder"] = 2953, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 79160, 
			["g"] = 1000000000, 
		}, 
		[182] = {
			["id"] = "kalguur_notable24", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "8% increased Skill Effect Duration", 
				[2] = "20% increased Effect of your Mark Skills", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Targeted Strike", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranDexNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "skill_effect_duration_+%", 
				[2] = "mark_effect_+%", 
			}, 
			["stats"] = {
				["skill_effect_duration_+%"] = {
					["max"] = 8, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 8, 
					["statOrder"] = 1579, 
				}, 
				["mark_effect_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 2268, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 64617, 
			["g"] = 1000000000, 
		}, 
		[183] = {
			["id"] = "kalguur_notable25", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "40% increased Elemental Damage", 
				[2] = "20% increased Defences from Equipped Shield", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Steel Bastion", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranDexNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "elemental_damage_+%", 
				[2] = "shield_armour_+%", 
			}, 
			["stats"] = {
				["shield_armour_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 20, 
					["statOrder"] = 1673, 
				}, 
				["elemental_damage_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 1660, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 27124, 
			["g"] = 1000000000, 
		}, 
		[184] = {
			["id"] = "kalguur_notable26", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "10% increased Cast Speed", 
				[2] = "10% chance to Pierce an Enemy", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Forceful Energies", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranIntNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_cast_speed_+%", 
				[2] = "base_chance_to_pierce_%", 
			}, 
			["stats"] = {
				["base_cast_speed_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 934, 
				}, 
				["base_chance_to_pierce_%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 991, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 82620, 
			["g"] = 1000000000, 
		}, 
		[185] = {
			["id"] = "kalguur_notable27", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "40% increased Cold Damage", 
				[2] = "+15 to Dexterity", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Winter Forest", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranIntNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "cold_damage_+%", 
				[2] = "base_dexterity", 
			}, 
			["stats"] = {
				["cold_damage_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 857, 
				}, 
				["base_dexterity"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 15, 
					["statOrder"] = 9324, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 84022, 
			["g"] = 1000000000, 
		}, 
		[186] = {
			["id"] = "kalguur_notable28", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "8% increased Accuracy Rating", 
				[2] = "Minions deal 40% increased Damage", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Fight as One", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranIntNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "accuracy_rating_+%", 
				[2] = "minion_damage_+%", 
			}, 
			["stats"] = {
				["accuracy_rating_+%"] = {
					["max"] = 8, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 8, 
					["statOrder"] = 1270, 
				}, 
				["minion_damage_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 1655, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 37694, 
			["g"] = 1000000000, 
		}, 
		[187] = {
			["id"] = "kalguur_notable29", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "15% increased Evasion Rating", 
				[2] = "Minions have 30% increased maximum Life", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Summer Meadows", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranIntNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "evasion_rating_+%", 
				[2] = "minion_maximum_life_+%", 
			}, 
			["stats"] = {
				["evasion_rating_+%"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 15, 
					["statOrder"] = 868, 
				}, 
				["minion_maximum_life_+%"] = {
					["max"] = 30, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 30, 
					["statOrder"] = 955, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 88895, 
			["g"] = 1000000000, 
		}, 
		[188] = {
			["id"] = "kalguur_notable30", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "40% increased Spell Damage", 
				[2] = "10% increased Flask and Charm Charges gained", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Druidic Training", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranIntNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "spell_damage_+%", 
				[2] = "charges_gained_+%", 
			}, 
			["stats"] = {
				["spell_damage_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 854, 
				}, 
				["charges_gained_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 970, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 78681, 
			["g"] = 1000000000, 
		}, 
		[189] = {
			["id"] = "kalguur_notable31", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "30% increased Critical Hit Chance for Spells", 
				[2] = "5% chance to Blind Enemies on Hit", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Corrupted Vision", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranIntNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "spell_critical_strike_chance_+%", 
				[2] = "global_chance_to_blind_on_hit_%", 
			}, 
			["stats"] = {
				["global_chance_to_blind_on_hit_%"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 5, 
					["statOrder"] = 9362, 
				}, 
				["spell_critical_strike_chance_+%"] = {
					["max"] = 30, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 30, 
					["statOrder"] = 927, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 55867, 
			["g"] = 1000000000, 
		}, 
		[190] = {
			["id"] = "kalguur_notable32", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "30% increased Mana Regeneration Rate", 
				[2] = "15% increased Elemental Ailment Threshold", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Runic Tattoos", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranIntNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "mana_regeneration_rate_+%", 
				[2] = "ailment_threshold_+%", 
			}, 
			["stats"] = {
				["ailment_threshold_+%"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 15, 
					["statOrder"] = 4149, 
				}, 
				["mana_regeneration_rate_+%"] = {
					["max"] = 30, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 30, 
					["statOrder"] = 965, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 79194, 
			["g"] = 1000000000, 
		}, 
		[191] = {
			["id"] = "kalguur_notable33", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "+2% to Maximum Cold Resistance", 
				[2] = "10% reduced Shock duration on you", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Furs and Leather", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranIntNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_maximum_cold_damage_resistance_%", 
				[2] = "base_self_shock_duration_-%", 
			}, 
			["stats"] = {
				["base_self_shock_duration_-%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 989, 
				}, 
				["base_maximum_cold_damage_resistance_%"] = {
					["max"] = 2, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 2, 
					["statOrder"] = 946, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 1855, 
			["g"] = 1000000000, 
		}, 
		[192] = {
			["id"] = "kalguur_notable34", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "8% increased Projectile Speed", 
				[2] = "30% increased Freeze Buildup", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Sudden Hail", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranIntNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "base_projectile_speed_+%", 
				[2] = "hit_damage_freeze_multiplier_+%", 
			}, 
			["stats"] = {
				["hit_damage_freeze_multiplier_+%"] = {
					["max"] = 30, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 30, 
					["statOrder"] = 980, 
				}, 
				["base_projectile_speed_+%"] = {
					["max"] = 8, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 8, 
					["statOrder"] = 877, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 55548, 
			["g"] = 1000000000, 
		}, 
		[193] = {
			["id"] = "kalguur_notable35", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "40% increased maximum Energy Shield", 
				[2] = "+10% to Lightning Resistance", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Natural Energies", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranIntNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "maximum_energy_shield_+%", 
				[2] = "base_lightning_damage_resistance_%", 
			}, 
			["stats"] = {
				["maximum_energy_shield_+%"] = {
					["max"] = 40, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 40, 
					["statOrder"] = 870, 
				}, 
				["base_lightning_damage_resistance_%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 952, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 87796, 
			["g"] = 1000000000, 
		}, 
		[194] = {
			["id"] = "kalguur_notable36", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "10% increased Charm Effect Duration", 
				[2] = "10% of Damage taken Recouped as Life", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Oaken Form", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranIntNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "charm_duration_+%", 
				[2] = "damage_taken_goes_to_life_over_4_seconds_%", 
			}, 
			["stats"] = {
				["charm_duration_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 10, 
					["statOrder"] = 880, 
				}, 
				["damage_taken_goes_to_life_over_4_seconds_%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 959, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 29714, 
			["g"] = 1000000000, 
		}, 
		[195] = {
			["id"] = "kalguur_notable37", 
			["in"] = {
			}, 
			["not"] = true, 
			["sd"] = {
				[1] = "37% increased Chaos Damage", 
				[2] = "Projectiles have 6% chance to Chain an additional time from terrain", 
			}, 
			["isMultipleChoiceOption"] = false, 
			["dn"] = "Spider's Lesson", 
			["isJewelSocket"] = false, 
			["m"] = false, 
			["icon"] = "Art/2DArt/SkillIcons/passives/KalguuranIntNotable.dds", 
			["isMultipleChoice"] = false, 
			["ks"] = false, 
			["passivePointsGranted"] = 0, 
			["sortedStats"] = {
				[1] = "chaos_damage_+%", 
				[2] = "projectile_chance_to_chain_1_extra_time_from_terrain_%", 
			}, 
			["stats"] = {
				["projectile_chance_to_chain_1_extra_time_from_terrain_%"] = {
					["max"] = 6, 
					["fmt"] = "d", 
					["index"] = 2, 
					["min"] = 6, 
					["statOrder"] = 8306, 
				}, 
				["chaos_damage_+%"] = {
					["max"] = 37, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 37, 
					["statOrder"] = 859, 
				}, 
			}, 
			["da"] = 0, 
			["o"] = 3, 
			["sa"] = 0, 
			["out"] = {
			}, 
			["ia"] = 0, 
			["spc"] = {
			}, 
			["oidx"] = 51555, 
			["g"] = 1000000000, 
		}, 
	}, 
	["additions"] = {
		[1] = {
			["id"] = "vaal_small_fire_damage", 
			["dn"] = "Fire Damage", 
			["sd"] = {
				[1] = "(7-12)% increased Fire Damage", 
			}, 
			["sortedStats"] = {
				[1] = "fire_damage_+%", 
			}, 
			["stats"] = {
				["fire_damage_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 856, 
				}, 
			}, 
		}, 
		[2] = {
			["id"] = "vaal_small_cold_damage", 
			["dn"] = "Cold Damage", 
			["sd"] = {
				[1] = "(7-12)% increased Cold Damage", 
			}, 
			["sortedStats"] = {
				[1] = "cold_damage_+%", 
			}, 
			["stats"] = {
				["cold_damage_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 857, 
				}, 
			}, 
		}, 
		[3] = {
			["id"] = "vaal_small_lightning_damage", 
			["dn"] = "Lightning Damage", 
			["sd"] = {
				[1] = "(7-12)% increased Lightning Damage", 
			}, 
			["sortedStats"] = {
				[1] = "lightning_damage_+%", 
			}, 
			["stats"] = {
				["lightning_damage_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 858, 
				}, 
			}, 
		}, 
		[4] = {
			["id"] = "vaal_small_physical_damage", 
			["dn"] = "Physical Damage", 
			["sd"] = {
				[1] = "(7-12)% increased Physical Damage", 
			}, 
			["sortedStats"] = {
				[1] = "physical_damage_+%", 
			}, 
			["stats"] = {
				["physical_damage_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 9380, 
				}, 
			}, 
		}, 
		[5] = {
			["id"] = "vaal_small_chaos_damage", 
			["dn"] = "Chaos Damage", 
			["sd"] = {
				[1] = "(7-12)% increased Chaos Damage", 
			}, 
			["sortedStats"] = {
				[1] = "chaos_damage_+%", 
			}, 
			["stats"] = {
				["chaos_damage_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 859, 
				}, 
			}, 
		}, 
		[6] = {
			["id"] = "vaal_small_minion_damage", 
			["dn"] = "Minion Damage", 
			["sd"] = {
				[1] = "Minions deal (8-13)% increased Damage", 
			}, 
			["sortedStats"] = {
				[1] = "minion_damage_+%", 
			}, 
			["stats"] = {
				["minion_damage_+%"] = {
					["max"] = 13, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 8, 
					["statOrder"] = 1655, 
				}, 
			}, 
		}, 
		[7] = {
			["id"] = "vaal_small_attack_damage", 
			["dn"] = "Attack Damage", 
			["sd"] = {
				[1] = "(7-12)% increased Attack Damage", 
			}, 
			["sortedStats"] = {
				[1] = "attack_damage_+%", 
			}, 
			["stats"] = {
				["attack_damage_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 1095, 
				}, 
			}, 
		}, 
		[8] = {
			["id"] = "vaal_small_spell_damage", 
			["dn"] = "Spell Damage", 
			["sd"] = {
				[1] = "(7-12)% increased Spell Damage", 
			}, 
			["sortedStats"] = {
				[1] = "spell_damage_+%", 
			}, 
			["stats"] = {
				["spell_damage_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 854, 
				}, 
			}, 
		}, 
		[9] = {
			["id"] = "vaal_small_area_damage", 
			["dn"] = "Area Damage", 
			["sd"] = {
				[1] = "(7-12)% increased Area Damage", 
			}, 
			["sortedStats"] = {
				[1] = "area_damage_+%", 
			}, 
			["stats"] = {
				["area_damage_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 1710, 
				}, 
			}, 
		}, 
		[10] = {
			["id"] = "vaal_small_projectile_damage", 
			["dn"] = "Projectile Damage", 
			["sd"] = {
				[1] = "(7-12)% increased Projectile Damage", 
			}, 
			["sortedStats"] = {
				[1] = "projectile_damage_+%", 
			}, 
			["stats"] = {
				["projectile_damage_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 1675, 
				}, 
			}, 
		}, 
		[11] = {
			["id"] = "vaal_small_damage_over_time", 
			["dn"] = "Damage Over Time", 
			["sd"] = {
				[1] = "(7-12)% increased Damage over Time", 
			}, 
			["sortedStats"] = {
				[1] = "damage_over_time_+%", 
			}, 
			["stats"] = {
				["damage_over_time_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 1106, 
				}, 
			}, 
		}, 
		[12] = {
			["id"] = "vaal_small_area_of_effect", 
			["dn"] = "Area Of Effect", 
			["sd"] = {
				[1] = "(4-7)% increased Area of Effect", 
			}, 
			["sortedStats"] = {
				[1] = "base_skill_area_of_effect_+%", 
			}, 
			["stats"] = {
				["base_skill_area_of_effect_+%"] = {
					["max"] = 7, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 4, 
					["statOrder"] = 1564, 
				}, 
			}, 
		}, 
		[13] = {
			["id"] = "vaal_small_projectile_speed", 
			["dn"] = "Projectile Speed", 
			["sd"] = {
				[1] = "(7-12)% increased Projectile Speed", 
			}, 
			["sortedStats"] = {
				[1] = "base_projectile_speed_+%", 
			}, 
			["stats"] = {
				["base_projectile_speed_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 877, 
				}, 
			}, 
		}, 
		[14] = {
			["id"] = "vaal_small_critical_strike_chance", 
			["dn"] = "Critical Strike Chance", 
			["sd"] = {
				[1] = "(7-14)% increased Critical Hit Chance", 
			}, 
			["sortedStats"] = {
				[1] = "critical_strike_chance_+%", 
			}, 
			["stats"] = {
				["critical_strike_chance_+%"] = {
					["max"] = 14, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 9353, 
				}, 
			}, 
		}, 
		[15] = {
			["id"] = "vaal_small_critical_strike_multiplier", 
			["dn"] = "Critical Strike Multiplier", 
			["sd"] = {
				[1] = "(6-10)% increased Critical Damage Bonus", 
			}, 
			["sortedStats"] = {
				[1] = "base_critical_strike_multiplier_+", 
			}, 
			["stats"] = {
				["base_critical_strike_multiplier_+"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 6, 
					["statOrder"] = 929, 
				}, 
			}, 
		}, 
		[16] = {
			["id"] = "vaal_small_attack_speed", 
			["dn"] = "Attack Speed", 
			["sd"] = {
				[1] = "(3-4)% increased Attack Speed", 
			}, 
			["sortedStats"] = {
				[1] = "attack_speed_+%", 
			}, 
			["stats"] = {
				["attack_speed_+%"] = {
					["max"] = 4, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 3, 
					["statOrder"] = 933, 
				}, 
			}, 
		}, 
		[17] = {
			["id"] = "vaal_small_cast_speed", 
			["dn"] = "Cast Speed", 
			["sd"] = {
				[1] = "(2-3)% increased Cast Speed", 
			}, 
			["sortedStats"] = {
				[1] = "base_cast_speed_+%", 
			}, 
			["stats"] = {
				["base_cast_speed_+%"] = {
					["max"] = 3, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 2, 
					["statOrder"] = 934, 
				}, 
			}, 
		}, 
		[18] = {
			["id"] = "vaal_small_movement_speed", 
			["dn"] = "Movement Speed", 
			["sd"] = {
				[1] = "(2-3)% increased Movement Speed", 
			}, 
			["sortedStats"] = {
				[1] = "base_movement_velocity_+%", 
			}, 
			["stats"] = {
				["base_movement_velocity_+%"] = {
					["max"] = 3, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 2, 
					["statOrder"] = 828, 
				}, 
			}, 
		}, 
		[19] = {
			["id"] = "vaal_small_chance_to_ignite", 
			["dn"] = "Chance To Ignite", 
			["sd"] = {
				[1] = "(3-6)% chance to Ignite", 
			}, 
			["sortedStats"] = {
				[1] = "base_chance_to_ignite_%", 
			}, 
			["stats"] = {
				["base_chance_to_ignite_%"] = {
					["max"] = 6, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 3, 
					["statOrder"] = 977, 
				}, 
			}, 
		}, 
		[20] = {
			["id"] = "vaal_small_chance_to_freeze", 
			["dn"] = "Chance To Freeze", 
			["sd"] = {
				[1] = "(3-6)% chance to Freeze", 
			}, 
			["sortedStats"] = {
				[1] = "base_chance_to_freeze_%", 
			}, 
			["stats"] = {
				["base_chance_to_freeze_%"] = {
					["max"] = 6, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 3, 
					["statOrder"] = 979, 
				}, 
			}, 
		}, 
		[21] = {
			["id"] = "vaal_small_chance_to_shock", 
			["dn"] = "Chance To Shock", 
			["sd"] = {
				[1] = "(3-6)% chance to Shock", 
			}, 
			["sortedStats"] = {
				[1] = "base_chance_to_shock_%", 
			}, 
			["stats"] = {
				["base_chance_to_shock_%"] = {
					["max"] = 6, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 3, 
					["statOrder"] = 981, 
				}, 
			}, 
		}, 
		[22] = {
			["id"] = "vaal_small_duration", 
			["dn"] = "Duration", 
			["sd"] = {
				[1] = "(4-7)% increased Skill Effect Duration", 
			}, 
			["sortedStats"] = {
				[1] = "skill_effect_duration_+%", 
			}, 
			["stats"] = {
				["skill_effect_duration_+%"] = {
					["max"] = 7, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 4, 
					["statOrder"] = 1579, 
				}, 
			}, 
		}, 
		[23] = {
			["id"] = "vaal_small_life", 
			["dn"] = "Life", 
			["sd"] = {
				[1] = "(2-4)% increased maximum Life", 
			}, 
			["sortedStats"] = {
				[1] = "maximum_life_+%", 
			}, 
			["stats"] = {
				["maximum_life_+%"] = {
					["max"] = 4, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 2, 
					["statOrder"] = 872, 
				}, 
			}, 
		}, 
		[24] = {
			["id"] = "vaal_small_mana", 
			["dn"] = "Mana", 
			["sd"] = {
				[1] = "(4-6)% increased maximum Mana", 
			}, 
			["sortedStats"] = {
				[1] = "maximum_mana_+%", 
			}, 
			["stats"] = {
				["maximum_mana_+%"] = {
					["max"] = 6, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 4, 
					["statOrder"] = 874, 
				}, 
			}, 
		}, 
		[25] = {
			["id"] = "vaal_small_mana_regeneration", 
			["dn"] = "Mana Regeneration", 
			["sd"] = {
				[1] = "(12-17)% increased Mana Regeneration Rate", 
			}, 
			["sortedStats"] = {
				[1] = "mana_regeneration_rate_+%", 
			}, 
			["stats"] = {
				["mana_regeneration_rate_+%"] = {
					["max"] = 17, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 12, 
					["statOrder"] = 965, 
				}, 
			}, 
		}, 
		[26] = {
			["id"] = "vaal_small_armour", 
			["dn"] = "Armour", 
			["sd"] = {
				[1] = "(7-12)% increased Armour", 
			}, 
			["sortedStats"] = {
				[1] = "physical_damage_reduction_rating_+%", 
			}, 
			["stats"] = {
				["physical_damage_reduction_rating_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 866, 
				}, 
			}, 
		}, 
		[27] = {
			["id"] = "vaal_small_evasion", 
			["dn"] = "Evasion", 
			["sd"] = {
				[1] = "(7-12)% increased Evasion Rating", 
			}, 
			["sortedStats"] = {
				[1] = "evasion_rating_+%", 
			}, 
			["stats"] = {
				["evasion_rating_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 7, 
					["statOrder"] = 868, 
				}, 
			}, 
		}, 
		[28] = {
			["id"] = "vaal_small_energy_shield", 
			["dn"] = "Energy Shield", 
			["sd"] = {
				[1] = "(3-5)% increased maximum Energy Shield", 
			}, 
			["sortedStats"] = {
				[1] = "maximum_energy_shield_+%", 
			}, 
			["stats"] = {
				["maximum_energy_shield_+%"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 3, 
					["statOrder"] = 870, 
				}, 
			}, 
		}, 
		[29] = {
			["id"] = "vaal_small_attack_block", 
			["dn"] = "Attack Block", 
			["sd"] = {
				[1] = "+1% to Block chance", 
			}, 
			["sortedStats"] = {
				[1] = "additional_block_%", 
			}, 
			["stats"] = {
				["additional_block_%"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 2132, 
				}, 
			}, 
		}, 
		[30] = {
			["id"] = "vaal_small_spell_block", 
			["dn"] = "Spell Block", 
			["sd"] = {
				[1] = "1% Chance to Block Spell Damage", 
			}, 
			["sortedStats"] = {
				[1] = "base_spell_block_%", 
			}, 
			["stats"] = {
				["base_spell_block_%"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 864, 
				}, 
			}, 
		}, 
		[31] = {
			["id"] = "vaal_small_attack_dodge", 
			["dn"] = "Attack Dodge", 
			["sd"] = {
				[1] = "3% chance to Avoid Elemental Ailments", 
			}, 
			["sortedStats"] = {
				[1] = "avoid_all_elemental_status_%", 
			}, 
			["stats"] = {
				["avoid_all_elemental_status_%"] = {
					["max"] = 3, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 3, 
					["statOrder"] = 1533, 
				}, 
			}, 
		}, 
		[32] = {
			["id"] = "vaal_small_spell_dodge", 
			["dn"] = "Spell Dodge", 
			["sd"] = {
				[1] = "+2% chance to Suppress Spell Damage", 
			}, 
			["sortedStats"] = {
				[1] = "base_spell_suppression_chance_%", 
			}, 
			["stats"] = {
				["base_spell_suppression_chance_%"] = {
					["max"] = 2, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 2, 
					["statOrder"] = 954, 
				}, 
			}, 
		}, 
		[33] = {
			["id"] = "vaal_small_aura_effect", 
			["dn"] = "Aura Effect", 
			["sd"] = {
				[1] = "(2-4)% increased effect of Non-Curse Auras from your Skills", 
			}, 
			["sortedStats"] = {
				[1] = "non_curse_aura_effect_+%", 
			}, 
			["stats"] = {
				["non_curse_aura_effect_+%"] = {
					["max"] = 4, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 2, 
					["statOrder"] = 3181, 
				}, 
			}, 
		}, 
		[34] = {
			["id"] = "vaal_small_curse_effect", 
			["dn"] = "Curse Effect", 
			["sd"] = {
				[1] = "2% increased Effect of your Curses", 
			}, 
			["sortedStats"] = {
				[1] = "curse_effect_+%", 
			}, 
			["stats"] = {
				["curse_effect_+%"] = {
					["max"] = 2, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 2, 
					["statOrder"] = 2266, 
				}, 
			}, 
		}, 
		[35] = {
			["id"] = "vaal_small_fire_resistance", 
			["dn"] = "Fire Resistance", 
			["sd"] = {
				[1] = "+(9-14)% to Fire Resistance", 
			}, 
			["sortedStats"] = {
				[1] = "base_fire_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_fire_damage_resistance_%"] = {
					["max"] = 14, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 9, 
					["statOrder"] = 950, 
				}, 
			}, 
		}, 
		[36] = {
			["id"] = "vaal_small_cold_resistance", 
			["dn"] = "Cold Resistance", 
			["sd"] = {
				[1] = "+(9-14)% to Cold Resistance", 
			}, 
			["sortedStats"] = {
				[1] = "base_cold_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_cold_damage_resistance_%"] = {
					["max"] = 14, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 9, 
					["statOrder"] = 951, 
				}, 
			}, 
		}, 
		[37] = {
			["id"] = "vaal_small_lightning_resistance", 
			["dn"] = "Lightning Resistance", 
			["sd"] = {
				[1] = "+(9-14)% to Lightning Resistance", 
			}, 
			["sortedStats"] = {
				[1] = "base_lightning_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_lightning_damage_resistance_%"] = {
					["max"] = 14, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 9, 
					["statOrder"] = 952, 
				}, 
			}, 
		}, 
		[38] = {
			["id"] = "vaal_small_chaos_resistance", 
			["dn"] = "Chaos Resistance", 
			["sd"] = {
				[1] = "+(6-10)% to Chaos Resistance", 
			}, 
			["sortedStats"] = {
				[1] = "base_chaos_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_chaos_damage_resistance_%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 6, 
					["statOrder"] = 953, 
				}, 
			}, 
		}, 
		[39] = {
			["id"] = "karui_attribute_strength", 
			["dn"] = "Strength", 
			["sd"] = {
				[1] = "+2 to Strength", 
			}, 
			["sortedStats"] = {
				[1] = "base_strength", 
			}, 
			["stats"] = {
				["base_strength"] = {
					["max"] = 2, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 2, 
					["statOrder"] = 9322, 
				}, 
			}, 
		}, 
		[40] = {
			["id"] = "karui_small_strength", 
			["dn"] = "Strength", 
			["sd"] = {
				[1] = "+4 to Strength", 
			}, 
			["sortedStats"] = {
				[1] = "base_strength", 
			}, 
			["stats"] = {
				["base_strength"] = {
					["max"] = 4, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 4, 
					["statOrder"] = 9322, 
				}, 
			}, 
		}, 
		[41] = {
			["id"] = "karui_notable_add_strength", 
			["dn"] = "Add Strength", 
			["sd"] = {
				[1] = "+20 to Strength", 
			}, 
			["sortedStats"] = {
				[1] = "base_strength", 
			}, 
			["stats"] = {
				["base_strength"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 9322, 
				}, 
			}, 
		}, 
		[42] = {
			["id"] = "karui_notable_add_percent_strength", 
			["dn"] = "Add Percent Strength", 
			["sd"] = {
				[1] = "5% increased Strength", 
			}, 
			["sortedStats"] = {
				[1] = "strength_+%", 
			}, 
			["stats"] = {
				["strength_+%"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 5, 
					["statOrder"] = 9323, 
				}, 
			}, 
		}, 
		[43] = {
			["id"] = "karui_notable_add_armour", 
			["dn"] = "Add Armour", 
			["sd"] = {
				[1] = "20% increased Armour", 
			}, 
			["sortedStats"] = {
				[1] = "physical_damage_reduction_rating_+%", 
			}, 
			["stats"] = {
				["physical_damage_reduction_rating_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 866, 
				}, 
			}, 
		}, 
		[44] = {
			["id"] = "karui_notable_add_leech", 
			["dn"] = "Add Leech", 
			["sd"] = {
			}, 
			["sortedStats"] = {
				[1] = "dummy_stat_display_nothing", 
			}, 
			["stats"] = {
				["dummy_stat_display_nothing"] = {
					["max"] = 40, 
					["min"] = 40, 
					["index"] = 1, 
				}, 
			}, 
		}, 
		[45] = {
			["id"] = "karui_notable_add_double_damage", 
			["dn"] = "Add Double Damage", 
			["sd"] = {
				[1] = "5% chance to deal Double Damage", 
			}, 
			["sortedStats"] = {
				[1] = "chance_to_deal_double_damage_%", 
			}, 
			["stats"] = {
				["chance_to_deal_double_damage_%"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 5, 
					["statOrder"] = 4972, 
				}, 
			}, 
		}, 
		[46] = {
			["id"] = "karui_notable_add_life", 
			["dn"] = "Add Life", 
			["sd"] = {
				[1] = "4% increased maximum Life", 
			}, 
			["sortedStats"] = {
				[1] = "maximum_life_+%", 
			}, 
			["stats"] = {
				["maximum_life_+%"] = {
					["max"] = 4, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 4, 
					["statOrder"] = 872, 
				}, 
			}, 
		}, 
		[47] = {
			["id"] = "karui_notable_add_fortify_effect", 
			["dn"] = "Add Fortify Effect", 
			["sd"] = {
				[1] = "+1 to maximum Fortification", 
			}, 
			["sortedStats"] = {
				[1] = "base_max_fortification", 
			}, 
			["stats"] = {
				["base_max_fortification"] = {
					["max"] = 1, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 4490, 
				}, 
			}, 
		}, 
		[48] = {
			["id"] = "karui_notable_add_life_regen", 
			["dn"] = "Add Life Regen", 
			["sd"] = {
				[1] = "Regenerate 1% of Life per second", 
			}, 
			["sortedStats"] = {
				[1] = "life_regeneration_rate_per_minute_%", 
			}, 
			["stats"] = {
				["life_regeneration_rate_per_minute_%"] = {
					["max"] = 1, 
					["fmt"] = "g", 
					["index"] = 1, 
					["min"] = 1, 
					["statOrder"] = 1627, 
				}, 
			}, 
		}, 
		[49] = {
			["id"] = "karui_notable_add_fire_resistance", 
			["dn"] = "Add Fire Resistance", 
			["sd"] = {
				[1] = "+20% to Fire Resistance", 
			}, 
			["sortedStats"] = {
				[1] = "base_fire_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_fire_damage_resistance_%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 950, 
				}, 
			}, 
		}, 
		[50] = {
			["id"] = "karui_notable_add_melee_damage", 
			["dn"] = "Add Melee Damage", 
			["sd"] = {
				[1] = "20% increased Melee Damage", 
			}, 
			["sortedStats"] = {
				[1] = "melee_damage_+%", 
			}, 
			["stats"] = {
				["melee_damage_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 1126, 
				}, 
			}, 
		}, 
		[51] = {
			["id"] = "karui_notable_add_damage_from_crits", 
			["dn"] = "Add Damage From Crits", 
			["sd"] = {
				[1] = "Hits against you have 10% reduced Critical Damage Bonus", 
			}, 
			["sortedStats"] = {
				[1] = "base_self_critical_strike_multiplier_-%", 
			}, 
			["stats"] = {
				["base_self_critical_strike_multiplier_-%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 942, 
				}, 
			}, 
		}, 
		[52] = {
			["id"] = "karui_notable_add_melee_crit_chance", 
			["dn"] = "Add Melee Crit Chance", 
			["sd"] = {
				[1] = "30% increased Melee Critical Hit Chance", 
			}, 
			["sortedStats"] = {
				[1] = "melee_critical_strike_chance_+%", 
			}, 
			["stats"] = {
				["melee_critical_strike_chance_+%"] = {
					["max"] = 30, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 30, 
					["statOrder"] = 1312, 
				}, 
			}, 
		}, 
		[53] = {
			["id"] = "karui_notable_add_burning_damage", 
			["dn"] = "Add Burning Damage", 
			["sd"] = {
				[1] = "20% increased Burning Damage", 
			}, 
			["sortedStats"] = {
				[1] = "burn_damage_+%", 
			}, 
			["stats"] = {
				["burn_damage_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 1561, 
				}, 
			}, 
		}, 
		[54] = {
			["id"] = "karui_notable_add_totem_damage", 
			["dn"] = "Add Totem Damage", 
			["sd"] = {
				[1] = "20% increased Totem Damage", 
			}, 
			["sortedStats"] = {
				[1] = "totem_damage_+%", 
			}, 
			["stats"] = {
				["totem_damage_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 1091, 
				}, 
			}, 
		}, 
		[55] = {
			["id"] = "karui_notable_add_melee_crit_multi", 
			["dn"] = "Add Melee Crit Multi", 
			["sd"] = {
				[1] = "+15% to Melee Critical Damage Bonus", 
			}, 
			["sortedStats"] = {
				[1] = "melee_weapon_critical_strike_multiplier_+", 
			}, 
			["stats"] = {
				["melee_weapon_critical_strike_multiplier_+"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 15, 
					["statOrder"] = 1331, 
				}, 
			}, 
		}, 
		[56] = {
			["id"] = "karui_notable_add_physical_damage", 
			["dn"] = "Add Physical Damage", 
			["sd"] = {
				[1] = "20% increased Physical Damage", 
			}, 
			["sortedStats"] = {
				[1] = "physical_damage_+%", 
			}, 
			["stats"] = {
				["physical_damage_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 9380, 
				}, 
			}, 
		}, 
		[57] = {
			["id"] = "karui_notable_add_warcry_buff_effect", 
			["dn"] = "Add Warcry Buff Effect", 
			["sd"] = {
				[1] = "8% increased Warcry Buff Effect", 
			}, 
			["sortedStats"] = {
				[1] = "warcry_buff_effect_+%", 
			}, 
			["stats"] = {
				["warcry_buff_effect_+%"] = {
					["max"] = 8, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 8, 
					["statOrder"] = 9115, 
				}, 
			}, 
		}, 
		[58] = {
			["id"] = "karui_notable_add_totem_placement_speed", 
			["dn"] = "Add Totem Placement Speed", 
			["sd"] = {
				[1] = "12% increased Totem Placement speed", 
			}, 
			["sortedStats"] = {
				[1] = "summon_totem_cast_speed_+%", 
			}, 
			["stats"] = {
				["summon_totem_cast_speed_+%"] = {
					["max"] = 12, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 12, 
					["statOrder"] = 2250, 
				}, 
			}, 
		}, 
		[59] = {
			["id"] = "karui_notable_add_stun_duration", 
			["dn"] = "Add Stun Duration", 
			["sd"] = {
				[1] = "20% increased Stun Duration on Enemies", 
			}, 
			["sortedStats"] = {
				[1] = "base_stun_duration_+%", 
			}, 
			["stats"] = {
				["base_stun_duration_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 975, 
				}, 
			}, 
		}, 
		[60] = {
			["id"] = "karui_notable_add_faster_ignite", 
			["dn"] = "Add Faster Ignite", 
			["sd"] = {
				[1] = "Ignites you inflict deal Damage 10% faster", 
			}, 
			["sortedStats"] = {
				[1] = "faster_burn_%", 
			}, 
			["stats"] = {
				["faster_burn_%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 2236, 
				}, 
			}, 
		}, 
		[61] = {
			["id"] = "karui_notable_add_reduced_stun_threshold", 
			["dn"] = "Add Reduced Stun Threshold", 
			["sd"] = {
				[1] = "10% reduced Enemy Stun Threshold", 
			}, 
			["sortedStats"] = {
				[1] = "base_stun_threshold_reduction_+%", 
			}, 
			["stats"] = {
				["base_stun_threshold_reduction_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 972, 
				}, 
			}, 
		}, 
		[62] = {
			["id"] = "karui_notable_add_physical_added_as_fire", 
			["dn"] = "Add Physical Added As Fire", 
			["sd"] = {
				[1] = "Gain 5% of Physical Damage as Extra Fire Damage", 
			}, 
			["sortedStats"] = {
				[1] = "non_skill_base_physical_damage_%_to_gain_as_fire", 
			}, 
			["stats"] = {
				["non_skill_base_physical_damage_%_to_gain_as_fire"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 5, 
					["statOrder"] = 1614, 
				}, 
			}, 
		}, 
		[63] = {
			["id"] = "karui_notable_add_rage_on_melee_hit", 
			["dn"] = "Add Rage On Melee Hit", 
			["sd"] = {
				[1] = "5% of Physical Damage from Hits taken as Fire Damage", 
			}, 
			["sortedStats"] = {
				[1] = "physical_damage_taken_%_as_fire", 
			}, 
			["stats"] = {
				["physical_damage_taken_%_as_fire"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 5, 
					["statOrder"] = 2121, 
				}, 
			}, 
		}, 
		[64] = {
			["id"] = "karui_notable_add_endurance_charge_on_kill", 
			["dn"] = "Add Endurance Charge On Kill", 
			["sd"] = {
				[1] = "5% chance to gain an Endurance Charge on Kill", 
			}, 
			["sortedStats"] = {
				[1] = "endurance_charge_on_kill_%", 
			}, 
			["stats"] = {
				["endurance_charge_on_kill_%"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 5, 
					["statOrder"] = 2293, 
				}, 
			}, 
		}, 
		[65] = {
			["id"] = "karui_notable_add_intimidate", 
			["dn"] = "Add Intimidate", 
			["sd"] = {
				[1] = "10% chance to Intimidate Enemies for 4 seconds on Hit", 
			}, 
			["sortedStats"] = {
				[1] = "chance_to_intimidate_on_hit_%", 
			}, 
			["stats"] = {
				["chance_to_intimidate_on_hit_%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 5025, 
				}, 
			}, 
		}, 
		[66] = {
			["id"] = "maraketh_attribute_dex", 
			["dn"] = "Dex", 
			["sd"] = {
				[1] = "+2 to Dexterity", 
			}, 
			["sortedStats"] = {
				[1] = "base_dexterity", 
			}, 
			["stats"] = {
				["base_dexterity"] = {
					["max"] = 2, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 2, 
					["statOrder"] = 9324, 
				}, 
			}, 
		}, 
		[67] = {
			["id"] = "maraketh_small_dex", 
			["dn"] = "Dex", 
			["sd"] = {
				[1] = "+4 to Dexterity", 
			}, 
			["sortedStats"] = {
				[1] = "base_dexterity", 
			}, 
			["stats"] = {
				["base_dexterity"] = {
					["max"] = 4, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 4, 
					["statOrder"] = 9324, 
				}, 
			}, 
		}, 
		[68] = {
			["id"] = "maraketh_notable_add_dexterity", 
			["dn"] = "Add Dexterity", 
			["sd"] = {
				[1] = "+20 to Dexterity", 
			}, 
			["sortedStats"] = {
				[1] = "base_dexterity", 
			}, 
			["stats"] = {
				["base_dexterity"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 9324, 
				}, 
			}, 
		}, 
		[69] = {
			["id"] = "maraketh_notable_add_percent_dexterity", 
			["dn"] = "Add Percent Dexterity", 
			["sd"] = {
				[1] = "5% increased Dexterity", 
			}, 
			["sortedStats"] = {
				[1] = "dexterity_+%", 
			}, 
			["stats"] = {
				["dexterity_+%"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 5, 
					["statOrder"] = 9325, 
				}, 
			}, 
		}, 
		[70] = {
			["id"] = "maraketh_notable_add_evasion", 
			["dn"] = "Add Evasion", 
			["sd"] = {
				[1] = "20% increased Evasion Rating", 
			}, 
			["sortedStats"] = {
				[1] = "evasion_rating_+%", 
			}, 
			["stats"] = {
				["evasion_rating_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 868, 
				}, 
			}, 
		}, 
		[71] = {
			["id"] = "maraketh_notable_add_flask_charges", 
			["dn"] = "Add Flask Charges", 
			["sd"] = {
				[1] = "10% increased Flask and Charm Charges gained", 
			}, 
			["sortedStats"] = {
				[1] = "charges_gained_+%", 
			}, 
			["stats"] = {
				["charges_gained_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 970, 
				}, 
			}, 
		}, 
		[72] = {
			["id"] = "maraketh_notable_add_speed", 
			["dn"] = "Add Speed", 
			["sd"] = {
				[1] = "5% increased Attack and Cast Speed", 
			}, 
			["sortedStats"] = {
				[1] = "attack_and_cast_speed_+%", 
			}, 
			["stats"] = {
				["attack_and_cast_speed_+%"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 5, 
					["statOrder"] = 1717, 
				}, 
			}, 
		}, 
		[73] = {
			["id"] = "maraketh_notable_add_life", 
			["dn"] = "Add Life", 
			["sd"] = {
				[1] = "4% increased maximum Life", 
			}, 
			["sortedStats"] = {
				[1] = "maximum_life_+%", 
			}, 
			["stats"] = {
				["maximum_life_+%"] = {
					["max"] = 4, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 4, 
					["statOrder"] = 872, 
				}, 
			}, 
		}, 
		[74] = {
			["id"] = "maraketh_notable_add_blind", 
			["dn"] = "Add Blind", 
			["sd"] = {
				[1] = "5% chance to Blind Enemies on Hit", 
			}, 
			["sortedStats"] = {
				[1] = "global_chance_to_blind_on_hit_%", 
			}, 
			["stats"] = {
				["global_chance_to_blind_on_hit_%"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 5, 
					["statOrder"] = 9362, 
				}, 
			}, 
		}, 
		[75] = {
			["id"] = "maraketh_notable_add_movement_speed", 
			["dn"] = "Add Movement Speed", 
			["sd"] = {
				[1] = "5% increased Movement Speed", 
			}, 
			["sortedStats"] = {
				[1] = "base_movement_velocity_+%", 
			}, 
			["stats"] = {
				["base_movement_velocity_+%"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 5, 
					["statOrder"] = 828, 
				}, 
			}, 
		}, 
		[76] = {
			["id"] = "maraketh_notable_add_cold_resistance", 
			["dn"] = "Add Cold Resistance", 
			["sd"] = {
				[1] = "+20% to Cold Resistance", 
			}, 
			["sortedStats"] = {
				[1] = "base_cold_damage_resistance_%", 
			}, 
			["stats"] = {
				["base_cold_damage_resistance_%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 951, 
				}, 
			}, 
		}, 
		[77] = {
			["id"] = "maraketh_notable_add_projectile_damage", 
			["dn"] = "Add Projectile Damage", 
			["sd"] = {
				[1] = "20% increased Projectile Damage", 
			}, 
			["sortedStats"] = {
				[1] = "projectile_damage_+%", 
			}, 
			["stats"] = {
				["projectile_damage_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 1675, 
				}, 
			}, 
		}, 
		[78] = {
			["id"] = "maraketh_notable_add_stun_avoid", 
			["dn"] = "Add Stun Avoid", 
			["sd"] = {
				[1] = "20% chance to Avoid being Stunned", 
			}, 
			["sortedStats"] = {
				[1] = "base_avoid_stun_%", 
			}, 
			["stats"] = {
				["base_avoid_stun_%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 1541, 
				}, 
			}, 
		}, 
		[79] = {
			["id"] = "maraketh_notable_add_global_crit_chance", 
			["dn"] = "Add Global Crit Chance", 
			["sd"] = {
				[1] = "25% increased Critical Hit Chance", 
			}, 
			["sortedStats"] = {
				[1] = "critical_strike_chance_+%", 
			}, 
			["stats"] = {
				["critical_strike_chance_+%"] = {
					["max"] = 25, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 9353, 
				}, 
			}, 
		}, 
		[80] = {
			["id"] = "maraketh_notable_add_poison_damage", 
			["dn"] = "Add Poison Damage", 
			["sd"] = {
			}, 
			["sortedStats"] = {
				[1] = "dummy_stat_display_nothing", 
			}, 
			["stats"] = {
				["dummy_stat_display_nothing"] = {
					["max"] = 20, 
					["min"] = 20, 
					["index"] = 1, 
				}, 
			}, 
		}, 
		[81] = {
			["id"] = "maraketh_notable_add_minion_damage", 
			["dn"] = "Add Minion Damage", 
			["sd"] = {
				[1] = "Minions deal 20% increased Damage", 
			}, 
			["sortedStats"] = {
				[1] = "minion_damage_+%", 
			}, 
			["stats"] = {
				["minion_damage_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 1655, 
				}, 
			}, 
		}, 
		[82] = {
			["id"] = "maraketh_notable_add_accuracy", 
			["dn"] = "Add Accuracy", 
			["sd"] = {
				[1] = "5% increased Accuracy Rating", 
			}, 
			["sortedStats"] = {
				[1] = "accuracy_rating_+%", 
			}, 
			["stats"] = {
				["accuracy_rating_+%"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 5, 
					["statOrder"] = 1270, 
				}, 
			}, 
		}, 
		[83] = {
			["id"] = "maraketh_notable_add_elemental_damage", 
			["dn"] = "Add Elemental Damage", 
			["sd"] = {
				[1] = "20% increased Elemental Damage", 
			}, 
			["sortedStats"] = {
				[1] = "elemental_damage_+%", 
			}, 
			["stats"] = {
				["elemental_damage_+%"] = {
					["max"] = 20, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 20, 
					["statOrder"] = 1660, 
				}, 
			}, 
		}, 
		[84] = {
			["id"] = "maraketh_notable_add_aura_effect", 
			["dn"] = "Add Aura Effect", 
			["sd"] = {
				[1] = "8% increased effect of Non-Curse Auras from your Skills", 
			}, 
			["sortedStats"] = {
				[1] = "non_curse_aura_effect_+%", 
			}, 
			["stats"] = {
				["non_curse_aura_effect_+%"] = {
					["max"] = 8, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 8, 
					["statOrder"] = 3181, 
				}, 
			}, 
		}, 
		[85] = {
			["id"] = "maraketh_notable_add_minion_movement_speed", 
			["dn"] = "Add Minion Movement Speed", 
			["sd"] = {
				[1] = "Minions have 15% increased Movement Speed", 
			}, 
			["sortedStats"] = {
				[1] = "minion_movement_speed_+%", 
			}, 
			["stats"] = {
				["minion_movement_speed_+%"] = {
					["max"] = 15, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 15, 
					["statOrder"] = 1460, 
				}, 
			}, 
		}, 
		[86] = {
			["id"] = "maraketh_notable_add_ailment_duration", 
			["dn"] = "Add Ailment Duration", 
			["sd"] = {
				[1] = "10% increased Duration of Elemental Ailments on Enemies", 
			}, 
			["sortedStats"] = {
				[1] = "base_elemental_status_ailment_duration_+%", 
			}, 
			["stats"] = {
				["base_elemental_status_ailment_duration_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 1551, 
				}, 
			}, 
		}, 
		[87] = {
			["id"] = "maraketh_notable_add_faster_poison", 
			["dn"] = "Add Faster Poison", 
			["sd"] = {
				[1] = "Poisons you inflict deal Damage 10% faster", 
			}, 
			["sortedStats"] = {
				[1] = "faster_poison_%", 
			}, 
			["stats"] = {
				["faster_poison_%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 5815, 
				}, 
			}, 
		}, 
		[88] = {
			["id"] = "maraketh_notable_add_ailment_effect", 
			["dn"] = "Add Ailment Effect", 
			["sd"] = {
				[1] = "10% increased Magnitude of Non-Damaging Ailments you inflict", 
			}, 
			["sortedStats"] = {
				[1] = "non_damaging_ailment_effect_+%", 
			}, 
			["stats"] = {
				["non_damaging_ailment_effect_+%"] = {
					["max"] = 10, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 10, 
					["statOrder"] = 8071, 
				}, 
			}, 
		}, 
		[89] = {
			["id"] = "maraketh_notable_add_physical_added_as_cold", 
			["dn"] = "Add Physical Added As Cold", 
			["sd"] = {
				[1] = "Gain 5% of Physical Damage as Extra Cold Damage", 
			}, 
			["sortedStats"] = {
				[1] = "non_skill_base_physical_damage_%_to_gain_as_cold", 
			}, 
			["stats"] = {
				["non_skill_base_physical_damage_%_to_gain_as_cold"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 5, 
					["statOrder"] = 1615, 
				}, 
			}, 
		}, 
		[90] = {
			["id"] = "maraketh_notable_add_alchemists_genius", 
			["dn"] = "Add Alchemists Genius", 
			["sd"] = {
				[1] = "25% chance to gain Alchemist's Genius when you use a Flask", 
			}, 
			["sortedStats"] = {
				[1] = "gain_alchemists_genius_on_flask_use_%", 
			}, 
			["stats"] = {
				["gain_alchemists_genius_on_flask_use_%"] = {
					["max"] = 25, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 25, 
					["statOrder"] = 5984, 
				}, 
			}, 
		}, 
		[91] = {
			["id"] = "maraketh_notable_add_frenzy_charge_on_kill", 
			["dn"] = "Add Frenzy Charge On Kill", 
			["sd"] = {
				[1] = "5% chance to gain a Frenzy Charge on Kill", 
			}, 
			["sortedStats"] = {
				[1] = "add_frenzy_charge_on_kill_%_chance", 
			}, 
			["stats"] = {
				["add_frenzy_charge_on_kill_%_chance"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 5, 
					["statOrder"] = 2295, 
				}, 
			}, 
		}, 
		[92] = {
			["id"] = "maraketh_notable_add_onslaught", 
			["dn"] = "Add Onslaught", 
			["sd"] = {
				[1] = "You gain Onslaught for 8 seconds on Kill", 
			}, 
			["sortedStats"] = {
				[1] = "onslaught_buff_duration_on_kill_ms", 
			}, 
			["stats"] = {
				["onslaught_buff_duration_on_kill_ms"] = {
					["max"] = 8, 
					["fmt"] = "g", 
					["index"] = 1, 
					["min"] = 8, 
					["statOrder"] = 2307, 
				}, 
			}, 
		}, 
		[93] = {
			["id"] = "templar_small_devotion", 
			["dn"] = "Devotion", 
			["sd"] = {
				[1] = "+5 to Devotion", 
			}, 
			["sortedStats"] = {
				[1] = "base_devotion", 
			}, 
			["stats"] = {
				["base_devotion"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 5, 
					["statOrder"] = 9321, 
				}, 
			}, 
		}, 
		[94] = {
			["id"] = "templar_notable_devotion", 
			["dn"] = "Devotion", 
			["sd"] = {
				[1] = "+5 to Devotion", 
			}, 
			["sortedStats"] = {
				[1] = "base_devotion", 
			}, 
			["stats"] = {
				["base_devotion"] = {
					["max"] = 5, 
					["fmt"] = "d", 
					["index"] = 1, 
					["min"] = 5, 
					["statOrder"] = 9321, 
				}, 
			}, 
		}, 
	}, 
}