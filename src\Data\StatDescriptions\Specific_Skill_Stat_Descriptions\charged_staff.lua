-- This file is automatically generated, do not edit!
-- Item data (c) Grinding Gear Games

return {
	[1]={
		[1]={
			[1]={
				limit={
					[1]={
						[1]="#",
						[2]="#"
					},
					[2]={
						[1]="#",
						[2]="#"
					}
				},
				text="Buff grants {0} to {1} Added Quarterstaff Attack Lightning damage per Power Charge Consumed, up to your maximum Power Charges"
			}
		},
		stats={
			[1]="charged_staff_attack_minimum_added_lightning_damage_per_stack",
			[2]="charged_staff_attack_maximum_added_lightning_damage_per_stack"
		}
	},
	[2]={
		[1]={
			[1]={
				[1]={
					k="milliseconds_to_seconds_2dp_if_required",
					v=1
				},
				limit={
					[1]={
						[1]="#",
						[2]="#"
					}
				},
				text="Buff duration is {0} seconds per Power Charge Consumed"
			}
		},
		stats={
			[1]="charged_staff_buff_duration_per_stack_ms"
		}
	},
	["charged_staff_attack_maximum_added_lightning_damage_per_stack"]=1,
	["charged_staff_attack_minimum_added_lightning_damage_per_stack"]=1,
	["charged_staff_buff_duration_per_stack_ms"]=2,
	parent="skill_stat_descriptions"
}