name: Create new installer
on:
  release:
    types: [published]
  workflow_dispatch:
env:
  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
jobs:
  release:
    runs-on: windows-latest
    steps:
            - name: Checkout
              uses: actions/checkout@v3
              with:
                repository: 'PathOfBuildingCommunity/PathOfBuilding-Installer'
                ref: 'master'
                ssh-key: '${{ secrets.POB_INSTALLER_KEY }}'
            - name: Create installer
              run: 'python3 make_release.py --game-version 2'
            - name: Upload artifact
              run: >
                gh release upload ${{ github.event.release.tag_name }} (Get-ChildItem Dist -File).FullName --clobber -R ${{ github.repository }};

